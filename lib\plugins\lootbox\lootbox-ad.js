ig.module('plugins.lootbox.lootbox-ad')
    .requires(
        'plugins.lootbox.lootbox-game-object'
    )
    .defines(function () {
        ig.LootboxAd = ig.LootboxSimpleButton.extend({

            zIndex: 999999,
            callback: null,
            text: "<PERSON> is showing right now",

            init: function (x, y, settings) {

                var pageW = ig.responsive ? ig.responsive.originalWidth : ig.system.width;
                var pageH = ig.responsive ? ig.responsive.originalHeight : ig.system.height;

                this.parent(pageW / 2, pageH / 2, settings);

                setTimeout(function () {
                    this.text = "Ad completed"
                }.bind(this), 1500);

                setTimeout(function () {
                    this.adSuccess();
                }.bind(this), 2000);

                ig.game.sortEntitiesDeferred();
            },

            adSuccess: function () {
                this.callback(true)
                this.exit()
            }


        });
    });
