/**
 * Created by <PERSON><PERSON> <PERSON>
 * RoomEvent and event types
 */

var RoomEvent = function (type, time, info) {
    var self = {};

    self.init = function (type, time, info) {
        self.type = type;
        self.time = time;
        self.info = info;

        return self;
    };

    self.packData = function () {
        var package = {};
        
        package.type = self.type;
        package.time = self.time;
        package.info = self.info;

        return package;
    };

    return self.init(type, time, info);
};


RoomEvent.EVENT_TYPE = {
    PLAYER_JOIN: 1, // server side
    PLAYER_LEAVE: 2, // server side

    PLAYER_READY: 11,
    AUTO_START_STATE_CHANGE: 12,
    KICK_PLAYER: 13,
    SEND_EMOJI: 14,

    ROOM_START: 22, // server side
    ROOM_STARTED: 23, // server side
    ROOM_END: 25, // server side

    CLIENT_GAME_EVENT: 35,
    NETWORK_GAME_EVENT: 40,
    SYNC_PLAYER_COUNT: 50,
    REPLACE_MATCHING_BOT:60
};


/**
 * Export module
 */
if (typeof module !== "undefined") {
    module.exports.RoomEvent = RoomEvent;
}
