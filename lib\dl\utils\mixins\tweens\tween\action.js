'use strict';

function _typeof (obj) { '@babel/helpers - typeof'; return _typeof = typeof Symbol == 'function' && typeof Symbol.iterator == 'symbol' ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol == 'function' && obj.constructor === Symbol && obj !== Symbol.prototype ? 'symbol' : typeof obj; }, _typeof(obj); }

function _classCallCheck (instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError('Cannot call a class as a function'); } }

function _defineProperties (target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ('value' in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }

function _createClass (Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, 'prototype', { writable: false }); return Constructor; }

ig.module('dl.utils.mixins.tweens.tween.action').requires('dl.utils.mixins.tweens.tween.easing').defines(function () {
  'use strict';

  dl.TweenAction = /* #__PURE__ */(function () {
    function dl_TweenAction (object, properties, duration, settings) {
      _classCallCheck(this, dl_TweenAction);

      this._object = object;
      this._properties = properties;
      this._duration = duration;
      this.__valuesStart = {};
      this.__valuesDelta = {};
      this.__valuesEnd = {};
      this.easing = settings.easing || dl.TweenEasing.Linear.EaseNone;
      this.onCompleteAction = settings.onCompleteAction || null;
      this.onPropertiesChanged = settings.onPropertiesChanged || null;
      this.__timer = null;
      this.__setupValueFlag = false;
      this.__started = false;
      this.__completed = false;
    }

    _createClass(dl_TweenAction, [{
      key: '_initEnd',
      value: function _initEnd (prop, from, to) {
        if (_typeof(from[prop]) !== 'object') {
          to[prop] = from[prop];
        } else {
          for (var subprop in from[prop]) {
            if (!to[prop]) to[prop] = {};

            this._initEnd(subprop, from[prop], to[prop]);
          }
        }
      }
    }, {
      key: '_initStart',
      value: function _initStart (prop, end, from, to) {
        if (_typeof(from[prop]) !== 'object') {
          if (typeof end[prop] !== 'undefined') {
            to[prop] = from[prop];
          }
        } else {
          for (var subprop in from[prop]) {
            if (!to[prop]) to[prop] = {};

            if (typeof end[prop] !== 'undefined') {
              this._initStart(subprop, end[prop], from[prop], to[prop]);
            }
          }
        }
      }
    }, {
      key: '_initDelta',
      value: function _initDelta (prop, delta, start, end) {
        if (_typeof(end[prop]) !== 'object') {
          delta[prop] = end[prop] - start[prop];
        } else {
          for (var subprop in end[prop]) {
            if (!delta[prop]) delta[prop] = {};

            this._initDelta(subprop, delta[prop], start[prop], end[prop]);
          }
        }
      }
    }, {
      key: '_propertyUpdate',
      value: function _propertyUpdate (prop, obj, start, delta, value) {
        if (_typeof(start[prop]) !== 'object') {
          if (typeof start[prop] !== 'undefined') {
            obj[prop] = start[prop] + delta[prop] * value;
          } else {
            obj[prop] = obj[prop];
          }
        } else {
          for (var subprop in start[prop]) {
            this._propertyUpdate(subprop, obj[prop], start[prop], delta[prop], value);
          }
        }
      }
    }, {
      key: '_propertyUpdateFinished',
      value: function _propertyUpdateFinished (prop, obj, end) {
        if (_typeof(end[prop]) !== 'object') {
          obj[prop] = end[prop];
        } else {
          for (var subprop in end[prop]) {
            this._propertyUpdateFinished(subprop, obj[prop], end[prop]);
          }
        }
      }
    }, {
      key: '_setupValues',
      value: function _setupValues () {
        for (var property in this._properties) {
          this._initEnd(property, this._properties, this.__valuesEnd);
        }

        for (var property in this.__valuesEnd) {
          this._initStart(property, this.__valuesEnd, this._object, this.__valuesStart);

          this._initDelta(property, this.__valuesDelta, this._object, this.__valuesEnd);
        }
      }
    }, {
      key: 'update',
      value: function update () {
        if (!this.__started) return false;
        if (this.__completed) return false;

        var elapsed = (this._duration + this.__timer.delta()) / this._duration;

        if (elapsed <= 0) return false;

        if (!this.__setupValueFlag) {
          this._setupValues();

          this.__setupValueFlag = true;
        }

        elapsed = elapsed >= 1 ? 1 : elapsed;

        if (elapsed >= 1) {
          this._updateCompleted(true);
        } else {
          var value = this.easing(elapsed);

          for (var property in this.__valuesDelta) {
            this._propertyUpdate(property, this._object, this.__valuesStart, this.__valuesDelta, value);
          }

          this._onPropertiesChanged();
        }
      }
    }, {
      key: '_updateCompleted',
      value: function _updateCompleted (doCallback) {
        for (var property in this.__valuesEnd) {
          this._propertyUpdateFinished(property, this._object, this.__valuesEnd);
        }

        this._onPropertiesChanged();

        this.__completed = true;
        if (this.__timer) this.__timer.kill();
        
        if (doCallback) {
          this._onCompleteAction();
        }
      }
    }, {
      key: '_onPropertiesChanged',
      value: function _onPropertiesChanged () {
        if (dl.check.isFunction(this.onPropertiesChanged)) {
          this.onPropertiesChanged();
        }
      }
    }, {
      key: '_onCompleteAction',
      value: function _onCompleteAction () {
        if (dl.check.isFunction(this.onCompleteAction)) {
          this.onCompleteAction();
        }
      }
    }, {
      key: 'start',
      value: function start (timer) {
        this.__started = true;
        this.__completed = false;
        if (this.__timer) this.__timer.kill();
        this.__timer = timer;
        this.__setupValueFlag = false;
      }
    }, {
      key: 'pause',
      value: function pause () {
        if (!this.__started) return false;
        if (this.__completed) return false;

        this.__timer.pause();
      }
    }, {
      key: 'resume',
      value: function resume () {
        if (!this.__started) return false;
        if (this.__completed) return false;

        this.__timer.resume();
      }
    }, {
      key: 'stop',
      value: function stop (doCallback) {
        if (!this.__started) return false;
        if (this.__completed) return false;

        if (doCallback) {
          this._onCompleteAction();
        }
      }
    }, {
      key: 'finish',
      value: function finish (doCallback) {
        if (!this.__started) return false;
        if (this.__completed) return false;

        this._updateCompleted(doCallback);
      }
    }, {
      key: 'isCompleted',
      value: function isCompleted () {
        if (!this.__started) return false;
        return this.__completed;
      }
    }]);

    return dl_TweenAction;
  }());
});
