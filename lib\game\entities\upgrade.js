ig.module('game.entities.upgrade')
.requires(
    'impact.entity',
    'game.entities.buttons.button'
)
.defines(function() {
    EntityUpgrade = ig.Entity.extend({
        type:ig.Entity.TYPE.B,
        size:{x:1300,y:1760},
        coin:new ig.Image("media/graphics/sprites/coin.png"),
        upgradeIcon:[
                            new ig.Image("media/graphics/sprites/upgrade/icon1.png"),
                            new ig.Image("media/graphics/sprites/upgrade/icon2.png"),
                            new ig.Image("media/graphics/sprites/upgrade/icon3.png"),
                            new ig.Image("media/graphics/sprites/upgrade/icon4.png"),
                            new ig.Image("media/graphics/sprites/upgrade/icon5.png"),
                            new ig.Image("media/graphics/sprites/upgrade/icon6.png"),
                            new ig.Image("media/graphics/sprites/upgrade/icon7.png"),
                    ],
        upgradeName:['Military Monday','Tactical Tuesday','War Machine Wednesday','Trouble Thursday','Full Attack Friday','Slugfest Saturday','Sabotage Sunday'],
        upgradePrice:[
                        [1000,1080,1166,1259,1360,1469,1587,1714,1851,1999,2159,2332,2519,2721,2939,3174,3428,3702,3998,4318,4663,5036,5439,5874,6344,6852,7400,7992,8631,9321,10067,10872,11742,12681,13695,14791,15974,17252,18632,20123,21733,23472,25350,27378,29568,31933,34488,37247,40227,43445,46921,50675,54729,59107,63836,68943,74458,80415,86848,93796,101300,109404,118156,127608,137817,148842,160749,173609,187498,202498,218698,236194,255090,275497,297537,321340,347047,374811,404796,437180,472154,509926,550720,594778,642360,693749,749249,809189,873924,943838,1019345,1100893,1188964,1284081,1386807,1497752,1617572,1746978,1886736,2037675],
                        [1000,1080,1166,1259,1360,1469,1587,1714,1851,1999,2159,2332,2519,2721,2939,3174,3428,3702,3998,4318,4663,5036,5439,5874,6344,6852,7400,7992,8631,9321,10067,10872,11742,12681,13695,14791,15974,17252,18632,20123,21733,23472,25350,27378,29568,31933,34488,37247,40227,43445,46921,50675,54729,59107,63836,68943,74458,80415,86848,93796,101300,109404,118156,127608,137817,148842,160749,173609,187498,202498,218698,236194,255090,275497,297537,321340,347047,374811,404796,437180,472154,509926,550720,594778,642360,693749,749249,809189,873924,943838,1019345,1100893,1188964,1284081,1386807,1497752,1617572,1746978,1886736,2037675],
                        [1000,1080,1166,1259,1360,1469,1587,1714,1851,1999,2159,2332,2519,2721,2939,3174,3428,3702,3998,4318,4663,5036,5439,5874,6344,6852,7400,7992,8631,9321,10067,10872,11742,12681,13695,14791,15974,17252,18632,20123,21733,23472,25350,27378,29568,31933,34488,37247,40227,43445,46921,50675,54729,59107,63836,68943,74458,80415,86848,93796,101300,109404,118156,127608,137817,148842,160749,173609,187498,202498,218698,236194,255090,275497,297537,321340,347047,374811,404796,437180,472154,509926,550720,594778,642360,693749,749249,809189,873924,943838,1019345,1100893,1188964,1284081,1386807,1497752,1617572,1746978,1886736,2037675],
                        [1000,1080,1166,1259,1360,1469,1587,1714,1851,1999,2159,2332,2519,2721,2939,3174,3428,3702,3998,4318,4663,5036,5439,5874,6344,6852,7400,7992,8631,9321,10067,10872,11742,12681,13695,14791,15974,17252,18632,20123,21733,23472,25350,27378,29568,31933,34488,37247,40227,43445,46921,50675,54729,59107,63836,68943,74458,80415,86848,93796,101300,109404,118156,127608,137817,148842,160749,173609,187498,202498,218698,236194,255090,275497,297537,321340,347047,374811,404796,437180,472154,509926,550720,594778,642360,693749,749249,809189,873924,943838,1019345,1100893,1188964,1284081,1386807,1497752,1617572,1746978,1886736,2037675],
                        [1000,1080,1166,1259,1360,1469,1587,1714,1851,1999,2159,2332,2519,2721,2939,3174,3428,3702,3998,4318,4663,5036,5439,5874,6344,6852,7400,7992,8631,9321,10067,10872,11742,12681,13695,14791,15974,17252,18632,20123,21733,23472,25350,27378,29568,31933,34488,37247,40227,43445,46921,50675,54729,59107,63836,68943,74458,80415,86848,93796,101300,109404,118156,127608,137817,148842,160749,173609,187498,202498,218698,236194,255090,275497,297537,321340,347047,374811,404796,437180,472154,509926,550720,594778,642360,693749,749249,809189,873924,943838,1019345,1100893,1188964,1284081,1386807,1497752,1617572,1746978,1886736,2037675],
                        [1000,1080,1166,1259,1360,1469,1587,1714,1851,1999,2159,2332,2519,2721,2939,3174,3428,3702,3998,4318,4663,5036,5439,5874,6344,6852,7400,7992,8631,9321,10067,10872,11742,12681,13695,14791,15974,17252,18632,20123,21733,23472,25350,27378,29568,31933,34488,37247,40227,43445,46921,50675,54729,59107,63836,68943,74458,80415,86848,93796,101300,109404,118156,127608,137817,148842,160749,173609,187498,202498,218698,236194,255090,275497,297537,321340,347047,374811,404796,437180,472154,509926,550720,594778,642360,693749,749249,809189,873924,943838,1019345,1100893,1188964,1284081,1386807,1497752,1617572,1746978,1886736,2037675],
                        [1000,1080,1166,1259,1360,1469,1587,1714,1851,1999,2159,2332,2519,2721,2939,3174,3428,3702,3998,4318,4663,5036,5439,5874,6344,6852,7400,7992,8631,9321,10067,10872,11742,12681,13695,14791,15974,17252,18632,20123,21733,23472,25350,27378,29568,31933,34488,37247,40227,43445,46921,50675,54729,59107,63836,68943,74458,80415,86848,93796,101300,109404,118156,127608,137817,148842,160749,173609,187498,202498,218698,236194,255090,275497,297537,321340,347047,374811,404796,437180,472154,509926,550720,594778,642360,693749,749249,809189,873924,943838,1019345,1100893,1188964,1284081,1386807,1497752,1617572,1746978,1886736,2037675],
                     ],
        buyButton:[],
        alpha:0,
        windowAnim:false,
        upgradeData:[
                        [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                        [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                        [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                        [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                        [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                        [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                        [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                    ],
        init:function(x,y,settings){
            this.parent(x,y,settings);

            ig.game.popupShow=true;
            ig.game.backendproccess=false;

            this.pointer=ig.game.spawnEntityBackward(EntityPointer,0,0);
            this.scaleY=3.5555;
            this.scale=2.4;

            this.initposx=-1000;
            this.posx=ig.system.width/2-this.size.x/2;
            this.posy=ig.system.height/2-this.size.y/2;

            this.offsetY = this.posy+this.initposx;

            this.windowAnim=true;
            this.tween({offsetY:0,alpha:1},0.6,{easing: ig.Tween.Easing.Back.EaseOut, onComplete:function(){
                this.windowAnim=false;
            }.bind(this)}).start();

            if(ig.game.sessionData.upgradeRawData){
                this.upgradeRawData=ig.game.sessionData.upgradeRawData;
                this.upgradeData=ig.game.sessionData.upgradeRawData;
            }else{
                this.resetUpgradeData();
            }


            this.btclose=ig.game.spawnEntityBackward(EntityUpgradeClose,this.posx+this.size.x,this.posy, {parrent: this,zIndex:this.zIndex+20,scale:this.scale});
            this.initBuyButton();

            // console.log(this.zIndex);

            ig.game.sortEntitiesDeferred();
        },
        resetUpgradeData:function(){
            ig.game.upgradeRawData=this.initUpgradeData;
            ig.game.sessionData.upgradeRawData=this.initUpgradeData;
            ig.game.saveLocalStorage();
        },
        initBuyButton:function(){
            this.buyButton=[];
            var posy=this.posy+56*this.scaleY;
            for(var i=0;i<this.upgradeIcon.length;i++){
                var offsetY=i*62*this.scaleY;
                this.buyButton.push(ig.game.spawnEntityBackward(EntityButtonBuy,this.posx+this.size.x*0.72,posy+offsetY, {parrent: this,zIndex:this.zIndex+10,buyId:i,scale:this.scale}));
            }
        },

        buyUpgrade:function(buyId,price){
            ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.click2);
            var upgradedata=this.upgradeData[buyId];
            for(var i=0;i<upgradedata.length;i++){
                if(upgradedata[i]==0){
                    this.upgradeData[buyId][i]=1;
                    ig.game.sessionData.upgradeRawData=this.upgradeData;
                    this.getUpgradeMultiplier();
                    ig.game.saveLocalStorage();                    
                    ig.game.client.deductUpgrade(price);
                    break;
                }
            }
        },
        getUpgradeMultiplier:function(){
            var cc=ig.game.today-1;
            if(cc==-1) cc=0;
            var upgradeData=ig.game.sessionData.upgradeRawData[cc];
            var result=0;
            for(var i=0;i<upgradeData.length;i++){
                if(upgradeData[i]==1){
                    result +=1;
                }
            }                
            ig.game.multiplierValue=result;
        },
        setPrice:function(buyId){
            var upgradedata=this.upgradeData[buyId];
            for(var i=0;i<upgradedata.length;i++){

                if(upgradedata[i]==0){
                    this.buyButton[buyId].value=this.upgradePrice[buyId][i];
                    if(this.upgradePrice[buyId][i]>ig.game.balance){
                        this.buyButton[buyId].disable=true;
                    }
                    break;
                }else{
                    this.buyButton[buyId].disable=false;                    
                }
            }

            if(upgradedata[99]==1){
                this.buyButton[buyId].disable=true;                        
                this.buyButton[buyId].maxed=true;                        
            }

        },
        close:function(){

            ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.click2);
            this.windowAnim=true;
            this.tween({offsetY:-1000,alpha:0},0.4,{easing: ig.Tween.Easing.Back.EaseIn, onComplete:function(){
                this.windowAnim=false;

                ig.game.popupShow=false;
                this.btclose.kill();

                for(var i=0;i<this.buyButton.length;i++){
                    this.buyButton[i].kill();
                }
                this.pointer.kill();
                // ig.game.enableEntities();
                // ig.game.disableEntities();

                ig.game.showUpgradeStatus = false;
                var reward = ig.game.spawnEntityBackward(ig.AchievementPopup);
                // reward.onBack.addOnce(function () {
                //     ig.game.enableEntities();
                // }.bind(this));
                this.kill();

            }.bind(this)}).start();


        },
        tempDelay:30,
        update:function(){
            this.parent();


            this.pos.x=this.posx;
            this.pos.y=this.posy+this.offsetY;


            if(!this.windowAnim){
                    this.posx=ig.system.width/2-this.size.x/2;
                    this.posy=ig.system.height/2-this.size.y/2;
            }

            if(this.tempDelay>=5){
                this.tempDelay=0;
                for(var i=0;i<this.buyButton.length;i++){
                    this.setPrice(i);
                }
            }
            this.tempDelay +=1;

            if(this.windowAnim){
                this.btclose.posx=this.posx+this.size.x-20;
                for(var i=0;i<this.buyButton.length;i++){
                    this.buyButton[i].pos.x=this.posx+this.size.x-100;
                }
            }

        },
        getUpgradeData:function(id){
            var one=0;
            for(var i=0;i<this.upgradeData[id].length;i++){
                if(this.upgradeData[id][i]==1){
                    one +=1;
                }
            }
            return one; 
        },
        draw:function(){
            this.parent();
            var ctx=ig.system.context;

            ctx.fillStyle='rgba(10,10,10,0.7)';
            ctx.fillRect(0,0,ig.system.width*2,ig.system.height);

            ctx.globalAlpha = this.alpha
            ctx.fillStyle='#ffffff';
            ctx.strokeStyle='#dddddd';
            ctx.lineWidth=5;                
            ig.game.roundRect(ctx, this.pos.x, this.pos.y, this.size.x, this.size.y, 20, true, true);

            ctx.fillStyle='#202020';
            ctx.textAlign = "center";
            ctx.textBaseline = "Alphabetic";
            ctx.font = (18*this.scale)+"px montserrat-bold";
            ctx.fillText('Daily Event Bonus Upgrade',this.pos.x+this.size.x/2,this.pos.y+(36*this.scale));

            var posy=this.pos.y+50*this.scaleY;
            for(var i=0;i<this.upgradeIcon.length;i++){
                var offsetY=i*62*this.scaleY;

                ctx.fillStyle='#dddddd';
                ctx.strokeStyle='#dddddd';
                ctx.lineWidth=4;                    
                ig.game.roundRect(ctx, this.pos.x+10*this.scale, posy+offsetY, this.size.x-20*this.scale, 70*this.scale, 10*this.scale, true, true);
    
                this.upgradeIcon[i].drawImage(
                    0,
                    0,
                    this.upgradeIcon[i].width,  
                    this.upgradeIcon[i].height,
                    this.pos.x+24*this.scale,
                    posy+offsetY+7*this.scaleY,
                    46*this.scale,
                    46*this.scale
                );                

                ctx.fillStyle='#101010 ';
                ctx.textAlign = "left";
                ctx.textBaseline = "Alphabetic";
                ctx.font = (16*this.scale)+"px Arial";
                ctx.fillText(this.upgradeName[i],this.pos.x+90*this.scale,posy+offsetY+18*this.scaleY);

                var greenLength = this.getUpgradeData(i);

                ctx.fillStyle='#b3b3b3';
                ctx.lineWidth=2;
                var barlength=this.size.x*0.5;
                ig.game.roundRect(ctx, this.pos.x+90*this.scale, posy+offsetY+28*this.scaleY, barlength, 20*this.scale, 4*this.scale, true, false);


                if(greenLength>0){
                    ctx.fillStyle='#39b54a';

                    // if(greenLength>=4){
                        ig.game.roundRect(ctx, this.pos.x+90*this.scale, posy+offsetY+28*this.scaleY, greenLength/100*barlength, 20*this.scale, 4*this.scale, true, false);
                    // }else{
                    //     var newH=greenLength*2*this.scale;
                    //     if(newH>20*this.scale) newH=20*this.scale;
                    //     var addY=(20*this.scale-newH)/2;
                    //     ig.game.roundRect(ctx, this.pos.x+90*this.scale, posy+offsetY+28*this.scaleY+addY, greenLength/100*barlength, newH, greenLength, true, false);
                    // }


                    // if(greenLength>=4){
                        var gradient = ctx.createLinearGradient(this.pos.x+94*this.scale, posy+offsetY+26*this.scaleY,this.pos.x+94*this.scale, posy+offsetY+34*this.scaleY);
                        gradient.addColorStop(0, 'rgba(255,255,255,1');
                        gradient.addColorStop(1, 'rgba(255,255,255,0)');
                        ctx.fillStyle=gradient;
                        ig.game.roundRect(ctx, this.pos.x+93*this.scale, posy+offsetY+30*this.scaleY, (greenLength/100*barlength-10), 10*this.scaleY, 2*this.scale, true, false);
                    // }
                }

                ctx.fillStyle='#101010';
                ctx.textAlign = "center";
                ctx.textBaseline = "Alphabetic";
                ctx.font = (12*this.scale)+"px Arial";
                ctx.fillText(greenLength +' / 100',this.pos.x+90*this.scale+barlength/2,posy+offsetY+40*this.scaleY-10);
            }


        },
    });


    EntityUpgradeClose = EntityButton.extend({
        btsize:{x:40,y:40},
        // btclose: new ig.Image("media/graphics/sprites/buttons/button-kick.png"),
        avaIdx:0,
        offsetY:0,
        init:function(x,y,settings){
            this.btclose = dl.preload['popup-button-close'];
            this.parent(x,y,settings);
            this.posx=x;
            this.posy=y;
            ig.game.sortEntitiesDeferred();
        },
        update:function(){
            this.parent();
            this.pos.x=this.parrent.posx+this.parrent.size.x-40*this.scale;
            this.pos.y=this.posy+this.parrent.offsetY;

            this.size.x=this.btsize.x*this.scale;
            this.size.y=this.btsize.y*this.scale;
            
            this.scale= this.parrent.scale;
        },
        clicked:function(){
            this.offsetY=3;
        },
        leave:function(){
            this.offsetY =0;
        },
        released:function(){
            this.offsetY =0;
            this.parrent.close();

        },
        draw:function(){
            this.parent();

            var context = ig.system.context;
            context.globalAlpha = this.parrent.alpha;
            this.btclose.drawImage(
                0,
                0,
                this.btclose.width,
                this.btclose.height,
                this.pos.x,
                this.pos.y+this.offsetY,
                this.size.x,
                this.size.y
            );
            context.globalAlpha = 1;

        },

    });


    EntityButtonBuy = EntityButton.extend({
        gravityFactor:0,
        btsize:{x:120,y:50},
        image : new ig.Image('media/graphics/sprites/buttons/button-green-small.png'),
        coin:new ig.Image("media/graphics/sprites/coin.png"),
        offsetY:0,
        offsetX:0,
        value:0,
        maxed:false,
        disable:false,
        init:function(x,y,settings){
            this.parent(x,y,settings);
            this.posx =x;
            this.posy =y;
            ig.game.sortEntitiesDeferred();
        },
        update:function(){
            this.parent();
            if(this.windowAnim){
                this.pos.y =this.posy+this.parrent.offsetY;
            }else{
                this.pos.y =this.posy;
                this.pos.x =this.posx;            
            }
            this.size.x=this.btsize.x*this.scale;
            this.size.y=this.btsize.y*this.scale;

            this.scale=this.parrent.scale;
        },
        draw:function(){
            this.parent();

            var context=ig.system.context;
            context.globalAlpha = this.parrent.alpha;

            this.image.drawImage(
                0,
                0,
                this.image.width,
                this.image.height,
                this.pos.x+this.offsetX,
                this.pos.y+this.parrent.offsetY+this.offsetY,
                this.btsize.x*this.scale,
                this.btsize.y*this.scale
            );

            var price=this.value.toLocaleString();                

            if(!this.maxed){
                context.font = (18*this.scale)+'px Arial';
                context.textAlign = "center";
                context.textBaseline = "Alphabetic";
                context.fillStyle = '#202020';
                context.fillText(price,this.pos.x+this.btsize.x/2*this.scale+30, this.posy+this.btsize.y/2*this.scale+this.offsetY+this.parrent.offsetY+12);                
            }

            // if(this.disable || ig.game.backendproccess){
            //     context.fillStyle = 'rgba(0,0,0,0.5)';
            //     ig.game.roundRect(context, this.pos.x, this.pos.y+this.parrent.offsetY+2+this.offsetY, this.btsize.x*this.scale, (this.btsize.y*this.scale-4), 10*this.scale, true, false);
            // }
            context.globalAlpha = 1;


            if(this.maxed){
                context.font = (18*this.scale)+'px Arial';
                context.textAlign = "center";
                context.textBaseline = "Alphabetic";
                context.fillStyle = '#ffffff';
                context.fillText('MAXED',this.pos.x+this.btsize.x/2*this.scale+8, this.pos.y+this.btsize.y*this.scale/2+this.parrent.offsetY+2+this.offsetY);                
            }


            this.coin.drawImage(
                0,
                0,
                this.coin.width,
                this.coin.height,
                this.pos.x+5*this.scale,
                this.pos.y+10*this.scale+this.parrent.offsetY+this.offsetY,
                28*this.scale,
                28*this.scale
            );

            if(this.disable || (ig.game.backendproccess==true && ig.game.svasData.uid!=0)){
                context.fillStyle = 'rgba(0,0,0,0.5)';
                ig.game.roundRect(context, this.pos.x, this.pos.y+this.parrent.offsetY+2+this.offsetY, this.btsize.x*this.scale, (this.btsize.y*this.scale-4), 10*this.scale, true, false);
            }

            // if(this.disable || ig.game.backendproccess){
            //     context.fillStyle = 'rgba(0,0,0,0.5)';
            //     context.beginPath();
            //     context.arc(this.pos.x+5*this.scale,this.pos.y+10*this.scale+this.parrent.offsetY+this.offsetY, 14*this.scale, 0, 2 * Math.PI);
            //     context.fill();
            //     context.closePath();
            // }

        },
        clicked:function(){
            if(this.disable) return;
            if(ig.game.backendproccess && ig.game.svasData.uid!=0) return;
            this.offsetY =3;
        },
        clicking:function(){
            if(this.disable) return;
            if(ig.game.backendproccess && ig.game.svasData.uid!=0) return;
            this.offsetY =3;

        },
        released:function(){
            this.offsetY =0;
            if(this.disable) return;
            if(ig.game.backendproccess && ig.game.svasData.uid!=0) return;
            ig.game.backendproccess =true;
            this.parrent.buyUpgrade(this.buyId,this.value);
        },
    });



});
