ig.module(
    'dl-plugins.notification.factory.image'
).defines(function () {
    'use strict';

    dl.Notification.Factory.Image = {
        generate: function (configs, contentCanvas) {
            if (!configs.image) { return {
                configs: configs,
                canvas: contentCanvas
            }; }

            var canvas = document.createElement('canvas');
            var context = canvas.getContext('2d');

            // update canvas size
            canvas.width = configs.size.x;
            canvas.height = configs.size.y;

            // update canvas size with content
            if (contentCanvas) {
                canvas.width += contentCanvas.width + configs.padding;

                if (contentCanvas.height > canvas.height) {
                    canvas.height = contentCanvas.height;
                }
            }

            this.draw(configs, context, contentCanvas);
            this.drawContentCanvas(configs, context, contentCanvas);

            return {
                configs: configs,
                canvas: canvas
            };
        },

        draw: function (configs, ctx, contentCanvas) {
            var width = configs.size.x;
            var height = configs.size.y;

            var x = 0;
            var y = ctx.canvas.height * 0.5 - height * 0.5;
            if (contentCanvas) {
                x += contentCanvas.width + configs.padding;
            }

            var imageData = dl.canvas.checkPackerPlugin(configs.image);
            ctx.drawImage(
                imageData.data,
                imageData.x, imageData.y,
                imageData.width, imageData.height,
                x,
                y,
                width,
                height
            );
        },

        drawContentCanvas: function (configs, ctx, contentCanvas) {
            if (!contentCanvas) return ctx;

            var x = 0;
            var y = ctx.canvas.height * 0.5 - contentCanvas.height * 0.5;

            ctx.drawImage(contentCanvas, x, y);
        }
    };

    dl.Notification.Factory.Image.DEFAULT_CONFIGS = {
        image: null,
        size: { x: 50, y: 50 },
        padding: 10
    };
});
