ig.module(
    'dl.templates.network-entities.entity-popup-share'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.popup',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.animation-sheet',
    'dl.templates.mixins.text',
    'dl.templates.entities.buttons.entity-button-image-text',
    'dl.templates.entities.buttons.entity-button-image',
    'dl.templates.entities.entity-text'
).defines(function () {
    'use strict';

    dl.EntityPopupShare = dl.Entity
        .extend(dl.MixinPopup)
        .extend(dl.MixinDocker)
        .extend({
            postInit: function () {
                this.parent();

                this.content = this.spawnEntity(dl.EntityPopupShare_Content, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    }
                });
            }
        });

    dl.EntityPopupShare_Content = dl.Entity
        .extend(dl.MixinPopupContent)
        .extend(dl.MixinDocker)
        .extend(dl.MixinAnimationSheet)
        .extend(dl.MixinText)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this.image = dl.preload['popup-large'];
                this._useAnimationSheetAsSize = true;

                this.text = _STRINGS.NETWORK.POPUP_SHARE_TITLE;

                return this;
            },

            postInit: function () {
                this.parent();

                this.initButtons();
                this.initShareText();

                this.tweenIn();
            },

            _initTextComponent: function () {
                this.c_TextComponent.updateProperties({
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                    textAlign: 'center', // [center|end|left|right|start];
                    textBaseline: 'middle', // [alphabetic|top|hanging|middle|ideographic|bottom];
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name,

                    fontSize: 50,

                    _docker: { x: 0.5, y: 0.1 },
                    _anchor: { x: 0.5, y: 0.5 },
                    _offset: { x: 0, y: 0 }
                });
            },

            initButtons: function () {
                this.btnCopy = this.spawnEntity(dl.EntityButtonImageText, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.9 },
                        dockerOffset: { x: 0, y: -10 }
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                        fontSize: 40,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:600,y:90}
                    },
                    image: dl.preload['button-green-small'],
                    text: _STRINGS.NETWORK.POPUP_SHARE_BUTTON_COPY,
                    onButtonClicked: this.copyTextToClipboard.bind(this)
                });

                this.btnClose = this.spawnEntity(dl.EntityButtonImage, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 1, y: 0 },
                        dockerOffset: { x: -10, y: 10 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:90,y:90}
                    },
                    anchor: { x: 1, y: 0 },
                    image: dl.preload['popup-button-close'],
                    onButtonClicked: function () {
                        this.tweenOut(function () {
                            this.kill();
                        }.bind(this.parentInstance));
                    }.bind(this)
                });
            },

            initShareText: function () {
                // var shareText = this.getShareUrl();
                var shareText = ig.game.client.roomPassword;

                this.shareText = this.spawnEntity(dl.EntityText, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.3 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    text: shareText,
                    c_TextComponent: {
                        fontSize: 100
                    }
                });

                var server=SERVER_CONFIGS.SERVER_IPS_NAME[ig.game.defaultServer]

                this.sameserver=this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.shareText,
                        dockerPercent: { x: 0.5, y: 1 },
                        dockerOffset: { x: 0, y: 120 }
                    },
                    anchor: { x: 0.5, y: 0.5 },
                    c_TextComponent: {
                        fontSize: 40,
                        fillStyle: dl.configs.NOTIFICATION_COLORS.PLAYER_1,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    text: "Please make sure all participant <br> are on the same server ("+server+") <br>in order to match correctly"
                });


            },

            getUrl: function () {
                // var url = window.location.href;
                // var params = document.location.search.substr(1).split('&');
                // var shareInfos = [
                //     {
                //         key: ig.game.client.INVITE_SERVER,
                //         value: ig.game.defaultServer
                //     },
                //     {
                //         key: ig.game.client.INVITE_CODE_KEY,
                //         value: ig.game.client.roomPassword
                //     },
                //     {
                //         key: ig.game.client.INVITE_MAX_PLAYER_KEY,
                //         value: ig.game.client.roomMaxPlayer
                //     }
                // ];
                // for (var infoIndex = 0; infoIndex < shareInfos.length; infoIndex++) {
                //     var key = shareInfos[infoIndex].key;
                //     var value = shareInfos[infoIndex].value;

                //     var kvp = key + '=' + encodeURIComponent(value); // key value pair
                //     var regex = new RegExp('(&|\\?)' + key + '=[^\&]*');
                //     var newUrl = url.replace(regex, '$1' + kvp);

                //     var hasCode = false;
                //     for (var i = 0; i <= params.length - 1; i++) {
                //         if (params[i].substr(0, key.length) == key) {
                //             hasCode = true;
                //             var param2 = newUrl.split('&');
                //             var resUrl = '';
                //             for (var j = 0; j <= param2.length - 2; j++) {
                //                 resUrl += param2[j] + '&';
                //             }
                //             url = resUrl + kvp;
                //         }
                //     }

                //     if (!hasCode) {
                //         if (params[0] == '') {
                //             url += '?' + kvp;
                //         } else {
                //             url += '&' + kvp;
                //         }
                //     } else {
                //         url = newUrl;
                //     }
                // }
                // this.url = url;
                this.url = ig.game.client.roomPassword;
                console.log(this.url);
                return this.url;


            },

            getShareUrl: function () {
                // var tempString = this.getUrl();
                // var maxRowLength = 28; // number of char per row
                // var urlLines = [];
                // do {
                //     urlLines.push(tempString.substr(0, maxRowLength));
                //     tempString = tempString.substr(maxRowLength);
                // } while (tempString.length > 0);

                // this.shareURL = '';
                // for (var i = 0; i < urlLines.length; i++) {
                //     if (i != 0) this.shareURL += '<br>';
                //     this.shareURL += urlLines[i];
                // }

                return this.getUrl();
            },

            copyTextToClipboard: function () {
                var textArea = document.createElement('textarea');
                textArea.value = ig.game.client.roomPassword;
                textArea.id = 'shareText';
                // Avoid scrolling to bottom
                textArea.style.top = '0';
                textArea.style.left = '0';
                textArea.style.position = 'fixed';

                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                textArea.setSelectionRange(0, 99999);
                try {
                    var successful = document.execCommand('copy');
                    this.showNotification(successful);
                } catch (e) { }

                document.body.removeChild(textArea);
            },

            showNotification: function (successful) {
                if (successful) {
                    dl.scene.spawnNotification({
                        notificationDrawConfigs: {
                            contentConfigs: [
                                {
                                    type: 'text',
                                    text: _STRINGS.NETWORK.POPUP_SHARE_NOTIFICATION,
                                    fillStyle: dl.configs.TEXT_COLOR.WHITE,
                                    fontSize: 46,
                                    fontFamily: dl.configs.FONT.SOURCE_SANS.name
                                }],
                            backgroundConfigs: {
                                lineWidth: 2,
                                fillStyle: dl.configs.NOTIFICATION_COLORS.PLAYER_3,
                                strokeStyle: dl.configs.NOTIFICATION_COLORS.PLAYER_3,

                                box: { width: 800, height: 90, round: 10, padding: { x: 100, y: 5 } }
                            }
                        }
                    });
                    this.tweenOut(function () {
                        this.kill();
                    }.bind(this.parentInstance));
                }
            },

            tweenIn: function (callback) {
                this.parent(callback);
            },

            tweenOut: function (callback) {
                this.btnClose.setEnable(false);
                this.btnCopy.setEnable(false);

                this.parent(callback);
            }
        });
});
