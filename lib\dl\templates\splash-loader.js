ig.module(
    'dl.templates.splash-loader'
).requires(
    'impact.loader',
    'impact.animation'
).defines(function () {
    ig.SplashLoader = ig.Loader.extend({
        tapToStartDivId: 'tap-to-start',

        init: function (gameClass, resources) {
            this.parent(gameClass, resources);
            // ADS
            ig.apiHandler.run('MJSPreroll');
        },

        end: function () {
            this._endParent = this.parent;
            this._drawStatus = 1;
            this.draw();

            if (_SETTINGS.TapToStartAudioUnlock.Enabled) {
                this.tapToStartDiv(function () {
                    if (dl.check.isFunction(this._endParent)) {
                        this._endParent();
                    }
                }.bind(this));
            } else {
                if (dl.check.isFunction(this._endParent)) {
                    this._endParent();
                }
            }
        },

        tapToStartDiv: function (onClickCallbackFunction) {
            this.desktopCoverDIV = document.getElementById(this.tapToStartDivId);

            // singleton pattern
            if (this.desktopCoverDIV) {
                return;
            }

            /* create DIV */
            this.desktopCoverDIV = document.createElement('div');
            this.desktopCoverDIV.id = this.tapToStartDivId;
            this.desktopCoverDIV.setAttribute('class', 'play');
            this.desktopCoverDIV.setAttribute('style', 'position: absolute; display: block; z-index: 999999; background-color: rgba(23, 32, 53, 0.7); visibility: visible; font-size: 10vmin; text-align: center; vertical-align: middle; -webkit-touch-callout: none; -webkit-user-select: none; -khtml-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none;');
            this.desktopCoverDIV.innerHTML = "<div style='color:white;background-color: rgba(255, 255, 255, 0.3); border: 2px solid #fff; font-size:20px; border-radius: 5px; position: relative; float: left; top: 50%; left: 50%; transform: translate(-50%, -50%);'><div style='padding:20px 50px; font-family: montserrat-bold;'>" + _STRINGS.Splash.TapToStart + '</div></div>';

            /* inject DIV */
            var parentDIV = document.getElementById('play').parentNode || document.getElementById('ajaxbar');
            parentDIV.appendChild(this.desktopCoverDIV);

            /* reize DIV */
            try {
                if (typeof (ig.sizeHandler) !== 'undefined') {
                    if (typeof (ig.sizeHandler.coreDivsToResize) !== 'undefined') {
                        ig.sizeHandler.coreDivsToResize.push(('#' + this.tapToStartDivId));
                        if (typeof (ig.sizeHandler.reorient) === 'function') {
                            ig.sizeHandler.reorient();
                        }
                    }
                }
                else if (typeof (coreDivsToResize) !== 'undefined') {
                    coreDivsToResize.push(this.tapToStartDivId);
                    if (typeof (sizeHandler) === 'function') {
                        sizeHandler();
                    }
                }
            } catch (error) {
                console.log(error);
            }

            /* click DIV */
            this.desktopCoverDIV.addEventListener('click', function () {
                ig.soundHandler.unlockWebAudio();

                /* hide DIV */
                this.setAttribute('style', 'visibility: hidden;');

                /* end function */
                if (typeof (onClickCallbackFunction) === 'function') {
                    onClickCallbackFunction();
                }
            });
        },

        draw: function () {
            // if (this._drawStatus > 0.5) debugger; // DEBUG

            this._drawStatus += (this.status - this._drawStatus) / 5;
            this.updateOrientation();
            this.drawCanvas();
            this.drawImage();
            this.drawVersion();
        },

        drawCanvas: function () {
            var ctx = ig.system.context;
            ctx.save();
            ctx.fillStyle = 'grey';
            ctx.fillRect(0, 0, ig.system.width, ig.system.height);
            ctx.restore();
        },

        drawImage: function () {
            // background
            // var background = dl.preload["background"];
            // dl.canvas.drawFullscreenImage(ig.system.context, background);
            ig.system.context.fillStyle = '#FFF';
            ig.system.context.fillRect(0, 0, ig.system.width, ig.system.height);
            /**
             * Title
             */
            /**
             * Title
             */
             var title = dl.preload.title;
             dl.canvas.drawImage(
                 ig.system.context,
                 title,
                 this.titlePos.x - title.width * 0.5,
                 this.titlePos.y - title.height * 0.5
             );

             /**
              * Loading bar
              */
             var bg = dl.preload['loading-background'];
             var fg = dl.preload['loading-foreground'];
             dl.canvas.drawImage(
                 ig.system.context,
                 bg,
                 this.loadingPos.x - bg.width * 0.5,
                 this.loadingPos.y - bg.height * 0.5
             );
             dl.canvas.drawImage(
                 ig.system.context,
                 fg,
                 this.loadingPos.x - fg.width * 0.5,
                 this.loadingPos.y - fg.height * 0.5,
                 {
                     imageDrawPercent: { x: this._drawStatus, y: 1 }
                 }
             );
        },

        drawVersion: function () {
            if (typeof (_SETTINGS.Versioning) !== 'undefined' && _SETTINGS.Versioning !== null) {
                if (_SETTINGS.Versioning.DrawVersion) {
                    var ctx = ig.system.context;
                    fontSize = _SETTINGS.Versioning.FontSize,
                        fontFamily = _SETTINGS.Versioning.FontFamily,
                        fillStyle = _SETTINGS.Versioning.FillStyle;

                    ctx.save();
                    ctx.textBaseline = 'bottom';
                    ctx.textAlign = 'left';
                    ctx.font = fontSize + ' ' + fontFamily || '10px Arial';
                    ctx.fillStyle = fillStyle || '#ffffff';
                    ctx.fillText('v' + _SETTINGS.Versioning.Version + '+build.' + _SETTINGS.Versioning.Build, 10, ig.system.height - 10);
                    ctx.restore();
                }
            }
        },

        updateOrientation: function () {
            if (ig.sizeHandler.isPortraitOrientation) {
                this.titlePos = {
                    x: ig.system.width * 0.5,
                    y: ig.system.height * 0.25
                };
                this.loadingPos = {
                    x: ig.system.width * 0.5,
                    y: ig.system.height * 0.7
                };
            } else {
                this.titlePos = {
                    x: ig.system.width * 0.5,
                    y: ig.system.height * 0.25
                };
                this.loadingPos = {
                    x: ig.system.width * 0.5,
                    y: ig.system.height * 0.7
                };
            }
        }
    });
});
