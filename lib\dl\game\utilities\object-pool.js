'use strict';

function _classCallCheck (instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError('Cannot call a class as a function'); } }

function _defineProperties (target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ('value' in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }

function _createClass (Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, 'prototype', { writable: false }); return Constructor; }

function _defineProperty (obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

ig.module('dl.game.utilities.object-pool').defines(function () {
  'use strict';
  /**
   * Usage: dl.objectPool.enableFor(ObjectClass);
   */

  var _class;

  dl.ObjectPool = (_class = /* #__PURE__ */(function () {
    function dl_ObjectPool () {
      _classCallCheck(this, dl_ObjectPool);

      this._pools = {};
    }

    _createClass(dl_ObjectPool, [{
      key: 'enableFor',
      value: function enableFor (classObject) {
        Object.assign(classObject.prototype, {
          _enablePool: true,
          _poolId: ++dl_ObjectPool.LAST_POOL_ID
        });
      }
    }, {
      key: 'putInPool',
      value: function putInPool (instance) {
        if (!this._pools[instance._poolId]) {
          this._pools[instance._poolId] = [instance];
        } else {
          var pool = this._pools[instance._poolId];

          for (var i = 0; i < pool.length; i++) {
            if (pool[i] == instance) {
              return false;
            }
          }

          pool.push(instance);
        }
      }
    }, {
      key: 'getFromPool',
      value: function getFromPool (classObject) {
        var pool = this._pools[classObject.prototype._poolId];

        if (!pool || !pool.length) {
          return null;
        }

        return pool.pop();
      }
    }, {
      key: 'clearPool',
      value: function clearPool () {
        this._pools = {};
      }
    }]);

    return dl_ObjectPool;
  }()), _defineProperty(_class, 'LAST_POOL_ID', 0), _class); // Init pool

  dl.objectPool = new dl.ObjectPool();
});
