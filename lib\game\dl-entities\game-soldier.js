ig.module(
    'game.dl-entities.game-soldier'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker'
).defines(function () {
    dl.EntityGameSoldier = dl.Entity
        .extend(dl.MixinDocker)
        .extend({
            staticInstantiate: function () {
                this.parent();
                this.estimates = {};
                this.uid = Utilities.generateUID();
                this.size = { x: 20, y: 20 };
                this.tweenOffset = { x: 0, y: 0 };
                this.diameter = 0.5 * (this.size.x + this.size.y);
                this.radius = 0.5 * this.diameter;
                this.setSize(
                    this.size.x,
                    this.size.y
                );
                this.canMarch = false;
                this.movementSpeed = NETWORK_GAME_CONFIGS.SOLDIER_MOVEMENT_SPEED || 90;
                this.reachedTarget = false;
                this.isTweening = false;
                this.show =false;

                ig.game.soldier.push(this);
                this.arrayIndex = ig.game.soldier.length-1; 

                return this;
            },
            postInit: function () {
                this.parent();
                this.color = this.colorManager.getSoldierColor(this.sourceCapital);
                this.estimates.travelDistance = Utilities.distanceBetweenTwoPoints(this.sourceCapital.capitalData.initialPoint, this.targetCapital.capitalData.initialPoint);
                this.estimates.travelTime = (this.estimates.travelDistance / this.movementSpeed) * 1000;
                this.tweenIn();
            },

            tweenIn: function () {
                // TODO: Spawn issue sometimes, spawning outside map
                // console.log('tweenIn', this._entityId);
                if (this.tweenInInstance) {
                    this.tweenInInstance.finish();
                    this.tweenInInstance = null;
                }
                if (Date.now() - this.realSpawnTime >= this.estimates.travelTime) {
                    this.targetCapital.startDeactivateWarningTimer();
                    this.kill();
                } else {


                    this.tweenInInstance = this.createTween()
                        .to({
                            c_DockerComponent: {
                                dockerOffset: { x: this.tweenOffset.x, y: this.tweenOffset.y }
                            }
                        }, 200, {
                            easing: dl.TweenEasing.Linear.EaseNone,
                            onPropertiesChanged: function () {
                                this.updatePos();
                            }.bind(this)
                        })
                        .start({
                            onCompleteTween: function () {
                                this.marchAngle = Utilities.calcAngle(
                                    this.sourceCapital.pos.x, this.sourceCapital.pos.y,
                                    this.targetCapital.pos.x, this.targetCapital.pos.y,
                                    true
                                );
                                this.canMarch = true;
                                this.show = true;
                            }.bind(this)
                        });
                }
            },

            tweenCapture: function () {
                if (this.isTweening) return;
                if (this.tweenInInstance) return;
                this.isTweening = true;
                // console.log('tweenCapture', this._entityId);
                // dl.TweenTemplate.scaleOut(this, 200);
                // this.updatePos();
                var offset = {
                    x: this.targetCapital.pos.x - this.pos.x,
                    y: this.targetCapital.pos.y - this.pos.y
                };
                this.tweenInInstance = this.createTween()
                    .to({
                        c_DockerComponent: {
                            dockerOffset: {
                                x: this.c_DockerComponent.dockerOffset.x + offset.x,
                                y: this.c_DockerComponent.dockerOffset.y + offset.y
                            }
                        }
                    }, 200, {
                        easing: dl.TweenEasing.Quadratic.EaseIn,
                        onPropertiesChanged: function () {
                            this.updatePos();
                        }.bind(this)
                    })
                    .start({
                        onCompleteTween: function () {                          

                            if (!this.gameManager.isTargetAlly(this.sourceCapital, this.targetCapital)) {
                            // if(this.sourceCapital.capitalData.assignedPlayer.playerId == this.targetCapital.capitalData.assignedPlayer.playerId){

                                var targetData =Math.floor(this.targetCapital.capitalData.soldierCount-1);
                                if(targetData<0) {
                                    targetData=0;
                                    this.targetCapital.activateShield(Date.now(),15000);
                                    this.cleanAttackingSoldiers(this.targetCapital.capitalData.capitalIndex);
                                    this.cleanSpawnedSoldiers(this.targetCapital.capitalData.capitalIndex);
                                }

                                if (ig.game.client &&
                                    ig.game.client.clientGameRoom &&
                                    ig.game.client.clientGameRoom.networkGame) {
                                    ig.game.client.clientGameRoom.networkGame.sendGameEvent(
                                        new NetworkGameEvent(NetworkGameEvent.EVENT_TYPE.SYNC_SOLDIER_COUNT,Date.now(),{
                                            soldierCount: targetData,
                                            capitalIndex:this.targetCapital.capitalData.capitalIndex || "",
                                            sourceCapitalIndex:this.sourceCapital.capitalData.capitalIndex,
                                            attackerPlayerId:this.playerId,
                                            screenId:ig.game.client.playerId,
                                            attackedStatus:true,
                                        })
                                    );
                                }

                                this.targetCapital.capitalData.soldierCount=targetData;
                                this.targetCapital.soldierCountText.updateText(Math.floor(this.targetCapital.capitalData.soldierCount));
                                this.kill();                                

                                this.targetCapital.shake(0.5, 5);
                                this.targetCapital.playUnderAttackSFX();

                                if(ig.game.shootStatus){
                                    ig.game.shootStatus=false;
                                    ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.shoot1);
                                    setTimeout(function(){
                                        ig.game.shootStatus=true;
                                    }.bind(this),50);
                                }


                            }else{
                                this.kill();                                

                                var targetData =Math.floor(this.targetCapital.capitalData.soldierCount+1);
                                if(targetData>100) targetData=100;

                                if (ig.game.client &&
                                    ig.game.client.clientGameRoom &&
                                    ig.game.client.clientGameRoom.networkGame) {
                                    ig.game.client.clientGameRoom.networkGame.sendGameEvent(
                                        new NetworkGameEvent(NetworkGameEvent.EVENT_TYPE.SYNC_SOLDIER_COUNT,Date.now(),{
                                            soldierCount: targetData,
                                            capitalIndex:this.targetCapital.capitalData.capitalIndex || "",
                                            sourceCapitalIndex:this.sourceCapital.capitalData.capitalIndex,
                                            attackerPlayerId:this.playerId,
                                            screenId:ig.game.client.playerId,
                                            attackedStatus:false,
                                        })
                                    );
                                }

                                this.targetCapital.capitalData.soldierCount=targetData;
                                this.targetCapital.soldierCountText.updateText(Math.floor(this.targetCapital.capitalData.soldierCount));

                            }
                            this.targetCapital.startDeactivateWarningTimer();
                        }.bind(this)
                    });
            },

            cleanAttackingSoldiers: function (capitalIndex) {
                var source = null;
                var target = null;
                for (var i = 0; i < this.gameManager.soldierList.length; i++) {
                    if (this.gameManager.soldierList[i] &&
                        this.gameManager.soldierList[i].targetCapital &&
                        this.gameManager.soldierList[i].targetCapital.capitalData.capitalIndex == capitalIndex
                        )
                    {
                        // console.log('Kill soldiers attacking capitalIndex: ', capitalIndex);
                        source = this.gameManager.soldierList[i].sourceCapital.capitalData.capitalIndex;
                        target = this.gameManager.soldierList[i].targetCapital.capitalData.capitalIndex;
                        this.gameManager.soldierList[i].kill();
                    }
                }
                if (source && target) {
                    ig.game.clearSpawnDelays({
                        source: source,
                        target: target
                    });
                }
            },

            cleanSpawnedSoldiers: function (capitalIndex) {
                var source = null;
                var target = null;
                for (var i = 0; i < this.gameManager.soldierList.length; i++) {
                    if (this.gameManager.soldierList[i] &&
                        this.gameManager.soldierList[i].sourceCapital &&
                        this.gameManager.soldierList[i].sourceCapital.capitalData.capitalIndex == capitalIndex
                        )
                    {
                        // console.log('Kill spawned soldiers attacking capitalIndex: ', capitalIndex);
                        source = this.gameManager.soldierList[i].sourceCapital.capitalData.capitalIndex;
                        target = this.gameManager.soldierList[i].targetCapital.capitalData.capitalIndex;
                        this.gameManager.soldierList[i].kill();
                    }
                }
                if (source && target) {
                    ig.game.clearSpawnDelays({
                        source: source,
                        target: target
                    });
                }
            },

            marchSoldier: function () {
                if (!ig.game.client) return;
                if (!ig.game.client.clientGameRoom) return;
                if (!ig.game.client.clientGameRoom.networkGame) return;
                if (!this.reachedTarget) {
                    // if (ig.game.client &&
                    //     ig.game.client.clientGameRoom &&
                    //     ig.game.client.clientGameRoom.networkGame) {
                    //     var serverTime = ig.game.client.clientGameRoom.networkGame.getServerTime();
                    // } else {
                        var serverTime = Date.now();
                    // }
                    this.c_DockerComponent.dockerOffset.x += this.movementSpeed * (Math.cos(this.marchAngle) * (serverTime - this.spawnTime) * 0.001);
                    this.c_DockerComponent.dockerOffset.y += this.movementSpeed * (Math.sin(this.marchAngle) * (serverTime - this.spawnTime) * 0.001);
                    this.updatePos();
                    this.spawnTime = serverTime;
                }
            },

            getAabb: function () {
                return {
                    x: this.pos.x - this.size.x * 0.5,
                    y: this.pos.y - this.size.y * 0.5,
                    w: this.size.x,
                    h: this.size.y
                };
            },

            aabbCheck: function (aabb1, aabb2) {
                return (
                    aabb1.x < aabb2.x + aabb2.w &&
                    aabb1.x + aabb1.w > aabb2.x &&
                    aabb1.y < aabb2.y + aabb2.h &&
                    aabb1.y + aabb1.h > aabb2.y
                );
            },

            kill: function (targetStatus) {
                ig.game.soldier[this.arrayIndex]=null;
                this.parent();
            },
            updateDataCount:0,
            update: function () {
                this.parent();

                this.updateDataCount +=1;
                if(this.updateDataCount % 3!==0) return
                if(this.updateDataCount>=30) this.updateDataCount=0;

                if(this.targetCapital.haveShield) this.kill();       

                if (this.canMarch) {
                    if (Date.now() - this.realSpawnTime >= this.estimates.travelTime) {
                        this.canMarch = false;
                        this.tweenCapture();
                    }
                    if (this.isGameOver) return;
                    this.marchSoldier();
                    if (this.aabbCheck(this.getAabb(), this.targetCapital.getAabb())) {
                        this.tweenCapture();
                    }

                    for (var i = 0; i < this.gameManager.soldierList.length; i++) {
                        var soldier = this.gameManager.soldierList[i];
                        if (soldier.playerId != this.playerId && this.aabbCheck(this.getAabb(), soldier.getAabb())) {
                            this.kill();
                            soldier.kill();
                        }
                    }
                }
            },

            draw: function (ctx) {
                this.parent(ctx);
                if(this.show){
                    ctx.save();
                    ctx.beginPath();
                    ctx.fillStyle = this.color;
                    ctx.arc(
                        this.pos.x,
                        this.pos.y,
                        this.radius,
                        0,
                        360
                    );
                    ctx.fill();
                    ctx.restore();
                }
                // ctx.save();
                // ctx.beginPath();
                // ctx.strokeStyle = '#000';
                // ctx.rect(this.pos.x - this.size.x * 0.5, this.pos.y - this.size.y * 0.5, this.size.x, this.size.y);
                // ctx.stroke();
                // ctx.restore();
            }
        });

    // Enable cache
    // dl.enableCacheCanvas(dl.EntityGameMapContinent);
});
