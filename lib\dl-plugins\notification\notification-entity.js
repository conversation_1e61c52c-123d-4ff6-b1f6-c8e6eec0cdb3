ig.module(
    'dl-plugins.notification.notification-entity'
).requires(
    'dl.game.entity',
    'dl.game.entity.components.position.docker',
    'dl-plugins.notification.notification-factory'
).defines(function () {
    'use strict';

    dl.Notification.Entity = dl.Entity
        .extend({
            staticInstantiate: function () {
                this.parent();

                this.notificationIndex = 0;

                this.notificationAlpha = 1;
                this.notificationScale = { x: 0, y: 0 };

                return this;
            },

            initComponents: function () {
                this.parent();

                this._initDockerComponent();
            },

            _initDockerComponent: function () {
                var xx=window.innerWidth;
                var yy=window.innerHeight;

                if(!this.c_DockerComponent){
                    if(ig.ua.mobile && !ig.ua.iPad && xx<yy){
                        var dockerPercent= { x: 0.5, y: 0.23 };
                    }else{
                        var dockerPercent= { x: 0.5, y: 0.3 };
                    }
                    this.c_DockerComponent = this.addComponent(dl.DockerComponent, {
                        dockerObject: dl.game.camera,
                        dockerPercent: dockerPercent,
                        dockerOffset: { x: 0, y: 0 }
                    });
                }                
            },

            postInit: function () {
                this.parent();

                if (!this.notificationDrawConfigs) this.notificationDrawConfigs = {};

                var canvasData = dl.Notification.FactoryManager.generateCanvas(this.notificationDrawConfigs);
                this._notificationCanvasConfigs = canvasData.configs;
                this._notificationCanvas = canvasData.canvas;

                this.setSize(this._notificationCanvas.width * 1.2, this._notificationCanvas.height * 1.2); // addition 1.2 space for tween effect
            },

            draw: function (ctx) {
                this.parent(ctx);

                ctx.save();

                // alpha
                // ctx.globalAlpha = this.notificationAlpha;
                ctx.globalAlpha = 1;
                // scale
                ctx.translate(this.pos.x, this.pos.y);
                ctx.scale(this.notificationScale.x, this.notificationScale.y);
                ctx.translate(-this.pos.x, -this.pos.y);

                // draw canvas
                ctx.drawImage(this._notificationCanvas,
                    this.pos.x - this._notificationCanvas.width * 0.5,
                    this.pos.y - this._notificationCanvas.height * 0.5);

                ctx.restore();
            },

            updateNotificationIndex: function (index) {
                if (index == this.notificationIndex) return;

                this.notificationIndex = index;

                this.moveNotification();
            },

            showNotification: function () {
                this.notificationAlpha = 1;
                this.notificationScale = { x: 0, y: 0 };
                this.updateScale();

                this.createTween()
                    .to({
                        notificationAlpha: 1,
                        notificationScale: { x: 1, y: 1 }
                    }, dl.Notification.Entity.SHOW_TIME, {
                        onPropertiesChanged: function () {
                            this.updateScale();
                        }.bind(this)
                    })
                    .start({
                        onCompleteTween: function () {
                            this.aliveNotification();
                        }.bind(this)
                    });
            },

            aliveNotification: function () {
                this.createTween()
                    .to({}, dl.Notification.Entity.ALIVE_TIME, {})
                    .start({
                        onCompleteTween: function () {
                            this.hideNotification();
                        }.bind(this)
                    });
            },

            moveNotification: function () {
                this.createTween()
                    .to({
                        c_DockerComponent: {
                            dockerOffset: { x: 0, y: this.notificationIndex * -this.size.y }
                        }
                    }, dl.Notification.Entity.MOVE_TIME, {
                        onPropertiesChanged: function () {
                            this.updatePos();
                        }.bind(this)
                    })
                    .start();
            },

            hideNotification: function () {
                this.createTween()
                    .to({
                        // notificationAlpha: 0,
                        notificationScale: { x: 0, y: 0 }
                    }, dl.Notification.Entity.HIDE_TIME, {
                        onPropertiesChanged: function () {
                            this.updateScale();
                        }.bind(this)
                    })
                    .start({
                        onCompleteTween: function () {
                            this.kill();
                        }.bind(this)
                    });
            }
        });

    /**
     * Configs
     */
    dl.Notification.Entity.SHOW_TIME = 300;
    dl.Notification.Entity.ALIVE_TIME = 3000;
    dl.Notification.Entity.MOVE_TIME = 100;
    dl.Notification.Entity.HIDE_TIME = 200;
});
