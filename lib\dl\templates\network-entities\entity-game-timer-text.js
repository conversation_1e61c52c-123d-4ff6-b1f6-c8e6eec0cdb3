ig.module(
    'dl.templates.network-entities.entity-game-timer-text'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.text'
).defines(function () {
    'use strict';

    dl.EntityGameTimerText = dl.Entity
        .extend(dl.MixinDocker)
        .extend(dl.MixinText)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this._useTextAsSize = true;

                this.timer = null;
                this._lastTimeLeft = null;

                return this;
            },

            _initTextComponent: function () {
                this.c_TextComponent.updateProperties({
                    textAlign: 'center',
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                    fontSize: 100
                });
            },

            update: function () {
                this.parent();

                this.updateTimer();
            },

            updateTimer: function () {
                if (!this.timer) {
                    this.updateText('00:00');
                } else {
                    if (this.timer.delta() <= 0) {
                        var timeLeft = Math.abs(Math.floor(this.timer.delta() / 1000));
                        this.updateTimerText(timeLeft);
                    } else {
                        this.timer = null;
                    }
                }
            },

            updateTimerText: function (timeLeft) {
                var minute = Math.floor(timeLeft / 60);
                var second = timeLeft % 60;

                var minuteText = (minute < 10) ? ('0' + minute) : minute;
                var secondText = (second < 10) ? ('0' + second) : second;
                this.updateText(minuteText + ':' + secondText);
            },

            startTimer: function (startTime, duration) {
                this.timer = new dl.GlobalTimer(startTime, duration);
                // this.timer.pause();

                return this.timer;
            }
        });
});
