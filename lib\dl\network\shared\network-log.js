/**
 * Created by <PERSON><PERSON>
 * Store network logs
 */

// if (typeof require !== 'undefined') {
// 	var RoomEvent = require('./room-event.js').RoomEvent;
// }

var NetworkLog = function (room) {
	var self = {};
	self.logs = [];

	self.init = function (room) {
		self.room = room;
	};

	self.addLog = function (data) {
		self.logs.push(new NetworkLog.Log({
			sender: data.sender,
			message: data.message,
			senderOnly: data.senderOnly
		}));
	};

	self.packData = function () {
		var data = {};

		data.logs = [];
		for (var i = 0, iLength = self.logs.length; i < iLength; i++) {
			data.logs.push(self.logs[i].packData());
		}

		return data;
	};

	self.importData = function (data) {
		if (!data) return;

		self.logs = [];
		for (var i = 0, iLength = data.logs.length; i < iLength; i++) {
			self.logs.push(new NetworkLog.Log(data.logs[i]));
		}
	};

	self.init(room);
	return self;
};

NetworkLog.Log = function (data) {
	var self = {};

	self.sender = {};
	self.message = '';
	self.senderOnly = false;

	self.init = function (data) {
		self.sender = {};
		self.message = '';
		self.senderOnly = false;

		self.importData(data);
	};

	self.packData = function () {
		var data = {};

		data.sender = self.sender;
		data.message = self.message;
		data.senderOnly = self.senderOnly;

		return data;
	};

	self.importData = function (data) {
		if (!data) return;

		if (typeof data.sender !== 'undefined') self.sender = data.sender;
		if (typeof data.message !== 'undefined') self.message = data.message;
		if (typeof data.senderOnly !== 'undefined') self.senderOnly = data.senderOnly;
	};

	self.init(data);
	return self;
};

/**
 * Export module
 */
if (typeof module !== 'undefined') {
	module.exports.NetworkLog = NetworkLog;
}
