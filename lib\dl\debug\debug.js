ig.module(
    'dl.debug.debug'
).defines(function () {
    'use strict';

    dl.debug = {};
    dl.debug.enabled = false;
    dl.debug.FONT_SIZE = 20;
    /**
     * get context for draw
     * @returns draw context
     */
    dl.debug.getContext = function () {
        return dl.system.context;
    };

    /**
     * Draw entity
     */
    dl.debug.DRAW_ENTITY = dl.debug.enabled;
    dl.debug.drawEntity = function (entity) {
        if (!dl.debug.DRAW_ENTITY) return;

        var ctx = dl.debug.getContext();
        ctx.save();
        ctx.strokeStyle = 'blue';
        ctx.lineWidth = 2;
        ctx.strokeRect(
            entity.pos.x - entity.size.x * 0.5,
            entity.pos.y - entity.size.y * 0.5,
            entity.size.x,
            entity.size.y);
        ctx.restore();
    };

    /**
     * Draw entity
     */
    dl.debug.DRAW_COMPONENT = dl.debug.enabled;
    dl.debug.drawComponent = function (component) {
        if (!dl.debug.DRAW_COMPONENT) return;

        var ctx = dl.debug.getContext();
        ctx.save();
        ctx.strokeStyle = 'green';
        ctx.lineWidth = 6;
        ctx.strokeRect(
            component.pos.x - component.size.x * 0.5,
            component.pos.y - component.size.y * 0.5,
            component.size.x,
            component.size.y);
        ctx.restore();
    };

    /**
     * Draw collision box
     */
    dl.debug.DRAW_COLLISION = dl.debug.enabled;

    /**
     * Draw collision bounding box
     */
    dl.debug.DRAW_COLLISION_BOUNDING_BOX = dl.debug.enabled;
    dl.debug.drawCollisionBoundingBox = function (component) {
        if (!dl.debug.DRAW_COLLISION_BOUNDING_BOX) return;

        var ctx = dl.debug.getContext();
        ctx.save();
        ctx.lineWidth = 6;
        ctx.strokeStyle = 'red';
        ctx.strokeRect(
            component.collisionBoundingBox.xMin,
            component.collisionBoundingBox.yMin,
            component.collisionBoundingBox.xMax - component.collisionBoundingBox.xMin,
            component.collisionBoundingBox.yMax - component.collisionBoundingBox.yMin);
        ctx.restore();
    };
});
