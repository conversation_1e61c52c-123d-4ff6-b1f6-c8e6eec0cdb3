/* eslint-disable quote-props */
ig.module(
    'game.dl-entities.game-manager'
).requires(
    'dl.game.entity',
    'game.dl-entities.game-bot-manager'
).defines(function () {
    dl.EntityGameManager = dl.Entity
        .extend({
            staticInstantiate: function () {
                this.parent();
                this.guideArrowAttribs = {
                    isActive: false,
                    currentDrag: 0,
                    dragAngle: 0,
                    maxDrag: 99999,
                    minDrag: 60
                };
                this.currentTargetCapitalIndex = null;
                this.players = [];
                this.playersCache = [];
                this.soldierList = [];
                this.playerSoldierList = [];
                this.enemySoldierList = [];
                this.playersToEliminate = [];
                this.mapData = null;
                this.offsetList = [];
                this.maxSoldierCount = 100;
                this.soldierIncrementInterval = 1000;
                // angleInterval in degreesf
                this.defaultAngleInterval = NETWORK_GAME_CONFIGS.ANGLE_INTERVAL;
                this.colorManager = ig.game.managers.color;
                this.enableControls = false;
                this.isGameOver = false;
                this.isSpectator = false;
                // this.haveWon = false;
                this.targetList = [];
                this.spawnIndex=[];
                this.attackIndex=[];
                // this.targetListTimerInterval = 0.1;
                // this.targetListTimer = new ig.Timer(this.targetListTimerInterval);
                ig.game.popupStatus=false;
                this.playerEnd=[false,false,false,false];
                this.gameEndTimerCheck=new ig.Timer();

                window.gamemanager = this;
                return this;
            },

            postInit: function () {
                this.parent();
                window.botmanager = ig.game.managers.bot = this.spawnEntity(dl.EntityGameBotManager, {});
                setTimeout(function(){
                    this.configCapital();

                    if(ig.game.svasData.uid==0){
                        if(ig.game.arenaId==1){
                            var deductValue=200; 
                        }else if(ig.game.arenaId==2){
                            var deductValue=500; 
                        }else if(ig.game.arenaId==3){
                            var deductValue=2500; 
                        }else if(ig.game.arenaId==4){
                            var deductValue=1500; 
                        }
                        ig.game.svasData.coins -=deductValue;
                        ig.game.balance=ig.game.svasData.coins;             
                        ig.game.sessionData.balance=ig.game.svasData.coins;             
                        ig.game.saveLocalStorage()
                    }

                }.bind(this),1000);
            },
            configCapital:function(){
                for (var i = 0; i < this.capitalList.length; i++) {
                    var capitalIndex=this.capitalList[i].capitalData.capitalIndex
                    this.spawnIndex.push({capitalIndex:capitalIndex,spawnIndex:0,attackIndex:0});
                    this.attackIndex[capitalIndex]=0;
                }
            },
            generateOffsetListBasedOnAngle: function (angleInterval, distance, angle,soldierPerRow) {
                angleInterval = typeof angleInterval == 'undefined' ? this.defaultAngleInterval : angleInterval;
                distance = typeof distance == 'undefined' ? NETWORK_GAME_CONFIGS.DISTANCE_FROM_SOURCE : distance;
                // indexList below simply means: [Center, FirstRight, FirstLeft, SecondRight, SecondLeft]
                // visually: [SecondLeft, FirstLeft, Center, FirstRight, SecondRight] = ooooo (but in slightly arced positions)
                if(soldierPerRow==1){
                    var indexList = [0];
                    var additionalAngle = 0;
                }else if(soldierPerRow==2){
                    var indexList = [0,1];
                    var additionalAngle = -0.5*angleInterval;
                }else if(soldierPerRow==3){
                    var indexList = [0, 1, -1];
                    var additionalAngle = 0;
                }else if(soldierPerRow==4){
                    var indexList = [0, 1, -1 ,2];
                    var additionalAngle = -0.5*angleInterval;
                }else{
                    var indexList = [0, 1, -1, 2,-2];
                    var additionalAngle = 0;
                }

                var offsetList = [];
                for (var i = 0; i < soldierPerRow; i++) {
                    var offset = {};
                    offset = this.findNewPoint(0, 0, angle + additionalAngle + (angleInterval * indexList[i]), distance);
                    offsetList.push({
                        angle: i,
                        offset: offset
                    });
                }
                // dl.log(offsetList);
                return offsetList;
            },

            findNewPoint:function (x, y, angle, distance) {
                var result = {};

                result.x = Math.round(Math.cos(angle * Math.PI / 180) * distance + x);
                result.y = Math.round(Math.sin(angle * Math.PI / 180) * distance + y);

                return result;
            },
            calcAngle:function (originX, originY, targetX, targetY, radians) {
                var dx = originX - targetX;
                var dy = originY - targetY;

                // var theta = Math.atan2(dy, dx);  // [0, Ⲡ] then [-Ⲡ, 0]; clockwise; 0° = west
                // theta *= 180 / Math.PI;          // [0, 180] then [-180, 0]; clockwise; 0° = west
                // if (theta < 0) theta += 360;     // [0, 360]; clockwise; 0° = west

                // var theta = Math.atan2(-dy, dx); // [0, Ⲡ] then [-Ⲡ, 0]; anticlockwise; 0° = west
                // theta *= 180 / Math.PI;          // [0, 180] then [-180, 0]; anticlockwise; 0° = west
                // if (theta < 0) theta += 360;     // [0, 360]; anticlockwise; 0° = west

                // var theta = Math.atan2(dy, -dx); // [0, Ⲡ] then [-Ⲡ, 0]; anticlockwise; 0° = east
                // theta *= 180 / Math.PI;          // [0, 180] then [-180, 0]; anticlockwise; 0° = east
                // if (theta < 0) theta += 360;     // [0, 360]; anticlockwise; 0° = east

                var theta = Math.atan2(-dy, -dx); // [0, Ⲡ] then [-Ⲡ, 0]; clockwise; 0° = east
                theta *= 180 / Math.PI; // [0, 180] then [-180, 0]; clockwise; 0° = east
                if (theta < 0) theta += 360; // [0, 360]; clockwise; 0° = east

                if (radians) theta *= Math.PI / 180;

                return theta;
            },

            /* old function, retained for reference */
            spawnSoldier: function (source, isEnemy, targetCapitalIndex,spawnSoldierCount,idx) {
                // clear existing spawn delays

                var target = this.findCapitalByIndex(targetCapitalIndex);

                if(this.isGameOver) return;
                if(this.isSpectator && ig.game.popupStatus) return;
                if(this.allPlayerEnd) return;
                if(source.haveShield) return;
                if(target.haveShield) return;
                if(!ig.game.managers.game.enableControls) return;

                 
                if(target.capitalData.assignedPlayer && target.capitalData.assignedPlayer.playerId==source.capitalData.assignedPlayer.playerId) {
                    if(target.capitalData.soldierCount>=100){
                        return;
                    }else{
                        var maxCount= (100-target.capitalData.soldierCount)
                        if(spawnSoldierCount>maxCount){
                            spawnSoldierCount = maxCount;    
                        }                        
                    }
                }

                var sourceCapitalIndex=source.capitalData.capitalIndex;
                ig.game.clearSpawnDelays(source.capitalData.capitalIndex);
                var targetIndex = targetCapitalIndex || this.currentTargetCapitalIndex;
                // find capital by index
                var targetCapital = this.findCapitalByIndex(targetIndex);
                // get the angle of source capital to target capital
                var angleToTarget = this.calcAngle(
                    source.pos.x, source.pos.y,
                    targetCapital.pos.x, targetCapital.pos.y
                );


                // get the positions where the soldier will spawn before marching
                var offsetList = this.generateOffsetListBasedOnAngle(this.defaultAngleInterval, 60, angleToTarget,5);
                var soldierCount = spawnSoldierCount;
                var maxSoldier = spawnSoldierCount;
                var currentIndex = 0;
                var spawnLimit = 5;
                var batchIndex = 0;
                var realSpawnTime = Date.now();
                while (soldierCount > 0) {
                    // source.decreaseStatus=false
                    if (soldierCount < 5) {
                        spawnLimit = soldierCount;
                        var offsetList = this.generateOffsetListBasedOnAngle(this.defaultAngleInterval, 60, angleToTarget,spawnLimit);
                    }
                    // spawn the soldiers by batch
                    for (var i = 0; i < spawnLimit; i++) {
                        ig.game.spawnWithDelay({ source: source.capitalData.capitalIndex, target: targetIndex }, (0.4 * batchIndex)+(0.05*i), function (i, offsetList, source, targetCapital, isEnemy, realSpawnTime, batchIndex) {                            
                            

                            // var maxSpawn=-1;
                            // console.log(this.spawnIndex);
                            for(var j=0;j<this.spawnIndex.length;j++){
                                if(this.spawnIndex[j].capitalIndex==sourceCapitalIndex && this.spawnIndex[j].attackIndex==idx && this.spawnIndex[j].spawnIndex!=0){
                                    this.spawnIndex[j-1].spawnIndex=0;
                                    maxSpawn = this.spawnIndex[j].spawnIndex-1;
                                }
                            }

                            if(currentIndex<=maxSpawn){
                                this.soldierList.push(
                                    this.spawnEntity(dl.EntityGameSoldier, {
                                        c_DockerComponent: {
                                            dockerObject: source,
                                            dockerPercent: { x: 0.5, y: 0.5 },
                                            dockerOffset: { x: 0, y: 0 }
                                        },
                                        mapData: this.mapData,
                                        sourceCapital: source,
                                        targetCapital: targetCapital,
                                        playerId: source.capitalData.assignedPlayer.playerId,
                                        gameManager: this,
                                        colorManager: ig.game.managers.color,
                                        tweenOffset: { x: offsetList[i].offset.x, y: offsetList[i].offset.y },
                                        isEnemy: isEnemy,
                                        spawnTime: realSpawnTime + (batchIndex * 500),
                                        realSpawnTime: realSpawnTime + (batchIndex * 500)
                                    })
                                );
                                source.decreaseSoldierCountNetwork(currentIndex,maxSoldier-1);
                            }

                            currentIndex ++;
                 
                        }.bind(this, i, offsetList, source, targetCapital, isEnemy, realSpawnTime, batchIndex,currentIndex,maxSoldier,sourceCapitalIndex));
                        soldierCount--;
                    }
                    batchIndex++;
                }

            },
            
            onNetworkSpawnSoldier: function (soldier, player) {

                if(this.isGameOver) return;
                if(this.allPlayerEnd) return;
                if (ig.game.client.isMyPlayerId(player.playerId)) return;

                var sourcCapitalIndex=soldier.sourceCapitalIndex;
                var source = this.findCapitalByIndex(soldier.sourceCapitalIndex);
                var targetCapitalIndex = soldier.targetCapitalIndex;

                for(var i=0;i<this.spawnIndex.length;i++){
                    if(this.spawnIndex[i].capitalIndex==sourcCapitalIndex && this.spawnIndex[i].attackIndex==this.attackIndex[sourcCapitalIndex]){
                        // this.spawnIndex[i].spawnIndex=0;
                        this.spawnIndex[i].attackIndex +=1;
                        var idx = this.spawnIndex[i].attackIndex;
                        this.attackIndex[sourcCapitalIndex]=idx;
                        this.spawnIndex.push({capitalIndex:sourcCapitalIndex,spawnIndex:soldier.spawnSoldier,attackIndex:this.attackIndex[sourcCapitalIndex]});
                        this.spawnSoldier(source, true, targetCapitalIndex,soldier.spawnSoldier,idx);
                        break;
                    }
                }
            },
            attackDelay:false,
            capitalAttack: function (data) {
                if(this.attackDelay) return;
                this.attackDelay=true;
                setTimeout(function(){
                    this.attackDelay=false;
                }.bind(this),500);

                var sourcCapitalIndex=data.capitalIndex;
                var source = this.findCapitalByIndex(data.capitalIndex);
                var targetCapitalIndex = data.currentTargetCapitalIndex;

                if (ig.game.client.isMyPlayerId(data.playerId)){
                    var soldier = source.capitalData.soldierCount;
                    for(var i=0;i<this.spawnIndex.length;i++){
                        if(this.spawnIndex[i].capitalIndex==sourcCapitalIndex && this.spawnIndex[i].attackIndex==this.attackIndex[sourcCapitalIndex]){
                            this.spawnIndex[i].attackIndex +=1;
                            var idx = this.spawnIndex[i].attackIndex;
                            this.attackIndex[sourcCapitalIndex]=idx;
                            this.spawnIndex.push({capitalIndex:sourcCapitalIndex,spawnIndex:soldier,attackIndex:this.attackIndex[sourcCapitalIndex]});
                            this.spawnSoldier(source, true,targetCapitalIndex,soldier,idx);
                            break;
                        }
                    }
                }

                if (ig.game.client &&
                    ig.game.client.clientGameRoom &&
                    ig.game.client.clientGameRoom.networkGame) {
                    ig.game.client.clientGameRoom.networkGame.sendGameEvent(
                        new NetworkGameEvent(NetworkGameEvent.EVENT_TYPE.CAPITAL_ATTACK, Date.now(), {
                            attackData: data
                        })
                    );
                }
            },


            // change capital owner
            onChangeOwner: function (data) {
                // activate shield

                var startTime = ig.game.networkClient.getServerTimeInClient(data.startTime);
                // ig.game.game.countDownTimer.startTimer(startTime, data.duration);
                var capital = this.findCapitalByIndex(data.capitalIndex);
                // console.log(capital);
                if (capital && !ig.game.popupStatus) {
                    capital.activateShield(startTime, data.duration);
                    capital.deactivateWarning();
                    ig.game.timerUtility('deactivate_shield', 0.5, capital.deactivateWarning.bind(capital));
                    this.cleanAttackingSoldiers(data.capitalIndex);
                    this.cleanSpawnedSoldiers(data.capitalIndex);
                    
                    capital.capitalData.soldierCount=0;
                    capital.soldierCountText.updateText(Math.round(capital.capitalData.soldierCount));

                    var playerNumber = this.getPlayerNumber(data.newOwner.playerId);
					ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.notif);
                    var str = '';
                    if (capital.capitalData.isBuilding) {
                        switch (capital.capitalData.buildingData.BLDG_CODE) {
                            case 'FORT':
                                str = _STRINGS.NETWORK.CAPTURED_BUILDING.FORT[0].replace('<replace>', data.newOwner.playerName);
                                dl.scene.spawnNotification({
                                    notificationDrawConfigs: {
                                        contentConfigs: [{
                                                type: 'image',
                                                image: dl.preload['game-player-info'].icons[playerNumber - 1]
                                            },
                                            {
                                                type: 'text',
                                                text: str,
                                                fillStyle: dl.configs.TEXT_COLOR.WHITE,
                                                fontSize: 46,
                                                fontFamily: dl.configs.FONT.SOURCE_SANS.name
                                            }
                                        ],
                                        backgroundConfigs: {
                                            lineWidth: 2,
                                            fillStyle: dl.configs.NOTIFICATION_COLORS['PLAYER_' + playerNumber],
                                            strokeStyle: dl.configs.NOTIFICATION_COLORS['PLAYER_' + playerNumber],

                                            box: {
                                                width: 800,
                                                height: 90,
                                                round: 10,
                                                padding: {
                                                    x: 100,
                                                    y: 5
                                                }
                                            }
                                        }
                                    }
                                });


                                setTimeout(function(){                                   
                                    str = _STRINGS.NETWORK.CAPTURED_BUILDING.FORT[1];
                                    dl.scene.spawnNotification({
                                        notificationDrawConfigs: {
                                            contentConfigs: [{
                                                    type: 'image',
                                                    image: dl.preload['game-player-info'].icons[playerNumber - 1]
                                                },
                                                {
                                                    type: 'text',
                                                    text: str,
                                                    fillStyle: dl.configs.TEXT_COLOR.WHITE,
                                                    fontSize: 46,
                                                    fontFamily: dl.configs.FONT.SOURCE_SANS.name
                                                }
                                            ],
                                            backgroundConfigs: {
                                                lineWidth: 2,
                                                fillStyle: dl.configs.NOTIFICATION_COLORS['PLAYER_' + playerNumber],
                                                strokeStyle: dl.configs.NOTIFICATION_COLORS['PLAYER_' + playerNumber],

                                                box: {
                                                    width: 800,
                                                    height: 90,
                                                    round: 10,
                                                    padding: {
                                                        x: 100,
                                                        y: 5
                                                    }
                                                }
                                            }
                                        }
                                    });
                                }.bind(this),2000);

                                break;
                            case 'PT':
                                str = _STRINGS.NETWORK.CAPTURED_BUILDING.PT[0].replace('<replace>', data.newOwner.playerName);
                                dl.scene.spawnNotification({
                                    notificationDrawConfigs: {
                                        contentConfigs: [{
                                                type: 'image',
                                                image: dl.preload['game-player-info'].icons[playerNumber - 1]
                                            },
                                            {
                                                type: 'text',
                                                text: str,
                                                fillStyle: dl.configs.TEXT_COLOR.WHITE,
                                                fontSize: 46,
                                                fontFamily: dl.configs.FONT.SOURCE_SANS.name
                                            }
                                        ],
                                        backgroundConfigs: {
                                            lineWidth: 2,
                                            fillStyle: dl.configs.NOTIFICATION_COLORS['PLAYER_' + playerNumber],
                                            strokeStyle: dl.configs.NOTIFICATION_COLORS['PLAYER_' + playerNumber],

                                            box: {
                                                width: 800,
                                                height: 90,
                                                round: 10,
                                                padding: {
                                                    x: 100,
                                                    y: 5
                                                }
                                            }
                                        }
                                    }
                                });
                                setTimeout(function(){                                   
                                    str = _STRINGS.NETWORK.CAPTURED_BUILDING.PT[1];
                                    dl.scene.spawnNotification({
                                        notificationDrawConfigs: {
                                            contentConfigs: [{
                                                    type: 'image',
                                                    image: dl.preload['game-player-info'].icons[playerNumber - 1]
                                                },
                                                {
                                                    type: 'text',
                                                    text: str,
                                                    fillStyle: dl.configs.TEXT_COLOR.WHITE,
                                                    fontSize: 46,
                                                    fontFamily: dl.configs.FONT.SOURCE_SANS.name
                                                }
                                            ],
                                            backgroundConfigs: {
                                                lineWidth: 2,
                                                fillStyle: dl.configs.NOTIFICATION_COLORS['PLAYER_' + playerNumber],
                                                strokeStyle: dl.configs.NOTIFICATION_COLORS['PLAYER_' + playerNumber],

                                                box: {
                                                    width: 800,
                                                    height: 90,
                                                    round: 10,
                                                    padding: {
                                                        x: 100,
                                                        y: 5
                                                    }
                                                }
                                            }
                                        }
                                    });
                                }.bind(this),3000);
                                break;
                            case 'WD':
                                str = _STRINGS.NETWORK.CAPTURED_BUILDING.WD[0].replace('<replace>', data.newOwner.playerName);
                                dl.scene.spawnNotification({
                                    notificationDrawConfigs: {
                                        contentConfigs: [{
                                                type: 'image',
                                                image: dl.preload['game-player-info'].icons[playerNumber - 1]
                                            },
                                            {
                                                type: 'text',
                                                text: str,
                                                fillStyle: dl.configs.TEXT_COLOR.WHITE,
                                                fontSize: 46,
                                                fontFamily: dl.configs.FONT.SOURCE_SANS.name
                                            }
                                        ],
                                        backgroundConfigs: {
                                            lineWidth: 2,
                                            fillStyle: dl.configs.NOTIFICATION_COLORS['PLAYER_' + playerNumber],
                                            strokeStyle: dl.configs.NOTIFICATION_COLORS['PLAYER_' + playerNumber],

                                            box: {
                                                width: 800,
                                                height: 90,
                                                round: 10,
                                                padding: {
                                                    x: 100,
                                                    y: 5
                                                }
                                            }
                                        }
                                    }
                                });

                                setTimeout(function(){                                   
                                    str = _STRINGS.NETWORK.CAPTURED_BUILDING.WD[1];
                                    dl.scene.spawnNotification({
                                        notificationDrawConfigs: {
                                            contentConfigs: [{
                                                    type: 'image',
                                                    image: dl.preload['game-player-info'].icons[playerNumber - 1]
                                                },
                                                {
                                                    type: 'text',
                                                    text: str,
                                                    fillStyle: dl.configs.TEXT_COLOR.WHITE,
                                                    fontSize: 46,
                                                    fontFamily: dl.configs.FONT.SOURCE_SANS.name
                                                }
                                            ],
                                            backgroundConfigs: {
                                                lineWidth: 2,
                                                fillStyle: dl.configs.NOTIFICATION_COLORS['PLAYER_' + playerNumber],
                                                strokeStyle: dl.configs.NOTIFICATION_COLORS['PLAYER_' + playerNumber],

                                                box: {
                                                    width: 800,
                                                    height: 90,
                                                    round: 10,
                                                    padding: {
                                                        x: 100,
                                                        y: 5
                                                    }
                                                }
                                            }
                                        }
                                    });
                                }.bind(this),3000);

                                break;
                            case 'MS':
                                str = _STRINGS.NETWORK.CAPTURED_BUILDING.MS[0].replace('<replace>', data.newOwner.playerName);
                                dl.scene.spawnNotification({
                                    notificationDrawConfigs: {
                                        contentConfigs: [{
                                                type: 'image',
                                                image: dl.preload['game-player-info'].icons[playerNumber - 1]
                                            },
                                            {
                                                type: 'text',
                                                text: str,
                                                fillStyle: dl.configs.TEXT_COLOR.WHITE,
                                                fontSize: 46,
                                                fontFamily: dl.configs.FONT.SOURCE_SANS.name
                                            }
                                        ],
                                        backgroundConfigs: {
                                            lineWidth: 2,
                                            fillStyle: dl.configs.NOTIFICATION_COLORS['PLAYER_' + playerNumber],
                                            strokeStyle: dl.configs.NOTIFICATION_COLORS['PLAYER_' + playerNumber],

                                            box: {
                                                width: 800,
                                                height: 90,
                                                round: 10,
                                                padding: {
                                                    x: 100,
                                                    y: 5
                                                }
                                            }
                                        }
                                    }
                                });

                                setTimeout(function(){                                   
                                    str = _STRINGS.NETWORK.CAPTURED_BUILDING.MS[1];
                                    dl.scene.spawnNotification({
                                        notificationDrawConfigs: {
                                            contentConfigs: [{
                                                    type: 'image',
                                                    image: dl.preload['game-player-info'].icons[playerNumber - 1]
                                                },
                                                {
                                                    type: 'text',
                                                    text: str,
                                                    fillStyle: dl.configs.TEXT_COLOR.WHITE,
                                                    fontSize: 46,
                                                    fontFamily: dl.configs.FONT.SOURCE_SANS.name
                                                }
                                            ],
                                            backgroundConfigs: {
                                                lineWidth: 2,
                                                fillStyle: dl.configs.NOTIFICATION_COLORS['PLAYER_' + playerNumber],
                                                strokeStyle: dl.configs.NOTIFICATION_COLORS['PLAYER_' + playerNumber],

                                                box: {
                                                    width: 800,
                                                    height: 90,
                                                    round: 10,
                                                    padding: {
                                                        x: 100,
                                                        y: 5
                                                    }
                                                }
                                            }
                                        }
                                    });
                                }.bind(this),3000);

                                break;
                        }
                    } else {
                    if (!data.previousOwner || typeof data.previousOwner === 'undefined') {
                        str = _STRINGS.Game.CAPTURE_NEUTRAL.replace('<replace>', data.newOwner.playerName);
                    } else {
                        str = _STRINGS.Game.CAPTURE_ENEMY.replace('<replace1>', data.newOwner.playerName).replace('<replace2>', data.previousOwner.playerName);
                    }
                    dl.scene.spawnNotification({
                        notificationDrawConfigs: {
                            contentConfigs: [
                                {
                                    type: 'image',
                                    image: dl.preload['game-player-info'].icons[playerNumber - 1]
                                },
                                {
                                    type: 'text',
                                    text: str,
                                    fillStyle: dl.configs.TEXT_COLOR.WHITE,
                                    fontSize: 46,
                                    fontFamily: dl.configs.FONT.SOURCE_SANS.name
                                }],
                            backgroundConfigs: {
                                lineWidth: 2,
                                fillStyle: dl.configs.NOTIFICATION_COLORS['PLAYER_' + playerNumber],
                                strokeStyle: dl.configs.NOTIFICATION_COLORS['PLAYER_' + playerNumber],

                                box: { width: 800, height: 90, round: 10, padding: { x: 100, y: 5 } }
                            }
                        }
                    });
                }
				}

                setTimeout(function(){
                    this.checkForGameEnd();                    
                }.bind(this),3000);
            },

            onClearSpawnTimers: function (info) {
                ig.game.clearSpawnDelays({ source: info.data.sourceCapitalIndex, target: info.data.targetCapitalIndex });
            },

            aabbCheck: function (aabb1, aabb2) {
                return (
                    aabb1.x < aabb2.x + aabb2.w &&
                    aabb1.x + aabb1.w > aabb2.x &&
                    aabb1.y < aabb2.y + aabb2.h &&
                    aabb1.y + aabb1.h > aabb2.y
                );
            },

            removeSoldierFromList: function () {
                var newList = this.soldierList.filter(function (soldier) {
                    return !soldier.killed;
                });
                this.soldierList = [];
                this.soldierList = newList;
            },

            isTargetAlly: function (source, target) {
                if (!target) return false;
                if (!source) return false;
                if (!target.capitalData) return false;
                if (!source.capitalData) return false;
                if (!target.capitalData.assignedPlayer) return false;
                if (!source.capitalData.assignedPlayer) return false;
                var sourceId = source.capitalData.assignedPlayer.playerId;
                var targetId = target.capitalData.assignedPlayer.playerId;
                return sourceId === targetId;
            },

            transferNationOwnership: function (capital) {
                var nation;
                for (var i = 0; i < this.nationList.length; i++) {
                    if (this.nationList[i].nationData.nationIndex == capital.capitalData.capitalIndex) {
                        this.nationList[i].nationData.assignedPlayer = capital.capitalData.assignedPlayer;
                        nation = this.nationList[i];
                        return nation;
                    }
                }
            },

            findNationByCapital: function (capital) {
                for (var i = 0; i < this.nationList.length; i++) {
                    if (this.nationList[i].nationData.nationIndex == capital.capitalData.capitalIndex) {
                        return this.nationList[i];
                    }
                }
            },

            findCapitalByNation: function (nation) {
                for (var i = 0; i < this.capitalList.length; i++) {
                    if (this.capitalList[i].capitalData.capitalIndex == nation.nationData.nationIndex) {
                        return this.capitalList[i];
                    }
                }
            },

            findCapitalByIndex: function (index) {
                for (var i = 0; i < this.capitalList.length; i++) {
                    if (this.capitalList[i].capitalData.capitalIndex == index) {
                        return this.capitalList[i];
                    }
                }
            },

            findCapitalByIgGameIndex: function (index) {
                for (var i = 0; i < ig.game.capitals.length; i++) {
                    if (ig.game.capitals[i].capitalData.capitalIndex == index) {
                        return ig.game.capitals[i];
                    }
                }
            },

            updateCapitalSoldier:function(data){
                // console.log(data.capitalIndex,data.soldierCount);
                // for (var i = 0; i < this.capitalList.length; i++) {
                //     console.log(this.capitalList[i].capitalData);
                // }

                var capital = this.findCapitalByIgGameIndex(data.capitalIndex);
                capital.updateData2(data);
            },
            /* old function, retained for reference */
            onCapitalBotAttack: function (data) {
                var source = this.findCapitalByIndex(data.capitalIndex);
                if (source) {
                    var targetCapitalIndex = data.targetCapitalIndex;
                    this.spawnSoldier(source, true, targetCapitalIndex,source.capitalData.soldierCount);
                }
            },

            onPlayerLeave: function (player) {
                for (var i = 0; i < this.capitalList.length; i++) {
                    if (this.capitalList[i].capitalData.assignedPlayer && this.capitalList[i].capitalData.assignedPlayer.playerId == player.playerId) {
                        // remove player ownership from the capital and nation
                        this.capitalList[i].removePlayer();
                        ig.game.clearSpawnDelays(this.capitalList[i].capitalData.capitalIndex);
                        var nation = this.findNationByCapital(this.capitalList[i]);
                        if (nation) {
                            nation.removePlayer();
                        }
                    }
                }
                for (var i = 0; i < this.players.length; i++) {
                    // mark player as eliminated
                    if (this.players[i].playerId == player.playerId) {
                        this.players[i].isLeaved = true;
                    }
                }
                // kill spawned soldiers
                this.cleanSoldiers(player.playerId);
            },

            getPlayerRankings: function () {
                if (!this.players) return;
                if (this.players.length === 0) return;
                var playerRankings = [];
                for (var i = 0; i < this.players.length; i++) {
                    var player = this.players[i];


                    if (player) {
                        var playerData = {
                            playerId: player.playerId,
                            playerName: player.playerName,
                            playerRank: player.playerRank,
                            playerScore: player.playerScore,
                            playerAvatarId: player.playerAvatarId,
                            seq:i,
                        };
                        playerRankings.push(playerData);
                    }
                }
                // sort playerRankings
                var finalRanking = playerRankings.sort(function (a, b) {
                    return b.playerScore - a.playerScore;
                });

                for(var i=0;i<finalRanking.length;i++){
                    if(i>0) window.playerList[finalRanking[i].seq].eliminated.updateImage(dl.preload['game-player-info'].eliminated);                    
                }

                return finalRanking;
            },

            getPlayerNumber: function (checkId) {
                if (!this.players) return;
                if (this.players.length === 0) return;
                if (typeof (checkId) !== 'number') return false;

                for (var i = 0; i < this.players.length; i++) {
                    if (this.players[i] && this.players[i].playerId == checkId) {
                        return i + 1;
                    }
                }
            },

            getPointerTarget: function (pointerPos) {
                var pointerAabb = {
                    x: pointerPos.x,
                    y: pointerPos.y,
                    w: 10,
                    h: 10
                };
                var capital = null;
                for (var i = 0; i < this.capitalList.length; i++) {
                    if (this.aabbCheck(pointerAabb, this.capitalList[i].getAabb())) {
                        capital = this.capitalList[i];
                    }
                }
                return capital;
            },
            showLoseTextStatus:false,
            allPlayerEnd:false,
            checkForGameEnd: function () {
                if(this.isGameOver) return;
                if(this.allPlayerEnd) return;

                try{
                    var isGameEnd = false;
                    var enemyLostCount = 0;
                    var humanPlayer = 0;
                    for (var i = 0; i < this.players.length; i++) {

                        // check if player doesn't have capital left
                        if (ig.game.client.isMyPlayerId(this.players[i].playerId)) {
                                if (this.playerEnd[i]) {
                                    this.cleanSoldiers(this.players[i].playerId);
                                    isGameEnd = true;

                                    if (ig.game.client.roomMaxPlayer == 2) {
                                        if (this.playerEnd[i] || this.players[i].isLeaved) {
                                            this.cleanSoldiers(this.players[i].playerId);
                                            isGameEnd = true;
                                            this.isSpectator=false;
                                        }
                                    } else {
                                        // four player mode
                                        if (this.playerEnd[i] || this.players[i].isLeaved) {
                                            this.cleanSoldiers(this.players[i].playerId);
                                            enemyLostCount++;
                                        }

                                        if(!this.players[i].botFlag){
                                            humanPlayer++;
                                        }

                                        if (enemyLostCount >= 3 || humanPlayer==0) {
                                            this.isSpectator=false;                                
                                            isGameEnd = true;
                                        }else if(enemyLostCount >= 1 && enemyLostCount <3 && humanPlayer>0){
                                            this.isSpectator=true;
                                            if(!this.showLoseTextStatus){
                                                this.showLoseTextStatus=true;
                                                this.showLoseSpectatorDlg();
                                            }
                                        }
                                    }
                                }

                        } else {
                            // two player mode
                            if (ig.game.client.roomMaxPlayer == 2) {
                                if (this.playerEnd[i] || this.players[i].isLeaved) {
                                    this.cleanSoldiers(this.players[i].playerId);
                                    isGameEnd = true;
                                    this.isSpectator=false;
                                }
                            } else {
                                // four player mode
                                if (this.playerEnd[i] || this.players[i].isLeaved) {
                                    this.cleanSoldiers(this.players[i].playerId);
                                    enemyLostCount++;
                                }

                                if (enemyLostCount >= 3) {
                                    this.isSpectator=false;                                
                                    isGameEnd = true;
                                }
                            }
                        }
                    }

                    if (isGameEnd && !this.isGameOver && !this.isSpectator) {
                        this.allPlayerEnd = true;
                        ig.game.popupStatus=true;                    
                        this.isGameOver = true;
                        this.clearAllData();
                        
                        setTimeout(function(){
                             ig.game.game.spawnResultPopup();
                        }.bind(this),1000); 

                    }
                }catch(e){
                    // console.log(e);
                }
            },
            showLoseSpectatorDlg:function(){
                ig.game.popupStatus=true;                    
                ig.game.game.spawnResultPopupSpectator();
            },
            showLoseText:function(){

                if(this.loseText) this.loseText.kill();
                this.loseText = this.spawnEntity(dl.EntityText, {
                    c_DockerComponent: {
                        dockerObject: dl.game.camera,
                        dockerPercent: { x: 0.5, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_TextComponent: {
                        fontSize: 0,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                        fillStyle: '#505050',
                    },
                    text: 'You Lose',
                    zIndex:1000,
                });


                this.tw1 = this.createTween()
                    .to({
                        loseText: { c_TextComponent: {fontSize:200} }
                    }, 500, {
                        easing: dl.TweenEasing.Quartic.EaseIn,
                        onPropertiesChanged: function () {
                            if (this.loseText) this.loseText.c_TextComponent.updateProperties();
                        }.bind(this)
                    })
                    .start({
                        onCompleteTween: function () {
                            setTimeout(function(){
                                this.closeLoseText();
                            }.bind(this),1200);
                        }.bind(this)
                    });                

            },
            closeLoseText:function(){
                this.tw2 = this.createTween()
                    .to({
                        loseText: { c_TextComponent: {fontSize:0} }
                    }, 400, {
                        easing: dl.TweenEasing.Quartic.EaseOut,
                        onPropertiesChanged: function () {
                            if (this.loseText) this.loseText.c_TextComponent.updateProperties();
                        }.bind(this)
                    })
                    .start({
                        onCompleteTween: function () {
                            this.loseText.kill();
                            this.showSpectatorText()
                        }.bind(this)
                    });                                
            },
            showSpectatorText:function(){
                if(this.allPlayerEnd) return;
                this.spectatorText = this.spawnEntity(dl.EntityText, {
                    c_DockerComponent: {
                        dockerObject: dl.game.camera,
                        dockerPercent: { x: 0.5, y: 0.95 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_TextComponent: {
                        fontSize: 50,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                        fillStyle: '#FF0000',
                    },
                    text: 'Spectator Mode'
                });
                // this.goBlink();
            },
            blinkOn:false,
            goBlink:function(){
                setTimeout(function(){
                    if(this.blinkOn){
                        this.blinkOn=false;
                        this.showSpectatorText();
                    }else{
                        this.blinkOn=true;
                        if(this.spectatorText) this.spectatorText.kill();
                        this.goBlink();
                    }
                }.bind(this),200);
            },
            clearAllData:function(){
                for (var i = 0; i < this.capitalList.length; i++) {
                    // if (this.capitalList[i].capitalData.capitalIndex == index) {
                        this.capitalList[i].kill();
                    // }
                }                
            },

            cleanSoldiers: function (playerId) {
                for (var i = 0; i < this.soldierList.length; i++) {
                    if (this.soldierList[i] &&
                        this.soldierList[i].playerId &&
                        this.soldierList[i].playerId == playerId
                        )
                    {
                        this.soldierList[i].kill();
                    }
                }
            },

            cleanAttackingSoldiers: function (capitalIndex) {
                var source = null;
                var target = null;
                for (var i = 0; i < this.soldierList.length; i++) {
                    if (this.soldierList[i] &&
                        this.soldierList[i].targetCapital &&
                        this.soldierList[i].targetCapital.capitalData.capitalIndex == capitalIndex
                        )
                    {
                        // console.log('Kill soldiers attacking capitalIndex: ', capitalIndex);
                        source = this.soldierList[i].sourceCapital.capitalData.capitalIndex;
                        target = this.soldierList[i].targetCapital.capitalData.capitalIndex;
                        this.soldierList[i].kill();
                    }
                }
                if (source && target) {
                    ig.game.clearSpawnDelays({
                        source: source,
                        target: target
                    });
                }
            },

            cleanSpawnedSoldiers: function (capitalIndex) {
                var source = null;
                var target = null;
                for (var i = 0; i < this.soldierList.length; i++) {
                    if (this.soldierList[i] &&
                        this.soldierList[i].sourceCapital &&
                        this.soldierList[i].sourceCapital.capitalData.capitalIndex == capitalIndex
                        )
                    {
                        // console.log('Kill spawned soldiers attacking capitalIndex: ', capitalIndex);
                        source = this.soldierList[i].sourceCapital.capitalData.capitalIndex;
                        target = this.soldierList[i].targetCapital.capitalData.capitalIndex;
                        this.soldierList[i].kill();
                    }
                }
                if (source && target) {
                    ig.game.clearSpawnDelays({
                        source: source,
                        target: target
                    });
                }
            },
            // gather the current targets of the soldiers on the field
            tempCheckTarget:0,
            gatherTargetList: function () {

                if(this.tempCheckTarget==5){
                    this.tempCheckTarget=0;
                    this.targetList = [];
                    var tempList = [];
                    for (var i = 0; i < this.soldierList.length; i++) {
                        if (this.soldierList[i] &&
                            this.soldierList[i].targetCapital &&
                            this.soldierList[i].targetCapital.capitalData.capitalIndex != -1) {
                                if (this.soldierList[i].targetCapital.capitalData.assignedPlayer &&
                                    this.soldierList[i].sourceCapital.capitalData.assignedPlayer.playerId != this.soldierList[i].targetCapital.capitalData.assignedPlayer.playerId) {
                                    this.targetList.push(this.soldierList[i].targetCapital.capitalData.capitalIndex);
                                }
                        }
                    }
                }
                this.tempCheckTarget +=1;

                // remove duplicates
                // this.targetList = tempList.filter(function (v, i, a) {
                //     return a.indexOf(v) === i;
                // });
            },

            onCaptureBldg: function (data) {
                if(this.isGameOver) return;
                if(this.allPlayerEnd) return;
                // console.log(data);
                // setTimeout(function(){                        
                    switch (data.bldgCode) {
                        case 'FORT':
                            ig.soundHandler.bgmPlayer.volume(0);
                            ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.training_soldier);
                            setTimeout(function(){
                                ig.soundHandler.bgmPlayer.volume(1);
                            }.bind(this),6000);

                            // console.log('someone captured a fort');
                            break;
                        case 'WD':
                            // console.log('someone captured a weather dome');
                            // console.log(data);
                            ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.thunder)
                            for (var i = 0; i < this.capitalList.length; i++) {
                                if (this.capitalList[i].capitalData.assignedPlayer && this.capitalList[i].capitalData.assignedPlayer.playerId != data.player.playerId) {
                                    // console.log('summon thundercloud');
                                    this.capitalList[i].spawnThundercloud();
                                    this.capitalList[i].capitalData.soldierCount -=NETWORK_GAME_CONFIGS.BUILDINGS[1].EFFECT;
                                    if(this.capitalList[i].capitalData.soldierCount<=0) this.capitalList[i].capitalData.soldierCount=0;

                                    if (ig.game.client &&
                                        ig.game.client.clientGameRoom &&
                                        ig.game.client.clientGameRoom.networkGame) {
                                        ig.game.client.clientGameRoom.networkGame.sendGameEvent(
                                            new NetworkGameEvent(NetworkGameEvent.EVENT_TYPE.SYNC_SOLDIER_COUNT,Date.now(),{
                                                soldierCount: this.capitalList[i].capitalData.soldierCount,
                                                capitalIndex: this.capitalList[i].capitalData.capitalIndex || "",
                                                sourceCapitalIndex:data.sourceCapitalIndex,
                                                attackerPlayerId:data.player.playerId,
                                                attackedStatus:false,
                                            })
                                        );
                                    }

                                }
                            }
                            break;
                        case 'MS':
                            for (var i = 0; i < this.capitalList.length; i++) {
                                if (this.capitalList[i].capitalData.isBuilding) {
                                    // console.log('summon thundercloud');
                                    this.capitalList[i].activateMissile(data.targetCapitalIndex);
                                }
                            }
                            // console.log('someone captured a missile silo');
                            break;
                        case 'PT':
                            // console.log('someone captured a propaganda tower');
                            ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.radio)
                            var startTime = ig.game.networkClient.getServerTimeInClient(data.timestamp);
                            for (var i = 0; i < this.capitalList.length; i++) {
                                if (this.capitalList[i].capitalData.assignedPlayer && this.capitalList[i].capitalData.assignedPlayer.playerId != data.player.playerId && !this.capitalList[i].capitalData.isBuilding) {
                                    this.capitalList[i].activatePropaganda(startTime, data.duration);
                                }
                            }
                            break;
                    }
                // }.bind(this),2000);

            },
            update: function () {
                this.parent();
                this.checkEliminatePlayer();

                if(this.gameEndTimerCheck.delta()>3){
                    this.gameEndTimerCheck.reset();
                    this.checkForGameEnd();
                }

                if (this.playersToEliminate.length > 0) {
                    this.playerEliminated(this.playersToEliminate);
                    this.playersToEliminate = [];
                }

                if (this.isGameOver) return;
                this.gatherTargetList();
                this.removeSoldierFromList();
            },

            checkEliminatePlayer:function(){       
                try{         
                    for (var i = 0; i < this.players.length; i++) {
                        if (!this.players[i].eliminated && this.checkPlayerCapitalEmpty(this.players[i].playerId)) {
                            if (ig.game.client &&
                                ig.game.client.clientGameRoom &&
                                ig.game.client.clientGameRoom.networkGame) {
                                ig.game.client.clientGameRoom.networkGame.sendGameEvent(
                                    new NetworkGameEvent(NetworkGameEvent.EVENT_TYPE.PLAYER_ELIMINATED, Date.now(), {
                                        playerId:this.players[i].playerId,
                                    })
                                );
                            }

                        }
                    }
                }catch(e){}

                try{         
                    for (var i = 0; i < this.playerEnd.length; i++) {
                        if (!this.playerEnd[i] && this.checkPlayerCapitalEmpty(this.players[i].playerId)) {
                            if (ig.game.client &&
                                ig.game.client.clientGameRoom &&
                                ig.game.client.clientGameRoom.networkGame) {
                                this.playerEnd[i]=true;
                            }

                        }
                    }
                }catch(e){}

            },
            checkPlayerCapitalEmpty:function(id){
                for (var i = 0; i < this.capitalList.length; i++) {
                    if (this.capitalList[i].capitalData.assignedPlayer && this.capitalList[i].capitalData.assignedPlayer.playerId == id) {
                        return false;
                    }
                }
                return true;
            },

            updateData: function (data) {
                if (!data) return;
                for (var i = 0; i < ig.game.client.roomMaxPlayer; i++) {
                    this.players[i] = data.playerList[i];
                }

                if (this.players.length > 0 && this.playersCache.length == 0) {
                    this.playersCache = this.players.map(function (player) {
                        return player;
                    });
                }

                if (data.mapData && data.mapData.capitalData) {
                    var capitalData = data.mapData.capitalData;
                    for (var i = 0; i < capitalData.length; i++) {
                        for (var j = 0; j < this.capitalList.length; j++) {
                            if (this.capitalList[j].capitalData.capitalIndex == capitalData[i].capitalIndex) {
                                this.capitalList[j].updateData(capitalData[i]);
                            }
                        }
                    }
                }
            },

        });
});
