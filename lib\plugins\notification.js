/* Version: 1.0
 *
 * This plugins works like an entity, and relies on the tween function injected in the entity
 *
 * How to use:
 *
 * 0. Include 'plugins.notification' in your main.js
 * 1. Create an empty notification array in your GAME CONTROLLER
 *     e.g. notificationArr: []
 *
 * 2. Spawn the entity using the standard way, then pass your GAME CONTROLLER to the spawned entity
 *     e.g. ig.game.spawnEntity(EntityNotification, ig.system.width * 0.5, ig.system.height * 0.5, { parentEntity: this })
 *       => the 'this' in the settings assumes that you spawn the entity IN the game controller,
 *          if not can use your alias (e.g. ig.game.gameControl) instead
 *       => By default, the position is anchored to the center, meaning the (0,0) starts from the center of the draw instead of upper left.
 *          You can change these to suit your needs but you have to change also how the draws are made
 *
 * Sample Usage:
 *
 * ig.game.spawnEntity(EntityNotification, ig.system.width * 0.5, ig.system.height * 0.5, {
 *   text: 'Your Turn!',
 *   parentEntity: this
 * });
 *
 * == More information on the line by line comments ==
 *
 */

ig.module('plugins.notification')
.requires(
    'impact.entity'
)
.defines(function () {
    EntityNotification = ig.Entity.extend({
        checkAgainst: ig.Entity.TYPE.NONE,
        zIndex: 1000,

        size: new Vector2(0, 0),
        scale: new Vector2(0, 1),
        // the notification panel background
        notifImageList: [
            new ig.Image('media/graphics/sprites/ui/notification.png'),
            new ig.Image('media/graphics/sprites/ui/notification-background.png')
        ],
        notifImage: '',
        emojiImage: new ig.Image('media/graphics/sprites/game/emoji-notif.png'),
        emojiScale: 0.5,
        emojiIndex: 0,

        alpha: 0,
        parentEntity: null,
        isAnchorCenter: true,
        delayTimer: null,
        // below is the text passed from the settings during spawn
        text: '',
        // below value is used in delaying the chain of tweens
        delayedFunction: null,
        withEmoji: false,
        fontFace: 'montserrat-bold',
        fontSize: 50,
        fontColor: '#ffffff',
        notifType: 0,
        widthMultiplier: 1,
        init: function (x, y, settings) {
            this.parent(x, y, settings);
            if (this.notifType === 0) {
                this.notifImage = this.notifImageList[0];
            } else {
                this.notifImage = this.notifImageList[1];
                this.widthMultiplier = 1.5;
            }
            this.size.x = this.notifImage.width * this.widthMultiplier;
            this.size.y = this.notifImage.height;
            var textInput = ig.game.getEntitiesByType(EntityTextInput)[0];
            if (textInput) {
                textInput.container.hide();
            }
            // By default, the spawn is anchored to the center
            if (this.isAnchorCenter) {
                this.anchorCenter();
            }

            // we check if there are existing notification on the game
            if (this.parentEntity.notificationArr.length > 0) {
                // we move the existing notification before spawning the new one
                for (var i = 0; i < this.parentEntity.notificationArr.length; i++) {
                    this.parentEntity.notificationArr[i].tweenMoveUp();
                }
            }
            // we add this notification to the notification array in the game controller
            this.parentEntity.notificationArr.push(this);
            // we tween the entrace of the notification
            this.tweenIn();
            ig.game.sortEntitiesDeferred();
        },

        anchorCenter: function () {
            this.pos.x = this.pos.x + this.size.x * -0.5;
            this.pos.y = this.pos.y + this.size.y * -0.5;
        },

        // function below delays the execution of the chained tween (if there is)
        handleDelayTimer: function () {
            if (this.delayTimer && this.delayTimer.delta() > 0) {
                // after the timer is up, we set the timer to null to prepare for future uses
                this.delayTimer = null;
                // then we exectute the saved function (in our case, the tween function)
                this.delayedFunction();
                // then we set the variable to null to prepare for future uses
                this.delayedFunction = null;
            }
        },

        tweenIn: function () {
            this.tween({
                alpha: 1,
                scale: { x: 1 }
            }, 0.1, {
                onComplete: function () {
                    // below are optional if you want to delay the chain of tween
                    // we set first a timer of how many seconds we want to delay, in this case 1 second
                    this.delayTimer = new ig.Timer(1);
                    // then we assign the next tween function to the delayedFunction variable
                    this.delayedFunction = this.tweenFadeOut;
                }.bind(this)
            }).start();
        },

        tweenFadeOut: function () {
            this.tween({ alpha: 0 }, 0.5, {
                onComplete: function () {
                    // we kill the notification after it served it purpose
                    this.kill();
                    // then we remove the killed notification in the notification array in the game controller
                    this.parentEntity.notificationArr.shift();
                }.bind(this)
            }).start();
        },

        // this tween handles the moving up animation of the notification
        tweenMoveUp: function () {
            this.tween({
                pos: {
                    y: this.pos.y - this.size.y * 1.2
                }
            }, 0.1).start();
        },

        kill: function () {
            this.parent();
            var textInput = ig.game.getEntitiesByType(EntityTextInput)[0];
            var panel = ig.game.getEntitiesByType(EntityPopupPassword)[0];
            if (textInput && !panel) {
                textInput.container.show();
            }
        },

        draw: function () {
            this.parent();

            var ctx = ig.system.context;
            ctx.save();
            ctx.globalAlpha = this.alpha;
            // we anchor the draws to the center so that the tweenIn animation starts from the center
            ctx.translate(this.pos.x + this.size.x * 0.5, this.pos.y + this.size.y * 0.5);
            ctx.scale(this.scale.x, this.scale.y);
            // this.notifImage.draw(
            //     this.size.x * -0.5,
            //     this.size.y * -0.5
            // );
            // texts are aligned to the center
            // you can change the font's size or family if you want
            ctx.font = this.fontSize + 'px ' + this.fontFace;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillStyle = this.fontColor;
            // we draw it from (0,0) because of the translate above

            if (this.withEmoji) {
                var emojiWidthOffset = this.emojiImage.width * 0.25 * this.emojiScale * 0.5 * -1;
                ctx.fillText(this.text, emojiWidthOffset, 0);
                var textLength = ctx.measureText(this.text).width;
                this.emojiImage.drawImage(
                    this.emojiImage.width * 0.25 * this.emojiIndex,
                    0,
                    this.emojiImage.width * 0.25,
                    this.emojiImage.height,
                    textLength * 0.5 + (emojiWidthOffset) + 7,
                    this.emojiImage.height * 0.5 * this.emojiScale * -1,
                    this.emojiImage.width * 0.25 * this.emojiScale,
                    this.emojiImage.height * this.emojiScale
                );
            } else {
                // ctx.fillText(this.text, 0, 0);
                var lineCount = countTextLines(ctx, this.text, this.size.x * 0.98, 55);
                this.notifImage.drawImage(
                    this.size.x * -0.5,
                    this.size.y * -0.5,
                    this.size.x,
                    this.size.y + (lineCount * 55)
                );
                wrapText(ctx, this.text, 0, 0, this.size.x * 0.98, 55);
            }
            ctx.restore();
        },

        update: function () {
            this.parent();
            // we call the delay handler every update
            this.handleDelayTimer();
        }
    });
});
