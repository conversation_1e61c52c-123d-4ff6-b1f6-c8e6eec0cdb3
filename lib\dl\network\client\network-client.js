/**
 * Created by <PERSON><PERSON>
 * Handle user, messages and ping on client side
 */
ig.module(
    'dl.network.client.network-client'
).requires(

).defines(function () {
    NetworkClient = ig.Class.extend({
        init: function () {
            this.server_ip = getQueryVariable('server_ip') || dl.game.serverIP;
            this.socket = null;

            // handlers
            this.onServerConnectHandlers = [];
            this.onServerDisconnectHandlers = [];
            this.onServerMessageHandlers = [];
            this.onServerPingHandlers = [];
            this.onPingServerReplyHandlers = [];

            // pings
            this.latencyAverage = 0;
            this.timeDiffAverage = 0;
            this._latencyLog = [];
            this._latencyLogSize = SERVER_CONFIGS.PING_LOG_SIZE;
            this.lastPingTime = 0;
            this.lastTimeDiff = 0;
        },

        // ----------------
        // Start Message's functions
        // ----------------
        sendMessage: function (data) {
            this.socket.emit(SERVER_CONFIGS.TAGS.MESSAGE, data);
        },

        onServerMessage: function (data) {
            if (!data) return;

            for (var i = 0, iLength = this.onServerMessageHandlers.length; i < iLength; i++) {
                var handler = this.onServerMessageHandlers[i];
                if (typeof (handler) === 'function') handler(data);
            }
        },
        // ----------------
        // End Message's functions
        // ----------------

        // ----------------
        // Start Connection's functions
        // ----------------
        checkConnection: function () {
            if (!this.socket) return false;
            if (this.socket.disconnected) return false;

            return true;
        },

        connectSocket: function () {
            if (typeof (this.server_ip) === 'undefined' || !this.server_ip) {
                if (typeof dl.game.serverIP === 'undefined' || !dl.game.serverIP) {
                    if (typeof (SERVER_IP) === 'undefined' || !SERVER_IP) {
                        this.server_ip = 'localhost';
                    } else {
                        this.server_ip = SERVER_IP;
                    }
                } else {
                    this.server_ip = dl.game.serverIP;
                }
            }

            var ip=this.server_ip.split(":");            
            for(var i=0;i<SERVER_CONFIGS.SERVER_IPS.length;i++){
                if(SERVER_CONFIGS.SERVER_IPS[i]==ip[0]){
                    ig.game.serverActive=i;
                }
            }

            var port = SERVER_CONFIGS.HTTP_PORT;
            if (document.location.protocol &&
                typeof (document.location.protocol.includes) == 'function' &&
                document.location.protocol.includes('https')) {
                port = SERVER_CONFIGS.HTTPS_PORT;
            } else {
                port = SERVER_CONFIGS.HTTP_PORT;
            }

            if (!dl.game.serverIP) dl.game.serverIP = this.server_ip;

            if(ip.length==1){
                this.server_ip += ':' + port;
            }            
            console.log('Connecting to ' + this.server_ip);
            this.socket = io(this.server_ip);


            return !!this.socket;
        },

        connect: function (successCallback, failCallback) {
            this.resetPing();

            var connecting = false;
            if (!this.socket) {
                connecting = this.connectSocket();
            } else {
                if (this.socket.disconnected) {
                    this.socket.connect();
                    connecting = true;
                }
            }

            if (connecting) {
                this.socket.off();
                this.socket.on('connect', function () {
                    this.onServerConnect();

                    this.socket.off('disconnect');
                    this.socket.on('disconnect', function () {
                        this.onServerDisconnect();
                    }.bind(this));

                    this.socket.off(SERVER_CONFIGS.TAGS.MESSAGE);
                    this.socket.on(SERVER_CONFIGS.TAGS.MESSAGE, function (data) {
                        this.onServerMessage(data);
                    }.bind(this));

                    this.socket.off(SERVER_CONFIGS.TAGS.PING);
                    this.socket.on(SERVER_CONFIGS.TAGS.PING, function (data) {
                        this.onServerPing(data);
                    }.bind(this));

                    this.socket.off(SERVER_CONFIGS.TAGS.PING_REPLY);
                    this.socket.on(SERVER_CONFIGS.TAGS.PING_REPLY, function (data) {
                        this.onPingServerReply(data);
                    }.bind(this));
                }.bind(this));

                if (typeof (successCallback) == 'function') successCallback();
                return true;
            }
            

            if (typeof (failCallback) == 'function') failCallback();
            return false;
        },

        disconnect: function (successCallback, failCallback) {
            if (this.socket) {
                if (!this.socket.disconnected) {
                    this.socket.once('disconnect', function () {
                        if (typeof (successCallback) == 'function') successCallback();
                    }.bind(this));

                    this.socket.disconnect();
                    return true;
                }
            }

            if (typeof (failCallback) == 'function') failCallback();
            return false;
        },

        onServerConnect: function () {
            for (var i = 0, iLength = this.onServerConnectHandlers.length; i < iLength; i++) {
                var handler = this.onServerConnectHandlers[i];
                if (typeof (handler) === 'function') {
                    handler();
                }
            }
        },

        onServerDisconnect: function () {
            for (var i = 0, iLength = this.onServerDisconnectHandlers.length; i < iLength; i++) {
                var handler = this.onServerDisconnectHandlers[i];
                if (typeof (handler) === 'function') {
                    handler();
                }
            }
        },
        // ----------------
        // End Connection's functions
        // ----------------

        // ----------------
        // Start Ping's functions
        // ----------------
        resetPing: function () {
            this.latencyAverage = 0;
            this.timeDiffAverage = 0;
            this._latencyLog = [];
            this._latencyLogSize = 10;
            this.lastPingTime = 0;
            this.lastTimeDiff = 0;
        },
        pingStatus:true,
        pingServer: function () {

            if(!this.pingStatus) return
            this.pingStatus = false;
            setTimeout(function(){
                this.pingStatus = true;                
            }.bind(this),5000);


            if (!this.socket) return;

            var now = Date.now();
            if ((now - this.lastPingTime) <= SERVER_CONFIGS.PING_DELAY) {
                return;
            }

            // ping
            this.socket.emit(SERVER_CONFIGS.TAGS.PING, {
                pingTime: now
            });

            // set lastPingTime for next time check
            this.lastPingTime = now;
        },

        onServerPing: function (data) {
            data.pongTime = Date.now();
            this.socket.emit(SERVER_CONFIGS.TAGS.PING_REPLY, data);

            for (var i = 0, iLength = this.onServerPingHandlers.length; i < iLength; i++) {
                var handler = this.onServerPingHandlers[i];
                if (typeof (handler) === 'function') {
                    handler(data);
                }
            }
        },

        onPingServerReply: function (data) {

            if (!data) return;
            if (isNaN(data.pingTime) || data.pingTime === null) return;
            if (isNaN(data.pongTime) || data.pongTime === null) return;

            var roundTripTime = Date.now() - data.pingTime;
            this.lastTimeDiff = Date.now() - data.pongTime + roundTripTime / 2;

            var latency = Math.floor(roundTripTime / 2);
            this.calculateAverageTimeDiff(latency, data.pongTime);

            for (var i = 0, iLength = this.onPingServerReplyHandlers.length; i < iLength; i++) {
                var handler = this.onPingServerReplyHandlers[i];
                if (typeof (handler) === 'function') {
                    handler(socket, data);
                }
            }
        },

        calculateAverageTimeDiff: function (latency, pongTime) {
            if (isNaN(latency) || latency === null) return;
            if (isNaN(pongTime) || pongTime === null) return;

            if (this._latencyLog.length >= this._latencyLogSize) {
                this._latencyLog.shift();
            }

            this._latencyLog.push(latency);

            var sum = 0;
            for (var i = 0, iLength = this._latencyLog.length; i < iLength; i++) {
                sum += this._latencyLog[i];
            }

            this.latencyAverage = Math.round(sum / this._latencyLog.length);
            this.timeDiffAverage = Date.now() - (pongTime + this.latencyAverage);
        },

        getServerTimeInClient: function (serverTime) {
            return serverTime + this.timeDiffAverage;
        },

        getInfo: function () {
            if (!this.checkConnection()) return 'Offline';
            // if (this.latencyAverage<10) this.latencyAverage=10;
            return this.latencyAverage.toString() + ' ms';
        }
        // ----------------
        // End Ping's functions
        // ----------------
    });
});
