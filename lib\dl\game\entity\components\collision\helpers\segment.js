ig.module(
    'dl.game.entity.components.collision.helpers.segment'
).requires(
    'dl.game.entity.components.collision.helpers.point'
).defines(function () {
    'use strict';

    dl.CollisionHelpers_Segment = {
        /**
         * distance of two point
         * @param {number} sx1
         * @param {number} sy1
         * @param {number} sx2
         * @param {number} sy2
         * @returns {boolean}
         */
        distanceSquared: function (sx1, sy1, sx2, sy2) {
            return Math.sqrt(Math.pow(sx1 - sx2, 2) + Math.pow(sy1 - sy2, 2));
        },

        /**
         * segment-point collision
         * @param {number} sx1 x of first point
         * @param {number} sy1 y of first point
         * @param {number} sx2 x of second point
         * @param {number} sy2 y of second point
         * @param {number} px x of second point
         * @param {number} py y of second point
         * @returns {boolean}
         */
        point: function (sx1, sy1, sx2, sy2, px, py, tolerance) {
            tolerance = tolerance || 1;
            return Math.abs(this.distanceSquared(sx1, sy1, sx2, sy2) - (this.distanceSquared(sx1, sy1, px, py) + distanceSquared(sx2, sy2, px, py))) <= tolerance;
        },

        /**
         * segment-segment
         * http://stackoverflow.com/questions/563198/how-do-you-detect-where-two-line-segments-intersect
         * @param {number} x1 x of first point segment 1
         * @param {number} y1 y of first point segment 1
         * @param {number} x2 x of second point segment 1
         * @param {number} y2 y of second point segment 1
         * @param {number} x3 x of first point segment 2
         * @param {number} y3 y of first point segment 2
         * @param {number} x4 x of second point segment 2
         * @param {number} y4 y of second point segment 2
         * @return {boolean}
         */
        segment: function (x1, y1, x2, y2, x3, y3, x4, y4) {
            var s1_x = x2 - x1;
            var s1_y = y2 - y1;
            var s2_x = x4 - x3;
            var s2_y = y4 - y3;
            var s = (-s1_y * (x1 - x3) + s1_x * (y1 - y3)) / (-s2_x * s1_y + s1_x * s2_y);
            var t = (s2_x * (y1 - y3) - s2_y * (x1 - x3)) / (-s2_x * s1_y + s1_x * s2_y);
            return s >= 0 && s <= 1 && t >= 0 && t <= 1;
        },

        /**
         * segment-rectangle collision
         * @param {number} x1 x of first point segment
         * @param {number} y1 y of first point segment
         * @param {number} x2 x of second point segment
         * @param {number} y2 y of second point segment
         * @param {number} rx top-left corner of rectangle
         * @param {number} ry top-left corner of rectangle
         * @param {number} rw width of rectangle
         * @param {number} rh height of rectangle
         * @returns
         */
        rectangle: function (x1, y1, x2, y2, rx, ry, rw, rh) {
            if (dl.CollisionHelpers_Point.rectangle(x1, y1, rx, ry, rw, rh) ||
                dl.CollisionHelpers_Point.rectangle(x2, y2, rx, ry, rw, rh)) {
                return true;
            }

            return this.segment(x1, y1, x2, y2, rx, ry, rx + rw, ry) ||
                this.segment(x1, y1, x2, y2, rx + rw, ry, rx + rw, ry + rh) ||
                this.segment(x1, y1, x2, y2, rx, ry + rh, rx + rw, ry + rh) ||
                this.segment(x1, y1, x2, y2, rx, ry, rx, ry + rh);
        },

        dot: function (v1, v2) {
            return (v1.x * v2.x) + (v1.y * v2.y);
        },

        /**
         * segment-cicrle collision
         * @param {number} x1 x of first point segment
         * @param {number} y1 y of first point segment
         * @param {number} x2 x of second point segment
         * @param {number} y2 y of second point segment
         * @param {number} cx center x of cicrle
         * @param {number} cy center y of cicrle
         * @param {number} cr radius of cicrle
        */
        circle: function (x1, y1, x2, y2, cx, cy, cr) {
            var ac = {
                x: cx - x1,
                y: cy - y1
            };
            var ab = {
                x: x2 - x1,
                y: y2 - y1
            };
            var ab2 = this.dot(ab, ab);
            var acab = this.dot(ac, ab);
            var t = acab / ab2;
            t = (t < 0) ? 0 : t;
            t = (t > 1) ? 1 : t;
            var h = {
                x: (ab.x * t + x1) - cx,
                y: (ab.y * t + y1) - cy
            };
            var h2 = this.dot(h, h);
            return h2 <= Math.pow(cr, 2);
        }
    };
});
