#
# MarketJS Deployment System
# -----------------------------------------------------------------------
# Copyright (c) 2021 MarketJS Limited. Certain portions may come from 3rd parties and
# carry their own licensing terms and are referenced where applicable.
# -----------------------------------------------------------------------

#! /bin/bash
# Usage: bash build-compiled-backend.sh [options]
# Example: bash build-compiled-backend.sh
ARCHIVE_NAME=${PWD##*/}"-backend.zip"

compile_backend (){
    # https://esbuild.github.io/getting-started/#bundling-for-node
    esbuild index.js --bundle --platform=node > backend.js
    echo "Done!"
}

secure_regular (){
    echo ""
    echo "Securing by obscuring ..."
    echo ""
    javascript-obfuscator 'backend.js' -o 'backend.js' --config 'tools/javascript-obfuscator-dev.json'
    sed -i.bak 's/{data;}else{return;}/{}else{return;}/g' game.js
    rm *.bak

    echo ""
    echo "Securing Done!"
    echo ""
}

build_source_archive_multiplayer (){
	echo "Building archive ..."

	if [ ! -f ./$ARCHIVE_NAME ];
	then
	    echo "File not found!"
	else
	echo "File exist. Removing"
	rm ./$ARCHIVE_NAME
	fi

	zip -r ./$ARCHIVE_NAME ./backend.js ./package.json ./package-lock.json ./game-config.json -x "*.zip*" -x "*.git*" -x "*.psd*" -x "*.xcf*" -x "*.aif*" -x "*.tiff*" -x "*.au*" -x "*.txt*" -x "*.bat*" -x "*.htaccess" -x "*.DS_Store"

	echo "Done"
}


compile_backend
secure_regular
build_source_archive_multiplayer