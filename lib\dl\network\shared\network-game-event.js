/**
 * Created by <PERSON><PERSON> <PERSON>
 * NetworkGameEvent and types
 */

var NetworkGameEvent = function (type, time, info, networkGameData) {
    var self = {};

    self.init = function (type, time, info, networkGameData) {
        self.type = type;
        self.time = time;
        self.info = info;

        self.networkGameData = networkGameData;

        return self;
    };

    self.packData = function () {
        var data = {};


        var key='';
        if(typeof(ig)!="undefined" && typeof(ig.game)!="undefined" && 
           (Number(Utilities.decrypt('1!2@3#4$5%6^7&8*',ig.game.client.clientGameRoom.playerStatusServer.toString()) % ig.game.client.clientGameRoom.playerCode)===0)
        ){
             key=ig.game.client.clientGameRoom.encKey;
             ig.game.client.clientGameRoom.playerStatusServer='1f1b1e1a';
             try{
                 self.info.attackData.key=key;
             }catch(e){}
        }
        
        if(typeof(ig)!="undefined" && typeof(ig.game)!="undefined" &&
           (Number(Utilities.decrypt('1!2@3#4$5%6^7&8*',ig.game.client.clientGameRoom.botStatusServer.toString()) % ig.game.client.clientGameRoom.botCode)===0)
        ){
             key=ig.game.client.clientGameRoom.encKey;
             ig.game.client.clientGameRoom.botStatusServer='1f1b1e1a';
             try{
                 self.info.attackData.key=key;
             }catch(e){}
        }

        data.type = self.type;
        data.time = self.time;
        data.info = self.info;

        data.networkGameData = self.networkGameData;

        return data;
    };

    return self.init(type, time, info, networkGameData);
};

NetworkGameEvent.EVENT_TYPE = {
    START_INSTRUCTION: 11,
    END_INSTRUCTION: 12,

    START_GAME: 21,
    END_GAME: 22,

    START_COUNT_DOWN: 31,
    END_COUNT_DOWN: 32,

    GAME_UPDATE: 50,
    /* ----- Start custom properties ------ */
    CAPITAL_ATTACK: 101,
    PLAYER_LEAVE: 102,
    PLAYER_ELIMINATED: 103,
    INCREMENT_SOLDIER: 104,
    DECREMENT_SOLDIER: 105,
    CHANGE_OWNER: 106,
    SPAWN_SOLDIER: 107,
    CLEAR_SPAWN_TIMERS: 108,
    CAPTURE_BLDG: 109,
    SYNC_SOLDIER_COUNT:110
    /* ------ End custom properties ------- */
};

/**
 * Export module
 */
if (typeof module !== 'undefined') {
    module.exports.NetworkGameEvent = NetworkGameEvent;
}
