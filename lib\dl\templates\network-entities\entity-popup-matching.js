ig.module(
    'dl.templates.network-entities.entity-popup-matching'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.popup',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.animation-sheet',
    'dl.templates.mixins.text'
).defines(function () {
    'use strict';

    dl.EntityMatchingPopup = dl.Entity
        .extend(dl.MixinPopup)
        .extend(dl.MixinDocker)
        .extend({
            staticInstantiate: function () {
                this.parent();

                // callback when matching success
                this.onMatchingSuccess = null;

                return this;
            },
            postInit: function () {
                this.parent();

                this.content = this.spawnEntity(dl.EntityMatchingPopup_Content, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    }
                });
            }
        });

    dl.EntityMatchingPopup_Content = dl.Entity
        .extend(dl.MixinDocker)
        .extend(dl.MixinAnimationSheet)
        .extend(dl.MixinText)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this.image = dl.preload['popup-background-small'];

                this.timer = null;
                this.SPEED_MULTIPLY = 1;
                // DEBUG
                if (dl.debug && dl.debug.SKIP_TIME) {
                    this.SPEED_MULTIPLY = 0.1;
                }
                this.TWEEN_IN_TIME = 400 * this.SPEED_MULTIPLY;
                this.TWEEN_OUT_TIME = 400 * this.SPEED_MULTIPLY;
                this.NOTIFY_TIME = 1000 * this.SPEED_MULTIPLY;

                this.STATES = {
                    INIT: 0,
                    CHECK_CONNECTION: 1,
                    NO_CONNECTION: 2,
                    REQUEST_MATCHING: 3,
                    MATCHING: 4,
                    MATCHING_SUCCESS: 5,
                    MATCHING_FAIL: 6,
                    CLOSE: 7
                };
                this.currentState = -1;

                return this;
            },

            postInit: function () {
                this.parent();

                this.switchState(this.STATES.INIT);
                this.tweenIn(function () {
                    this.switchState(this.STATES.CHECK_CONNECTION);
                }.bind(this));
            },

            _initTextComponent: function () {
                this.c_TextComponent.updateProperties({
                    fontSize: 36,
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT')
                });
            },

            update: function () {
                this.parent();
                this.updateState();
            },

            checkConnection: function () {
                if (!dl.game) return false;
                if (!dl.game.networkClient) return false;

                return dl.game.networkClient.checkConnection();
            },

            matching: function () {
                var requestInfo = {};
                // room info
                requestInfo.roomPassword = ig.game.client.roomPassword;
                requestInfo.roomMaxPlayer = ig.game.client.roomMaxPlayer;
                requestInfo.roomArena = ig.game.client.roomArena;
                // player info
                requestInfo.playerName = ig.game.sessionData.playerName;
                requestInfo.playerAvatarId = ig.game.sessionData.playerAvatarId;
                requestInfo.playerSvasId = ig.game.svasData.uid;
                requestInfo.playerAlliance = ig.game.svasData.alliance;

                ig.game.client.requestRoom(
                    requestInfo,
                    this.onMatchingSuccess.bind(this),
                    this.onMatchingFail.bind(this)
                );
            },

            onMatchingSuccess: function () {
                this.switchState(this.STATES.MATCHING_SUCCESS);
            },

            onMatchingFail: function (data) {
                this.switchState(this.STATES.MATCHING_FAIL);
                switch (data) {
                    case SERVER_MESSAGE.REQUEST_REJECT_REASON.UNKNOWN:
                    default:
                        this.updateText(_STRINGS.NETWORK.MATCHING_POPUP_REJECT_REASON_UNKNOWN);
                        break;
                    case SERVER_MESSAGE.REQUEST_REJECT_REASON.SERVER_ERROR:
                        this.updateText(_STRINGS.NETWORK.MATCHING_POPUP_REJECT_REASON_SERVER_ERROR);
                        break;
                    case SERVER_MESSAGE.REQUEST_REJECT_REASON.INVALID_REQUEST:
                        this.updateText(_STRINGS.NETWORK.MATCHING_POPUP_REJECT_REASON_INVALID_REQUEST);
                        break;
                    case SERVER_MESSAGE.REQUEST_REJECT_REASON.VERSION_MISMATCH:
                        this.updateText(_STRINGS.NETWORK.MATCHING_POPUP_REJECT_REASON_VERSION_MISMATCH);
                        break;
                    case SERVER_MESSAGE.REQUEST_REJECT_REASON.CANNOT_FIND_ROOM:
                        this.updateText(_STRINGS.NETWORK.MATCHING_POPUP_REJECT_REASON_CANNOT_FIND_ROOM);
                        break;
                }
            },

            switchState: function (newState) {
                this.currentState = newState;
                switch (newState) {
                    case this.STATES.INIT:
                        this.switchState(this.STATES.CHECK_CONNECTION);
                        break;

                    case this.STATES.CHECK_CONNECTION:
                        if (this.checkConnection()) {
                            this.switchState(this.STATES.REQUEST_MATCHING);
                        } else {
                            this.switchState(this.STATES.NO_CONNECTION);
                        }
                        break;

                    case this.STATES.NO_CONNECTION:
                        if (this.timer) this.timer.kill();
                        this.timer = new dl.GameTimer(this.NOTIFY_TIME);
                        this.updateText(_STRINGS.NETWORK.MATCHING_POPUP_NO_CONNECTION);
                        break;

                    case this.STATES.REQUEST_MATCHING:
                        if (this.timer) this.timer.kill();
                        this.timer = new dl.GameTimer(this.NOTIFY_TIME);
                        this.updateText(_STRINGS.NETWORK.MATCHING_POPUP_MATCHING);
                        break;

                    case this.STATES.MATCHING:
                        this.matching();
                        break;

                    case this.STATES.MATCHING_SUCCESS:
                        console.log('Request Game Success');
                        if (this.timer) this.timer.kill();
                        this.timer = new dl.GameTimer(this.NOTIFY_TIME);
                        this.updateText(_STRINGS.NETWORK.MATCHING_POPUP_MATCHING_SUCCESS);
                        break;

                    case this.STATES.MATCHING_FAIL:
                        console.log('Request Game Failed');
                        if (this.timer) this.timer.kill();
                        this.timer = new dl.GameTimer(this.NOTIFY_TIME);
                        break;

                    case this.STATES.CLOSE:
                        this.tweenOut(function () {
                            this.kill();
                        }.bind(this.parentInstance));
                        break;
                }
            },

            updateState: function () {
                switch (this.currentState) {
                    case this.STATES.NO_CONNECTION:
                        if (!this.timer) return;

                        if (this.timer.delta() >= 0) {
                            this.timer = null;
                            this.switchState(this.STATES.CLOSE);
                        }
                        break;

                    case this.STATES.REQUEST_MATCHING:
                        if (!this.timer) return;

                        if (this.timer.delta() >= 0) {
                            this.timer = null;

                            this.switchState(this.STATES.MATCHING);
                        }
                        break;

                    case this.STATES.MATCHING_SUCCESS:
                        if (!this.timer) return;

                        if (this.timer.delta() >= 0) {
                            this.timer = null;

                            this.parentInstance.onPopupClose = this.parentInstance.onMatchingSuccess;
                            this.switchState(this.STATES.CLOSE);
                        }
                        break;

                    case this.STATES.MATCHING_FAIL:
                        if (!this.timer) return;

                        if (this.timer.delta() >= 0) {
                            this.timer = null;
                            this.switchState(this.STATES.CLOSE);
                        }
                        break;
                }
            },

            tweenIn: function (callback) {
                dl.TweenTemplate.moveInTop(this, this.TWEEN_IN_TIME, callback);
            },

            tweenOut: function (callback) {
                dl.TweenTemplate.moveOutTop(this, this.TWEEN_OUT_TIME, callback);
            }
        });
});
