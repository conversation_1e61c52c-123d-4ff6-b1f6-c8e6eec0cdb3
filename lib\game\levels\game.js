ig.module(
    'game.levels.game'
).requires(
    'dl.network.mixins.game',
    'dl.templates.entities.entity-level',
    'dl.templates.entities.backgrounds.entity-color-background',
    'dl.templates.entities.buttons.entity-button-image',
    'dl.templates.network-entities.entity-game-count-down-timer-text',
    'dl.templates.network-entities.entity-popup-game-result',
    'game.dl-entities.game-area',
    'game.dl-entities.game-players-panel',
    'game.dl-entities.emoji-bar'
).defines(function () {
    LevelGame = {
        entities: [
            { type: 'EntityGameController', x: 0, y: 0 }
        ],
        layer: [],
        useCustomLib: true
    };

    EntityGameController = dl.EntityLevel
        .extend(MixinGame)
        .extend({
            delay:0,
            upgradeName:['Military Monday','Tactical Tuesday','War Machine Wednesday','Trouble Thursday','Full Attack Friday','Slugfest Saturday','Sabotage Sunday'],
            postInit: function () {
                this.parent();

                this.initBackground();
                this.initGameArea();
                this.initPlayersPanel();
                this.initCountDownTimer();
                this.initMainButton();

                ig.game.game = this;
                this.forceNetworkGameUpdate();
                // this.tweenIn();
                // this.initServerStatus();
            },

            forceNetworkGameUpdate: function () {
                if (!ig.game.client) return;
                if (!ig.game.client.clientGameRoom) return;
                if (!ig.game.client.clientGameRoom.networkGame) return;
                if (!ig.game.client.clientGameRoom.networkGame.networkGameData) return;

                this.updateData(ig.game.client.clientGameRoom.networkGame.networkGameData);
            },

            initBackground: function () {
                this.background = this.spawnEntity(dl.EntityColorBackground, {});
            },

            initServerStatus: function () {

                this.delay +=1;
                if(this.delay>=50){
                    this.delay = 0;
                    if(this.serverProp) this.serverProp.kill();

                    if(ig.ua.mobile && !ig.ua.iPad){
                        var bt1size={x:700,y:180};
                        var bt1fontsize=60;
                    }else{
                        var bt1size={x:580,y:121};
                        var bt1fontsize=40;
                    }            

                    var serverText = _STRINGS.NETWORK.SERVER_STATUS_PING + ': ' + ig.game.client.networkClient.latencyAverage.toString() + '<br>' +
                                        _STRINGS.NETWORK.SERVER_STATUS_ONLINE + ': ' +ig.game.client.serverUserCount.toString() + '<br>' +
                                        window.buildversion;


                    this.serverProp = this.spawnEntity(dl.EntityText, {
                        c_DockerComponent: {
                            dockerObject: dl.game.camera,
                            dockerPercent: { x: 0, y: 1 },
                            dockerOffset: { x: 20, y: -20 }
                        },
                        c_TextComponent: {
                            textAlign:'left',
                            fontSize: bt1fontsize,
                        },
                        anchor: { x: 0, y: 1 },
                        text:serverText
                    });
                }            

                // if(ig.ua.mobile){
                //     var bt1fontsize=60;
                // }else{
                //     var bt1fontsize=40;
                // }            
                // this.serverStatus = this.spawnEntity(dl.EntityServerStatus, {
                //     c_DockerComponent: {
                //         dockerObject: dl.game.camera,
                //         dockerPercent: { x: 0, y: 1 },
                //         dockerOffset: { x: 5, y: -5 }
                //     },
                //     c_TextComponent: {
                //         fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                //         fontSize: bt1fontsize,
                //         fontFamily: dl.configs.getConfig('FONT', 'bold').name
                //     },
                //     anchor: { x: 0, y: 1 }
                // });
            },

            initGameArea: function () {
                this.gameArea = this.spawnEntity(dl.EntityGameArea, {
                    c_DockerComponent: {
                        dockerObject: dl.game.camera,
                        dockerPercent: { x: 0.5, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    }
                });
            },

            initPlayersPanel: function () {
                var xx=ig.system.width;
                var yy=ig.system.height;

                if(ig.ua.mobile && !ig.ua.iPad){
                    if(xx<yy){
                        var panelSize={x:ig.system.width-20,y:600};
                    }else{
                        var panelSize={x:ig.system.width*0.9,y:250};
                    }
                }else{
                    var panelSize={x:ig.system.width*0.9,y:200};
                }

                if(ig.ua.mobile){
                    this.playersPanel = this.spawnEntity(dl.EntityGamePlayersPanel, {
                        _useParentScale: true,
                        c_DockerComponent: {
                            dockerObject: dl.game.camera,
                            dockerPercent: { x: 0.5, y: 0.005 },
                            dockerOffset: { x: 0, y: 0 }
                        },
                        c_AnimationSheetComponent: {
                            _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                            _size: panelSize
                        },
                        anchor: { x: 0.5, y: 0 },
                        image: dl.preload['game-player-panel-mobile']
                    });

                }else{
                    this.playersPanel = this.spawnEntity(dl.EntityGamePlayersPanel, {
                        _useParentScale: true,
                        c_DockerComponent: {
                            dockerObject: dl.game.camera,
                            dockerPercent: { x: 0.5, y: 0.005 },
                            dockerOffset: { x: 0, y: 0 }
                        },
                        c_AnimationSheetComponent: {
                            _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                            _size: panelSize
                        },
                        anchor: { x: 0.5, y: 0 },
                        image: dl.preload['game-player-panel']
                    });

                }



                if(ig.ua.mobile && !ig.ua.iPad){
                    var fontSize=60;
                    if(xx<yy){
                        var dockerPercent={x:0,y:1};
                        var anchorPos={x:0,y:0};
                    }else{
                        var dockerPercent={x:0.5,y:1};
                        var anchorPos={x:0.5,y:0};
                    }
                }else{
                    if(xx<yy){
                        var dockerPercent={x:0,y:1};
                        var anchorPos={x:0,y:0};
                    }else{
                        var dockerPercent={x:0.5,y:1};
                        var anchorPos={x:0.5,y:0};

                    }
                    var fontSize=50;
                }



                this.mapname = this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.playersPanel,
                        dockerPercent: dockerPercent,
                        dockerOffset: { x: 0, y: 140 }
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                        fontSize: fontSize
                    },
                    anchor:anchorPos,
                    text:ig.game.mapname
                });


                if(ig.game.showFPS){            
                    this.fps = this.spawnEntity(dl.EntityText, {
                        _useParentScale: true,
                        c_DockerComponent: {
                            dockerObject: dl.game.camera,
                            dockerPercent: { x: 0, y: 0.3 },
                            dockerOffset: { x: 50, y: 0 }
                        },
                        c_TextComponent: {
                            fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                            fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                            fontSize: 40
                        },
                        anchor:{x:0,y:0},
                        text:ig.system.fps.toFixed(0)
                    });
                }

                this.utcbg = this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.playersPanel,
                        dockerPercent: { x: 1, y: 1 },
                        dockerOffset: { x:  0, y: 80 }
                    },
                    image: dl.preload['utcbox'+ig.game.today],
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:600,y:200}
                    },
                    anchor: { x: 1, y: 0 }
                });

                this.utcEvent = this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.utcbg,
                        dockerPercent: { x: 0.5, y: 1 },
                        dockerOffset: { x: 0, y: -20 }
                    },
                    c_TextComponent: {
                        textAlign:'center',
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                        fontSize: 36,
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'WHITE'),
                    },
                    anchor: { x: 0.5, y: 1 },
                    text:'',
                });

                this.utcDate = this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.utcbg,
                        dockerPercent: { x: 1, y: 0 },
                        dockerOffset: { x: 0, y: -15 }
                    },
                    c_TextComponent: {
                        textAlign:'center',
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                        fontSize: 46,
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                    },
                    anchor: { x: 1, y: 1 },
                    text:'',
                });


                // if(!ig.ua.mobile){               
                    if(ig.ua.mobile && !ig.ua.iPad){
                        if(yy>xx){
                            var dockerPos={x:0.5,y:0.92};
                        }else{
                            var dockerPos={x:0.97,y:0.5};
                        }

                    }else{
                        var dockerPos={x:0.97,y:0.5};
                    }

                    this.emojiBar = this.spawnEntity(dl.EntityNetworkEmojiBar, {
                        _useParentScale: true,
                        c_DockerComponent: {
                            dockerObject: dl.game.camera,
                            dockerPercent: dockerPos,
                            dockerOffset: { x: 0, y: 0 }
                        }
                    });
                // }
            },
            getAllianceExist:function(){
                for(var i=0;i<ig.game.matchingAllianceData.length;i++){
                    if(ig.game.matchingAllianceData[i].allianceIcon!==null){
                        return true;
                    }
                }
                return false;
            },
            update:function(){
                this.parent();

                var xx=ig.system.width;
                var yy=ig.system.height;

                if(ig.ua.mobile && !ig.ua.iPad){
                    if(xx<yy){
                        var panelSize={x:ig.system.width-26,y:600};
                    }else{
                        var panelSize={x:ig.system.width*0.9,y:250};

                    }
                }else{
                    if(this.getAllianceExist()==true){ 
                        var panelSize={x:ig.system.width*0.9,y:230};
                    }else{
                        var panelSize={x:ig.system.width*0.9,y:150};                        
                    }
                }
                this.playersPanel.c_AnimationSheetComponent._size=panelSize;
                this.playersPanel.updatePos();

                var nameIndex=ig.game.today-1;
                if(nameIndex<0) nameIndex=6;
                if(ig.game.dayText2==''){
                    var txt='';
                }else{
                    var txt=this.upgradeName[nameIndex];
                }
                this.utcEvent.updateText(txt);
                this.utcDate.updateText(ig.game.utcTextSync);

                if(ig.game.showFPS){            
                    this.fps.updateText(ig.system.fps.toFixed(0)+' fps');
                }

                if(ig.ua.mobile && !ig.ua.iPad){
                    var fontSize=60;
                    if(xx<yy){
                        var dockerPercent={x:0,y:1};
                        var anchorPos={x:0,y:0};
                    }else{
                        var dockerPercent={x:0.5,y:1};
                        var anchorPos={x:0.5,y:0};
                    }
                }else{
                    if(xx<yy){
                        var dockerPercent={x:0,y:1};
                        var anchorPos={x:0,y:0};
                    }else{
                        var dockerPercent={x:0.5,y:1};
                        var anchorPos={x:0.5,y:0};

                    }
                    var fontSize=50;
                }

                if(this.mapname){
                    this.mapname.updateText(ig.game.mapname);
                    this.mapname.anchor=anchorPos;
                    this.mapname.c_TextComponent.fontSize=fontSize;
                    this.mapname.c_TextComponent.updateProperties();
                    this.mapname.c_DockerComponent.dockerPercent=dockerPercent;
                    this.mapname.updatePos();
                }

            },
            initCountDownTimer: function () {
                this.countDownTimer = this.spawnEntity(dl.EntityGameCountDownTimerText, {
                    c_DockerComponent: {
                        dockerObject: dl.game.camera,
                        dockerPercent: { x: 0.5, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    }
                });
            },

            initMainButton: function () {
                var xx=window.innerWidth;
                var yy=window.innerHeight;

                if(ig.ua.mobile && !ig.ua.iPad){
                    if(yy>xx){
                        var btsize={x:180,y:180};
                    }else{
                        var btsize={x:180,y:180};
                    }

                }else{
                    var btsize={x:100,y:100};
                }

                this.btnHome = this.spawnEntity(dl.EntityButtonImage, {
                    c_DockerComponent: {
                        dockerObject: dl.game.camera,
                        dockerPercent: { x: 1, y: 1 },
                        dockerOffset: { x: -15, y: -15 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: btsize
                    },
                    anchor: { x: 1, y: 1 },
                    image: dl.preload['button-home'],
                    onButtonClicked: function () {
                        this.spawnHomePopup();
                    }.bind(this)
                });
            },

            tweenIn: function (callback) {
                var time = dl.configs.getConfig('GAME_LEVEL_TWEEN_TIME', 'IN');
                this.playersPanel.tweenIn(time);
            },

            tweenOut: function (callback) {
                var time = dl.configs.getConfig('GAME_LEVEL_TWEEN_TIME', 'OUT');
                this.playersPanel.tweenOut(time);
            },
            // updateDataCount:0,
            updateData: function (data) {
                // this.updateDataCount +=1;
                // if((this.updateDataCount % 25) != 0) return
                // if(this.updateDataCount>=50) this.updateDataCount=0;
                // console.log(this.updateDataCount);

                this.gameArea.updateData(data);
                this.playersPanel.updateData(data);


                if(!ig.ua.mobile){
                    var xx=window.innerWidth;
                    var yy=window.innerHeight;

                    if(ig.ua.mobile && !ig.ua.iPad){
                        if(yy>xx){
                            var dockerPos={x:0.5,y:0.92};
                        }else{
                            var dockerPos={x:0.97,y:0.5};
                        }

                    }else{
                        var dockerPos={x:0.97,y:0.5};
                    }
                    if(this.emojiBar){
                        this.emojiBar.c_DockerComponent.dockerPercent=dockerPos;
                        this.emojiBar.updatePos();
                        this.emojiBar.updateButtonPos()
                    }
                }
            }
        }).extend(dl.GameDebugMixin);
});
