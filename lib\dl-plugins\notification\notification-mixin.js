// Create Namespace
dl.Notification = {};

ig.module(
    'dl-plugins.notification.notification-mixin'
).requires(
    'dl-plugins.notification.notification-manager'
).defines(function () {
    'use strict';

    dl.Notification.Mixin = {
        staticInstantiate: function () {
            this.parent();

            this.notificationManager = new dl.Notification.Manager(this);
        },

        update: function () {
            this.notificationManager.update();

            this.parent();
        }
    };
});
