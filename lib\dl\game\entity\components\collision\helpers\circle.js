ig.module(
    'dl.game.entity.components.collision.helpers.circle'
).requires(
    'dl.game.entity.components.collision.helpers.point',
    'dl.game.entity.components.collision.helpers.polygon'
).defines(function () {
    'use strict';

    dl.CollisionHelpers_Circle = {
        /**
         *
         * @param {number} x center x of circle
         * @param {number} y center y of circle
         * @param {number} r radius of circle
         * @param {number} px x of point
         * @param {number} py y of point
         * @returns {boolean}
         */
        point: function (x, y, r, px, py) {
            return dl.CollisionHelpers_Point.circle(px, py, x, y, r);
        },

        /**
         * circle-rectangle collision
         * @param {number} cx center x of circle
         * @param {number} cy center y of circle
         * @param {number} cr radius of circle
         * @param {number} rx top-left corner of rectangle
         * @param {number} ry top-left corner of rectangle
         * @param {number} rw width of rectangle
         * @param {number} rh height of rectangle
         * @return {boolean}
         */
        rectangle: function (cx, cy, cr, rx, ry, rw, rh) {
            var rCenterX = rx + rw * 0.5;
            var rCenterY = ry + rh * 0.5;
            var rHalfWidth = rw * 0.5;
            var rHalfHeight = rh * 0.5;

            var circleDistanceX = Math.abs(cx - rCenterX);
            var circleDistanceY = Math.abs(cy - rCenterY);

            if (circleDistanceX > (rHalfWidth + cr)) { return false; }
            if (circleDistanceY > (rHalfHeight + cr)) { return false; }

            if (circleDistanceX <= rHalfWidth) { return true; }
            if (circleDistanceY <= rHalfHeight) { return true; }

            var cornerDistance_sq =
                Math.pow(circleDistanceX - rHalfWidth, 2) +
                Math.pow(circleDistanceY - rHalfHeight, 2);

            return cornerDistance_sq <= Math.pow(cr, 2);
        },

        /**
         * circle-circle collision
         * @param {number} c1x center x of circle 1
         * @param {number} c1y center y of circle 1
         * @param {number} c1r radius of circle 1
         * @param {number} c2x center x of circle 2
         * @param {number} c2y center y of circle 2
         * @param {number} c2r radius of circle 2
         * @returns {boolean}
         */
        circle: function (c1x, c1y, c1r, c2x, c2y, c2r) {
            var dx = c2x - c1x;
            var dy = c2y - c1y;
            var ar = c1r + c2r;
            return dx * dx + dy * dy <= ar * ar;
        },

        /**
         * circle-circle collision
         * @param {number} c1x center x of circle 1
         * @param {number} c1y center y of circle 1
         * @param {number} c1r radius of circle 1
         * @param {number} c2x center x of circle 2
         * @param {number} c2y center y of circle 2
         * @param {number} c2r radius of circle 2
         * @returns {boolean}
         */
        circlePosition: function (c1x, c1y, c1r, c2x, c2y, c2r) {
            var distanceSquare = Math.pow(c1x - c2x, 2) + Math.pow(c1y - c2y, 2);
            var totalRadianSquare = Math.pow(c1r + c2r, 2);

            if (distanceSquare == totalRadianSquare) {
                // touches
                return true;
            } else {
                if (distanceSquare > totalRadianSquare) {
                    // not touches
                    return false;
                } else {
                    // intersects
                    return true;
                }
            }
        },

        /**
         * circle-polygon collision
         * @param {number} cx center x of circle
         * @param {number} cy center y of circle
         * @param {number} cr radius of circle
         * @param {array} polygon array of point({x:0,y:0}) in polygon
         * @returns {boolean}
         */
        polygon: function (cx, cy, cr, polygon) {
            return dl.CollisionHelpers_Polygon.circle(polygon, cx, cy, cr);
        }
    };
});
