ig.module(
    'dl.templates.mixins.animation-sheet'
).requires(
    'dl.game.entity.components.draw.animation-sheet'
).defines(function () {
    'use strict';

    dl.MixinAnimationSheet = {
        staticInstantiate: function () {
            this.parent();

            this.image = null;
            this._useAnimationSheetAsSize = false;

            return this;
        },

        postInit: function () {
            this.parent();

            this.updateImage(this.image);
        },

        initComponents: function () {
            this.parent();

            this.c_AnimationSheetComponent = this.addComponent(dl.AnimationSheetComponent);
            if (this._useAnimationSheetAsSize) {
                this._useScale = false;
                this.c_AnimationSheetComponent.onEvent('sizeChanged', this.setSize.bind(this));
            }
            this._initAnimationSheetComponent();
        },

        _initAnimationSheetComponent: function () {
            this.c_AnimationSheetComponent.updateProperties({
                _drawType: dl.AnimationSheetComponent.DRAW_SIZE.RAW,

                _animationSheetImage: null,
                _animationSheetRow: 1,
                _animationSheetCol: 1,

                _size: { x: 16, y: 16 },
                _docker: { x: 0.5, y: 0.5 },
                _anchor: { x: 0.5, y: 0.5 },
                _offset: { x: 0, y: 0 }
            });
        },

        updateImage: function (image) {
            if (!image) return;
            if (this.c_AnimationSheetComponent &&
                this.c_AnimationSheetComponent._animationSheetImage &&
                this.c_AnimationSheetComponent._animationSheetImage.path == image.path) return;

            this.c_AnimationSheetComponent.updateProperties({
                _animationSheetImage: image,
                _animationSheetRow: 1,
                _animationSheetCol: 1,
                _setupDefaultAnimation: function () {
                    this.addAnimation('idle', 0, [0], true);
                    this.setAnimation('idle');
                }
            });
        }
    };
});
