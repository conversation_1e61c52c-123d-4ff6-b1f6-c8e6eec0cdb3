ig.module(
    'game.dl-entities.game-area'
).requires(
    'dl.game.entity',
    'game.dl-entities.game-map',
    'game.dl-entities.game-color-manager',
    'game.dl-entities.game-manager'
).defines(function () {
    dl.EntityGameArea = dl.Entity.extend({
        postInit: function () {
            ig.game.managers = {};
            this.initColorManager();
            this.initMap();
        },

        initMap: function () {
            window.gameMap = this.gameMap = this.spawnEntity(dl.EntityGameMap, {
                mapData: ig.game.client.clientGameRoom.mapData
            });
        },

        initColorManager: function () {
            window.colorManager = ig.game.managers.color = this.colorManager = this.spawnEntity(dl.EntityGameColorManager, {

            });
        },

        initPlayers: function () {
            this.totalPlayer = ig.game.client.roomMaxPlayer;
            this.players = [];

            for (var i = 0; i < this.totalPlayer; i++) {
                var player = this.spawnEntity(dl.EntityGamePlayer, {
                    gridInstance: this.gameGrid
                });

                this.players.push(player);
            }
        },

        updateData: function (data) {
            this.gameMap.updateData(data.mapData);
            if (ig.game.managers.game) {
                ig.game.managers.game.updateData(data);
            }
        }
    });
});
