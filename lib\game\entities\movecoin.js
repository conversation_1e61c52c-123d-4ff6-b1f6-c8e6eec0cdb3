ig.module('game.entities.movecoin')
.requires(
	'impact.entity'
)
.defines(function () {
	EntityMoveCoin = ig.Entity.extend ({
		zIndex: 1100,
		coin:new ig.Image('media/graphics/sprites/coin.png'),
		init: function (x, y, settings){
			this.parent (x, y, settings);
			// ig.system.fps=dl.FPS_INTERVAL;
			
			this.posx=this.pos.x;
			this.posy=this.pos.y;
			this.moveToTarget();
			ig.game.playCoin();
		},
		moveToTarget:function(){
			this.tween({posx: (ig.system.width/2)+this.target.x-50}, 0.8, {
				easing: ig.Tween.Easing.Linear.EaseNone,
			}).start();			
			this.tween({posy:(ig.system.height/2)+this.target.y-50}, 0.8, {
				easing: ig.Tween.Easing.Quadratic.EaseOut,
				onComplete: function (){
					ig.game.playCoin();
					this.control.addPrizeText(this.addVal);
					this.kill ();
				}.bind (this)
			}).start();			
			//ig.Tween.Easing.Linear.EaseNone
		},
		update: function (){
			this.parent ();
			this.pos.x=this.posx;
			this.pos.y=this.posy;			
		},
		
		draw: function (){
			this.parent ();
			var ctx = ig.system.context;
            this.coin.drawImage(
                0,
                0,
                this.coin.width,  
                this.coin.height,
                this.pos.x,
                this.pos.y,
                100,
                100
            );         
		},
		
	});

	EntityMoveCoinResult = ig.Entity.extend ({
		zIndex: 1100,
		coin:new ig.Image('media/graphics/sprites/coin.png'),
		
		init: function (x, y, settings){
			this.parent (x, y, settings);
			this.posx=this.pos.x;
			this.posy=this.pos.y;
			this.control.deductCoin(this.deductObj,this.addVal)
			ig.game.playCoin();
			this.moveToTarget();
		},
		moveToTarget:function(){
			this.tween({posx: (ig.system.width/2)+this.target.x-40}, 0.8, {
				easing: ig.Tween.Easing.Circular.EaseOut,
			}).start();			
			this.tween({posy:(ig.system.height/2)+this.target.y-40}, 0.8, {
				easing: ig.Tween.Easing.Linear.EaseNone,
				onComplete: function (){
					ig.game.playCoin();
					this.control.addCoin(this.addObj,this.addVal);
					this.kill ();
				}.bind (this)
			}).start();			

			//ig.Tween.Easing.Linear.EaseNone
		},
		update: function (){
			this.parent ();
			this.pos.x=this.posx;
			this.pos.y=this.posy;			
		},
		
		draw: function (){
			this.parent ();
			var ctx = ig.system.context;

            this.coin.drawImage(
                0,
                0,
                this.coin.width,  
                this.coin.height,
                this.pos.x,
                this.pos.y,
                80,
                80
            );                

		},
		
	});

});