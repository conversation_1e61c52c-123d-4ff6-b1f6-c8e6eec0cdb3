
# BUILD
sh push.sh -b

# PUSH TO S3

optstring="am"
# No arguments given
if [ $# -eq 0 ]
then
    python boto-s3-upload.py -l en -n
    echo "----- PUSHED NEW -----"
fi

# Execute Prioritized Options
while getopts "$optstring" opt
do
   case $opt in
    a)
        python boto-s3-upload.py -l en -a
        echo "----- PUSHED ALL -----"
        ;;
    m)
        python boto-s3-upload.py -l en -m
        echo "----- PUSHED MEDIA -----"
        ;;
    :)
        echo "Error: -${OPTARG} requires an argument."
        exit 1
        ;;
    \?)
        echo "Invalid option: -$OPTARG" >&2
        exit 1
        ;;
   esac
done