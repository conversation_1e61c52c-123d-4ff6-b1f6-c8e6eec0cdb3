ig.module(
    'game.levels.matching'
).requires(
    'dl.templates.entities.entity-level',
    'dl.network.mixins.matching',
    'dl.templates.entities.backgrounds.entity-color-background',
    'dl.templates.entities.entity-text',
    'dl.templates.network-entities.entity-matching-players',
    'dl.templates.network-entities.entity-button-auto-start'
).defines(function () {
    LevelMatching = {
        entities: [
            { type: 'EntityMatchingController', x: 0, y: 0 }
        ],
        layer: [],
        useCustomLib: true
    };

    EntityMatchingController = dl.EntityLevel
        .extend({
            postInit: function () {
                this.parent();

                this.initEntities();

                // this.tweenIn();
            },

            initEntities: function () {
                this.initBackgroundAndTitle();
                this.initPlayerInfo();
                this.initMainButtons();
                this.initSubButtons();
                window.matching = this;
                ig.game.disableBtStatus = false;
            },

            initBackgroundAndTitle: function () {
                this.background = this.spawnEntity(dl.EntityColorBackground, {});

                this.title = this.spawnEntity(dl.EntityText, {
                    orientationData: {
                        portrait: {
                            _orientationScale: { x: 0.75, y: 0.75 }
                        },
                        landscape: {
                            _orientationScale: { x: 1, y: 1 }
                        }
                    },
                    c_DockerComponent: {
                        dockerObject: dl.game.camera,
                        dockerPercent: { x: 0.5, y: 0.1 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                        fontSize: 100,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    text: _STRINGS.NETWORK.MATCHING_TITLE_WAIT
                });
            },

            initMainButtons: function () {

                var xx=window.innerWidth;
                var yy=window.innerHeight;

                if(ig.ua.mobile && !ig.ua.iPad){
                    if(xx<yy){ //portrait
                        var offset = 360;
                        var bt1size={x:600,y:180};
                        var bt1fontsize=70;
                        var posY=0.75;
                    }else{  //landscape              
                        var offset = 400;
                        var bt1size={x:700,y:180};
                        var bt1fontsize=70;
                        var posY=0.80;
                    }

                }else{

                    if(xx<yy){ //portrait
                        var offset = 400;
                        var bt1size={x:580,y:121};
                        var bt1fontsize=40;
                        var posY=0.85;
                    }else{  //landscape              
                        var offset = 400;
                        var bt1size={x:580,y:121};
                        var bt1fontsize=40;
                        var posY=0.80;
                    }

                }        

                this.btnReady = this.spawnEntity(dl.EntityButtonImageText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: dl.game.camera,
                        dockerPercent: { x: 0.5, y: posY },
                        dockerOffset: { x: -offset, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: bt1size
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                        fontSize: bt1fontsize,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    image: dl.preload['button-green'],
                    text: _STRINGS.NETWORK.MATCHING_BUTTON_START,
                    onButtonClicked: this.onPlayerReady.bind(this)
                });

                this.btnExit = this.spawnEntity(dl.EntityButtonImageText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: dl.game.camera,
                        dockerPercent: { x: 0.5, y: posY },
                        dockerOffset: { x: offset, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: bt1size
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                        fontSize: bt1fontsize,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    image: dl.preload['button-gray'],
                    text: _STRINGS.NETWORK.MATCHING_BUTTON_EXIT,
                    onButtonClicked: function () {
                        this.onPlayerExit();
                            ig.game.client.roomPassword="";
                            ig.game.director.jumpTo(LevelTitle);
                        // this.tweenOut(function () {
                        //     ig.game.director.jumpTo(LevelTitle);
                        // }.bind(this), true);
                    }.bind(this)
                });

                if(ig.game.showFPS){            
                    this.fps = this.spawnEntity(dl.EntityText, {
                        _useParentScale: true,
                        c_DockerComponent: {
                            dockerObject: dl.game.camera,
                            dockerPercent: { x: 0, y: 0.3 },
                            dockerOffset: { x: 50, y: 0 }
                        },
                        c_TextComponent: {
                            fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                            fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                            fontSize: 40
                        },
                        anchor:{x:0,y:0},
                        text:ig.system.fps.toFixed(0)
                    });
                }
                
            },

            initSubButtons: function () {
                this.btnAutoStart = this.spawnEntity(dl.EntityButtonAutoStart, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: dl.game.camera,
                        dockerPercent: { x: 0, y: 1 },
                        dockerOffset: { x: 40, y: -40 }
                    },
                    anchor: { x: 0, y: 1 }
                });
            },

            update:function(){
                this.parent(); 

                if(ig.game.showFPS){            
                    this.fps.updateText(ig.system.fps.toFixed(0)+' fps');
                }

                var xx=window.innerWidth;
                var yy=window.innerHeight;

                if(ig.ua.mobile && !ig.ua.iPad){
                    if(xx<yy){ //portrait
                        var offset = 360;
                        var bt1size={x:600,y:180};
                        var bt1fontsize=70;
                        var posY=0.75;
                    }else{  //landscape              
                        var offset = 400;
                        var bt1size={x:700,y:180};
                        var bt1fontsize=70;
                        var posY=0.80;
                    }

                }else{

                    if(xx<yy){ //portrait
                        var offset = 400;
                        var bt1size={x:580,y:121};
                        var bt1fontsize=40;
                        var posY=0.85;
                    }else{  //landscape              
                        var offset = 340;
                        var bt1size={x:580,y:121};
                        var bt1fontsize=40;
                        var posY=0.76;
                    }

                }        

                this.btnReady.c_DockerComponent.dockerPercent={ x: 0.5, y: posY };
                this.btnReady.c_DockerComponent.dockerOffset={ x: -offset, y: 0 };
                this.btnReady.updatePos();

                this.btnExit.c_DockerComponent.dockerPercent={ x: 0.5, y: posY };
                this.btnExit.c_DockerComponent.dockerOffset={ x: offset, y: 0 };
                this.btnExit.updatePos();

            },

            initPlayerInfo: function () {
                if(ig.ua.mobile && !ig.ua.iPad){
                    this.matchingPlayers = this.spawnEntity(dl.EntityMatchingPlayers, {
                        _useParentScale: true,
                        orientationData: {
                            landscape: {
                                c_DockerComponent: {
                                    dockerObject: dl.game.camera,
                                    dockerPercent: { x: 0.5, y: 0.5 },
                                    dockerOffset: { x: 0, y: 0 }
                                }
                            },
                            portrait: {
                                c_DockerComponent: {
                                    dockerObject: dl.game.camera,
                                    dockerPercent: { x: 0.5, y: 0.45 },
                                    dockerOffset: { x: 0, y: 0 }
                                }
                            }
                        },
                    });
                }else{
                    this.matchingPlayers = this.spawnEntity(dl.EntityMatchingPlayers, {
                        _useParentScale: true,
                        orientationData: {
                            landscape: {
                                c_DockerComponent: {
                                    dockerObject: dl.game.camera,
                                    dockerPercent: { x: 0.5, y: 0.5 },
                                    dockerOffset: { x: 0, y: 0 }
                                }
                            },
                            portrait: {
                                c_DockerComponent: {
                                    dockerObject: dl.game.camera,
                                    dockerPercent: { x: 0.5, y: 0.57 },
                                    dockerOffset: { x: 0, y: 0 }
                                }
                            }
                        },
                    });                    
                }
            },
            updateButtonsData: function (data) {
                if (this.roomStarted) return;
                if (!data) return;

                var isHost = ig.game.client.clientGameRoom.isHostPlayerId(data.playerId);
                if (isHost) {
                    this.btnReady.updateText(_STRINGS.NETWORK.MATCHING_BUTTON_START);
                    if (this.checkAllReady()) {
                        this.btnReady.setEnable(true);
                    } else {
                        this.btnReady.setEnable(false);
                    }

                    this.btnAutoStart.setVisible(true);
                    this.btnAutoStart.setEnable(true);
                } else {
                    if (!data.playerReady) {
                        this.btnReady.updateText(_STRINGS.NETWORK.MATCHING_BUTTON_READY);
                    } else {
                        this.btnReady.updateText(_STRINGS.NETWORK.MATCHING_BUTTON_NOT_READY);
                    }

                    this.btnAutoStart.setVisible(false);
                    this.btnAutoStart.setEnable(false);
                }
            },

            onRoomUpdate: function (data) {
                if (this.roomStarted) return;
                this.matchingPlayers.updateData(data);
            },

            onRoomStart: function (data) {
                this.roomStarted = true;
                this.disableAll();
                ig.game.director.jumpTo(LevelPrizePot);
                
                // dl.TweenTemplate.scaleOutIn(this.title, dl.configs.getConfig('TEXT_CHANGE_TWEEN_TIME'), function () {
                //     this.title.updateText(_STRINGS.NETWORK.MATCHING_TITLE_START);
                // }.bind(this));
            },

            onRoomStarted: function (data) {
                // console.log('run pot');
                // ig.game.director.jumpTo(LevelPrizePot);
                // ig.game.director.jumpTo(LevelInstruction);
                // this.tweenOut(function () {
                //     ig.game.director.jumpTo(LevelInstruction);
                // }.bind(this));
            },

            disableAll: function () {
                this.btnReady.setEnable(false);
                this.btnExit.setEnable(false);
                this.btnAutoStart.setEnable(false);

                this.matchingPlayers.disableAll();
            },

            tweenIn: function (callback) {
                var time = dl.configs.getConfig('MATCHING_LEVEL_TWEEN_TIME', 'IN');
                dl.TweenTemplate.fadeIn(this.title, time);
                dl.TweenTemplate.fadeIn(this.btnReady, time);
                dl.TweenTemplate.fadeIn(this.btnExit, time);

                this.btnAutoStart.tweenIn(time);
                this.matchingPlayers.tweenIn(time);
            },

            tweenOut: function (callback) {
                var time = dl.configs.getConfig('MATCHING_LEVEL_TWEEN_TIME', 'OUT');
                dl.TweenTemplate.fadeOut(this.title, time, callback);
                dl.TweenTemplate.fadeOut(this.btnReady, time);
                dl.TweenTemplate.fadeOut(this.btnExit, time);

                this.btnAutoStart.tweenOut(time);
                this.matchingPlayers.tweenOut(time);
            }
        }).extend(MixinNetworkMatching);
});
