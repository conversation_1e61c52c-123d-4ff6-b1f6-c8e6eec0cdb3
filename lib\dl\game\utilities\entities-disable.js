ig.module(
    'dl.game.utilities.entities-disable'
).requires(
    'impact.game'
).defines(function () {
    'use strict';

    ig.Game.inject({
        /**
         *
         * @param {dl.Entity} baseEntity
         * @param {Array} entities - Must sorted in lowest to highest order
         * @param {Boolean} startDisable - Flag to know the loop is under the baseEntity
         */
        _disableEntityUnderRecursive: function (baseEntity, entities, startDisable) {
            for (var index = entities.length - 1; index >= 0; index--) {
                var entity = entities[index];
                if (entity == baseEntity) {
                    startDisable = true;
                    continue;
                }

                if (startDisable) {
                    if (dl.check.isFunction(entity.disableEntityUnder)) {
                        baseEntity.entitiesDisabled.push(entity);
                        entity.disableEntityUnder();
                    }
                }

                if (entity._entities) {
                    this._disableEntityUnderRecursive(baseEntity, entity._entities, startDisable);
                }
            }
        },

        /**
         * Disable entities under it
         * @param {dl.Entity} entity
         */
        disableEntityUnder: function (entity) {
            if (dl.check.isArray(entity.entitiesDisabled)) {
                this.enableEntityUnder(entity);
            }
            entity.entitiesDisabled = [];

            this._disableEntityUnderRecursive(entity, this.entities, false);
        },

        /**
         * Enable entities under it
         * @param {dl.Entity} entity
         */
        enableEntityUnder: function (entity) {
            if (dl.check.isArray(entity.entitiesDisabled)) {
                var currentEntity = entity.entitiesDisabled.pop();
                while (currentEntity) {
                    if (dl.check.isFunction(currentEntity.enableEntityUnder)) {
                        currentEntity.enableEntityUnder();
                    }

                    currentEntity = entity.entitiesDisabled.pop();
                }
            }

            delete entity.entitiesDisabled;
        },

        _disableEntitiesRecursive: function (entities) {
            for (var index = entities.length - 1; index >= 0; index--) {
                var entity = entities[index];

                if (dl.check.isFunction(entity.disableEntityUnder)) {
                    dl.game.entitiesDisabled.push(entity);
                    entity.disableEntityUnder();
                }

                if (entity._entities) {
                    this._disableEntitiesRecursive(entity._entities);
                }
            }
        },

        disableEntities: function () {
            if (dl.check.isArray(dl.game.entitiesDisabled)) {
                this.enableEntities();
            }
            dl.game.entitiesDisabled = [];

            this._disableEntitiesRecursive( this.entities);
        },

        enableEntities: function () {
            if (dl.check.isArray(dl.game.entitiesDisabled)) {
                var currentEntity = dl.game.entitiesDisabled.pop();
                while (currentEntity) {
                    if (dl.check.isFunction(currentEntity.enableEntityUnder)) {
                        currentEntity.enableEntityUnder();
                    }

                    currentEntity = dl.game.entitiesDisabled.pop();
                }
            }

            delete dl.game.entitiesDisabled;
        }
    });
});
