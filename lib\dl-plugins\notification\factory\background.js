ig.module(
    'dl-plugins.notification.factory.background'
).defines(function () {
    "use strict";

    dl.Notification.Factory.Background = {
        generate: function (configs, contentCanvas) {
            var canvas = document.createElement('canvas');
            var context = canvas.getContext('2d');

            // update canvas size with content
            if (contentCanvas) {
                canvas.width = contentCanvas.width;
                canvas.height = contentCanvas.height;
            } else {
                canvas.width = configs.box.width;
                canvas.height = configs.box.height;
            }

            // update canvas size with background
            var contentArea = this.updateCanvasSize(configs, canvas);
            configs.contentArea = contentArea;

            // draw background
            this.draw(configs, context);
            // draw content
            this.drawContentCanvas(configs, context, contentCanvas);

            return {
                configs: configs,
                canvas: canvas
            };
        },

        updateCanvasSize: function (configs, canvas) {
            var width = canvas.width;
            var height = canvas.height;

            // content min size
            if (width < configs.box.width) width = configs.box.width;
            if (height < configs.box.height) height = configs.box.height;

            // content center pos
            var contentArea = {
                x: width * 0.5,
                y: height * 0.5,
                w: width,
                h: height
            };

            // add line width  
            contentArea.x += configs.lineWidth;
            contentArea.y += configs.lineWidth;
            contentArea.w += configs.lineWidth;
            contentArea.h += configs.lineWidth;
            width += configs.lineWidth * 2;
            height += configs.lineWidth * 2;

            // add padding
            contentArea.x += configs.box.padding.x;
            contentArea.y += configs.box.padding.y;
            contentArea.w += configs.box.padding.x;
            contentArea.h += configs.box.padding.y;
            width += configs.box.padding.x * 2;
            height += configs.box.padding.y * 2;

            // update canvas size
            canvas.width = width;
            canvas.height = height;

            // return content center pos
            return contentArea;
        },

        draw: function (configs, ctx) {
            var round = configs.box.round;
            var x = configs.contentArea.x - configs.contentArea.w * 0.5;
            var y = configs.contentArea.y - configs.contentArea.h * 0.5;
            var w = configs.contentArea.w;
            var h = configs.contentArea.h;
            var xMax = x + w;
            var yMax = y + h;

            ctx.save();

            ctx.beginPath();
            ctx.moveTo(x + round, y);
            ctx.lineTo(xMax - round, y);
            ctx.quadraticCurveTo(xMax, y, xMax, y + round);
            ctx.lineTo(xMax, yMax - round);
            ctx.quadraticCurveTo(xMax, yMax, xMax - round, yMax);
            ctx.lineTo(x + round, yMax);
            ctx.quadraticCurveTo(x, yMax, x, yMax - round);
            ctx.lineTo(x, y + round);
            ctx.quadraticCurveTo(x, y, x + round, y);
            ctx.closePath();

            ctx.fillStyle = configs.fillStyle;
            ctx.fill();

            ctx.lineWidth = configs.lineWidth;
            ctx.strokeStyle = configs.strokeStyle;
            ctx.stroke();

            ctx.restore();
        },

        drawContentCanvas: function (configs, ctx, contentCanvas) {
            if (!contentCanvas) return ctx;

            var x = configs.contentArea.x - contentCanvas.width * 0.5;
            var y = configs.contentArea.y - contentCanvas.height * 0.5;

            ctx.drawImage(contentCanvas, x, y);
        }
    };

    dl.Notification.Factory.Background.DEFAULT_CONFIGS = {
        lineWidth: 2,
        fillStyle: "lightblue",
        strokeStyle: "black",

        box: { width: 500, height: 60, round: 10, padding: { x: 100, y: 5 } }
    };
});