ig.module('plugins.lootbox.lootbox-notification-dot')
    .requires(
        'plugins.lootbox.lootbox-game-object'
    )
    .defines(function () {
        ig.LootboxNotificationDot = ig.LootboxSimpleButton.extend({

            zIndex: 999999,
            text: "1",
            forceDraw: true,
            dotColor: "#ff7777",
            textColor: "#ffffff",
            type: "assembly",

            init: function (x, y, settings) {
                this.parent(x, y, settings);
                if (!ig.Lootbox.saveName) ig.Lootbox.loadData();
                this.dotColor = ig.Lootbox.notificationDot.color;
                this.textColor = ig.Lootbox.notificationDot.textColor;
                this.width = this.height = ig.Lootbox.notificationDot.size;
                this.font = ig.Lootbox.notificationDot.font
                this.offsetX = ig.Lootbox.notificationDot.textOffsetX;
                this.offsetY = ig.Lootbox.notificationDot.textOffsetY;

            },

            update: function () {
                this.parent();

                if (this.type == "assembly") {
                    var count = {};
                    var possibleAssembly = 0
                    for (var i = 0; i < ig.Lootbox.data.cards.length; i++) {
                        var card = ig.Lootbox.data.cards[i];
                        var key = card.id + "-" + card.level;
                        if (!count[key]) count[key] = 0;
                        count[key]++;
                    }
                    for (var key in count) {
                        if (Object.hasOwnProperty.call(count, key)) {
                            var c = count[key];
                            if (c >= 3) possibleAssembly++;
                        }
                    }
                    this.text = "" + possibleAssembly;
                    if (possibleAssembly > 0) this.visible = true;
                    else this.visible = false;
                } else if (this.type == "lootbox") {
                    var lootCount = 0
                    if (ig.Lootbox.getFreeBoxCollectionTime() == 0) {
                        lootCount++;
                    }
                    if (ig.Lootbox.getPremiumBoxCollectionTime() == 0) {
                        lootCount++;
                    }
                    if (lootCount > 0) {
                        this.visible = true;
                        this.text = "" + lootCount
                    } else {
                        this.visible = false;
                    }
                } else if (this.type == "collection") {
                    if (ig.Lootbox.isUpgradeMode) {
                        if (ig.Lootbox.data.cards.length == 0) this.visible = false;
                        var upgradables = {}
                        for (var i = 0; i < ig.Lootbox.data.cards.length; i++) {
                            var card = ig.Lootbox.data.cards[i];
                            if (card.level < ig.Lootbox.card.levelMax && card.exp >= ig.Lootbox.upgradeRequirements[card.level]) {
                                upgradables[card.id] = true;
                            }
                            var upgradableItems = Object.keys(upgradables).length;

                            this.text = "" + upgradableItems;
                            if (upgradableItems > 0) this.visible = true;
                            else this.visible = false;
                        }
                    } else {
                        var maxLevels = {}
                        for (var i = 0; i < ig.Lootbox.data.cards.length; i++) {
                            var card = ig.Lootbox.data.cards[i];
                            if (card.level >= ig.Lootbox.loot.actionableCollectionLevel) {
                                maxLevels[card.id] = true;
                            }
                        }

                        var actionableItem = Object.keys(maxLevels).length;

                        this.text = "" + actionableItem;
                        if (actionableItem > 0) this.visible = true;
                        else this.visible = false;
                    }
                }
            },

            drawObject: function (x, y) {
                var ctx = ig.system.context;
                ctx.save();
                var color = ig.hexToRgb(this.dotColor);
                if (this.alpha < 1) {
                    ctx.fillStyle = "rgba(" + color.r + "," + color.g + "," + color.b + "," + this.alpha + ")";
                } else {
                    ctx.fillStyle = color.hex;
                }
                ctx.beginPath();
                ctx.arc(x + this.width / 2, y + this.height / 2, this.width / 2, 0, Math.PI * 2);
                ctx.fill();
                ctx.closePath();
                ctx.restore();
                this.parent(x, y)
            },

        });
    });
