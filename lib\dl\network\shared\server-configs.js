var SERVER_CONFIGS = {};

SERVER_CONFIGS.SERVER_IPS = [
    'realtime3-singapore1.marketjs-multiplayer.com',
    'realtime2-amsterdam1.marketjs-multiplayer.com',
    'realtime1-newyork1.marketjs-multiplayer.com'
];

SERVER_CONFIGS.SERVER_IPS_NAME = [
    'Asia',
    'Europe',
    'America'
];

SERVER_CONFIGS.SERVER_IP = SERVER_CONFIGS.SERVER_IPS[0];

// Start port's configs
SERVER_CONFIGS.HTTP_PORT = 5572;
SERVER_CONFIGS.HTTPS_PORT = 5573;
// End port's configs

// Start tag's configs
SERVER_CONFIGS.TAGS = {
    MESSAGE: 'mjs_msg',
    PING: 'mjs_ping',
    PING_REPLY: 'mjs_ping_reply'
};
// End tag's configs

// Start ping's configs
SERVER_CONFIGS.PING_LOG_SIZE = 50;
SERVER_CONFIGS.PING_DELAY = 5;
// End ping's configs

/**
 * Export module
 */
if (typeof module !== 'undefined') {
    module.exports.SERVER_CONFIGS = SERVER_CONFIGS;
}
