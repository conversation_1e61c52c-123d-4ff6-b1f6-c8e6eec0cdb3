/* eslint-disable quote-props */
ig.module(
    'game.dl-entities.game-bot-manager'
).requires(
    'dl.game.entity'
).defines(function () {
    dl.EntityGameBotManager = dl.Entity
        .extend({
            staticInstantiate: function () {
                this.parent();
                this.botActionDelayMin = 3;
                this.botActionDelayMax = 8;
                this.botStatus = '1f1b1e1a';
                this.potentialTarget = [];
                return this;
            },

            postInit: function () {
                this.parent();
                this.botActionTimer = new ig.Timer(Utilities.randomInt(this.botActionDelayMin, this.botActionDelayMax));
                this.botSupportTimer = new ig.Timer(Utilities.randomInt(1, 5));
                this.cleanSoldiers();
            },

            getBotPlayers: function () {
                var players = ig.game.managers.game.players;
                var bots = [];
                for (var key in players) {
                    if (players[key] && players[key].botFlag) {
                        bots.push(players[key]);
                    }
                }
                return bots;
            },

            botDoAction: function () {
                // get bot list
                var botList = this.getBotPlayers();
                // choose random bot
                var botPlayer = botList[Utilities.randomInt(0, botList.length - 1)];
                if (botPlayer) {
                    // get capital list
                    var capitalList = ig.game.managers.game.capitalList;
                    var botCapitals = [];
                    // identify the capitals the bot owns
                    for (var i = 0; i < capitalList.length; i++) {
                        if (capitalList[i].capitalData.assignedPlayer && capitalList[i].capitalData.assignedPlayer.playerId === botPlayer.playerId) {
                            botCapitals.push(capitalList[i]);
                        }
                    }
                    // choose what capital to attack from
                    var sourceCapital = botCapitals[Utilities.randomInt(0, botCapitals.length - 1)];
                    if (sourceCapital && sourceCapital.haveShield) {
                        this.botActionTimer = new ig.Timer(Utilities.randomInt(this.botActionDelayMin, this.botActionDelayMax) * 0.5);
                        return;
                    }
                    // list attackable capitals
                    var targetCapitals = [];
                    for (var i = 0; i < capitalList.length; i++) {
                        if (capitalList[i] && sourceCapital) {
                            if (capitalList[i].capitalData.capitalIndex !== sourceCapital.capitalData.capitalIndex) {
                                targetCapitals.push(capitalList[i]);
                            }
                        }
                    }
                    // choose what capital to attack
                    var targetCapital = targetCapitals[Utilities.randomInt(0, targetCapitals.length - 1)];
                    // check if target can be attacked
                    if (targetCapital && targetCapital.haveShield) {
                        this.botActionTimer = new ig.Timer(Utilities.randomInt(this.botActionDelayMin, this.botActionDelayMax) * 0.5);
                        return;
                    }
                    // send attack command to server
                    if (sourceCapital &&
                        sourceCapital.capitalData &&
                        targetCapital &&
                        targetCapital.capitalData &&
                        !sourceCapital.supportStatus
                        ) {
                                sourceCapital.setSupportStatus();
                                ig.game.client.clientGameRoom.botStatusServer=this.botStatus;
                                var data = {
                                    playerId: botPlayer.playerId,
                                    capitalIndex: sourceCapital.capitalData.capitalIndex,
                                    currentTargetCapitalIndex: targetCapital.capitalData.capitalIndex,
                                    soldierCount: sourceCapital.capitalData.soldierCount
                                };
                                ig.game.managers.game.capitalAttack(data);


                                var showBotEmot=[0,0,0,1,1,1,1,1,1,1];
                                if(showBotEmot[Utilities.randomInt(0,9)]==1){                                
                                    var time=[1500,2000,2500,3000,3500];
                                    setTimeout(function(){
                                        var idx=[1,3,5];
                                        ig.game.client.clientGameRoom.sendBotEmoji(idx[Utilities.randomInt(0,2)],botPlayer.playerId);                                        
                                    }.bind(this),time[Utilities.randomInt(0,time.length-1)]);
                                }


                                if(targetCapital.capitalData && targetCapital.capitalData.assignedPlayer && targetCapital.capitalData.assignedPlayer.playerId &&
                                   sourceCapital.capitalData.assignedPlayer.playerId != targetCapital.capitalData.assignedPlayer.playerId &&
                                   sourceCapital.capitalData.soldierCount<targetCapital.capitalData.soldierCount
                                ){
                                    this.getFriendsCapital(sourceCapital,targetCapital);
                                }

                    }
                    // reset bot action timer
                    this.botActionTimer = new ig.Timer(Utilities.randomInt(this.botActionDelayMin, this.botActionDelayMax));
                }
            },
            getFriendsCapital:function(sourceCapital,targetCapital){
                var friends=[];
                var capitalList = ig.game.managers.game.capitalList;
                var capitalSupport = [];
                for (var i = 0; i < capitalList.length; i++) {
                    if( capitalList[i] && capitalList[i].capitalData && capitalList[i].capitalData.assignedPlayer && capitalList[i].capitalData.assignedPlayer.playerId &&
                        capitalList[i].capitalData.assignedPlayer.playerId==sourceCapital.capitalData.assignedPlayer.playerId &&
                        capitalList[i].capitalData.capitalIndex!=sourceCapital.capitalData.capitalIndex && 
                        capitalList[i].capitalData.capitalIndex!=targetCapital.capitalData.capitalIndex
                    ){
                        friends.push(capitalList[i]);
                    }
                }
                
                if(friends.length>0){
                    var helper = friends[Utilities.randomInt(0, friends.length - 1)];
                    if(!helper.supportStatus && !helper.haveShield && !helper.haveWarning){                       
                        helper.setSupportStatus();
                        ig.game.client.clientGameRoom.botStatusServer=this.botStatus;
                        var data = {
                            playerId: helper.capitalData.assignedPlayer.playerId,
                            capitalIndex: helper.capitalData.capitalIndex,
                            currentTargetCapitalIndex: targetCapital.capitalData.capitalIndex,
                            soldierCount: helper.capitalData.soldierCount
                        };
                        ig.game.managers.game.capitalAttack(data);

                        var showBotEmot=[0,0,0,1,1,1,1,1,1,1];
                        if(showBotEmot[Utilities.randomInt(0,9)]==1){                                
                            var time=[500,1000,1500,2000,3000];
                            var playerEmot=targetCapital.capitalData.assignedPlayer.playerId;
                            setTimeout(function(){
                                var idx=[0,2,4];
                                // console.log('need support');
                                ig.game.client.clientGameRoom.sendBotEmoji(idx[Utilities.randomInt(0,2)],playerEmot);                                        
                            }.bind(this),time[Utilities.randomInt(0,time.length-1)]);
                        }

                    }

                }


            },
            botSupport:function(){

                var tmp=ig.game.soldier;
                ig.game.soldier=[];
                var idx=0;
                for(var i=0;i<tmp.length;i++){
                    if(tmp[i] && tmp[i]!=null && tmp[i]!=''){
                        ig.game.soldier.push(tmp[i]);
                        ig.game.soldier[idx].arrayIndex=idx;
                        idx++;
                    }
                }

                var soldier = ig.game.soldier;
                var tcapitalIndex= null;
                for(var i=0;i<soldier.length;i++){
                    if(soldier[i] && soldier[i].targetCapital && soldier[i].targetCapital.capitalData && soldier[i].targetCapital.capitalData.assignedPlayer && soldier[i].targetCapital.capitalData.assignedPlayer.playerId &&
                        tcapitalIndex != soldier[i].targetCapital.capitalData.capitalIndex &&
                        !this.targetIsHuman(soldier[i].targetCapital.capitalData.assignedPlayer.playerId) &&
                        !soldier[i].targetCapital.haveShield && 
                        soldier[i].sourceCapital.capitalData.capitalIndex != soldier[i].targetCapital.capitalData.capitalIndex &&
                        soldier[i].sourceCapital.capitalData.assignedPlayer.playerId != soldier[i].targetCapital.capitalData.assignedPlayer.playerId
                        ){
                            this.potentialTarget.push(soldier[i].sourceCapital);
                            // console.log(soldier[i].targetCapital.capitalData.capitalIndex+' need help');

                            this.getSupport(soldier[i].targetCapital.capitalData.assignedPlayer.playerId,soldier[i].targetCapital.capitalData.capitalIndex,soldier[i].targetCapital.capitalData.assignedPlayer.playerId,soldier[i].sourceCapital.capitalData.capitalIndex);
                            tcapitalIndex = soldier[i].targetCapital.capitalData.capitalIndex;
                    }
                }

                this.botSupportTimer = new ig.Timer(Utilities.randomInt(1, 5));
            },
            getSupport:function(playerId,capitalToSupport,capitalToSupportPlayer,capitalTargetEnemy){
                var capitalList = ig.game.managers.game.capitalList;
                var capitalSupport = [];
                for (var i = 0; i < capitalList.length; i++) {
                    if(capitalList[i].capitalData && capitalList[i].capitalData.assignedPlayer && capitalList[i].capitalData.assignedPlayer.playerId &&
                        capitalList[i].capitalData.assignedPlayer.playerId == playerId &&
                        capitalList[i].capitalData.capitalIndex != capitalToSupport &&
                        !capitalList[i].haveShield &&
                        !capitalList[i].haveWarning){

                             capitalSupport.push(capitalList[i]);
                    }
                }

                var decision = [0,0,0,0,1,1,1,1,2,2];
                for(var i=0;i<capitalSupport.length;i++){
                    var code = decision[Utilities.randomInt(0, decision.length - 1)];
    
                    if(code==0){
                        //support friend
                        if (capitalSupport[i] && capitalSupport[i].capitalData && !capitalSupport[i].supportStatus) {
                            capitalSupport[i].setSupportStatus();
                            ig.game.client.clientGameRoom.botStatusServer=this.botStatus;
                            // console.log(capitalSupport[i].capitalData.capitalIndex+' support '+capitalToSupport);

                            var data = {
                                playerId: playerId,
                                capitalIndex: capitalSupport[i].capitalData.capitalIndex,
                                currentTargetCapitalIndex: capitalToSupport,
                                soldierCount: capitalSupport[i].capitalData.soldierCount
                            };
                            ig.game.managers.game.capitalAttack(data);
                        }

                    }else if(code==1){
                        //attack enemy
                        if (capitalSupport[i] && capitalSupport[i].capitalData && !capitalSupport[i].supportStatus) {
                            capitalSupport[i].setSupportStatus();
                            ig.game.client.clientGameRoom.botStatusServer=this.botStatus;
                            // console.log(capitalSupport[i].capitalData.capitalIndex+' attack '+capitalTargetEnemy);

                            var showBotEmot=[0,0,0,1,1,1,1,1,1,1];
                            if(showBotEmot[Utilities.randomInt(0,9)]==1){                                
                                var time=[1000,2000,3000];
                                var playerEmot=capitalSupport[i].capitalData.assignedPlayer.playerId;
                                setTimeout(function(){
                                    // console.log('attack support');
                                    var idx=[2];
                                    ig.game.client.clientGameRoom.sendBotEmoji(idx[0],playerEmot);                                        
                                }.bind(this),time[Utilities.randomInt(0,time.length-1)]);
                            }


                            var data = {
                                playerId: playerId,
                                capitalIndex: capitalSupport[i].capitalData.capitalIndex,
                                currentTargetCapitalIndex: capitalTargetEnemy,
                                soldierCount: capitalSupport[i].capitalData.soldierCount
                            };
                            ig.game.managers.game.capitalAttack(data);
                        }
                    }else{
                        // console.log(capitalSupport[i].capitalData.capitalIndex+' stay iddle ');                        
                    }
                }
            },
            targetIsHuman:function(playerId){
                var players = ig.game.managers.game.players;
                var res = false;
                for (var key in players) {
                    if (players[key] && !players[key].botFlag && players[key].playerId==playerId) {
                        res = true;
                    }
                }
                return res;
            },
            cleanSoldiers:function(){
                var tmp=ig.game.soldier;
                ig.game.soldier=[];
                var idx=0;
                for(var i=0;i<tmp.length;i++){
                    if(tmp[i] && tmp[i]!=null && tmp[i]!=''){
                        ig.game.soldier.push(tmp[i]);
                        ig.game.soldier[idx].arrayIndex=idx;
                        idx++;
                    }
                }
                setTimeout(function(){
                    this.cleanSoldiers();
                }.bind(this),1000);
            },
            update: function () {
                this.parent();
                if (ig.game.managers && ig.game.managers.game.isGameOver) {
                    if (this.botActionTimer) {
                        this.botActionTimer = null;
                    }
                    return;
                }
                if (this.botSupportTimer && this.botSupportTimer.delta() > 0) {
                    this.botStatus='1f1b1e1a';
                    if(ig.game.client.clientGameRoom && ig.game.client.clientGameRoom.botStatus) this.botStatus=ig.game.client.clientGameRoom.botStatus;
                    this.botSupportTimer.reset();
                    this.botSupport();
                }
                if (this.botActionTimer && this.botActionTimer.delta() > 0) {
                    this.botStatus='1f1b1e1a';
                    if(ig.game.client.clientGameRoom && ig.game.client.clientGameRoom.botStatus) this.botStatus=ig.game.client.clientGameRoom.botStatus;
                    this.botActionTimer = null;
                    this.botDoAction();
                }
            }
        });
});
