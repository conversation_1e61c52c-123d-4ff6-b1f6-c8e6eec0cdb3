ig.module(
    'dl.game.entity.components.collision.circle-collision'
).requires(
    'dl.game.entity.components.collision.collision',
    'dl.game.entity.components.collision.helpers.circle'
).defines(function () {
    'use strict';

    dl.CircleCollision = dl.CollisionComponent.extend({
        staticInstantiate: function (entity) {
            this.parent(entity);

            this._collisionType = dl.CollisionComponent.TYPES.CIRCLE;
            this._collisionRadius; // = 1;
            this._collisionOffset = { x: 0, y: 0 };

            this.radius = 1;
            this.offset = { x: 0, y: 0 };
            this.pos = { x: 0, y: 0 };

            // bind events
            this.entity.onEvent('positionChanged', this.updateAll.bind(this));

            return this;
        },

        onPropertiesChanged: function (properties) {
            this.updateAll();
        },

        updateAll: function () {
            this.updateSize();
            this.updateOffset();
            this.updatePos();
            this.updateBoundingBox();
        },

        updateSize: function () {
            if (this._collisionRadius) {
                this.radius = this._collisionRadius * Math.min(this.entity.scale.x, this.entity.scale.y);
            } else {
                this.radius = Math.min(this.entity.size.x, this.entity.size.y) * 0.5;
            }

            if (this.radius <= 1) {
                this.radius = 1;
            }
        },

        updateOffset: function () {
            this.offset.x = this._collisionOffset.x * this.entity.scale.x;
            this.offset.y = this._collisionOffset.y * this.entity.scale.y;
        },

        updatePos: function () {
            this.pos.x = this.entity.pos.x + this.offset.x;
            this.pos.y = this.entity.pos.y + this.offset.y;
        },

        updateBoundingBox: function () {
            this.collisionBoundingBox.xMin = this.pos.x - this.radius;
            this.collisionBoundingBox.yMin = this.pos.y - this.radius;
            this.collisionBoundingBox.xMax = this.pos.x + this.radius;
            this.collisionBoundingBox.yMax = this.pos.y + this.radius;
        },

        touches: function (other) {
            switch (other._collisionType) {
                case dl.CollisionComponent.TYPES.POINT: {
                    return dl.CollisionHelpers_Circle.point(
                        this.pos.x,
                        this.pos.y,
                        this.radius,
                        other.pos.x,
                        other.pos.y
                    );
                } break;
                case dl.CollisionComponent.TYPES.RECTANGLE: {
                    return dl.CollisionHelpers_Circle.rectangle(
                        this.pos.x,
                        this.pos.y,
                        this.radius,
                        other.pos.x - other.size.x * 0.5,
                        other.pos.y - other.size.y * 0.5,
                        other.size.x,
                        other.size.y
                    );
                } break;
                case dl.CollisionComponent.TYPES.CIRCLE: {
                    return dl.CollisionHelpers_Circle.circle(
                        this.pos.x,
                        this.pos.y,
                        this.radius,
                        other.pos.x,
                        other.pos.y,
                        other.radius
                    );
                } break;
                default: {
                    dl.warn('Undefined type [' + other._collisionType + ']');
                } break;
            }
            return false;
        },

        drawCollision: function (ctx) {
            ctx.save();
            ctx.beginPath();
            ctx.ellipse(
                this.pos.x, this.pos.y,
                this.radius, this.radius,
                0, 0,
                Math.PI * 2);
            ctx.closePath();
            ctx.fillStyle = 'green';
            ctx.globalAlpha = 0.5;
            ctx.fill();
            ctx.restore();
        }
    });
});
