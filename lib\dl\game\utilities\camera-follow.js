ig.module(
    'dl.game.utilities.camera-follow'
).defines(function () {
    'use strict';

    dl.CameraFollow = {
        staticInstantiate: function () {
            this.parent();

            this.cameraFollower = null;
            this._boundUpdateFollower = this.updateFollower.bind(this);
            return this;
        },

        reset: function () {
            this.setCameraFollower(null);

            this.parent();
        },

        update: function () {
            this.updateFollower();

            this.parent();
        },

        setCameraFollower: function (follower) {
            if (this.cameraFollower) {
                this.cameraFollower.offEvent('positionChanged', this._boundUpdateFollower);
                this.cameraFollower = null;
            }
            if (!follower) return;

            this.cameraFollower = follower;
            this.cameraFollower.onEvent('positionChanged', this._boundUpdateFollower);
        },

        updateFollower: function () {
            if (!this.cameraFollower) return;

            // var direction = new dl_Vector2(this.cameraFollower.pos.x - this.pos.x, this.cameraFollower.pos.y - this.pos.y);
            // direction = direction.scale(0.25);
            // this.setPos(this.pos.x + direction.x, this.pos.y + direction.y);

            this._setPos(this.cameraFollower.pos.x, this.cameraFollower.pos.y);
        }
    };
});
