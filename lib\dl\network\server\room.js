/**
 * Created by <PERSON><PERSON>
 * Handle room and store information on server side
 */

if (typeof require !== 'undefined') {
	var ROOM_CONFIGS = require('./room-configs.js').ROOM_CONFIGS;
	var SERVER_MESSAGE = require('../shared/server-message.js').SERVER_MESSAGE;

	var RoomEventHandler = require('./room-event-handler.js').RoomEventHandler;
	var RoomEvent = require('../shared/room-event.js').RoomEvent;
	var RoomPlayer = require('../shared/room-player.js').RoomPlayer;

	var CountDownTimer = require('../shared/count-down-timer.js').CountDownTimer;
	var NameAndAvatar = require('../shared/name-and-avatar.js').NameAndAvatar;
}

var Room = function (host, roomId, requestInfo) {
	var self = {};

	self.host = null;
	/**
	 * Room shared variable
	 */
	self.roomId = -1;
	self.roomPassword = null;
	self.roomMaxPlayer = 2;
	self.roomArena = 1;
	self.autoStartRoomFlag = false;
	self.roomUseBot = true;

	self.roomState = null;

	self.playerList = [];
	self.roomHostPlayerId = null;

	self.networkGame = null;
	self.roomClose = false;
	self.roomKey = Utilities.randomInt(10, 99);

	self.packData = function () {
		var data = {};

		data.roomId = self.roomId;
		data.roomPassword = self.roomPassword;
		data.roomMaxPlayer = self.roomMaxPlayer;
		data.roomArena = self.roomArena;
		data.autoStartRoomFlag = self.autoStartRoomFlag;
		data.roomUseBot = self.roomUseBot;
		data.roomState = self.roomState;
		data.roomKey = self.roomKey;

		data.playerList = [];
		for (var i = 0, iLength = self.playerList.length; i < iLength; i++) {
			data.playerList.push(self.playerList[i].packData());
		}

		data.roomHostPlayerId = self.roomHostPlayerId;

		return data;
	};

	self.importData = function (data) {
		if (!data) return;

		if (typeof data.roomPassword !== 'undefined') {
			self.roomPassword = data.roomPassword;
		}

		if (typeof data.roomMaxPlayer !== 'undefined') {
			self.roomMaxPlayer = data.roomMaxPlayer;
		}

		if (typeof data.roomArena !== 'undefined') {
			self.roomArena = data.roomArena;
		}

		if (typeof data.roomUseBot !== 'undefined') {
			self.roomUseBot = data.roomUseBot;
		}

		// force bot disable when in private room
		var validRoomPassword = (typeof self.roomPassword !== 'undefined') && self.roomPassword != '';
		if (validRoomPassword) self.roomUseBot = false;
	};

	/**
	 * Room private variable
	 */
	self.lastPlayerId = -1;
	self.playerIdHash = {};
	self.playerSocketHash = {};
	// Timer
	self.countDownTimerList = [];

	/**
	 * Room life cycle
	 */
	self.init = function (host, roomId, requestInfo) {
		// console.log('Init Room: ', roomId);

		self.host = host;
		self.roomId = roomId;

		/**
		 * Room shared variable
		 */
		self.roomMaxPlayer = ROOM_CONFIGS.MAX_PLAYER;
		self.roomArena = 1;
		self.autoStartRoomFlag = ROOM_CONFIGS.AUTO_START;
		self.roomUseBot = ROOM_CONFIGS.USE_BOT;
		self.roomState = ROOM_CONFIGS.ROOM_STATE.INITIALIZING;
		self.importData(requestInfo);

		// Switch state to wait player
		self.switchState(ROOM_CONFIGS.ROOM_STATE.WAITING_FOR_PLAYER);
	};

	self.updateDelay=true;	
	self.update = function () {

		self.updateTimers();

		if(!self.updateDelay) return;
		self.updateDelay=false;

		self.updateState();

		setTimeout(function(){
			var human=0;
			for(var i=0;i<self.playerList.length;i++){
				if(!self.playerList[i].botFlag){
					human +=1;
				}
			}
			if(human==0){

				for(var i=0;i<self.playerList.length;i++){
					self.applyEvent(self.createEvent(RoomEvent.EVENT_TYPE.PLAYER_LEAVE, {
						playerId: self.playerList[i].playerId,
						playerName: self.playerList[i].playerName
					}));
				}

				self.applyEvent(self.createEvent(RoomEvent.EVENT_TYPE.ROOM_END));
				self.host.shutDownRoom(self.roomId);
				// console.log('force shutDownRoom ',self.roomId);				
			}
			self.updateDelay=true;			
		}.bind(self),1000);
	};

	self.switchState = function (newState) {
		self.roomState = newState;
		switch (self.roomState) {
			case ROOM_CONFIGS.ROOM_STATE.INITIALIZING: {
			} break;
			case ROOM_CONFIGS.ROOM_STATE.WAITING_FOR_PLAYER: {
			} break;
			case ROOM_CONFIGS.ROOM_STATE.WAITING_TO_START: {
			} break;
			case ROOM_CONFIGS.ROOM_STATE.STARTING: {
				if (self.applyEvent(self.createEvent(RoomEvent.EVENT_TYPE.ROOM_START))) {

				}
			} break;
			case ROOM_CONFIGS.ROOM_STATE.GAME_PLAY: {
			} break;
			case ROOM_CONFIGS.ROOM_STATE.ENDED: {
				if (self.applyEvent(self.createEvent(RoomEvent.EVENT_TYPE.ROOM_END))) {

				}
			} break;
		}
	};

	self.updateState = function () {
		switch (self.roomState) {
			case ROOM_CONFIGS.ROOM_STATE.INITIALIZING: { } break;
			case ROOM_CONFIGS.ROOM_STATE.WAITING_FOR_PLAYER: {
				// check if enough player - switch to WAITING_TO_START
				if (self.playerList.length >= self.roomMaxPlayer) {
					self.switchState(ROOM_CONFIGS.ROOM_STATE.WAITING_TO_START);
				}
			} break;
			case ROOM_CONFIGS.ROOM_STATE.WAITING_TO_START: {
				/**
				 * check if not enough player - switch back to WAITING_FOR_PLAYER state
				 * other wise check auto start
				 */
				if (self.playerList.length < self.roomMaxPlayer) {
					self.switchState(ROOM_CONFIGS.ROOM_STATE.WAITING_FOR_PLAYER);
					return;
				} else {
					self.checkAutoStart();
				}
			} break;
			case ROOM_CONFIGS.ROOM_STATE.STARTING: {
			} break;
			case ROOM_CONFIGS.ROOM_STATE.GAME_PLAY: {
				if (self.networkGame) self.networkGame.update();
			} break;
			case ROOM_CONFIGS.ROOM_STATE.ENDED: {
			} break;
		}
	};

	self.checkAllPlayerReady = function () {
		for (var i = 0, iLength = self.playerList.length; i < iLength; i++) {
			if (!self.playerList[i].playerReady) {
				return false;
			}
		}

		return true;
	};

	self.checkAutoStart = function () {
		if (!self.autoStartRoomFlag) return false;

		self.startRoom();
	};

	self.startRoom = function () {
		if (self.playerList.length < self.roomMaxPlayer) return false;
		if (!self.checkAllPlayerReady()) return false;

		self.switchState(ROOM_CONFIGS.ROOM_STATE.STARTING);
	};

	self.onRoomStarted = function () {
		if (self.applyEvent(self.createEvent(RoomEvent.EVENT_TYPE.ROOM_STARTED))) {
			self.switchState(ROOM_CONFIGS.ROOM_STATE.GAME_PLAY);
		}
	};

	self.endRoom = function () {
		self.switchState(ROOM_CONFIGS.ROOM_STATE.ENDED);
	};

	/**
	 * Player
	 */
	self.addPlayer = function (socket, playerRequestInfo) {
		if (!self.canJoin()) return false;

		if (socket) {
			// Have socket - then it is player
			if (self.findPlayerBySocket(socket)) {
				// console.error('Player already joined');
				return false;
			}
		} else {
			// Doesn't have socket - then it is Bot
			socket = null;
			var avatarId = NameAndAvatar.randomAvatar();
			playerRequestInfo = {
				playerName: NameAndAvatar.randomName(avatarId),
				playerAvatarId: avatarId,
				playerSvasId: 0,
				playerAlliance: '',
				botFlag: true
			};
		}



		var player = new RoomPlayer(socket, self, ++self.lastPlayerId, playerRequestInfo);
		self.playerList.push(player);
		self.playerIdHash[player.playerId] = player;

		if (socket) {
			// Assign host
			self.updateRoomHost();

			// Join room success
			var data = {};
			data.roomId = self.roomId;
			data.joinedTime = player.joinedTime;
			data.playerId = player.playerId;
			data.roomData = self.packData();

			self.host.joinRoomSucceed(socket, data);
		} else {
			player.playerReady = true;
		}

		// Player join event
		self.applyEvent(self.createEvent(RoomEvent.EVENT_TYPE.PLAYER_JOIN, {
			playerId: player.playerId,
			playerName: player.playerName,
		}));

		return player;
	};

	self.removePlayer = function (player) {
		if (!player) {
			// console.error('Can not remove player');
			return null;
		}

		var tempPlayerList = [];
		for (var i = 0, iLength = self.playerList.length; i < iLength; i++) {
			if (self.playerList[i].playerId != player.playerId) {
				tempPlayerList.push(self.playerList[i]);
			}
		}
		self.playerList = tempPlayerList;
		self.rebuildPlayerHash();

		// Remove host
		if (self.roomHostPlayerId == player.playerId) {
			self.roomHostPlayerId = null;
			self.updateRoomHost();
		}

		// Player leave event
		self.applyEvent(self.createEvent(RoomEvent.EVENT_TYPE.PLAYER_LEAVE, {
			playerId: player.playerId,
			playerName: player.playerName
		}));

		return player;
	};

	self.findPlayerBySocket = function (socket) {
		for (var i = 0, iLength = self.playerList.length; i < iLength; i++) {
			if (self.playerList[i].socket == socket) {
				return self.playerList[i];
			}
		}

		return null;
	};

	self.findPlayerById = function (playerId) {
		// find in hash
		var foundPlayer = self.playerIdHash[playerId];
		if (foundPlayer) {
			return self.playerIdHash[playerId];
		}

		// find in list
		for (var i = 0, iLength = self.playerList.length; i < iLength; i++) {
			foundPlayer = self.playerList[i];

			if (foundPlayer.playerId == playerId) {
				// update hash id
				self.playerIdHash[foundPlayer.playerId] = foundPlayer;

				return foundPlayer;
			}
		}

		return null;
	};

	self.rebuildPlayerHash = function () {
		self.playerIdHash = {};
		var player = null;
		for (var i = 0, iLength = self.playerList.length; i < iLength; i++) {
			player = self.playerList[i];
			self.playerIdHash[player.id] = player;
		}
	};

	/**
	 * Room handle functions
	 */
	self.handleClientEvent = function (event) {
		return RoomEventHandler.handleEvent(self, event);
	};

	self.createEvent = function (eventType, eventInfo) {
		if (typeof eventInfo === 'undefined') {
			eventInfo = {};
		}

		return new RoomEvent(eventType, Date.now(), eventInfo);
	};

	self.applyEvent = function (event) {
		return RoomEventHandler.handleEvent(self, event);
	};

	self.broadcast = function (msg, exceptId) {
		for (var i = 0; i < self.playerList.length; i++) {
			if (typeof exceptId !== 'undefined') {
				if (self.playerList[i].id == exceptId) continue;
			}

			self.host.server.sendMessage(self.playerList[i].socket, msg);
		}
	};

	self.sendRoomEvent = function (data) {
		// Send room event to all players in room
		self.broadcast({
			type: SERVER_MESSAGE.HOST.ROOM_EVENT,
			data: data
		});
	};

	/**
	 * Room utilities functions
	 */
	self.startTimer = function (time, callback) {
		var timer = new CountDownTimer(time, callback);
		self.countDownTimerList.push(timer);

		return timer;
	};

	self.updateTimers = function () {
		var cleanFlag = false;
		var timer = null;
		for (var i = 0, iLength = self.countDownTimerList.length; i < iLength; i++) {
			timer = self.countDownTimerList[i];
			timer.update();

			if (timer.timeUp) {
				cleanFlag = true;
			}
		}

		// clean up after update - due to timer can be added during update
		if (cleanFlag) {
			self.countDownTimerList = self.countDownTimerList.filter(function (timer) {
				return !timer.timeUp;
			});
		}
	};

	self.canJoin = function (requestInfo) {
		if (self.roomState != ROOM_CONFIGS.ROOM_STATE.WAITING_FOR_PLAYER) return false;

		// Check player
		if (self.playerList.length >= self.roomMaxPlayer) return false;

		if (requestInfo) {
			// Check max player
			if (requestInfo.roomMaxPlayer != self.roomMaxPlayer) return false;

			if (requestInfo.roomArena != self.roomArena) return false;
			// Check password
			if (!self.checkPassword(requestInfo.roomPassword)) return false;
		}

		return true;
	};

	self.checkPassword = function (password) {
		var validRoomPassword = (typeof self.roomPassword !== 'undefined') &&
			self.roomPassword != null &&
			self.roomPassword != '';

		var validPassword = (typeof password !== 'undefined') &&
			password != null &&
			password != '';

		// no roomPassword and no password
		if (!validRoomPassword && !validPassword) return true;

		// have valid roomPassword and have valid password
		if (validRoomPassword && validPassword) {
			// match password
			return self.roomPassword == password;
		}

		return false;
	};

	self.checkEmptyRoom = function () {
		// check if there are no real player
		for (var i = 0, iLength = self.playerList.length; i < iLength; i++) {
			if (self.playerList[i].socket) {
				return false;
			}
		}

		return true;
	};

	self.updateRoomHost = function () {
		// No roomHostPlayerId, find new host
		if (isNaN(self.roomHostPlayerId) || self.roomHostPlayerId === null) {
			var player = null;
			for (var i = 0, iLength = self.playerList.length; i < iLength; i++) {
				player = self.playerList[i];
				if (player.socket) {
					player.playerReady = true;
					self.roomHostPlayerId = player.playerId;
					break;
				}
			}

			/**
			 * double check if new host is valid
			 * if no host, shut down room
			 * can remove these check, already have check empty room when leaveRoom in host
			 */
			if (isNaN(self.roomHostPlayerId) || self.roomHostPlayerId === null) {
				self.host.shutDownRoom(self.roomId);
			}
		}
	};

	self.init(host, roomId, requestInfo);
	return self;
};

/**
 * Export module
 */
if (typeof module !== 'undefined') {
	module.exports.Room = Room;
}
