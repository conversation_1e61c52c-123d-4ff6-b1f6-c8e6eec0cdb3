ig.module(
    'dl.templates.network-entities.name-and-avatar.entity-name-avatar-input'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'dl.templates.network-entities.name-and-avatar.entity-avatar-input',
    'dl.templates.network-entities.name-and-avatar.entity-name-input'
).defines(function () {
    'use strict';

    dl.EntityNameAvatarInput = dl.Entity
        .extend(dl.MixinDocker)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this.spacing = 50;

                return this;
            },

            postInit: function () {
                this.parent();

                this.avatarInput = this.spawnEntity(dl.EntityAvatarInput, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    anchor: { x: 0.5, y: 0 }
                });

                this.avatarInputFrame = this.spawnEntity(dl.EntityImage, {
                    c_DockerComponent: {
                        dockerObject: this.avatarInput.avatar,
                        dockerPercent: { x: 0.5, y: 0.5 },
                        dockerOffset: { x: 0, y: 1 }
                    },
                    image: dl.preload['name-avatar']['avatar-frame-gray']
                });

                this.nameInput = this.spawnEntity(dl.EntityNameInput, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 1 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    anchor: { x: 0.5, y: 1 }
                });

                this.updateData();
                this.calculateSize();
            },

            updateData: function () {
                this.avatarInput.updateData();
                this.nameInput.updateData();
            },

            calculateSize: function () {
                this.setSize(
                    Math.max(this.avatarInput.size.x, this.nameInput.size.x),
                    this.avatarInput.size.y + this.nameInput.size.y + this.spacing
                );
            },

            tweenIn: function (time) {
                dl.TweenTemplate.fadeIn(this, time);
                this.avatarInput.tweenIn(time);
                this.nameInput.tweenIn(time);
            },

            tweenOut: function (time) {
                dl.TweenTemplate.fadeOut(this, time);
                this.avatarInput.tweenOut(time);
                this.nameInput.tweenOut(time);
            }
        });

    // Enable cache
    dl.enableCacheCanvas(dl.EntityNameAvatarInput);
});
