/* MarketJS input query plugin */
ig.module('plugins.click-event-div-layer')
	.requires('plugins.data.vector')
	.defines(function () {
		ClickEventDivLayer = ig.Class.extend({
			pos: new Vector2(0, 0),
			size: new Vector2(0, 0),
			identifier: null,
			invisImagePath: 'media/graphics/misc/invisible.png',
			init: function (entity, onButtonPressed) {
				this.pos = new Vector2(entity.pos.x, entity.pos.y);
				this.size = new Vector2(entity.size.x, entity.size.y);

				var div_layer_name = 'more-games';

				if (entity.div_layer_name) {
					// console.log('settings found ... using that div layer name')
					div_layer_name = entity.div_layer_name;
				}

				if (onButtonPressed) {
					this.onButtonPressed = onButtonPressed;
				}

				this.createClickEventLayer(div_layer_name);
			},

			createClickEventLayer: function (divID) {
				this.identifier = divID;
				var id = '#' + divID;
				var elem = ig.domHandler.getElementById(id);

				if (elem) {
					ig.domHandler.delete(id);
					// since there's no way to remove the added event
					// we to delete the old elem and crete the new one
					// to keep updating the url text
				}
				this.createClickableOutboundLayer(divID, this.invisImagePath);
			},

			update: function (x, y) {
				if (this.pos.x == x && this.pos.y == y) {
					return;
				} else {
					this.pos.x = x;
					this.pos.y = y;
					ig.sizeHandler.dynamicClickableEntityDivs[this.identifier] = {};
					ig.sizeHandler.dynamicClickableEntityDivs[this.identifier].width = this.size.x;
					ig.sizeHandler.dynamicClickableEntityDivs[this.identifier].height = this.size.y;
					ig.sizeHandler.dynamicClickableEntityDivs[this.identifier].entity_pos_x = this.pos.x;
					ig.sizeHandler.dynamicClickableEntityDivs[this.identifier].entity_pos_y = this.pos.y;
					ig.sizeHandler.resizeLayers();
				}
			},

			onButtonPressed: function () {
				console.log('click event function');
			},

			createClickableOutboundLayer: function (id, image_path) {
				// CREATE LAYER
				// console.log("create clickable outbound layer");

				var div = ig.domHandler.create('div');
				ig.domHandler.attr(div, 'id', id);

				/* PATCH FOR ISSUE #8 */
				/* PREVENT DEFAULT MOUSEDOWN BEHAVIOR */
				ig.domHandler.addEvent(div, 'mousedown', function (e) {
					e.preventDefault();
				});

				ig.domHandler.addEvent(div, 'click', this.onButtonPressed.bind(this));

				var linkImg = ig.domHandler.create('img');

				ig.domHandler.css(linkImg, { width: '100%', height: '100%' });
				ig.domHandler.attr(linkImg, 'src', image_path);

				var aspectRatioMin = Math.min(
					ig.sizeHandler.scaleRatioMultiplier.x,
					ig.sizeHandler.scaleRatioMultiplier.y
				);
				if (ig.ua.mobile) {
					var canvas = ig.domHandler.getElementById('#canvas');

					var offsets = ig.domHandler.getOffsets(canvas);

					var offsetLeft = offsets.left;
					var offsetTop = offsets.top;

					console.log(offsets.left);

					if (ig.sizeHandler.disableStretchToFitOnMobileFlag) {
						var divleft =
							Math.floor(offsetLeft + this.pos.x * ig.sizeHandler.scaleRatioMultiplier.x) + 'px';
						var divtop = Math.floor(offsetTop + this.pos.y * ig.sizeHandler.scaleRatioMultiplier.y) + 'px';
						var divwidth = Math.floor(this.size.x * ig.sizeHandler.scaleRatioMultiplier.x) + 'px';
						var divheight = Math.floor(this.size.y * ig.sizeHandler.scaleRatioMultiplier.y) + 'px';
					} else {
						var divleft = Math.floor(this.pos.x * ig.sizeHandler.sizeRatio.x) + 'px';
						var divtop = Math.floor(this.pos.y * ig.sizeHandler.sizeRatio.y) + 'px';
						var divwidth = Math.floor(this.size.x * ig.sizeHandler.sizeRatio.x) + 'px';
						var divheight = Math.floor(this.size.y * ig.sizeHandler.sizeRatio.y) + 'px';
					}

					ig.domHandler.css(div, {
						float: 'left',
						position: 'absolute',
						left: divleft,
						top: divtop,
						width: divwidth,
						height: divheight,
						'z-index': 3
					});
				} else {
					var canvas = ig.domHandler.getElementById('#canvas');

					var offsets = ig.domHandler.getOffsets(canvas);

					var offsetLeft = offsets.left;
					var offsetTop = offsets.top;
					if (ig.sizeHandler.enableStretchToFitOnDesktopFlag) {
						var divleft = Math.floor(offsetLeft + this.pos.x * ig.sizeHandler.sizeRatio.x) + 'px';
						var divtop = Math.floor(offsetTop + this.pos.y * ig.sizeHandler.sizeRatio.y) + 'px';
						var divwidth = Math.floor(this.size.x * ig.sizeHandler.sizeRatio.x) + 'px';
						var divheight = Math.floor(this.size.y * ig.sizeHandler.sizeRatio.y) + 'px';
					} else {
						var divleft = Math.floor(offsetLeft + this.pos.x * aspectRatioMin) + 'px';
						var divtop = Math.floor(offsetTop + this.pos.y * aspectRatioMin) + 'px';
						var divwidth = Math.floor(this.size.x * aspectRatioMin) + 'px';
						var divheight = Math.floor(this.size.y * aspectRatioMin) + 'px';
					}
					ig.domHandler.css(div, {
						float: 'left',
						position: 'absolute',
						left: divleft,
						top: divtop,
						width: divwidth,
						height: divheight,
						'z-index': 3
					});
				}

				ig.domHandler.addEvent(div, 'mousemove', ig.input.mousemove.bind(ig.input), false);

				ig.domHandler.appendToBody(div);

				// ADD TO HANDLER FOR RESIZING
				ig.sizeHandler.dynamicClickableEntityDivs[id] = {};
				// ig.sizeHandler.dynamicClickableEntityDivs[id]['width'] = this.size.x*ig.sizeHandler.multiplier;
				// ig.sizeHandler.dynamicClickableEntityDivs[id]['height'] = this.size.y*ig.sizeHandler.multiplier;
				ig.sizeHandler.dynamicClickableEntityDivs[id].width = this.size.x;
				ig.sizeHandler.dynamicClickableEntityDivs[id].height = this.size.y;
				ig.sizeHandler.dynamicClickableEntityDivs[id].entity_pos_x = this.pos.x;
				ig.sizeHandler.dynamicClickableEntityDivs[id].entity_pos_y = this.pos.y;
			}
		});
	});
