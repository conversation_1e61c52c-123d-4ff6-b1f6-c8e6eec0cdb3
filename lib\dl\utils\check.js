ig.module(
    'dl.utils.check'
).defines(function () {
    'use strict';

    /**
     * Create name space
     */
    dl.check = {};

    /**
     * Check if object is array or not
     * @param {*} obj
     * @returns true or false
     */
    dl.check.isArray = function (obj) {
        // backward compatibility
        if (typeof Array.isArray === 'undefined') {
            return Object.prototype.toString.call(obj) === '[object Array]';
        };

        return Array.isArray(obj);
    };

    dl.check.isFunction = function (checker) {
        return typeof checker === 'function';
    };

    dl.check.isDefined = function (checker) {
        return typeof checker !== 'undefined' && checker != null;
    };

    dl.check.isEmptyObject = function (checker) {
        return checker && // null and undefined check
            Object.keys(checker).length === 0 && // length check
            checker.constructor === Object; // is object
    };

    dl.check.isNumber = function (checker) {
        return !isNaN(checker) && checker != null;
    };

    dl.check.arrGetMin = function (arr) {
        var min = arr[0];
        for (var i = 0, iLength = arr.length; i < iLength; i++) {
            if (arr[i] < min) {
                min = arr[i];
            }
        }
        return min;
    };

    dl.check.arrGetMax = function (arr) {
        var max = arr[0];
        for (var i = 0, iLength = arr.length; i < iLength; i++) {
            if (arr[i] > max) {
                max = arr[i];
            }
        }
        return max;
    };

    dl.check.arrConsecutive = function (arr) {
        if (arr.length < 1) return false;
        var min = this.arrGetMin(arr);
        var max = this.arrGetMax(arr);
        if ((max - min + 1) == arr.length) {
            var visited = {};
            for (var i = 0, iLength = arr.length; i < iLength; i++) {
                if (visited[arr[i]]) {
                    return false;
                }

                visited[arr[i]] = true;
            }

            return true;
        }
        return false;
    };
});
