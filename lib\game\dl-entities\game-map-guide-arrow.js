ig.module(
    'game.dl-entities.game-map-guide-arrow'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker'
).defines(function () {
    dl.EntityGameMapGuideArrow = dl.Entity
        .extend(dl.MixinDocker)
        .extend({
            staticInstantiate: function () {
                this.parent();
                this.gameManager = ig.game.managers.game;
                var playerNumber = ig.game.client.clientGameRoom.getPlayerNumber(ig.game.client.playerId);
                if (playerNumber === 1) {
                    this.arrowImage = dl.preload['guide-arrows'].player1;
                    this.arrowStick = dl.preload['guide-arrows-stick'].player1;
                    this.circleColor='#a92c35';
                } else if (playerNumber === 2) {
                    this.arrowImage = dl.preload['guide-arrows'].player2;
                    this.arrowStick = dl.preload['guide-arrows-stick'].player2;
                    this.circleColor='#135077';
                } else if (playerNumber === 3) {
                    this.arrowImage = dl.preload['guide-arrows'].player3;
                    this.arrowStick = dl.preload['guide-arrows-stick'].player3;
                    this.circleColor='#5aff8c';
                } else if (playerNumber === 4) {
                    this.arrowImage = dl.preload['guide-arrows'].player4;
                    this.arrowStick = dl.preload['guide-arrows-stick'].player4;
                    this.circleColor='#5aff8c';
                }
                this.maxDrag = this.gameManager.guideArrowAttribs.maxDrag;
                this.minDrag = this.gameManager.guideArrowAttribs.minDrag;
                return this;
            },
    
            postInit: function () {
                this.parent();
            },

            update: function () {
                this.parent();
                if (this.gameManager.guideArrowAttribs.isActive) {
                    if (this.gameManager.guideArrowAttribs.currentDrag) this.currentDrag = this.gameManager.guideArrowAttribs.currentDrag;
                    if (this.gameManager.guideArrowAttribs.dragAngle) this.dragAngle = this.gameManager.guideArrowAttribs.dragAngle;
                    if (this.gameManager.guideArrowAttribs.pos) this.pos = this.gameManager.guideArrowAttribs.pos;
                } else {
                    this.currentDrag = 0;
                    this.dragAngle = 0;
                }
            },

            draw: function (ctx) {
                this.parent(ctx);
                if (this.gameManager.guideArrowAttribs.isActive && this.gameManager.guideArrowAttribs.currentDrag > this.minDrag) {
                    var sizeOffset = this.gameManager.guideArrowAttribs.sizeOffset;
                    ctx.save();
                    ctx.translate((this.pos.x - sizeOffset.x * 0.5) + 5, (this.pos.y - sizeOffset.y * 0.5 - 3));
                    ctx.shadowOffsetX = 0;
                    ctx.shadowOffsetY = 0;
                    ctx.shadowColor = 'white';
                    ctx.shadowBlur = 30;
                    ctx.rotate(this.dragAngle);
                    this.arrowStick.drawImage(
                        this.radius,
                        -this.arrowStick.height * 0.5,
                        this.currentDrag-30,
                        this.arrowStick.height
                    );

                    ctx.shadowBlur = 0;

                    ctx.fillStyle=this.circleColor;
                    ctx.beginPath();
                    ctx.arc(0,0, 20, 0, 2 * Math.PI);
                    ctx.fill();
                    ctx.stroke();

                    this.arrowImage.drawImage(
                        this.currentDrag-50,
                        -this.arrowStick.height/2-10,
                        50,
                        this.arrowImage.height
                    );

                    ctx.restore();
                }
            }
        });

    // Enable cache
    // dl.enableCacheCanvas(dl.EntityGameMapContinent);
});
