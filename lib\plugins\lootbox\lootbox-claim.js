ig.module('plugins.lootbox.lootbox-claim')
    .requires(
        'plugins.lootbox.lootbox-game-object'
    )
    .defines(function () {
        ig.LootboxClaim = ig.LootboxGameObject.extend({

            zIndex: 9999,
            onBack: null,
            deck: null,
            title: null,
            actionButton: null,
            messageTextField: null,

            init: function (x, y, settings) {
                this.parent(x, y, settings);
                ig.Lootbox.loadData();
                this.forceDraw = true;
                this.onBack = new ig.LootboxSignal();

                var pageW = ig.responsive ? ig.responsive.originalWidth : ig.system.width;
                var pageH = ig.responsive ? ig.responsive.originalHeight : ig.system.height;

                if (this.customClaim) {
                    var closedImage = ig.Lootbox.images.boxPremiumClosed;
                    var openImage = ig.Lootbox.images.boxPremiumOpen;
                    if (this.customClaimBoxType == "free") {
                        closedImage = ig.Lootbox.images.boxFreeClosed;
                        openImage = ig.Lootbox.images.boxFreeOpen;
                    }
                    this.spawnCustomCards(closedImage, openImage, this.customClaimCardCount)

                    ig.game.sortEntitiesDeferred();
                    return;
                }

                var titleH = Math.floor(pageH * 0.09);
                this.title = ig.game.spawnEntityBackward(ig.LootboxTextField, pageW / 2, ig.Lootbox.page.titleY, { font: ig.Lootbox.page.titleFont, text: ig.Lootbox.strings.lootboxTitle, align: "center", color: "#ffffff", zIndex: 99999 });

                var fontSize = Math.floor(ig.Lootbox.images.simple.height * 0.5)

                this.backButton = ig.game.spawnEntityBackward(ig.LootboxSimpleButton, ig.Lootbox.images.back.width * 0.6, ig.Lootbox.images.back.height * 0.6, { image: ig.Lootbox.images.back, zIndex: 99999 });
                this.backButton.onClicked.addOnce(this.onClickBack, this)

                // var fontSize = Math.floor(ig.Lootbox.images.simple.height * 0.3)
                // this.messageTextField = ig.game.spawnEntityBackward(ig.LootboxTextField, this.actionButton.pos.x, this.actionButton.pos.y - this.backButton.height * 0.8, { font: fontSize + "px " + ig.Lootbox.card.font, text: ig.Lootbox.strings.assemblyMessage, align: "center", color: "#ffffff", zIndex: 99999 });

                if (ig.responsive) {
                    // this.messageTextField.anchoredPositionX = this.actionButton.anchoredPositionX;
                    // this.messageTextField.anchoredPositionY = this.actionButton.anchoredPositionY - this.backButton.height * 0.8;
                    this.backButton.anchorType = "top-left"
                }


                this.freeCard = ig.game.spawnEntityBackward(ig.LootboxCard, pageW / 2 - ig.Lootbox.card.width * 1.15, pageH / 2, {
                    isCustomCard: true,
                    customImage: ig.Lootbox.images.boxFreeClosed,
                    customName: ig.Lootbox.strings.boxFreeName,
                    customDescription: ig.Lootbox.strings.boxFreeDescription,
                    isNameOnTop: true,
                    zIndex: 99999,
                    anchorX: 0.5,
                    anchorY: 0.5,
                    scaleX: 2,
                    scaleY: 2,
                });
                this.premiumCard = ig.game.spawnEntityBackward(ig.LootboxCard, pageW / 2 + ig.Lootbox.card.width * 1.15, pageH / 2, {
                    isCustomCard: true,
                    customImage: ig.Lootbox.images.boxPremiumClosed,
                    customName: ig.Lootbox.strings.boxPremiumName,
                    customDescription: ig.Lootbox.strings.boxPremiumDescription,
                    isNameOnTop: true,
                    zIndex: 99999,
                    anchorX: 0.5,
                    anchorY: 0.5,
                    scaleX: 2,
                    scaleY: 2,
                });

                this.freeButton = ig.game.spawnEntityBackward(ig.LootboxSimpleButton, pageW / 2 - ig.Lootbox.card.width * 1.1, pageH * 0.8, {
                    image: ig.Lootbox.images.simple,
                    font: ig.Lootbox.page.button.font,
                    text: ig.Lootbox.strings.openButton,
                    textColor: ig.Lootbox.page.button.textColor ? ig.Lootbox.page.button.textColor : "#ffffff",
                    offsetY: ig.Lootbox.page.button.offsetY,
                    zIndex: 99999
                });
                this.freeButton.onClicked.add(this.onClickFree, this)
                this.premiumButton = ig.game.spawnEntityBackward(ig.LootboxSimpleButton, pageW / 2 + ig.Lootbox.card.width * 1.1, pageH * 0.8, {
                    text: ig.Lootbox.strings.watchButton,
                    image: ig.Lootbox.images.ad,
                    textColor: ig.Lootbox.page.button.textColor ? ig.Lootbox.page.button.textColor : "#ffffff",
                    offsetX: ig.Lootbox.page.claim.watchButtonOffsetX,
                    offsetY: ig.Lootbox.page.button.offsetY,
                    font: ig.Lootbox.page.button.font,
                    zIndex: 99999
                });
                this.premiumButton.onClicked.add(this.onClickPremium, this)

                // this.actionButton.visible = false;
                ig.game.sortEntitiesDeferred()

                // setTimeout(() => {
                // this.onClickFree();
                //     this.spawnPremiumCards();
                // }, 100);

            },

            spawnCustomCards: function (chestImageClosed, chestImageOpen, cardCount) {
                var pageW = ig.responsive ? ig.responsive.originalWidth : ig.system.width;
                var pageH = ig.responsive ? ig.responsive.originalHeight : ig.system.height;

                this.chestBox = ig.game.spawnEntityBackward(ig.LootboxGameObject, ig.system.width / 2, ig.system.height / 2, {
                    image: chestImageClosed,
                    entryType: "fadeIn",
                    exitType: "fadeOut",
                    exitDelay: 1,
                    anchorX: 0.5,
                    anchorY: 0.5,
                    zIndex: this.zIndex + 1
                });

                ig.game.sortEntitiesDeferred();

                if (ig.responsive) {
                    this.chestBox.setAnchoredPosition(0, 0);
                    this.chestBox.anchorType = "center"
                } else {
                    this.chestBox.pos.x = ig.system.width / 2;
                    this.chestBox.pos.y = ig.system.height / 2;
                }

                this.chestBox.tween({ scaleX: 1.4, scaleY: 1.4, }, 0.5, {
                    easing: ig.Tween.Easing.Back.EaseOut,
                    onComplete: function () {
                        this.chestBox.shake(5, 2);

                        for (var i = 0; i < 100; i++) {
                            if (ig.responsive) ig.game.spawnEntityBackward(ig.LootboxParticleIn, 0, 0, { anchorType: "center", delay: i * 0.006, fillColor: "#fdd600" })
                            else ig.game.spawnEntityBackward(ig.LootboxParticleIn, this.chestBox.pos.x, this.chestBox.pos.y, { delay: i * 0.006, fillColor: "#fdd600" })
                        }

                        this.chestBox.tween({ scaleX: 1.4, scaleY: 1.4, }, 1, {
                            easing: ig.Tween.Easing.Back.EaseOut,
                            onComplete: function () {
                                this.chestBox.shake(10, 10);
                                this.chestBox.exit();

                                for (var i = 0; i < 100; i++) {
                                    if (ig.responsive) ig.game.spawnEntityBackward(ig.LootboxParticleOut, 0, 0, { anchorType: "center", delay: i * 0.002, fillColor: "#fdd600" })
                                    else ig.game.spawnEntityBackward(ig.LootboxParticleOut, this.chestBox.pos.x, this.chestBox.pos.y, { delay: i * 0.006, fillColor: "#fdd600" })
                                }

                                this.chestBox.swapImage(chestImageOpen);
                                ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList[ig.Lootbox.sounds.openPremiumChest]);

                                var rewardIds = []
                                for (var i = 0; i < cardCount; i++) {
                                    rewardIds.push(Math.floor(Math.random() * ig.Lootbox.card.names.length));
                                }

                                this.customRewards = [];
                                var rowCount = Math.ceil(rewardIds.length / 2)
                                var cardW = ig.Lootbox.card.width;
                                var cardH = ig.Lootbox.card.height;
                                var spacing = cardW * 0.02;
                                var cardStartX = pageW / 2 - ((cardW + spacing) * rowCount) / 2;
                                for (var i = 0; i < rewardIds.length; i++) {
                                    var id = rewardIds[i];
                                    var cardX = cardStartX + (cardW + spacing) * (i % rowCount + 0.5);
                                    var cardSpaceY = (cardH + spacing) / 2;
                                    var cardY = pageH / 2 + (i >= rowCount ? cardSpaceY : -cardSpaceY);

                                    var card = ig.game.spawnEntityBackward(ig.LootboxCard, cardX, cardY, {
                                        id: id,
                                        level: ig.LootboxRandom.fromArray(ig.Lootbox.loot.cardRewardLevels),
                                        showExp: false,
                                        zIndex: 999999,
                                        anchorX: 0.5,
                                        anchorY: 0.5,
                                        // alpha: 0,
                                        scaleX: 0,
                                        scaleY: 0,
                                        faceDown: true,
                                        exitType: "fadeOut"
                                    });
                                    this.customRewards.push(card);

                                    if (!ig.Lootbox.isUpgradeMode) ig.Lootbox.addCardData(id, card.level);
                                    card.flip(1 + i * 0.1);
                                    card.tween({ scaleX: 1, scaleY: 1, alpha: 1 }, 0.5, {
                                        delay: 0.5,
                                        easing: ig.Tween.Easing.Back.EaseOut
                                    }).start()
                                }

                                if (!ig.Lootbox.isUpgradeMode) ig.Lootbox.saveData();

                                this.collectButton = ig.game.spawnEntityBackward(ig.LootboxSimpleButton, pageW / 2, pageH * 0.8, {
                                    image: ig.Lootbox.images.simple,
                                    font: ig.Lootbox.page.button.font,
                                    text: ig.Lootbox.strings.collectButton,
                                    textColor: ig.Lootbox.page.button.textColor ? ig.Lootbox.page.button.textColor : "#ffffff",
                                    offsetY: ig.Lootbox.page.button.offsetY,
                                    zIndex: 99999,
                                    entryType: "fadeIn",
                                    entryDelay: 3,
                                    exitType: "fadeOut"
                                });
                                this.collectButton.onClicked.addOnce(function () {
                                    ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList[ig.Lootbox.sounds.button]);
                                    this.collectButton.exit();
                                    this.animateUpgradeModeRewards(this.customRewards, function () {
                                        for (var i = 0; i < this.customRewards.length; i++) {
                                            var item = this.customRewards[i];
                                            item.exit();
                                        }
                                        this.onBack.dispatch();
                                        this.exit();
                                    }.bind(this))
                                }.bind(this), this)
                            }.bind(this)
                        }).start();
                    }.bind(this)
                }).start();
            },

            onClickPremium: function () {
                if (ig.Lootbox.getPremiumBoxCollectionTime() > 0) return;
                this.fadeOutAll();
                ig.game.spawnEntityBackward(ig.LootboxAd, 0, 0, {
                    callback: function (success) {
                        if (success) {
                            console.log("ad success : " + success);
                            this.spawnPremiumCards();
                        } else {
                            this.fadeInAll()
                        }
                    }.bind(this)
                })
                ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList[ig.Lootbox.sounds.button]);
            },

            spawnPremiumCards: function () {
                var pageW = ig.responsive ? ig.responsive.originalWidth : ig.system.width;
                var pageH = ig.responsive ? ig.responsive.originalHeight : ig.system.height;

                this.chestBox = ig.game.spawnEntityBackward(ig.LootboxGameObject, ig.system.width / 2, ig.system.height / 2, {
                    image: ig.Lootbox.images.boxPremiumClosed,
                    entryType: "fadeIn",
                    exitType: "fadeOut",
                    exitDelay: 1,
                    anchorX: 0.5,
                    anchorY: 0.5,
                    zIndex: this.zIndex + 1
                })

                ig.game.sortEntitiesDeferred();

                if (ig.responsive) {
                    this.chestBox.setAnchoredPosition(0, 0);
                    this.chestBox.anchorType = "center"
                } else {
                    this.chestBox.pos.x = ig.system.width / 2;
                    this.chestBox.pos.y = ig.system.height / 2;
                }

                this.chestBox.tween({ scaleX: 1.4, scaleY: 1.4, }, 0.5, {
                    easing: ig.Tween.Easing.Back.EaseOut,
                    onComplete: function () {
                        this.chestBox.shake(5, 2);

                        for (var i = 0; i < 100; i++) {
                            if (ig.responsive) ig.game.spawnEntityBackward(ig.LootboxParticleIn, 0, 0, { anchorType: "center", delay: i * 0.006, fillColor: "#fdd600" })
                            else ig.game.spawnEntityBackward(ig.LootboxParticleIn, this.chestBox.pos.x, this.chestBox.pos.y, { delay: i * 0.006, fillColor: "#fdd600" })
                        }

                        this.chestBox.tween({ scaleX: 1.4, scaleY: 1.4, }, 1, {
                            easing: ig.Tween.Easing.Back.EaseOut,
                            onComplete: function () {
                                this.chestBox.shake(10, 10);
                                this.chestBox.exit();

                                for (var i = 0; i < 100; i++) {
                                    if (ig.responsive) ig.game.spawnEntityBackward(ig.LootboxParticleOut, 0, 0, { anchorType: "center", delay: i * 0.002, fillColor: "#fdd600" })
                                    else ig.game.spawnEntityBackward(ig.LootboxParticleOut, this.chestBox.pos.x, this.chestBox.pos.y, { delay: i * 0.006, fillColor: "#fdd600" })
                                }

                                this.chestBox.swapImage(ig.Lootbox.images.boxPremiumOpen);
                                ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList[ig.Lootbox.sounds.openPremiumChest]);
                                var rewardIds = ig.Lootbox.collectPremiumBox();
                                this.premiumRewards = [];
                                var rowCount = Math.ceil(rewardIds.length / 2)
                                var cardW = ig.Lootbox.card.width;
                                var cardH = ig.Lootbox.card.height;
                                var spacing = cardW * 0.02;
                                var cardStartX = pageW / 2 - ((cardW + spacing) * rowCount) / 2;
                                for (var i = 0; i < rewardIds.length; i++) {
                                    var id = rewardIds[i];
                                    var cardX = cardStartX + (cardW + spacing) * (i % rowCount + 0.5);
                                    var cardSpaceY = (cardH + spacing) / 2;
                                    var cardY = pageH / 2 + (i >= rowCount ? cardSpaceY : -cardSpaceY);

                                    var card = ig.game.spawnEntityBackward(ig.LootboxCard, cardX, cardY, {
                                        id: id,
                                        level: ig.LootboxRandom.fromArray(ig.Lootbox.loot.cardRewardLevels),
                                        showExp: false,
                                        zIndex: 999999,
                                        anchorX: 0.5,
                                        anchorY: 0.5,
                                        // alpha: 0,
                                        scaleX: 0,
                                        scaleY: 0,
                                        faceDown: true,
                                        exitType: "fadeOut"
                                    });
                                    this.premiumRewards.push(card);

                                    if (!ig.Lootbox.isUpgradeMode) ig.Lootbox.addCardData(id, card.level);
                                    card.flip(1 + i * 0.1);
                                    card.tween({ scaleX: 1, scaleY: 1, alpha: 1 }, 0.5, {
                                        delay: 0.5,
                                        easing: ig.Tween.Easing.Back.EaseOut,
                                        onComplete: function () {
                                            // card.flip(0.5);
                                            //     
                                        }.bind(this)
                                    }).start()
                                }

                                if (!ig.Lootbox.isUpgradeMode) ig.Lootbox.saveData();

                                this.collectButton = ig.game.spawnEntityBackward(ig.LootboxSimpleButton, pageW / 2, pageH * 0.8, {
                                    image: ig.Lootbox.images.simple,
                                    font: ig.Lootbox.page.button.font,
                                    text: ig.Lootbox.strings.collectButton,
                                    textColor: ig.Lootbox.page.button.textColor ? ig.Lootbox.page.button.textColor : "#ffffff",
                                    offsetY: ig.Lootbox.page.button.offsetY,
                                    zIndex: 99999,
                                    entryType: "fadeIn",
                                    entryDelay: 3,
                                    exitType: "fadeOut"
                                });
                                this.collectButton.onClicked.addOnce(function () {
                                    ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList[ig.Lootbox.sounds.button]);
                                    this.collectButton.exit();
                                    this.animateUpgradeModeRewards(this.premiumRewards, function () {
                                        for (var i = 0; i < this.premiumRewards.length; i++) {
                                            var item = this.premiumRewards[i];
                                            item.exit();
                                        }
                                        this.fadeInAll();
                                    }.bind(this))
                                }.bind(this), this)
                            }.bind(this)
                        }).start();
                    }.bind(this)
                }).start();
            },

            onClickFree: function () {
                result = ig.Lootbox.collectFreeBox();

                if (result.length > 0) {
                    var pageW = ig.responsive ? ig.responsive.originalWidth : ig.system.width;
                    var pageH = ig.responsive ? ig.responsive.originalHeight : ig.system.height;

                    this.fadeOutAll();
                    this.chestBox = ig.game.spawnEntityBackward(ig.LootboxGameObject, ig.system.width / 2, ig.system.height / 2, {
                        image: ig.Lootbox.images.boxFreeClosed,
                        entryType: "fadeIn",
                        exitType: "fadeOut",
                        exitDelay: 1,
                        anchorX: 0.5,
                        anchorY: 0.5,
                        zIndex: this.zIndex + 1
                    })

                    ig.game.sortEntitiesDeferred();

                    if (ig.responsive) {
                        this.chestBox.setAnchoredPosition(0, 0);
                        this.chestBox.anchorType = "center"
                    } else {
                        this.chestBox.pos.x = ig.system.width / 2;
                        this.chestBox.pos.y = ig.system.height / 2;
                    }

                    this.chestBox.tween({ scaleX: 1.4, scaleY: 1.4, }, 0.5, {
                        easing: ig.Tween.Easing.Back.EaseOut,
                        onComplete: function () {
                            this.chestBox.shake(5, 2);

                            for (var i = 0; i < 100; i++) {
                                if (ig.responsive) ig.game.spawnEntityBackward(ig.LootboxParticleIn, 0, 0, { anchorType: "center", delay: i * 0.006 })
                                else ig.game.spawnEntityBackward(ig.LootboxParticleIn, this.chestBox.pos.x, this.chestBox.pos.y, { delay: i * 0.006 })
                            }


                            this.chestBox.tween({ scaleX: 1.4, scaleY: 1.4, }, 1, {
                                easing: ig.Tween.Easing.Back.EaseOut,
                                onComplete: function () {
                                    this.chestBox.shake(10, 10);
                                    this.chestBox.exit();

                                    for (var i = 0; i < 100; i++) {
                                        if (ig.responsive) ig.game.spawnEntityBackward(ig.LootboxParticleOut, 0, 0, { anchorType: "center", delay: i * 0.002 })
                                        else ig.game.spawnEntityBackward(ig.LootboxParticleOut, this.chestBox.pos.x, this.chestBox.pos.y, { delay: i * 0.002 })
                                    }

                                    this.chestBox.swapImage(ig.Lootbox.images.boxFreeOpen);
                                    ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList[ig.Lootbox.sounds.openFreeChest]);
                                    this.freeReward = ig.game.spawnEntityBackward(ig.LootboxCard, pageW / 2, pageH / 2, {
                                        id: result[0],
                                        level: ig.LootboxRandom.fromArray(ig.Lootbox.loot.cardRewardLevels),
                                        showExp: false,
                                        zIndex: 999999,
                                        anchorX: 0.5,
                                        anchorY: 0.5,
                                        // alpha: 0,
                                        scaleX: 0,
                                        scaleY: 0,
                                        faceDown: true,
                                        exitType: "fadeOut"
                                    });

                                    if (!ig.Lootbox.isUpgradeMode) {
                                        ig.Lootbox.addCardData(result[0], this.freeReward.level);
                                        ig.Lootbox.saveData();
                                    }
                                    this.freeReward.tween({ scaleX: 2, scaleY: 2, alpha: 1 }, 0.5, {
                                        delay: 0.8,
                                        easing: ig.Tween.Easing.Back.EaseOut,
                                        onComplete: function () {
                                            this.freeReward.flip(0.5);
                                            this.collectButton = ig.game.spawnEntityBackward(ig.LootboxSimpleButton, pageW / 2, pageH * 0.8, {
                                                image: ig.Lootbox.images.simple,
                                                font: ig.Lootbox.page.button.font,
                                                textColor: ig.Lootbox.page.button.textColor ? ig.Lootbox.page.button.textColor : "#ffffff",
                                                offsetY: ig.Lootbox.page.button.offsetY,
                                                text: ig.Lootbox.strings.collectButton,
                                                zIndex: 99999,
                                                entryType: "fadeIn",
                                                entryDelay: 1,
                                                exitType: "fadeOut"
                                            });
                                            this.collectButton.onClicked.addOnce(function () {
                                                ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList[ig.Lootbox.sounds.button]);
                                                this.collectButton.exit();
                                                this.animateUpgradeModeRewards([this.freeReward], function () {
                                                    this.freeReward.exit();
                                                    this.fadeInAll();
                                                }.bind(this));
                                            }.bind(this), this)
                                        }.bind(this)
                                    }).start()

                                }.bind(this)
                            }).start();

                        }.bind(this)
                    }).start();


                    ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList[ig.Lootbox.sounds.button]);
                }
            },

            animateUpgradeModeRewards: function (cards, callback) {

                var pageW = ig.responsive ? ig.responsive.originalWidth : ig.system.width;
                var pageH = ig.responsive ? ig.responsive.originalHeight : ig.system.height;

                if (!ig.Lootbox.isUpgradeMode) {
                    if (callback) callback();
                    return;
                }

                this.dummyTargetCard = ig.game.spawnEntityBackward(ig.LootboxCard, pageW / 2, ig.Lootbox.card.height / 2, {
                    id: 0,
                    level: ig.Lootbox.getLevelFromFirstCardWithId(0),
                    exp: ig.Lootbox.getExpFromFirstCardWithId(0),
                    zIndex: 99999,
                    anchorX: 0.5,
                    anchorY: 0.5,
                    // alpha: 0,
                    scaleX: 0,
                    scaleY: 0,
                    faceDown: true,
                    exitType: "fadeOut"
                });
                ig.game.sortEntitiesDeferred();

                this.existingCards = [];
                for (var i = 0; i < cards.length; i++) {
                    var card = cards[i];
                    card.addExpTo(this.dummyTargetCard, 0.5 + i * 0.7)
                }

                this.delayedCall(1 + cards.length * 0.7, function () {
                    if (callback) callback();
                    this.dummyTargetCard.exit();
                }.bind(this));
            },

            onClickBack: function () {
                this.onBack.dispatch();
                this.exitAll();
                ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList[ig.Lootbox.sounds.button]);
            },

            fadeOutAll: function () {
                var members = [
                    this.backButton,
                    this.freeButton,
                    this.premiumButton,
                    this.freeCard,
                    this.premiumCard,
                    this.title,
                ]
                for (var i = 0; i < members.length; i++) {
                    var item = members[i];
                    // item.tween({ alpha: 0 }, 0.2).start();
                    item.visible = false;
                }
            },

            fadeInAll: function () {
                var members = [
                    this.backButton,
                    this.freeButton,
                    this.premiumButton,
                    this.freeCard,
                    this.premiumCard,
                    this.title,
                ]
                for (var i = 0; i < members.length; i++) {
                    var item = members[i];
                    // item.tween({ alpha: 0 }, 0.2).start();
                    item.visible = true;
                }
            },

            exitAll: function () {
                this.backButton.exit();
                this.freeButton.exit();
                this.premiumButton.exit();
                this.freeCard.exit();
                this.premiumCard.exit();
                this.title.exit();
                this.exit();
            },

            update: function () {
                this.parent();
                if (!this.customClaim) {
                    this.freeButton.text = ig.Lootbox.getFreeBoxText();
                    this.premiumButton.text = ig.Lootbox.getPremiumBoxText();
                }
                if (this.dummyTargetCard) {
                    this.dummyTargetCard.scaleX += (1 - this.dummyTargetCard.scaleX) / 2;
                    this.dummyTargetCard.scaleY = this.dummyTargetCard.scaleX;
                    if (ig.responsive) {
                        var targetX = ig.responsive.originalWidth / 2;
                        var targetY = ig.Lootbox.card.height * 0.54;
                        this.dummyTargetCard.anchoredPositionX += (targetX - this.dummyTargetCard.anchoredPositionX) / 2;
                        this.dummyTargetCard.anchoredPositionY += (targetY - this.dummyTargetCard.anchoredPositionY) / 2;
                    } else {
                        var targetX = ig.system.width / 2;
                        var targetY = ig.Lootbox.card.height * 0.54;
                        this.dummyTargetCard.pos.x += (targetX - this.dummyTargetCard.pos.x) / 2;
                        this.dummyTargetCard.pos.y += (targetY - this.dummyTargetCard.pos.y) / 2;
                    }

                }
            },
            draw: function () {

                var ctx = ig.system.context;
                ctx.save();
                ctx.fillStyle = ig.Lootbox.overlay.color;
                ctx.globalAlpha = ig.Lootbox.overlay.alpha;
                ctx.fillRect(0, 0, ig.system.width, ig.system.height);
                ctx.globalAlpha = 1;
                ctx.restore();

                this.parent();
            },

        });
    });
