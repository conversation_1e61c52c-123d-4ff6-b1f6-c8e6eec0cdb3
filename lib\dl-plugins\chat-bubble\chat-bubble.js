ig.module(
    'dl-plugins.chat-bubble.chat-bubble'
).requires(
    'dl.game.entity',
    'dl.game.entity.components.position.docker',
    'dl-plugins.chat-bubble.chat-bubble-factory'
).defines(function () {
    'use strict';

    dl.ChatBubble.Entity = dl.Entity
        .extend({
            staticInstantiate: function () {
                this.parent();

                this.STATES = {
                    INIT: 0,
                    TWEEN_IN: 1,
                    TWEEN_OUT: 2,
                    ALIVE: 3,
                    KILL: 4
                };
                this.currentState = this.STATES.INIT;

                this.chatBubbleDrawConfigs = {};

                this.chatBubbleAppearTime = 400;
                this.chatBubbleAliveTime = 3000;
                this.chatBubbleDisappearTime = 300;

                this.chatBubbleDocker = null;
                this.chatBubblePercent = { x: 0.5, y: 0.5 };
                this.chatBubbleOffset = { x: 0, y: 0 };
                this.chatBubbleAlpha = 0.8;

                this._tweenProgress = 0;
                this._tweenInstance = null;

                return this;
            },

            initComponents: function () {
                this.parent();

                this._initDockerComponent();
            },

            _initDockerComponent: function () {
                this.c_DockerComponent = this.addComponent(dl.DockerComponent, {
                    dockerObject: this.chatBubbleDocker,
                    dockerPercent: { x: 0.5, y: 0.5 },
                    dockerOffset: { x: 0, y: 0 }
                });
            },

            postInit: function () {
                this.parent();

                if (!this.chatBubbleDrawConfigs) this.chatBubbleDrawConfigs = {};
                this.chatBubbleDrawConfigs.direction = this.chatBubbleDirection;

                var canvasData = dl.ChatBubble.FactoryManager.generateCanvas(this.chatBubbleDrawConfigs);
                this._chatBubbleCanvasConfigs = canvasData.configs;
                this._chatBubbleCanvas = canvasData.canvas;

                this.setSize(this._chatBubbleCanvas.width * 1.2, this._chatBubbleCanvas.height * 1.2); // addition 1.2 space for tween effect
                this.updateDirection();

                this.switchState(this.STATES.TWEEN_IN);
            },

            closeChatBubble: function () {
                if (this.currentState == this.STATES.KILL) return;

                this.switchState(this.STATES.KILL);
            },

            updateDirection: function () {
                this.c_DockerComponent.dockerObject = this.chatBubbleDocker;
                this.c_DockerComponent.dockerPercent = { x: this.chatBubblePercent.x, y: this.chatBubblePercent.y };
                this.c_DockerComponent.dockerOffset = { x: this.chatBubbleOffset.x, y: this.chatBubbleOffset.y };
                this.anchor = { x: this._chatBubbleCanvasConfigs.bubbleConfigs.tail.direction.x, y: this._chatBubbleCanvasConfigs.bubbleConfigs.tail.direction.y };

                this.updatePos();
            },

            switchState: function (newState) {
                this.currentState = newState;

                if (this._tweenInstance) this._tweenInstance.stop();

                switch (newState) {
                    case this.STATES.TWEEN_IN: {
                        this._tweenInstance = this.createTween()
                            .to({
                                _tweenProgress: 1
                            }, this.chatBubbleAppearTime, {
                                easing: dl.TweenEasing.Back.EaseOut,
                                onPropertiesChanged: function () {
                                    this.cacheRequestRedraw();
                                }.bind(this),
                                onCompleteAction: function () {
                                    this.switchState(this.STATES.ALIVE);
                                }.bind(this)
                            })
                            .start();
                    } break;

                    case this.STATES.ALIVE: {
                        this._tweenInstance = this.createTween()
                            .to({}, this.chatBubbleAliveTime,
                                {
                                    onCompleteAction: function () {
                                        this.switchState(this.STATES.TWEEN_OUT);
                                    }.bind(this)
                                })
                            .start();
                    } break;

                    case this.STATES.TWEEN_OUT: {
                        this._tweenInstance = this.createTween()
                            .to({
                                _tweenProgress: 0
                            }, this.chatBubbleDisappearTime, {
                                easing: dl.TweenEasing.Back.EaseIn,
                                onPropertiesChanged: function () {
                                    this.cacheRequestRedraw();
                                }.bind(this),
                                onCompleteAction: function () {
                                    this.switchState(this.STATES.KILL);
                                }.bind(this)
                            })
                            .start();
                    } break;

                    case this.STATES.KILL: {
                        this.kill();
                    } break;
                }
            },

            draw: function (ctx) {
                this.parent(ctx);

                var scale = {
                    x: 1 * this._tweenProgress,
                    y: 1 * this._tweenProgress
                };

                ctx.save();
                // alpha
                ctx.globalAlpha = this.chatBubbleAlpha * this._tweenProgress;
                // translate
                ctx.translate(
                    this._chatBubbleCanvas.width * 0.5 * (-1 + 2 * this.anchor.x) * (1 - scale.x),
                    this._chatBubbleCanvas.height * 0.5 * (-1 + 2 * this.anchor.y) * (1 - scale.y));
                // scale
                ctx.translate(this.pos.x, this.pos.y);
                ctx.scale(scale.x, scale.y);
                ctx.translate(-this.pos.x, -this.pos.y);

                // draw canvas
                ctx.drawImage(this._chatBubbleCanvas,
                    this.pos.x - this._chatBubbleCanvas.width * 0.5,
                    this.pos.y - this._chatBubbleCanvas.height * 0.5);

                ctx.restore();
            }
        });

    dl.enableCacheCanvas(dl.ChatBubble.Entity);
});
