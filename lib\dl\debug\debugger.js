ig.module(
    'dl.debug.debugger'
).requires(
    'dl.debug.debug',
    'dl.debug.performances',
    'dl.debug.draw'
).defines(function () {
    'use strict';

    dl.debug.SKIP_TIME = !true;
    dl.debug.enableDebugFeature = function () {
        // if (typeof dl.debug.enableDebugDraw === "function") {
        //     dl.debug.enableDebugDraw();
        // }

        // if (typeof dl.debug.enableDebugPerformance === "function") {
        //     dl.debug.enableDebugPerformance();
        // }
    };
});
