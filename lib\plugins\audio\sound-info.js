/**
 *  <PERSON><PERSON><PERSON><PERSON>
 *
 *  Created by <PERSON> on 2014-08-19.
 *  Copyright (c) 2014 __MyCompanyName__. All rights reserved.
 */

ig.module('plugins.audio.sound-info')
.requires(
)
.defines(function () {

    SoundInfo = ig.Class.extend({
		FORMATS:{
			OGG:".ogg",
			MP3:".mp3",
		},
        
		/**
		* Define your sounds here
		* 
        */
		sfx: {
			logosplash1: { path: "media/audio/opening/logosplash1" },
			logosplash2: { path: "media/audio/opening/logosplash2" },
			staticSound: { path: "media/audio/play/static" },
			rocketlaunch: { path: "media/audio/game/rocket_launch" },
			explotion: { path: "media/audio/game/explotion" },
			training_soldier: { path: "media/audio/game/training_soldier" },
			thunder: { path: "media/audio/game/thunder" },
			radio: { path: "media/audio/game/radio" },
			attack1: { path: "media/audio/game/attack/attack1" },
			attack2: { path: "media/audio/game/attack/attack2" },
			attack3: { path: "media/audio/game/attack/attack3" },
			attack4: { path: "media/audio/game/attack/attack4" },
			attack5: { path: "media/audio/game/attack/attack5" },
			attack6: { path: "media/audio/game/attack/attack6" },
			capture1: { path: "media/audio/game/capture/capture1" },
			capture2: { path: "media/audio/game/capture/capture2" },
			capture3: { path: "media/audio/game/capture/capture3" },
			"under-attack1" : { path: "media/audio/game/under-attack/under-attack1" },
			"under-attack2" : { path: "media/audio/game/under-attack/under-attack2" },
			"under-attack3" : { path: "media/audio/game/under-attack/under-attack3" },
			"under-attack4" : { path: "media/audio/game/under-attack/under-attack4" },
			achievementReward:{path:"media/audio/reward-claimed"},
			coins1:{path:"media/audio/coins1"},
			shoot1:{path:"media/audio/shoot1"},
			click2:{path:"media/audio/click2"},
			countdown1:{path:"media/audio/count-down-1"},
			countdown2:{path:"media/audio/count-down-2"},
			countdown3:{path:"media/audio/count-down-3"},
			countdownfight:{path:"media/audio/count-down-fight"},
			notification:{path:"media/audio/notification"},
			coin2:{path:"media/audio/coin2"},
			result:{path:"media/audio/result"}
		},
		
        /**
        * Define your BGM here
        */
		bgm:{
			background:{path:'media/audio/bgm',startOgg:0,endOgg:21.463,startMp3:0,endMp3:21.463}
		}
        
		
    });

});
