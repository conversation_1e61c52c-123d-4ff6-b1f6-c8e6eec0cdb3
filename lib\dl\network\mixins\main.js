/**
 * Created by <PERSON><PERSON>
 * Usage:
 * ig.Game.extend(ig.MixinNetworkGame);
 */
ig.module(
    'dl.network.mixins.main'
).requires(
    'dl.dl',
    'dl.network.client.network-client',
    'dl.network.client.client'
).defines(function () {
    'use strict';

    dl.MixinNetworkGame = {
        resetApp: function () {
            // ig.game.director.jumpTo(LevelTitle);
            ig.game.director.loadLevel(1);
        },

        dlInit: function () {
            this.parent();
            this.initNetwork();
        },

        /**
         * overriding MyGame
         */
        initData: function () {
            var parentData = this.parent();

            if (typeof parentData.playerAvatarId === 'undefined') parentData.playerAvatarId = null;
            if (typeof parentData.playerName === 'undefined') parentData.playerName = null;

            return parentData;
        },

        /**
         * overriding MyGame
         */
        loadData: function () {
            this.parent();

            if (typeof NameAndAvatar === 'undefined') {
                throw 'NameAndAvatar is undefined!';
            }

            if (ig.game.sessionData.playerAvatarId == null) {
                ig.game.sessionData.playerAvatarId = NameAndAvatar.randomAvatar();
            }

            if (ig.game.sessionData.playerName == null) {
                ig.game.sessionData.playerName = NameAndAvatar.randomName(ig.game.sessionData.playerAvatarId);
            }

            ig.game.saveAll();
        },

        initNetwork: function () {
            this.networkClient = new NetworkClient();
            this.client = new Client(this.networkClient);
        },

        closeNetwork: function () {
            if (this.networkClient) {
                this.networkClient.disconnect();
                this.networkClient = null;
            }
            if (this.client) {
                this.client = null;
            }
        },

        update: function () {
            if (this.networkClient) this.networkClient.pingServer();
            if (this.client && this.client.clientGameRoom) this.client.clientGameRoom.update();

            this.parent();
        }
    };
});
