/**
 * Chat bubble plugins
 * Created by: <PERSON><PERSON>/

/**
 * Usage:
 * In the entity that will spawn chat bubble
 *      Add 'dl-plugins.chat-bubble.chat-bubble-mixin' in requires
 *      Extend dl.ChatBubble.Mixin: .extend(dl.ChatBubble.Mixin)
 *      To spawn chat bubble, call:
 *              dl.scene.spawnChatBubble(EntityChatBubbleConfigs);
 *          With:
 *              EntityChatBubbleConfigs: configs for chat bubble
 */
dl.scene.spawnChatBubble({
    chatBubbleDrawConfigs: {
        textConfigs: {
            text: 'This is<br>Bubble Chat', // text display in chat bubble
            fillStyle: 'black',
            textAlign: 'center', // [center|left|right];
            fontSize: 24,
            fontFamily: 'Arial'
        },
        avatarConfigs: {
            image: null, // image display in chat bubble
            size: { x: 100, y: 100 }, // image size
            padding: { x: 4, y: 4 } // extra space outside image
        },
        bubbleConfigs: {
            lineWidth: 2,
            fillStyle: 'lightblue',
            strokeStyle: 'black',

            shadowColor: 'black',
            shadowBlur: 4,
            shadowOffsetX: 4,
            shadowOffsetY: 4,

            box: {
                width: 200, // content min width
                height: 100, // content min height
                round: 10, // round curves distance
                padding: { x: 10, y: 10 } // extra space outside the content area
            },
            tail: {
                length: 30, // tail length
                width: 15, // tail width
                direction: { x: 0.5, y: 1 } // tail direction, will be update if input invalid (0-1)
            }
        }
    },
    chatBubbleAppearTime: 400, // appear time - millisecond
    chatBubbleAliveTime: 3000, // alive time - millisecond
    chatBubbleDisappearTime: 200, // disappear time - millisecond
    chatBubbleDocker: null, // position percent of ChatBubbleDockerEntity
    chatBubblePercent: { x: 0.5, y: 0 }, // position percent of ChatBubbleDockerEntity (0-1) related to the ChatBubbleDockerEntity position and size
    chatBubbleOffset: { x: 0, y: 0 }, // extra offset from position percent of ChatBubbleDockerEntity
    chatBubbleAlpha: 0.8 // chat bubble alpha
});
