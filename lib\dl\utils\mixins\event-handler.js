ig.module(
  'dl.utils.mixins.event-handler'
).defines(function () {
  'use strict';
  
  function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

  function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

  function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

  function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }

  function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

  function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }

  dl.EventHandlerMixin = {
    isDefined: function isDefined(checker) {
      return typeof checker !== "undefined" && checker != null;
    },

    /**
     * Check if event with bound callback is duplicated.
     * @param {*} eventName 
     * @param {*} callback must be bound
     * @returns true if duplicated
     */
    checkDuplicatedEvent: function checkDuplicatedEvent(eventName, callback) {
      if (!this.isDefined(eventName)) throw "EventName is not define";
      if (!this.isDefined(callback)) throw "Callback is not define";
      var handlers = this.eventHandlers[eventName];
      if (!handlers) return false;

      for (var i = 0; i < handlers.length; i++) {
        if (handlers[i].callback == callback) {
          return true;
        }
      }

      return false;
    },

    /**
     * Subscribe to event
     * @param {*} eventName 
     * @param {*} callback must be bound 
     * @returns true if subscribed
     */
    onEvent: function onEvent(eventName, callback, clearTag) {
      if (!this.isDefined(this.eventHandlers)) this.eventHandlers = {};
      if (this.checkDuplicatedEvent(eventName, callback)) return false;
      if (!this.isDefined(this.eventHandlers[eventName])) this.eventHandlers[eventName] = [];
      if (!this.isDefined(clearTag)) clearTag = "AUTO";
      this.eventHandlers[eventName].push({
        callback: callback,
        clearTag: clearTag
      });
      return true;
    },

    /**
     * Cancel the event subscription
     * @param {*} eventName 
     * @param {*} callback must be bound
     */
    offEvent: function offEvent(eventName, callback) {
      if (!this.isDefined(eventName)) throw "EventName is not define";
      if (!this.isDefined(callback)) throw "Callback is not define";
      if (!this.isDefined(this.eventHandlers)) return false;
      var handlers = this.eventHandlers[eventName];
      if (!handlers) return false;

      for (var i = 0; i < handlers.length; i++) {
        if (handlers[i].callback === callback) {
          handlers.splice(i--, 1);
        }
      }

      return true;
    },

    /**
     * Trigger an event with the given name and data
     * @param {*} eventName 
     * @param  {...any} args data pass to bound callback
     */
    triggerEvent: function triggerEvent(eventName) {
      var _this = this;

      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
        args[_key - 1] = arguments[_key];
      }

      if (!this.isDefined(eventName)) throw "EventName is not define";
      if (!this.isDefined(this.eventHandlers)) return false;
      var handlers = this.eventHandlers[eventName];
      if (!handlers) return false; // call the handlers

      handlers.forEach(function (handlerData) {
        return handlerData.callback.apply(_this, args);
      });
    },
    clearEvent: function clearEvent(clearTag) {
      if (!this.isDefined(this.eventHandlers)) return false;
      if (!this.isDefined(clearTag)) clearTag = "AUTO";

      for (var _i = 0, _Object$entries = Object.entries(this.eventHandlers); _i < _Object$entries.length; _i++) {
        var _Object$entries$_i = _slicedToArray(_Object$entries[_i], 2),
          eventName = _Object$entries$_i[0],
          eventHandler = _Object$entries$_i[1];

        this.eventHandlers[eventName] = eventHandler.filter(function (handlerData) {
          return handlerData.clearTag != clearTag;
        });
      }
    }
  };
  /**
   * Usage:
      dl.addEventHandlerMixin(ObjectClass);
        this.onEvent("eventName", handler);
      this.offEvent("eventName", handler);
      this.triggerEvent("eventName", data1, data2);
   *
   */

  dl.addEventHandlerMixin = function (objectClass) {
    Object.assign(objectClass.prototype, dl.EventHandlerMixin);
  };
});
