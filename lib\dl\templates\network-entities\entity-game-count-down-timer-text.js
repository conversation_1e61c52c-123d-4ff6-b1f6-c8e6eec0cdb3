ig.module(
    'dl.templates.network-entities.entity-game-count-down-timer-text'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.text'
).defines(function () {
    'use strict';

    dl.EntityGameCountDownTimerText = dl.Entity
        .extend(dl.MixinDocker)
        .extend(dl.MixinText)
        .extend({
            staticInstantiate: function () {
                this.parent();
                setTimeout(function(){
                    this.startCountDown(3);
                }.bind(this),600);         
                return this;
            },

            _initTextComponent: function () {
                this.c_TextComponent.updateProperties({
                    textAlign: 'center',
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                    fontSize: 260
                });
            },
            startCountDown:function(time){
                if(time==1){
                    var text = '1';
                    ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.countdown1);
                }else if(time==2){
                    var text = '2';
                    ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.countdown2);
                }else if(time==3){
                    var text = '3';
                    ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.countdown3);
                }else if(time==0){
                    var text = 'Fight';
                    ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.countdownfight);
                } 

                this.updateText(text);
                dl.TweenTemplate.scaleIn(this, 500);

                setTimeout(function(){
                    var next = time-1;
                    if(next>=0) {
                        this.startCountDown(next)
                    }else{
                        setTimeout(function(){
                            this.updateText('');
                            ig.game.managers.game.enableControls = true;
                        }.bind(this),500)                        
                    }
                }.bind(this),1000)
            },

        });
});
