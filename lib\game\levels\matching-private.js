ig.module(
    'game.levels.matching-private'
).requires(
    'game.levels.matching',
    'dl.templates.network-entities.entity-popup-share'
).defines(function () {
    LevelMatchingPrivate = {
        entities: [
            { type: 'EntityMatchingPrivateController', x: 0, y: 0 }
        ],
        layer: [],
        useCustomLib: true
    };

    EntityMatchingPrivateController = EntityMatchingController.extend({
        initEntities: function () {
            this.parent();
            this.initRoomCode();
        },

        initMainButtons: function () {
            this.parent();

            this.btnShare = this.spawnEntity(dl.EntityButtonImageText, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0.5, y: 0.84 },
                    dockerOffset: { x: 0, y: 0 }
                },
                c_AnimationSheetComponent: {
                    _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                    _size: {x:400,y:90}
                },
                c_TextComponent: {
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                    fontSize: 40,
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name
                },
                image: dl.preload['button-red-small'],
                text: _STRINGS.NETWORK.MATCHING_BUTTON_SHARE,
                onButtonClicked: function () {
                    this.share();
                }.bind(this)
            });
        },

        initRoomCode: function () {
            this.roomCode = this.spawnEntity(dl.EntityRoomCode, {
                c_DockerComponent: {
                    dockerObject: this.btnShare,
                    dockerPercent: { x: 0.5, y: 1 },
                    dockerOffset: { x: 0, y: 40 }
                },
                anchor: { x: 0.5, y: 0 }
            });
        },

        share: function () {
            this.sharePopup = this.spawnEntity(dl.EntityPopupShare, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0.5, y: 0.5 },
                    dockerOffset: { x: 0, y: 0 }
                }
            });
        },

        disableAll: function () {
            this.parent();

            this.btnShare.setEnable(false);
        },

        tweenIn: function (callback) {
            var time = dl.configs.getConfig('MATCHING_LEVEL_TWEEN_TIME', 'IN');

            dl.TweenTemplate.fadeIn(this.btnShare, time);
            dl.TweenTemplate.fadeIn(this.roomCode, time);

            this.parent(callback);
        },

        tweenOut: function (callback) {
            var time = dl.configs.getConfig('MATCHING_LEVEL_TWEEN_TIME', 'OUT');

            dl.TweenTemplate.fadeOut(this.btnShare, time);
            dl.TweenTemplate.fadeOut(this.roomCode, time);

            this.parent(callback);
        }
    });
});
