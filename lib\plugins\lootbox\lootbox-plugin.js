ig.module('plugins.lootbox.lootbox-plugin')
    .requires(
        'plugins.lootbox.lootbox-settings',
        'plugins.lootbox.lootbox-game-object',
        'plugins.lootbox.lootbox-card',
        'plugins.lootbox.lootbox-deck-display',
        'plugins.lootbox.lootbox-card-collection',
        'plugins.lootbox.lootbox-claim',
        'plugins.lootbox.lootbox-card-assembly',
        'plugins.lootbox.lootbox-ad',
        'plugins.lootbox.lootbox-notification-dot'
    )
    .defines(function () {

        ig.Lootbox = {
            data: {
                cards: [],
                lastFreeBoxCollectTime: 0,
                lastPremiumBoxCollectTime: 0,
            },

            allAccess: function () {
                if (ig.Lootbox.isUpgradeMode) this.data.cards = [];
                for (var i = 0; i < this.card.icons.length; i++) {
                    this.addCardData(i, this.loot.actionableCollectionLevel);
                }
                this.saveData();
            },

            addCardData: function (id, level, exp) {
                if (!exp) exp = 0;
                this.data.cards.push({ id: id, level: level, exp: exp });
            },

            addCardDataThenSave: function (id, level, exp) {
                this.addCardData(id, level, exp);
                this.saveData();
            },

            levelUpCard: function (id) {
                if (this.getExpProgress < 1) return;

                for (var i = 0; i < this.data.cards.length; i++) {
                    var card = this.data.cards[i];
                    if (card.id == id) {
                        card.exp -= ig.Lootbox.upgradeRequirements[card.level + 1];
                        card.level++;
                    }
                }
            },

            getExpProgress: function (id) {
                for (var i = 0; i < this.data.cards.length; i++) {
                    var card = this.data.cards[i];
                    if (card.id == id) return card.exp / ig.Lootbox.upgradeRequirements[card.level];
                }
                return 0;
            },

            getFreeBoxText: function () {
                var time = this.getFreeBoxCollectionTime();
                if (time <= 0) {
                    return this.strings.openButton;
                }
                return this.convertTimeText(time);
            },

            getPremiumBoxText: function () {
                var time = this.getPremiumBoxCollectionTime();
                if (time <= 0) {
                    return this.strings.watchButton;
                }
                return this.convertTimeText(time);
            },

            convertTimeText: function (time) {

                var minutes = Math.floor(time / 60000);
                var seconds = Math.floor((time - minutes * 60000) / 1000)

                if (seconds < 10) seconds = "0" + seconds;
                return minutes + ":" + seconds;
            },

            getFreeBoxCollectionTime: function () {
                var maxMs = this.loot.freeBoxCooldownMinutes * 60 * 1000
                var date = new Date();
                var currentTime = date.getTime();
                var span = currentTime - this.data.lastFreeBoxCollectTime;
                if (span > maxMs) span = maxMs;
                return maxMs - span;
            },

            getPremiumBoxCollectionTime: function () {
                var maxMs = this.loot.premiumBoxCooldownMinutes * 60 * 1000
                var date = new Date();
                var currentTime = date.getTime();
                var span = currentTime - this.data.lastPremiumBoxCollectTime;
                if (span > maxMs) span = maxMs;
                return maxMs - span;
            },

            collectFreeBox: function () {
                if (this.getFreeBoxCollectionTime() > 0) return [];
                this.data.lastFreeBoxCollectTime = new Date().getTime();
                return [Math.floor(Math.random() * this.card.names.length)];
            },

            collectPremiumBox: function () {
                if (this.getPremiumBoxCollectionTime() > 0) return [];
                this.data.lastPremiumBoxCollectTime = new Date().getTime();
                var result = [];
                for (var i = 0; i < 10; i++) {
                    result.push(Math.floor(Math.random() * this.card.names.length));
                }
                return result;
            },

            clearData: function () {
                this.data = {
                    cards: [],
                    lastFreeBoxCollectTime: 0,
                    lastPremiumBoxCollectTime: 0,
                }
                this.saveData();
            },


            saveData: function () {
                var dataString = JSON.stringify(this.data);
                ig.LSLootbox.set(this.saveName, dataString);
            },

            loadData: function () {
                if (!ig.LSLootbox) {
                    ig.LSLootbox = new SecureLS({ encodingType: 'aes' });
                    this.saveName = this.hash(ig.game.name + "-lootbox-save-data", '').replace("-", "s");
                }
                var dataString = ig.LSLootbox.get(this.saveName)
                if (dataString == "") {
                    this.clearData();
                } else {
                    this.data = JSON.parse(dataString);
                }
            },

            isCardActionable: function (id) {
                for (var i = 0; i < this.data.cards.length; i++) {
                    var card = this.data.cards[i];
                    if (card.id == id && card.level >= this.loot.actionableCollectionLevel) {
                        return true
                    }
                }
                return false;
            },

            isCardWithIdAlreadyExist: function (id) {
                for (var i = 0; i < this.data.cards.length; i++) {
                    var card = this.data.cards[i];
                    if (card.id == id) {
                        return true
                    }
                }
                return false;
            },

            getLevelFromFirstCardWithId: function (id) {
                for (var i = 0; i < this.data.cards.length; i++) {
                    var card = this.data.cards[i];
                    if (card.id == id) {
                        return card.level
                    }
                }
                return 0;
            },

            getExpFromFirstCardWithId: function (id) {
                for (var i = 0; i < this.data.cards.length; i++) {
                    var card = this.data.cards[i];
                    if (card.id == id) {
                        return card.exp;
                    }
                }
                return 0;
            },

            incrementExpOfFirstCardWithId: function (id, amount) {
                for (var i = 0; i < this.data.cards.length; i++) {
                    var card = this.data.cards[i];
                    if (card.id == id) {
                        card.exp += amount;
                        // console.log(ig.Lootbox.card.names[id], "exp:", card.exp);
                        return card.exp;
                    }
                }
                return 0;
            },

            sortData: function () {
                this.data.cards.sort(function (a, b) {
                    var diff = a.id - b.id;
                    if (diff == 0) {
                        return a.level - b.level
                    }
                    return diff;
                })
            },

            hash: function (s) {
                var hash = 0, i, chr;
                if (s.length === 0) return hash;
                for (i = 0; i < s.length; i++) {
                    chr = s.charCodeAt(i);
                    hash = ((hash << 5) - hash) + chr;
                    hash |= 0; // Convert to 32bit integer
                }
                var hexHash = hash.toString(36);
                return hexHash;
            }
        }

        ig.initLootboxSettings();


    });
