ig.module(
    'dl.templates.mixins.text'
).requires(
    'dl.game.entity.components.draw.text'
).defines(function () {
    'use strict';

    dl.MixinText = {
        staticInstantiate: function () {
            this.parent();

            this.text = '';
            this._useTextAsSize = false;

            return this;
        },

        postInit: function () {
            this.parent();

            this.updateText(this.text);
        },

        initComponents: function () {
            this.parent();

            this.c_TextComponent = this.addComponent(dl.TextComponent);
            if (this._useTextAsSize) {
                this._useScale = false;
                this.c_TextComponent.onEvent('sizeChanged', this.setSize.bind(this));
            }

            this._initTextComponent();
        },

        _initTextComponent: function () {
            this.c_TextComponent.updateProperties({
                fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                textAlign: 'center', // [center|end|left|right|start];
                textBaseline: 'middle', // [alphabetic|top|hanging|middle|ideographic|bottom];

                fontSize: 24,
                fontFamily: 'Arial',
                maxWidth: undefined,

                _docker: { x: 0.5, y: 0.5 },
                _anchor: { x: 0.5, y: 0.5 },
                _offset: { x: 0, y: 0 }
            });
        },

        updateText: function (newText) {
            this.c_TextComponent.updateProperties({
                text: newText
            });
        },

        updateTextColor: function (newColor) {
            this.c_TextComponent.updateProperties({
                fillStyle: newColor
            });
        }
    };
});
