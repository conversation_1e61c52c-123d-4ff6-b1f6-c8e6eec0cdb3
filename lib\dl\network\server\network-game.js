/**
 * Created by <PERSON><PERSON>
 * Store network game data
 * Create and process event, timers
 */

if (typeof require !== 'undefined') {
    var NetworkGameData = require('../shared/network-game-data.js').NetworkGameData;
    var NetworkGameEvent = require('../shared/network-game-event.js').NetworkGameEvent;
    var NetworkGameEventHandler = require('./network-game-event-handler.js').NetworkGameEventHandler;

    var CountDownTimer = require('../shared/count-down-timer.js').CountDownTimer;
    var RoomEvent = require('../shared/room-event.js').RoomEvent;
}

var NetworkGame = function (room) {
    var self = {};

    self.init = function (room) {
        self.room = room;

        self.networkGameData = new NetworkGameData();
        self.networkGameData.importPlayerList(room.playerList);

        self.countDownTimerList = [];
        self.pendingEvents = [];

        NetworkGameEventHandler.init(self);
    };

    self.start = function () {
        NetworkGameEventHandler.start(self);
    };

    self.update = function () {
        self.networkGameData.lastServerUpdateTime = Date.now();
        self.updateTimers();
        self.handlePendingEvent();
        NetworkGameEventHandler.update(self);
    };

    self.end = function () {
        NetworkGameEventHandler.end(self);
    };

    // ----------------
    // Start event's handler functions
    // ----------------
    self.handlePendingEvent = function () {
        var unhandledPendingEvent = [];
        var event = null;
        for (var i = 0, iLength = self.pendingEvents.length; i < iLength; i++) {
            event = self.pendingEvents[i];
            if (!NetworkGameEventHandler.handleEvent(self, event.event, event.clientPlayerId)) {
                unhandledPendingEvent.push(event);
            }
        }

        self.pendingEvents = unhandledPendingEvent;
    };

    self.addPendingEvent = function (clientPlayerId, event) {
        self.pendingEvents.push({
            clientPlayerId: clientPlayerId,
            event: event
        });
    };

    self.createEvent = function (eventType, eventInfo) {
        if (typeof eventInfo === 'undefined') {
            eventInfo = {};
        }

        var event = new NetworkGameEvent(eventType, Date.now(), eventInfo, self.networkGameData.packData());
        return event;
    };

    self.sendEventToClient = function (event) {
        // update networkGameData before send
        event.networkGameData = self.networkGameData.packData();

        // create room event
        var roomEvent = self.room.createEvent(RoomEvent.EVENT_TYPE.NETWORK_GAME_EVENT, event.packData());
        self.room.applyEvent(roomEvent);
    };

    self.sendSyncSoldierData = function (event) {

        // event.networkGameData = self.networkGameData.packData();
        // create room event
        var roomEvent = self.room.createEvent(RoomEvent.EVENT_TYPE.SYNC_PLAYER_COUNT, event);
        self.room.applyEvent(roomEvent);
    };


    // ----------------
    // End event's handler functions
    // ----------------

    // ----------------
    // Start timer's functions
    // ----------------
    self.startTimer = function (time, callback) {

        var timer = new CountDownTimer(time, callback);
        self.countDownTimerList.push(timer);

        return timer;
    };

    self.updateTimers = function () {
        var cleanFlag = false;
        var timer = null;
        for (var i = 0, iLength = self.countDownTimerList.length; i < iLength; i++) {
            timer = self.countDownTimerList[i];
            timer.update();

            if (timer.timeUp) {
                cleanFlag = true;
            }
        }

        if (cleanFlag) {
            self.countDownTimerList = self.countDownTimerList.filter(function (timer) {
                return !timer.timeUp;
            });
        }
    };
    // ----------------
    // End timer's functions
    // ----------------

    // ----------------
    // Start other functions
    // ----------------
    self.playerLeave = function (playerId) {
        var leavePlayer = self.networkGameData.findPlayerById(playerId);
        if (!leavePlayer) return false;

        leavePlayer.isLeaved = true;
        NetworkGameEventHandler.handleEvent(self, self.createEvent(NetworkGameEvent.EVENT_TYPE.PLAYER_LEAVE, leavePlayer));
        // leavePlayer.botFlag = true;

        return true;
    };
    // ----------------
    // End other functions
    // ----------------

    self.init(room);
    return self;
};

/**
 * Export module
 */
if (typeof module !== 'undefined') {
    module.exports.NetworkGame = NetworkGame;
}
