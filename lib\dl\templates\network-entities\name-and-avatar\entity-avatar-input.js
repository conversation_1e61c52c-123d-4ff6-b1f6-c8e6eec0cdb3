ig.module(
    'dl.templates.network-entities.name-and-avatar.entity-avatar-input'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'dl.templates.entities.entity-image',
    'dl.templates.entities.buttons.entity-button-image'
).defines(function () {
    'use strict';

    dl.EntityAvatarInput = dl.Entity
        .extend(dl.MixinDocker)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this.spacing = 30;

                return this;
            },

            postInit: function () {
                this.parent();

                this.avatar = this.spawnEntity(dl.EntityImage, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    image: dl.preload['name-avatar']['avatar-empty']
                });

                this.btnLeft = this.spawnEntity(dl.EntityButtonImage, {
                    c_DockerComponent: {
                        dockerObject: this.avatar,
                        dockerPercent: { x: 0, y: 0.5 },
                        dockerOffset: { x: -this.spacing, y: 0 }
                    },
                    anchor: { x: 1, y: 0.5 },
                    image: dl.preload['name-avatar']['button-left'],
                    onButtonClicked: function () {
                        this.onChangeAvatar(-1);
                    }.bind(this)
                });

                this.btnRight = this.spawnEntity(dl.EntityButtonImage, {
                    c_DockerComponent: {
                        dockerObject: this.avatar,
                        dockerPercent: { x: 1, y: 0.5 },
                        dockerOffset: { x: this.spacing, y: 0 }
                    },
                    anchor: { x: 0, y: 0.5 },
                    image: dl.preload['name-avatar']['button-right'],
                    onButtonClicked: function () {
                        this.onChangeAvatar(1);
                    }.bind(this)
                });

                this.updateData();
                this.calculateSize();
            },

            onChangeAvatar: function (directionOffset) {
                var currentAvatarId = ig.game.sessionData.playerAvatarId;
                var newAvatarId = currentAvatarId + directionOffset;
                newAvatarId = (newAvatarId + NameAndAvatar.TOTAL_AVATAR) % NameAndAvatar.TOTAL_AVATAR;

                ig.game.sessionData.playerAvatarId = newAvatarId;
                ig.game.saveAll();

                this.updateData();
            },

            updateData: function () {
                this.avatar.updateImage(dl.preload['name-avatar'].avatars[ig.game.sessionData.playerAvatarId]);
            },

            calculateSize: function () {
                this.setSize(
                    this.avatar.size.x + this.btnLeft.size.x + this.btnRight.size.x + this.spacing * 2,
                    this.avatar.size.y
                );
            },

            tweenIn: function (time) {
                dl.TweenTemplate.fadeIn(this.avatar, time);
                dl.TweenTemplate.fadeIn(this.btnRight, time);
                dl.TweenTemplate.fadeIn(this.btnLeft, time);
            },

            tweenOut: function (time) {
                dl.TweenTemplate.fadeOut(this.avatar, time);
                dl.TweenTemplate.fadeOut(this.btnRight, time);
                dl.TweenTemplate.fadeOut(this.btnLeft, time);
            }
        });

    // Enable cache
    dl.enableCacheCanvas(dl.EntityAvatarInput);
});
