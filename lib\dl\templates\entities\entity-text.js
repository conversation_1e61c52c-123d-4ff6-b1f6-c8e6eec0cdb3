ig.module(
    'dl.templates.entities.entity-text'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.text'
).defines(function () {
    'use strict';

    dl.EntityText = dl.Entity
        .extend(dl.MixinDocker)
        .extend(dl.MixinText)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this._useTextAsSize = true;

                return this;
            }
        });

    // Enable cache
    dl.enableCacheCanvas(dl.EntityText);
});
