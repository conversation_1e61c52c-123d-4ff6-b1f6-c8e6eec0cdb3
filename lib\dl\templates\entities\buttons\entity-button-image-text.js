ig.module(
    'dl.templates.entities.buttons.entity-button-image-text'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.button',
    'dl.templates.mixins.animation-sheet',
    'dl.templates.mixins.text',
    'dl.templates.mixins.button-effect'
).defines(function () {
    'use strict';

    dl.EntityButtonImageText = dl.Entity
        .extend(dl.MixinDocker)
        .extend(dl.MixinButton)
        .extend(dl.MixinAnimationSheet)
        .extend(dl.MixinText)
        .extend(dl.MixinButtonScaleEffect)
        .extend(dl.MixinButtonBrightnessEffect)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this._useAnimationSheetAsSize = true;
                this.buttonId=0;
                return this;
            },

            onPointerReleased: function () {
                this.onButtonClicked();
            },

            onButtonClicked: function () {
                dl.warnImplement(this.constructor.name);
            }
        });

    // Enable cache
    dl.enableCacheCanvas(dl.EntityButtonImageText);
});
