ig.module('plugins.achievement.achievement-notification-dot')
    .requires(
        'plugins.achievement.achievement-game-object'
    )
    .defines(function () {
        ig.AchievementNotificationDot = ig.AchievementSimpleButton.extend({

            zIndex: 99999,
            text: "1",
            forceDraw: true,
            dotColor: "#ff7777",
            textColor: "#ffffff",
            type: "assembly",

            init: function (x, y, settings) {
                this.parent(x, y, settings);
                if (!ig.Achievement.saveName) ig.Achievement.loadData();
                this.dotColor = ig.Achievement.notificationDot.color;
                this.textColor = ig.Achievement.notificationDot.textColor;
                this.width = this.height = ig.Achievement.notificationDot.size;
                this.font = ig.Achievement.notificationDot.font;
                this.offsetY = ig.Achievement.notificationDot.textOffsetY;
                this.tempAlpha=this.alpha;
                ig.game.sortEntitiesDeferred();

            },
            value:0,
            update: function () {
                this.parent();

                var claimableCount = 0
                for (var i = 0; i < ig.Achievement.data.achievements.length; i++) {
                    var claimed = ig.Achievement.data.achievements[i].claimed;
                    var achieved = (ig.Achievement.data.achievements[i].progress >= ig.Achievement.list[i].goal)
                    if (achieved && !claimed) claimableCount++;
                }

                this.text = "" + claimableCount;
                if (claimableCount > 0) this.visible = true;
                else this.visible = false;

                this.value = claimableCount;
            },

            drawObject: function (x, y) {
                var ctx = ig.system.context;
                ctx.save();
                var color = ig.hexToRgb(this.dotColor);
                if (this.alpha < 1) {
                    ctx.fillStyle = "rgba(" + color.r + "," + color.g + "," + color.b + "," + this.alpha + ")";
                } else {
                    ctx.fillStyle = color.hex;
                }
                ctx.beginPath();
                ctx.arc(x + this.width / 2, y + this.height / 2, this.width / 2, 0, Math.PI * 2);
                ctx.fill();
                ctx.closePath();
                ctx.restore();
                this.parent(x, y)
            },

        });
    });
