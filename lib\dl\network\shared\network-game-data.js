/**
 * Created by <PERSON><PERSON>
 * Store network game data
 */

if (typeof require !== 'undefined') {
    NetworkGamePlayer = require('./network-game-player.js').NetworkGamePlayer;
    NetworkGameBullet = require('./network-game-bullet.js').NetworkGameBullet;
    NETWORK_GAME_CONFIGS = require('../shared/network-game-configs.js').NETWORK_GAME_CONFIGS;
}

var NetworkGameData = function () {
    var self = {};

    self.init = function () {
        self.playerList = [];

        self.networkGameState = null;
        self.serverStartTime = null;
        self.lastServerUpdateTime = null;

        /* ----- Start custom properties ------ */
        self.mapData = null;
        self.bulletList = [];
        /* ------ End custom properties ------- */

        return self;
    };

    self.packData = function () {
        var data = {};

        data.playerList = [];
        for (var i = 0, iLength = self.playerList.length; i < iLength; i++) {
            data.playerList.push(self.playerList[i].packData());
        }

        data.networkGameState = self.networkGameState;
        data.serverStartTime = self.serverStartTime;
        data.lastServerUpdateTime = self.lastServerUpdateTime;

        /* ----- Start custom properties ------ */
        data.mapData = self.mapData;
        data.bulletList = [];
        for (var i = 0, iLength = self.bulletList.length; i < iLength; i++) {
            data.bulletList.push(self.bulletList[i].packData());
        }
        /* ------ End custom properties ------- */

        return data;
    };

    self.importData = function (data) {
        if (!data) return;

        if (typeof data.playerList !== 'undefined') {
            self.importPlayerList(data.playerList);
        }
        if (typeof data.networkGameState !== 'undefined') {
            self.networkGameState = data.networkGameState;
        }
        if (typeof data.serverStartTime !== 'undefined') {
            self.serverStartTime = data.serverStartTime;
        }
        if (typeof data.lastServerUpdateTime !== 'undefined') {
            self.lastServerUpdateTime = data.lastServerUpdateTime;
        }

        /* ----- Start custom properties ------ */
        if (typeof data.mapData !== 'undefined') {
            self.mapData = data.mapData;
        }
        /* ------ End custom properties ------- */
    };

    self.addPlayer = function (player) {
        if (!player) return null;

        self.playerList.push(player);

        return player.playerId;
    };

    self.findPlayerById = function (playerId) {
        for (var i = 0, il = self.playerList.length; i < il; i++) {
            if (self.playerList[i].playerId == playerId) {
                return self.playerList[i];
            }
        }

        return null;
    };

    self.importPlayerList = function (playerList) {
        self.playerList = [];

        for (var i = 0, il = playerList.length; i < il; i++) {
            var playerData = playerList[i];

            var player = new NetworkGamePlayer(playerData.playerId);
            player.playerIndex = i;
            player.importData(playerData);

            self.addPlayer(player);
        }
    };

    return self.init();
};

NetworkGameData.interpolate = function (start, end, delta) {
    var result = new NetworkGameData();
    result.importData(start);

    result.lastServerUpdateTime = start.lastServerUpdateTime + (end.lastServerUpdateTime - start.lastServerUpdateTime) * delta;
    for (var i = 0, iLength = result.playerList.length; i < iLength; i++) {
        result.playerList[i] = new NetworkGamePlayer();

        var playerData = NetworkGamePlayer.interpolate(start.playerList[i], end.playerList[i], delta, result);
        result.playerList[i].importData(playerData);
    }

    return result;
};

/**
 * Export module
 */
if (typeof module !== 'undefined') {
    module.exports.NetworkGameData = NetworkGameData;
}
