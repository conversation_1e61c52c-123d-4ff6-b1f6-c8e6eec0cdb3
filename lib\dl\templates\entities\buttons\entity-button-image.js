ig.module(
    'dl.templates.entities.buttons.entity-button-image'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.button',
    'dl.templates.mixins.animation-sheet',
    'dl.templates.mixins.button-effect'
).defines(function () {
    'use strict';

    dl.EntityButtonImage = dl.Entity
        .extend(dl.MixinDocker)
        .extend(dl.MixinButton)
        .extend(dl.MixinAnimationSheet)
        .extend(dl.MixinButtonScaleEffect)
        .extend(dl.MixinButtonBrightnessEffect)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this._useAnimationSheetAsSize = true;

                return this;
            },

            onPointerReleased: function () {
                this.onButtonClicked();
            },

            onButtonClicked: function () {
                dl.warnImplement(this.constructor.name);
            }
        });

    // Enable cache
    dl.enableCacheCanvas(dl.EntityButtonImage);
});
