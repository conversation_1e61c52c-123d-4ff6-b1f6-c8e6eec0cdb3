ig.module(
    'game.dl-entities.game-map-capital'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.circle-button'
).defines(function () {
    var _utm = ig.utilsmath;
    var _utv2 = ig.utilsvector2;
    dl.EntityGameMapCapital = dl.Entity
        .extend(dl.MixinCircleButton)
        .extend(dl.MixinDocker)
        .extend({
            staticInstantiate: function () {
                this.parent();
                // this.gameManager = ig.game.managers.game;
                this.size = { x: 150, y: 150 };
                if(ig.ua.mobile && !ig.ua.iPad){
                    this.drawSize = { x: 100, y: 100 };
                }else{
                    this.drawSize = { x: 80, y: 80 };
                }
                this.diameter = 0.5 * (this.drawSize.x + this.drawSize.y);
                this.radius = 0.5 * this.diameter;
                this.color = '#3d3d3d';
                this.initialSoldierCount = 0;
                this.soldierCount = 0;
                this.playerStatus='1f1b1e1a';
                // === Shake Start
                this.shaking = false;
                this.shakeOffset = null;
                this.shakePos = { x: 0, y: 0 };
                this.shakeTimer = null;
                this.shakeDuration = 0;
                this.shakeStrength = 8;
                this.shakeFunction = false;
                // === Shake End
                this.enlargeOffset = 0;
                this.currentDrag = 0;
                this.lastAction = 0;
                this.lastTimerCall = 0;
                this.isHighlighted = false;
                this.haveShield = false;
                this.haveWarning = false;
                this.shieldActivationTime = 0;
                this.lastShieldTimerCall = 0;
                this.startClickPos = {};
                this.center = {};
                this.resumeIncrementTime = 1;
                this.resumeIncrementTimer = null;
                this.deactivateWarningTime = 0.5;
                this.deactivateWarningTimer = null;
                this.underAttackDelay = 5;
                this.underAttackTimer = null;
                this.assignedPlayer = null;
                this.shieldTimer = null;
                this.propagandaTimer = null;
                this._lastTimeLeft = null;
                this.supportStatus = false;
                this.setSize(
                    this.size.x,
                    this.size.y
                );
                return this;

            },

            postInit: function () {
                this.parent();

                this.initialSoldierCount = this.capitalData.soldierCount;

                this.shakeTimer = new ig.Timer();
            },

            initGameManager: function () {
                this.gameManager = ig.game.managers.game;
                this.maxDrag = this.gameManager.guideArrowAttribs.maxDrag;
                this.minDrag = this.gameManager.guideArrowAttribs.minDrag;
                this.maxSoldierCount = this.gameManager.maxSoldierCount;
                var playerNumber = -1;
                if (this.capitalData.assignedPlayer) {
                    playerNumber = ig.game.client.clientGameRoom.getPlayerNumber(this.capitalData.assignedPlayer.playerId);
                }
                this.setImage(playerNumber);

                // console.log(this.capitalData);

                var offset = { x: 0, y: 0 };
                if (this.capitalImage) {
                    offset.x = 3;
                    if (this.capitalData.isBuilding) {
                        offset.x = 5;
                        if (this.capitalData.buildingData.BLDG_CODE == 'PT') {
                            offset.y = 0;
                        } else if (this.capitalData.buildingData.BLDG_CODE == 'FORT') {
                            offset.y = 40;
                        } else if (this.capitalData.buildingData.BLDG_CODE == 'WD') {
                            offset.x = -1;
                            offset.y = 40;
                        }
                    }
                }

                if(ig.ua.mobile && !ig.ua.iPad){
                    var fontSize=60;
                }else{
                    var fontSize=30;
                }

                this.soldierCountText = this.spawnEntity(dl.EntityText, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 1 },
                        dockerOffset: { x: offset.x, y: -(this.size.y - this.drawSize.y) + 50 + offset.y }
                    },
                    anchor: { x: 0.5, y: 0 },
                    c_TextComponent: {
                        fontSize: fontSize,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'WHITE'),
                        shadow: true,
                        shadowColor: 'black',
                        shadowBlur: 4
                    },
                    text: this.capitalData.soldierCount
                });

                // this.capitalIndexText = this.spawnEntity(dl.EntityText, {
                //     c_DockerComponent: {
                //         dockerObject: this,
                //         dockerPercent: { x: 1, y: 0.5 },
                //         dockerOffset: { x: 0, y: 0 }
                //     },
                //     anchor: { x: 0.5, y: 0.5 },
                //     c_TextComponent: {
                //         fontSize: fontSize,
                //         fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                //         fillStyle: dl.configs.getConfig('TEXT_COLOR', 'WHITE'),
                //         shadow: true,
                //         shadowColor: 'black',
                //         shadowBlur: 4
                //     },
                //     text: this.capitalData.capitalIndex
                // });

                setTimeout(function(){
                    this.checkSoldierCounter();  
                }.bind(this),5000);
                
            },
            setSupportStatus:function(){
                if(this.supportStatus) return;
                this.supportStatus = true;
                setTimeout(function(){
                    this.supportStatus = false;                    
                }.bind(this),3000);
            },
            initNationPlayer: function () {
                if (!this.capitalData.assignedPlayer) return;
                var nation = this.gameManager.findNationByCapital(this);
                if (nation) {
                    nation.assignCapital(this);
                }
            },

            activateShield: function (startTime, duration) {
                // console.log(startTime, duration);
                if(this.haveShield) return;
                this.haveShield = true;
                this.shieldActivationTime = startTime;
                var offset = 0;
                if (this.capitalImage && this.capitalData.isBuilding) {
                    if (this.capitalData.buildingData.BLDG_CODE == 'PT') {
                        offset = -85;
                    } else if (this.capitalData.buildingData.BLDG_CODE == 'FORT') {
                        offset = -60;
                    } else if (this.capitalData.buildingData.BLDG_CODE == 'WD') {
                        offset = -60;
                    } else if (this.capitalData.buildingData.BLDG_CODE == 'MS') {
                        offset = -40;
                    }
                }

                if(ig.ua.mobile && !ig.ua.iPad){
                    var scale=2;
                    var textOffset={ x: -40, y: -25 };
                    var dockerPercent= { x: 0, y: -0.25 };
                }else{
                    var scale=1;
                    var textOffset={ x: 5, y: -3 };
                    var dockerPercent={ x: 0.25, y: 0 };
                }


                this.shield = this.spawnEntity(dl.EntityImage, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: dockerPercent,
                        dockerOffset: { x: 0, y: offset }
                    },
                    image: dl.preload.capitalShield
                });
                this.shieldTimerText = this.spawnEntity(dl.EntityText, {
                    c_DockerComponent: {
                        dockerObject: this.shield,
                        dockerPercent: { x: 1*scale, y: 0.5*scale },
                        dockerOffset: textOffset
                    },
                    anchor: { x: 0, y: 0.5 },
                    c_TextComponent: {
                        fontSize: 25*scale,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'WHITE'),
                        shadow: true,
                        shadowColor: 'black',
                        shadowBlur: 4
                    },
                    text: Utilities.toMMSS(dl.configs.SHIELD_DURATION)
                });
                this.shieldTimer = new dl.GlobalTimer(startTime, duration);
                // console.log('Shield Activated');
            },

            deactivateShield: function () {
                this.haveShield = false;
                this.shieldTimer = null;
                this.shield.kill();
                this.shieldTimerText.kill();
                // console.log('Shield Deactivated');
            },

            activateWarning: function () {
                this.haveWarning = true;
                this.warning = this.spawnEntity(dl.EntityImage, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.85, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: { x: 72, y: 72 }
                    },
                    anchor: { x: 0, y: 0.5 },
                    image: dl.preload.warning
                });

                dl.TweenTemplate.blink(this.warning, dl.configs.BLINK_INTERVAL);

                setTimeout(function(){
                    this.deactivateWarning();                    
                }.bind(this),3000)
            },

            deactivateWarning: function () {
                this.haveWarning = false;
                if (this.warning) this.warning.kill();
            },

            startDeactivateWarningTimer: function () {
                if (!this.deactivateWarningTimer) {
                    this.deactivateWarningTimer = new ig.Timer(this.deactivateWarningTime);
                } else {
                    this.deactivateWarningTimer.reset();
                }
            },

            spawnThundercloud: function () {
                var thundercloud = this.spawnEntity(dl.EntityImage, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.35 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    anchor: { x: 0.5, y: 0.5 },
                    image: dl.preload.thundercloud
                });

                dl.TweenTemplate.thundercloud(thundercloud, 200);
            },

            activateMissile: function (targetCapitalIndex) {

                try{
                    var targetCapital = this.gameManager.findCapitalByIndex(targetCapitalIndex);                
                    var originalOffset = this.missileImage.c_DockerComponent.dockerOffset;
                    var originalPos = this.missileImage.pos;
                    // console.log(originalOffset, originalPos);

                    ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.rocketlaunch)
                    this.missileImage.createTween()
                    .to({
                        c_DockerComponent: {
                            dockerOffset: {x:0, y: -2000 }
                        },
                    }, 1000, {
                        easing: dl.TweenEasing.Quadratic.EaseOut,
                        onPropertiesChanged: function () {
                            this.updatePos();
                        }.bind(this.missileImage)
                    })
                    .to({
                        c_DockerComponent: {
                            dockerOffset: { x: targetCapital.c_DockerComponent.dockerOffset.x }
                        },
                    }, 10, {
                        easing: dl.TweenEasing.Quadratic.EaseOut,
                        onPropertiesChanged: function () {
                            this.updatePos();
                        }.bind(this.missileImage)
                    })
                    .to({}, 10, {
                        onPropertiesChanged: function () {
                            this.angle = Math.PI;
                            this.cacheRequestRedraw();
                        }.bind(this.missileImage)
                    })
                    .to({
                        pos: {
                            x: targetCapital.pos.x,
                            y: targetCapital.pos.y - targetCapital.size.y * 0.45
                            }
                        }, 1000, {
                        easing: dl.TweenEasing.Quadratic.EaseIn,
                        onPropertiesChanged: function () {
                            // this.updatePos();
                            this.cacheRequestRedraw();
                        }.bind(this.missileImage),
                    })
                    .to({}, 1, {
                        onPropertiesChanged: function () {
                            targetCapital.shake(0.5, 5);
                            targetCapital.spawnExplosion();
                        }.bind(targetCapital)
                    })
                    .to({
                        _alpha: 0
                    }, 100, {
                        easing: dl.TweenEasing.Linear.EaseNone,
                        onPropertiesChanged: function () {
                            // this.cacheRequestRedraw();
                            this.updateAlpha();
                        }.bind(this.missileImage)
                    })
                    .to({
                        c_DockerComponent: {
                            dockerOffset: { y: originalOffset.y }
                        },
                        pos: {
                            x: originalPos.x,
                            y: originalPos.y
                        }
                    }, 10, {
                        onPropertiesChanged: function () {
                            // this.updatePos();
                        }.bind(this)
                    })
                    .start({
                        onCompleteTween: function () {
                            ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.explotion)                            
                            // create explosion
                            this.missileImage.angle = 0;
                            this.missileImage.cacheRequestRedraw();
                            this.missileImage.createTween()
                            .to({
                                _alpha: 1
                            }, 1000, {
                                easing: dl.TweenEasing.Linear.EaseNone,
                                onPropertiesChanged: function () {
                                    this.updateAlpha();
                                }.bind(this.missileImage)
                            })
                            .start();
                            this.updateMissilePos();
                        }.bind(this)
                    });

                    setTimeout(function(){
                        this.deductSoldier(targetCapitalIndex)
                    }.bind(this),3000)

                }catch(e){}
            },
            deductSoldier:function(targetCapitalIndex){

                var targetCapital = this.gameManager.findCapitalByIndex(targetCapitalIndex);                
                    targetCapital.capitalData.soldierCount = NETWORK_GAME_CONFIGS.BUILDINGS[2].EFFECT;
                    targetCapital.soldierCountText.updateText(targetCapital.capitalData.soldierCount);

                if (ig.game.client &&
                    ig.game.client.clientGameRoom &&
                    ig.game.client.clientGameRoom.networkGame) {
                    ig.game.client.clientGameRoom.networkGame.sendGameEvent(
                        new NetworkGameEvent(NetworkGameEvent.EVENT_TYPE.SYNC_SOLDIER_COUNT,Date.now(),{
                            soldierCount: NETWORK_GAME_CONFIGS.BUILDINGS[2].EFFECT,
                            capitalIndex: targetCapitalIndex,
                            sourceCapitalIndex:this.capitalData.capitalIndex,
                            attackerPlayerId:this.capitalData.assignedPlayer.playerId,
                            attackedStatus:false,
                        })
                    );
                }                
            },
            missileOriginalPos:{x:0,y:0},
            updateMissilePos:function(){
                setTimeout(function(){

                    this.missileImage.c_DockerComponent.dockerObject=this; //.capitalImage;
                    this.missileImage.c_DockerComponent.dockerPercent={ x: 0.53, y: 0 };
                    this.missileImage.c_DockerComponent.dockerOffset={ x: 0, y: -13 };
                    this.missileImage.updatePos();
                }.bind(this),500);
            },
            spawnExplosion: function () {
                var explosion = this.spawnEntity(dl.EntityImage, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.25, y: 0.25 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    image: dl.preload.explosion,
                    c_AnimationSheetComponent: {
                        _animationSheetImage: dl.preload.explosion,
                        _animationSheetRow: 1,
                        _animationSheetCol: 5,
                        _anchor: { x: 0.5, y: 0.5 },
                        _killOnStop: true,
                        _setupDefaultAnimation: function () {
                            this.addAnimation('idle', 48, [0, 1, 2, 3, 4], true);
                        }
                    }
                });
                explosion.c_AnimationSheetComponent.setAnimation('idle');

                this.createTween()
                .to({}, 100)
                .start({
                    easing: dl.TweenEasing.Linear.EaseNone,
                    onCompleteTween: function () {
                        var explosion = this.spawnEntity(dl.EntityImage, {
                            c_DockerComponent: {
                                dockerObject: this,
                                dockerPercent: { x: 0.75, y: 0.25 },
                                dockerOffset: { x: 0, y: 0 }
                            },
                            image: dl.preload.explosion,
                            c_AnimationSheetComponent: {
                                _animationSheetImage: dl.preload.explosion,
                                _animationSheetRow: 1,
                                _animationSheetCol: 5,
                                _anchor: { x: 0.5, y: 0.5 },
                                _killOnStop: true,
                                _setupDefaultAnimation: function () {
                                    this.addAnimation('idle', 48, [0, 1, 2, 3, 4], true);
                                }
                            }
                        });
                        explosion.c_AnimationSheetComponent.setAnimation('idle');
                    }.bind(this)
                });

                this.createTween()
                .to({}, 200)
                .start({
                    easing: dl.TweenEasing.Linear.EaseNone,
                    onCompleteTween: function () {
                        var explosion = this.spawnEntity(dl.EntityImage, {
                            c_DockerComponent: {
                                dockerObject: this,
                                dockerPercent: { x: 0.75, y: 0.75 },
                                dockerOffset: { x: 0, y: 0 }
                            },
                            image: dl.preload.explosion,
                            c_AnimationSheetComponent: {
                                _animationSheetImage: dl.preload.explosion,
                                _animationSheetRow: 1,
                                _animationSheetCol: 5,
                                _anchor: { x: 0.5, y: 0.5 },
                                _killOnStop: true,
                                _setupDefaultAnimation: function () {
                                    this.addAnimation('idle', 48, [0, 1, 2, 3, 4], true);
                                }
                            }
                        });
                        explosion.c_AnimationSheetComponent.setAnimation('idle');
                    }.bind(this)
                });

                this.createTween()
                .to({}, 300)
                .start({
                    easing: dl.TweenEasing.Linear.EaseNone,
                    onCompleteTween: function () {
                        var explosion = this.spawnEntity(dl.EntityImage, {
                            c_DockerComponent: {
                                dockerObject: this,
                                dockerPercent: { x: 0.25, y: 0.75 },
                                dockerOffset: { x: 0, y: 0 }
                            },
                            image: dl.preload.explosion,
                            c_AnimationSheetComponent: {
                                _animationSheetImage: dl.preload.explosion,
                                _animationSheetRow: 1,
                                _animationSheetCol: 5,
                                _anchor: { x: 0.5, y: 0.5 },
                                _killOnStop: true,
                                _setupDefaultAnimation: function () {
                                    this.addAnimation('idle', 48, [0, 1, 2, 3, 4], true);
                                }
                            }
                        });
                        explosion.c_AnimationSheetComponent.setAnimation('idle');
                    }.bind(this)
                });
            },
            propagandaStatus:false,
            activatePropaganda: function (startTime, duration) {
                this.propagandaStatus=true;
                var offset = -20;
                this.propaganda = this.spawnEntity(dl.EntityImage, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0, y: 0.5 },
                        dockerOffset: { x: offset, y: 0 }
                    },
                    anchor: { x: 0.5, y: 0.5 },
                    image: dl.preload.propaganda
                });

                dl.TweenTemplate.blink(this.propaganda, dl.configs.BLINK_INTERVAL);

                this.propagandaTimer = new dl.GlobalTimer(startTime, duration);
            },

            deactivatePropaganda: function () {
                this.propagandaStatus=false;
                if (this.propaganda) {
                    this.propaganda.kill();
                    this.propaganda = null;
                }
            },
            onPointerFirstClick: function () {
                if (this.haveShield) return;
                if (!this.gameManager.enableControls) return;
                if (this.capitalData && !this.capitalData.assignedPlayer) return;
                if (this.capitalData &&
                    this.capitalData.assignedPlayer &&
                    this.capitalData.assignedPlayer.playerId != ig.game.client.playerId) return;

                // dl.log('Pointer Clicked');
                this.isActive = true;
                var mousePosition = ig.game.io.getClickPos();
                var gamePosition = dl.game.camera.getGamePositionFromScreenPosition(mousePosition.x, mousePosition.y);
                this.startClickPos = gamePosition;
                this.gameManager.guideArrowAttribs.pos = this.center;
                this.gameManager.guideArrowAttribs.sizeOffset = this.drawSize;
                // dl.log('Start Click Pos: ', this.startClickPos, ' Pointer Pos: ', ig.game.pointer.pos);
            },

            onPointerReleased: function () {
                // dl.log('Pointer Released');
                // dl.log('Release Angle: ', this.dragAngle);
                if (this.currentDrag > this.minDrag && this.gameManager.currentTargetCapitalIndex && !this.haveShield) {
                    var targetCapital = this.gameManager.findCapitalByIndex(this.gameManager.currentTargetCapitalIndex);
                    if (targetCapital) {
                        if (targetCapital.haveShield) {
                            dl.scene.spawnNotification({
                                notificationDrawConfigs: {
                                    contentConfigs: [
                                        {
                                            type: 'text',
                                            text: _STRINGS.Game.HAVE_PROTECTION,
                                            fillStyle: dl.configs.TEXT_COLOR.GRAY,
                                            fontSize: 46,
                                            fontFamily: dl.configs.FONT.SOURCE_SANS.name
                                        }],
                                    backgroundConfigs: {
                                        lineWidth: 2,
                                        fillStyle: dl.configs.NOTIFICATION_COLORS.NEUTRAL,
                                        strokeStyle: dl.configs.NOTIFICATION_COLORS.NEUTRAL,

                                        box: { width: 800, height: 90, round: 10, padding: { x: 100, y: 5 } }
                                    }
                                }
                            });
                        } else {
                            var randomInt = Utilities.randomInt(1, 6);
                            ig.game.client.clientGameRoom.playerStatusServer=this.playerStatus;
                            if((targetCapital.capitalData.assignedPlayer && 
                                this.capitalData.assignedPlayer.playerId !== targetCapital.capitalData.assignedPlayer.playerId) || 
                                !targetCapital.capitalData.assignedPlayer &&
                                !this.gameManager.attackDelay
                                ){                            
                                ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList['attack' + randomInt]);
                            }

                            var data = {
                                playerId: ig.game.client.playerId,
                                capitalIndex: this.capitalData.capitalIndex,
                                currentTargetCapitalIndex: this.gameManager.currentTargetCapitalIndex,
                                soldierCount: this.capitalData.soldierCount
                            };
                            this.gameManager.capitalAttack(data);
                            this.spawnBotEmoji(targetCapital)
                        }
                    }
                }

                if (this.currentDrag < this.minDrag * 0.15 && this.capitalData.isBuilding) {
                    // console.log(this.capitalData.buildingData.BLDG_CODE);
                    var image = dl.preload['building-previews'][this.capitalData.buildingData.BLDG_CODE];
                    dl.scene.spawnChatBubble({
                        chatBubbleDrawConfigs: {
                            textConfigs: {
                                text: this.capitalData.buildingData.BLDG_DESC, // text display in chat bubble
                                fillStyle: 'black',
                                textAlign: 'center', // [center|left|right];
                                fontSize: 30,
                                fontFamily: dl.configs.FONT.default.name
                            },
                            avatarConfigs: {
                                image: image, // image display in chat bubble
                                size: { x: image.width * 0.65, y: image.height * 0.65 }, // image size
                                padding: { x: 8, y: 8 } // extra space outside image
                            },
                            bubbleConfigs: {
                                lineWidth: 2,
                                fillStyle: 'lightblue',
                                strokeStyle: 'black',

                                shadowColor: 'black',
                                shadowBlur: 4,
                                shadowOffsetX: 4,
                                shadowOffsetY: 4,

                                box: {
                                    width: 200, // content min width
                                    height: 100, // content min height
                                    round: 10, // round curves distance
                                    padding: { x: 10, y: 10 } // extra space outside the content area
                                },
                                tail: {
                                    length: 30, // tail length
                                    width: 15, // tail width
                                    direction: { x: 0.5, y: 1 } // tail direction, will be update if input invalid (0-1)
                                }
                            }
                        },
                        chatBubbleAppearTime: 400, // appear time - millisecond
                        chatBubbleAliveTime: 2000, // alive time - millisecond
                        chatBubbleDisappearTime: 200, // disappear time - millisecond
                        chatBubbleDocker: this,
                        chatBubblePercent: { x: 0.5, y: 0 }, // position percent of ChatBubbleParentEntity (0-1) related to the ChatBubbleParentEntity position and size
                        chatBubbleOffset: { x: 0, y: 0 }, // extra offset from position percent of ChatBubbleParentEntity
                        chatBubbleAlpha: 0.9 // chat bubble alpha
                    });
                }
                this.isActive = false;
                this.currentDrag = 0;
                this.dragAngle = 0;
                this.gameManager.guideArrowAttribs.isActive = this.isActive;
                this.gameManager.guideArrowAttribs.currentDrag = this.currentDrag;
                this.gameManager.guideArrowAttribs.dragAngle = this.dragAngle;
                this.gameManager.currentTargetCapitalIndex = null;
            },
            spawnBotEmoji:function(target){

                if(target.capitalData && target.capitalData.assignedPlayer && target.capitalData.assignedPlayer.playerId && !this.targetIsHuman(target.capitalData.assignedPlayer.playerId)){
                    var showBotEmot=[0,0,0,1,1,1,1,1,1,1];
                    if(showBotEmot[Utilities.randomInt(0,9)]==1){                                
                        var time=[500,1000,1500,2000,3000];
                        var playerEmot=target.capitalData.assignedPlayer.playerId;
                        setTimeout(function(){
                            var idx=[0,2,4];
                            // console.log('need support');
                            ig.game.client.clientGameRoom.sendBotEmoji(idx[Utilities.randomInt(0,2)],playerEmot);                                        
                        }.bind(this),time[Utilities.randomInt(0,time.length-1)]);
                    }                    
                }

            },
            targetIsHuman:function(playerId){
                var players = ig.game.managers.game.players;
                var res = false;
                for (var key in players) {
                    if (players[key] && !players[key].botFlag && players[key].playerId==playerId) {
                        res = true;
                    }
                }
                return res;
            },
            onPointerClicking: function () {
                // dl.log('Pointer Clicking');
            },

            onPointerLeave: function () {
                // dl.log('Pointer Left');
                this.gameManager.currentTargetCapitalIndex = null;
                this.deactivateHighlight();
            },

            onPointerOver: function () {
                // dl.log('Pointer Over');
                if (this.isActive) return;
                if (this.gameManager.guideArrowAttribs.isActive) {
                    this.gameManager.currentTargetCapitalIndex = this.capitalData.capitalIndex;
                    this.activateHighlight();
                }
            },
            // Create a game manager, transfer to game manager
            calculateDrag: function () {
                var mousePosition = ig.game.io.getClickPos();
                var gamePosition = dl.game.camera.getGamePositionFromScreenPosition(mousePosition.x, mousePosition.y);
                if (Utilities.distanceBetweenTwoPoints(this.startClickPos, gamePosition) < this.maxDrag) {
                    if (Utilities.distanceBetweenTwoPoints(this.startClickPos, gamePosition) > this.minDrag) {
                        this.currentDrag = Utilities.distanceBetweenTwoPoints(this.startClickPos, gamePosition);
                    } else {
                        this.currentDrag = 0;
                    }
                } else {
                    this.currentDrag = this.maxDrag;
                }
                this.dragAngle = Utilities.calcAngle(
                    this.startClickPos.x, this.startClickPos.y,
                    gamePosition.x, gamePosition.y,
                    true
                );
            },

            /* SHAKER START */

            /**
             * Starts shaking.
             **/
            shake: function (duration, strength, fn) {
                if (!this.shaking) {
                    this.shaking = true;
                    this.shakeDuration = duration || this.shakeDuration;
                    this.shakeStrength = strength || this.shakeStrength;
                    this.shakeFunction = fn || this.shakeFunction;

                    if (!this.shakeOffset) {
                        this.shakeOffset = {
                            x: 0,
                            y: 0
                        };
                    } else {
                        _utv2.zero(this.shakeOffset);
                    }

                    this.shakeTimer.set(this.shakeDuration);
                }
            },

            /**
             * Stops shaking.
             */
            shakeStop: function () {
                if (this.shaking) {
                    this.shaking = false;

                    this.pos.x -= this.shakeOffset.x;
                    this.pos.y -= this.shakeOffset.y;

                    if (this.capitalImage) {
                        this.capitalImage.pos.x -= this.shakeOffset.x;
                        this.capitalImage.pos.y -= this.shakeOffset.y;
                    }
                }
            },

            /**
             * Updates shake.
             */
            updateShake: function () {
                if (this.shaking) {
                    this.updatePos();
                    this.pos.x -= this.shakeOffset.x;
                    this.pos.y -= this.shakeOffset.y;
                    if (this.capitalImage) {
                        this.capitalImage.pos.x -= this.shakeOffset.x;
                        this.capitalImage.pos.y -= this.shakeOffset.y;
                    }
                    var delta = this.shakeTimer.delta();

                    if (delta >= 0) {
                        this.shakeStop();
                    } else {
                        var deltaPct = -delta / this.shakeDuration;
                        var value = this.shakeFunction ? this.shakeFunction() : (this.shakeStrength * Math.pow(deltaPct, 2));

                        if (value > 0.5) {
                            this.shakeOffset.x += _utm.map(Math.random(), 0, 1, -value, value);
                            this.shakeOffset.y += _utm.map(Math.random(), 0, 1, -value, value);
                        }

                        _utv2.multiplyScalar(this.shakeOffset, deltaPct);
                    }
                }
            },
            /* SHAKER END */

            enlargeEffect: function (callback) {
                this.shakeStop();
                if (!this.capitalImage) {
                    this.createTween()
                    .to({
                        radius: this.radius + 5
                    }, 150, {
                        easing: dl.TweenEasing.Quadratic.EaseOut,
                        onPropertiesChanged: function () {
                            this.updateScale();
                        }.bind(this)
                    })
                    .start({
                        onCompleteTween: function () {
                            this.createTween()
                            .to({
                                radius: this.radius - 5
                            }, 150, {
                                easing: dl.TweenEasing.Quadratic.EaseOut,
                                onPropertiesChanged: function () {
                                    this.updateScale();
                                }.bind(this)
                            })
                            .start({
                                onCompleteTween: function () {
                                    if (typeof callback == 'function') callback();
                                }.bind(this)
                            });
                        }.bind(this)
                    });
                } else {
                    this.capitalImage.createTween()
                    .to({
                        _scale: { x: 1.1, y: 1.1 }
                    }, 150, {
                        easing: dl.TweenEasing.Quadratic.EaseOut,
                        onPropertiesChanged: function () {
                            this.capitalImage.updateScale();
                        }.bind(this)
                    })
                    .start({
                        onCompleteTween: function () {
                            this.capitalImage.createTween()
                            .to({
                                _scale: { x: 1, y: 1 }
                            }, 150, {
                                easing: dl.TweenEasing.Quadratic.EaseOut,
                                onPropertiesChanged: function () {
                                    this.capitalImage.updateScale();
                                }.bind(this)
                            })
                            .start({
                                onCompleteTween: function () {
                                    if (typeof callback == 'function') callback();
                                }.bind(this)
                            });
                        }.bind(this)
                    });
                }
            },

            removePlayer: function () {
                this.capitalData.assignedPlayer = null;
                this.color = '#3d3d3d';
                this.capitalImage.kill();
                this.capitalImage = null;
            },

            setColor: function (color) {
                this.color = color;
            },

            setImage: function (playerNumber) {
                if (playerNumber != -1 || this.capitalData.isBuilding) {
                    var image = '';
                    var image2 = '';
                    var isBuilding = false;
                    var offset = {
                        x: 0, y: 0
                    };
                    if (this.capitalData.isBuilding) {
                        isBuilding = true;

                        // if (this.capitalData.buildingData.BLDG_CODE == 'MS') {
                        //     image = dl.preload.buildings[this.capitalData.buildingData.BLDG_CODE].stand;
                        //     var image2 = dl.preload.buildings[this.capitalData.buildingData.BLDG_CODE].missile;
                        // } else {
                        //     image = dl.preload.buildings[this.capitalData.buildingData.BLDG_CODE];
                        // }

                        if(this.capitalData.buildingData.BLDG_CODE == 'FORT') {
                            image = dl.preload['skinfort'+ig.game.skinFortUsed];
                        }else if(this.capitalData.buildingData.BLDG_CODE == 'MS'){
                            image  = dl.preload['skinmissilestand'+ig.game.skinMissileUsed];
                            image2 = dl.preload['skinmissile'+ig.game.skinMissileUsed];
                        }else if(this.capitalData.buildingData.BLDG_CODE == 'PT'){
                            image = dl.preload['skintower'+ig.game.skinTowerUsed];
                        }else if(this.capitalData.buildingData.BLDG_CODE == 'WD'){
                            image = dl.preload['skindome'+ig.game.skinDomeUsed];
                        }


                    } else {
                        // console.log(this.capitalData.assignedPlayer);
                        // image = dl.preload['game-player-info'].icons[playerNumber - 1]; this.capitalData.assignedPlayer.playerId
                        image = dl.preload['game-player-info'].icons[this.capitalData.assignedPlayer.playerIndex]; 
                    }
                    if (this.capitalImage) {
                        this.capitalImage.kill();
                        this.capitalImage = null;
                    }
                    if (isBuilding) {
                        // if (this.capitalData.buildingData.BLDG_CODE == 'WD') {
                        //     this.capitalImage = this.spawnEntity(dl.EntityImage, {
                        //         c_DockerComponent: {
                        //             dockerObject: this,
                        //             dockerPercent: { x: 0.5, y: 0.5 },
                        //             dockerOffset: { x: 0, y: -40 }
                        //         },
                        //         image: image,
                        //         c_AnimationSheetComponent: {
                        //             _animationSheetImage: image,
                        //             _animationSheetRow: 1,
                        //             _animationSheetCol: 10,
                        //             _anchor: { x: 0.5, y: 0.5 },
                        //             _setupDefaultAnimation: function () {
                        //                 this.addAnimation('idle', 48, [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], false);
                        //             }
                        //         }
                        //     });

                        //     this.capitalImage.c_AnimationSheetComponent.setAnimation('idle');
                        // }

                        // if (this.capitalData.buildingData.BLDG_CODE != 'WD') {

                            if (this.capitalData.buildingData.BLDG_CODE == 'PT') {
                                offset.x = 20;
                                offset.y = -20;
                            }
                            if (this.capitalData.buildingData.BLDG_CODE == 'FORT') {
                                offset.x = 0;
                                offset.y = 5;
                            }
                            if (this.capitalData.buildingData.BLDG_CODE == 'MS') {
                                offset.x = 0;
                                offset.y = 0;
                            }
                            if (this.capitalData.buildingData.BLDG_CODE == 'WD') {
                                offset.x = 0;
                                offset.y = 0;
                            }

                            this.capitalImage = this.spawnEntity(dl.EntityImage, {
                                c_DockerComponent: {
                                    dockerObject: this,
                                    dockerPercent: { x: 0.5, y: 0.5 },
                                    dockerOffset: { x: offset.x, y: offset.y }
                                },
                                image: image
                            });

                            if (this.capitalData.buildingData.BLDG_CODE == 'MS') {
                                this.missileImage = this.spawnEntity(dl.EntityImage, {
                                    c_DockerComponent: {
                                        dockerObject: this.capitalImage,
                                        dockerPercent: { x: 0.53, y: 0 },
                                        dockerOffset: { x: 0, y: -20 }
                                    },
                                    image: image2
                                });
                                this.missileOriginalPos=this.missileImage.pos;
                            }
                        // }

                    } else {
                        this.capitalImage = this.spawnEntity(dl.EntityImage, {
                            c_DockerComponent: {
                                dockerObject: this,
                                dockerPercent: { x: 0.5, y: 0.5 },
                                dockerOffset: { x: 0, y: 0 }
                            },
                            image: image
                        });
                    }
                }
            },

            activateHighlight: function () {
                if (this.isHighlighted) return;
                if (this.capitalData.isBuilding) {
                    this.capitalImage._scale = {
                        x: 1.25,
                        y: 1.25
                    };
                    this.capitalImage.updateScale();
                    this.isHighlighted = true;
                }
                if (!this.capitalData.assignedPlayer) return;

                // console.log(this.capitalData);
                // var playerNumber = ig.game.client.clientGameRoom.getPlayerNumber(this.capitalData.assignedPlayer.playerId);
                this.isHighlighted = true;
                if (this.capitalImage && !this.capitalData.isBuilding) {
                    this.capitalImage.updateImage(dl.preload['game-player-info'].iconsHighlighted[this.capitalData.assignedPlayer.playerIndex]);
                    this.capitalImage.c_DockerComponent.dockerOffset.x = 5;
                    this.capitalImage.c_DockerComponent.dockerOffset.y = -5;
                }
            },

            deactivateHighlight: function () {
                if (!this.isHighlighted) return;
                if (this.capitalData.isBuilding) {
                    // console.log('Is Building, reset scale');
                    this.capitalImage._scale = {
                        x: 1,
                        y: 1
                    };
                    this.capitalImage.updateScale();
                    this.isHighlighted = false;
                }
                if (!this.capitalData.assignedPlayer) return;
                this.isHighlighted = false;
                var playerNumber = ig.game.client.clientGameRoom.getPlayerNumber(this.capitalData.assignedPlayer.playerId);
                if (this.capitalImage && !this.capitalData.isBuilding) {
                    // this.capitalImage.updateImage(dl.preload['game-player-info'].icons[playerNumber - 1]);
                    this.capitalImage.updateImage(dl.preload['game-player-info'].icons[this.capitalData.assignedPlayer.playerIndex]);
                    this.capitalImage.c_DockerComponent.dockerOffset.x = 0;
                    this.capitalImage.c_DockerComponent.dockerOffset.y = 0;
                }
            },

            playUnderAttackSFX: function () {
                if (this.underAttackTimer) return;
                if (!this.capitalData.assignedPlayer) return;
                if (!ig.game.client.isMyPlayerId(this.capitalData.assignedPlayer.playerId)) return;
                this.underAttackTimer = new ig.Timer(this.underAttackDelay);
                var randomInt = Utilities.randomInt(1, 4);
                ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList['under-attack' + randomInt]);
            },

            getAabb: function () {
                if(ig.ua.mobile && !ig.ua.iPad){
                    return {
                        x: this.pos.x - this.drawSize.x * 0.5-20,
                        y: this.pos.y - this.drawSize.y * 0.5-20,
                        w: this.drawSize.x+40,
                        h: this.drawSize.y+40
                    };
                }else{
                    return {
                        x: this.pos.x - this.drawSize.x * 0.5-10,
                        y: this.pos.y - this.drawSize.y * 0.5-10,
                        w: this.drawSize.x+20,
                        h: this.drawSize.y+20
                    };                    
                }
            },

            updateAssignedPlayer: function () {
                var nation = this.gameManager.transferNationOwnership(this);
                ig.game.managers.color.updateCapitalColor(this);
                ig.game.managers.color.updateNationColor(nation);
                this.enlargeEffect(function () {
                    var playerNumber = ig.game.managers.game.getPlayerNumber(this.capitalData.assignedPlayer.playerId);
                    if (!this.capitalData.isBuilding) this.setImage(playerNumber);
                    this.initNationPlayer();
                }.bind(this));
                if (ig.game.client.isMyPlayerId(this.capitalData.assignedPlayer.playerId)) {
                    var randomInt = Utilities.randomInt(1, 3);
                    ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList['capture' + randomInt]);
                }
                // this.activateShield();
            },

            updateTimerText: function (text, duration, callback) {
                if (this.lastTimeLeft == text) return;
                this.lastTimeLeft = text;

                this.shieldTimerText.updateText(Utilities.toMMSS(text));
                if (typeof callback == 'function') callback();
                // dl.TweenTemplate.scaleIn(this, duration, callback);
                // if (text != _STRINGS.NETWORK.COUNT_DOWN_TIMER_START) ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.beep);
            },
            tempSoldierCount:null,
            tempCounterLoop:0,
            tempCount :0,
            checkSoldierCounter:function(){
                if(this.gameManager.isGameOver) return;
                if(!window.gameStart) return;
                // if(!this.isActive) return;

                // console.log(this.capitalData.isBuilding);
                if (this.capitalData.isBuilding) {
                    if (this.capitalData.buildingData.BLDG_CODE == 'FORT') {
                        if(this.tempSoldierCount==this.capitalData.soldierCount && this.capitalData.assignedPlayer!=null && this.capitalData.soldierCount<100){
                            this.capitalData.soldierCount=Math.floor(this.capitalData.soldierCount)+NETWORK_GAME_CONFIGS.BUILDINGS[0].EFFECT;
                            this.soldierCountText.text=Math.round(this.capitalData.soldierCount);
                            this.soldierCountText.updateText(Math.round(this.capitalData.soldierCount));
                        }
                        this.tempSoldierCount=this.capitalData.soldierCount;
                    } else if (this.capitalData.buildingData.BLDG_CODE == 'WD' || 
                               this.capitalData.buildingData.BLDG_CODE == 'MS' ||
                               this.capitalData.buildingData.BLDG_CODE == 'PT') {
                        if(this.tempSoldierCount==this.capitalData.soldierCount && this.capitalData.assignedPlayer!=null && this.capitalData.soldierCount<100){
                            this.capitalData.soldierCount=Math.floor(this.capitalData.soldierCount)+1;
                            this.soldierCountText.text=Math.round(this.capitalData.soldierCount);
                            this.soldierCountText.updateText(Math.round(this.capitalData.soldierCount));
                        }
                        this.tempSoldierCount=this.capitalData.soldierCount;

                    } 

                }else{
                    if(this.tempSoldierCount==this.capitalData.soldierCount && this.capitalData.assignedPlayer!=null && this.capitalData.soldierCount<100){
                        // console.log(this.capitalData.capitalIndex,this.capitalData.soldierCount);
                        if(this.propagandaStatus){
                            if((this.tempCounterLoop % 2)==0){
                                this.capitalData.soldierCount=Math.floor(this.capitalData.soldierCount)+1;
                                this.soldierCountText.text=Math.round(this.capitalData.soldierCount);
                                this.soldierCountText.updateText(Math.round(this.capitalData.soldierCount));
                            }
                        }else{
                            this.capitalData.soldierCount=Math.floor(this.capitalData.soldierCount)+1;
                            this.soldierCountText.text=Math.round(this.capitalData.soldierCount);
                            this.soldierCountText.updateText(Math.round(this.capitalData.soldierCount));                            
                        }
                    }
                    this.tempSoldierCount=this.capitalData.soldierCount;
                }

                if((this.tempCounterLoop % 2)==0){
                    if (ig.game.client &&
                        ig.game.client.clientGameRoom &&
                        ig.game.client.clientGameRoom.networkGame &&
                        this.capitalData.assignedPlayer!=null) {

                        ig.game.client.clientGameRoom.networkGame.sendGameEvent(
                            new NetworkGameEvent(NetworkGameEvent.EVENT_TYPE.SYNC_SOLDIER_COUNT,Date.now(),{
                                soldierCount: this.capitalData.soldierCount,
                                capitalIndex: this.capitalData.capitalIndex,
                                sourceCapitalIndex:-1,
                                attackerPlayerId:this.capitalData.assignedPlayer.playerId,
                                screenId:ig.game.client.playerId,
                                attackedStatus:false,
                            })
                        );

                    }else{
                        try{
                        ig.game.client.clientGameRoom.networkGame.sendGameEvent(
                            new NetworkGameEvent(NetworkGameEvent.EVENT_TYPE.SYNC_SOLDIER_COUNT,Date.now(),{
                                soldierCount: this.capitalData.soldierCount,
                                capitalIndex: this.capitalData.capitalIndex,
                                sourceCapitalIndex:-1,
                                attackerPlayerId:-1,
                                screenId:ig.game.client.playerId,
                                attackedStatus:false,
                            })
                        );
                        }catch(e){}
                    }
                }

                setTimeout(function(){
                    this.tempCounterLoop +=1;
                    if(this.tempCounterLoop==100) this.tempCounterLoop=0;
                    this.checkSoldierCounter();
                }.bind(this),1000);
            },
            updateData2:function(data){

                if(this.gameManager.isGameOver) return;
                if(ig.game.client.playerId==data.screenId) return;

                this.capitalData.soldierCount = Math.floor(data.soldierCount);

                this.soldierCountText.text=Math.floor(this.capitalData.soldierCount);
                this.soldierCountText.updateText(Math.floor(this.capitalData.soldierCount));

            },
            updateData: function (data) {
                if(this.gameManager.isGameOver) return;

                if (data.assignedPlayer != null) {
                    if (ig.game.client &&
                        ig.game.client.clientGameRoom &&
                        ig.game.client.clientGameRoom.networkGame &&
                        ig.game.client.clientGameRoom.networkGame.gameStarted) {
                        if (!this.capitalData.assignedPlayer) {
                            this.capitalData.assignedPlayer = data.assignedPlayer;
                            this.updateAssignedPlayer();                                
                        } else {
                            if (this.capitalData.assignedPlayer.playerId !== data.assignedPlayer.playerId) {
                                this.capitalData.assignedPlayer = data.assignedPlayer;
                                this.updateAssignedPlayer();
                            }
                        }
                    }
                }

            },
            decreaseSoldierCountNetwork:function(currentIndex,maxIndex,capital){
                
                var targetData =this.capitalData.soldierCount-1;
                if(targetData<0) targetData=0;

                this.capitalData.soldierCount = targetData;
                this.soldierCountText.updateText(Math.floor(this.capitalData.soldierCount));

                if (ig.game.client &&
                    ig.game.client.clientGameRoom &&
                    ig.game.client.clientGameRoom.networkGame &&
                    this.capitalData.assignedPlayer!=null) {
                    ig.game.client.clientGameRoom.networkGame.sendGameEvent(
                        new NetworkGameEvent(NetworkGameEvent.EVENT_TYPE.SYNC_SOLDIER_COUNT,Date.now(),{
                            soldierCount: targetData,
                            capitalIndex:this.capitalData.capitalIndex,
                            sourceCapitalIndex:-1,
                            attackerPlayerId:ig.game.client.playerId,
                            screenId:ig.game.client.playerId,
                            attackedStatus:false,
                        })
                    );
                }


                // if (this._tweenInstance) this._tweenInstance.stop();
                // this._tweenInstance = this.createTween()
                //     .to({
                //         capitalData: { soldierCount: targetData }
                //     }, 50, {
                //         easing: dl.TweenEasing.Linear.EaseNone,
                //         onPropertiesChanged: function () {
                //             if (this.soldierCountText) this.soldierCountText.updateText(Math.floor(this.capitalData.soldierCount));
                //         }.bind(this)
                //     })
                //     .start({
                //         onCompleteTween: function () {
                //             if(currentIndex==maxIndex){
                //                 setTimeout(function(){
                //                     this.decreaseStatus=false;
                //                 }.bind(this),1000);
                //             }
                //         }.bind(this)
                //     });                

            },
            update: function () {
                this.parent();
                this.center.x = this.pos.x + this.drawSize.x * 0.5;
                this.center.y = this.pos.y + this.drawSize.y * 0.5;
                this.updateShake();

                // this.capitalIndexText.updateText(this.capitalData.capitalIndex);

                if (this.gameManager.currentTargetCapitalIndex != this.capitalData.capitalIndex) {
                    this.deactivateHighlight();
                }

                if (ig.input.released('click')) {
                    if (ig.ua.mobile && !ig.ua.iPad) {
                        this.released();
                    }
                    if (this.isActive) {
                        this.playerStatus=ig.game.client.clientGameRoom.playerStatus;
                        this.onPointerReleased();
                        // dl.log('Capital Released', this.currentDrag, this.__firstClicked);
                    }
                }

                if (ig.input.state('click')) {
                    if (this.isActive) {
                        this.calculateDrag();
                        this.gameManager.guideArrowAttribs.isActive = this.isActive;
                        this.gameManager.guideArrowAttribs.currentDrag = this.currentDrag;
                        this.gameManager.guideArrowAttribs.dragAngle = this.dragAngle;
                    }
                }

                if (this.shieldTimer) {
                    if (this.shieldTimer.delta() <= 0) {
                        var timeLeft = Math.abs(Math.floor(this.shieldTimer.delta() / 1000));
                        var millisecondLeft = Math.abs(this.shieldTimer.delta() % 1000);
                        this.updateTimerText(timeLeft, millisecondLeft);
                    } else {
                        this.updateTimerText('0', 1000, function () {
                            this.deactivateShield();
                        }.bind(this));
                    }
                }

                if (this.propagandaTimer) {
                    if (this.propagandaTimer.delta() > 0) {
                        this.deactivatePropaganda();
                        this.propagandaTimer = null;
                    }
                }

                if (this.haveShield) {
                    if (this.lastTimeLeft <= 0) {
                        this.deactivateShield();
                    }
                }

                if (this.underAttackTimer && this.underAttackTimer.delta() > 0) {
                    this.underAttackTimer = null;
                }


                if (this.gameManager && this.capitalData.assignedPlayer && ig.game.client.isMyPlayerId(this.capitalData.assignedPlayer.playerId)) {
                    if (this.gameManager.targetList.length > 0) {
                        // if (ig.input.released('shoot')) console.log(this.gameManager.targetList, this.capitalData.capitalIndex, this.haveWarning);
                        for (var i = 0; i < this.gameManager.targetList.length; i++) {
                            if (this.gameManager.targetList[i] === this.capitalData.capitalIndex) {
                                if (!this.haveWarning) {
                                    // console.log('Activate Warning for capital: ', this.capitalData.capitalIndex);
                                    this.activateWarning();
                                }
                            } else if (this.gameManager.targetList[i] !== this.capitalData.capitalIndex && !this.deactivateWarningTimer && this.haveWarning) {
                                this.deactivateWarning();
                            }
                        }
                    }

                    if (this.deactivateWarningTimer && this.deactivateWarningTimer.delta() > 0) {
                        // console.log('Deactivate Warning for capital: ', this.capitalData.capitalIndex);
                        this.deactivateWarning();
                        this.deactivateWarningTimer = null;
                    }
                }

                if (ig.ua.mobile && !ig.ua.iPad && this.isActive) {
                    this.gameManager.currentTargetCapitalIndex = null;
                    var mousePosition = ig.game.io.getClickPos();
                    var gamePosition = dl.game.camera.getGamePositionFromScreenPosition(mousePosition.x, mousePosition.y);
                    var capital = this.gameManager.getPointerTarget(gamePosition);
                    if (capital && capital.capitalData.capitalIndex !== this.capitalData.capitalIndex) {
                        this.gameManager.currentTargetCapitalIndex = capital.capitalData.capitalIndex;
                        if (!capital.isHighlighted) {
                            capital.activateHighlight();
                        }
                    }
                }
            },

            draw: function (ctx) {
                this.parent(ctx);
                ctx.save();
                if (this.isActive || this.gameManager.currentTargetCapitalIndex == this.capitalData.capitalIndex) {
                    ctx.shadowOffsetX = 0;
                    ctx.shadowOffsetY = 0;
                    ctx.shadowColor = 'white';
                    ctx.shadowBlur = 30;
                }
                if (!this.capitalImage) {
                    ctx.translate(this.pos.x, this.pos.y);
                    ctx.beginPath();
                    ctx.fillStyle = this.color;
                    ctx.arc(
                        -this.enlargeOffset,
                        -this.enlargeOffset,
                        this.radius,
                        0,
                        360
                    );
                    ctx.fill();

                }
                ctx.restore();

                // var ctx=ig.system.context;
                // ctx.font = '40px montserrat';
                // ctx.fillStyle = '#101010';
                // ctx.fillText(this.capitalData.capitalIndex,this.pos.x-15,this.pos.y+120);

            }
        });
    // Enable cache
    // dl.enableCacheCanvas(dl.EntityGameMapContinent);
});
