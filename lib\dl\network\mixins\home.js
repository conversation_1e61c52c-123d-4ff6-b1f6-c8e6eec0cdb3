/**
 * Created by <PERSON><PERSON>
 * Usage:
 * dl.Entity.extend(MixinNetworkHome);
 */
ig.module(
    'dl.network.mixins.home'
).requires(
    'dl.templates.network-entities.entity-popup-connecting',
    'dl.templates.network-entities.entity-popup-matching',
    'dl.templates.network-entities.entity-popup-select-password',
    'dl.templates.network-entities.entity-popup-select-max-player',
    'dl.templates.network-entities.entity-popup-select-arena',
    'dl.templates.network-entities.entity-popup-reward-video'
).defines(function () {
    MixinNetworkHome = {
        staticInstantiate: function () {
            this.parent();

            this.onCheckNameSuccess = null;
            this.onCheckNameSuccess = null;

            return this;
        },

        postInit: function () {
            this.parent();
        },

        checkInviteCode: function () {
            if (!ig.game.client) return false;
            if (!ig.game.client.haveInviteCode) return false;

            ig.game.client.haveInviteCode = false;
            // ig.game.director.jumpTo(LevelTitle);

            // return true;
        },

        resetNetwork: function () {
            ig.game.client.reset();
        },

        checkConnection: function () {
            if (dl.game.networkClient.checkConnection()) return true;

            this.connectToServer();

            return false;
        },

        connectToServer: function () {
            this.connectingToServer = this.spawnEntity(dl.EntityConnectingPopup, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0.5, y: 0.5 },
                    dockerOffset: { x: 0, y: 0 }
                }
            });
        },

        checkName: function () {
            ig.game.client.requestCheckName(ig.game.sessionData.playerName, this.onCheckNameCallback.bind(this));
        },

        onCheckNameCallback: function (data) {
            if (!data) return false;

            if (typeof NameAndAvatar === 'undefined') {
                throw 'NameAndAvatar is undefined!';
            }

            switch (data.returnCode) {
                case NameAndAvatar.RETURN_CODE.VALID:
                    if (dl.check.isFunction(this.onCheckNameSuccess)) {
                        this.onCheckNameSuccess();
                    }

                    return true;
                    break;
                case NameAndAvatar.RETURN_CODE.EMPTY:
                    this.onCheckNameFail(_STRINGS.NETWORK.CHECK_NAME_NOTIFICATION_EMPTY);
                    break;
                case NameAndAvatar.RETURN_CODE.TOO_MUCH_LENGTH:
                    this.onCheckNameFail(_STRINGS.NETWORK.CHECK_NAME_NOTIFICATION_TOO_MUCH_LENGTH);
                    break;
                case NameAndAvatar.RETURN_CODE.CONTAIN_INVALID_CHARACTER:
                    this.onCheckNameFail(_STRINGS.NETWORK.CHECK_NAME_NOTIFICATION_CONTAIN_INVALID_CHARACTER);
                    break;
                case NameAndAvatar.RETURN_CODE.BAD_WORD:
                    this.onCheckNameFail(_STRINGS.NETWORK.CHECK_NAME_NOTIFICATION_BAD_WORD);
                    break;
                case NameAndAvatar.RETURN_CODE.INVALID:
                    break;
                default:
                    this.onCheckNameFail(_STRINGS.NETWORK.CHECK_NAME_NOTIFICATION_INVALID);
                    break;
            }
        },

        onCheckNameFail: function (text) {
            dl.scene.spawnNotification({
                notificationDrawConfigs: {
                    contentConfigs: [
                        {
                            type: 'text',
                            text: text,
                            fillStyle: dl.configs.TEXT_COLOR.WHITE,
                            fontSize: 46,
                            fontFamily: dl.configs.FONT.SOURCE_SANS.name
                        }],
                    backgroundConfigs: {
                        lineWidth: 2,
                        fillStyle: dl.configs.NOTIFICATION_COLORS.PLAYER_1,
                        strokeStyle: dl.configs.NOTIFICATION_COLORS.PLAYER_1,

                        box: { width: 800, height: 90, round: 10, padding: { x: 100, y: 5 } }
                    }
                }
            });
        },
        maxPopup:null,
        selectMaxPlayer: function (callback) {            
            this.maxPopup=this.spawnPopup(dl.EntityPopupSelectMaxPlayer, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0.5, y: 0.5 },
                    dockerOffset: { x: 0, y: 0 }
                },
                onPopupClose: callback
            });
        },

        selectArena: function (callback) {
            this.spawnPopup(dl.EntityPopupSelectArena, {
                _useParentScale: true,
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0.5, y: 0.5 },
                    dockerOffset: { x: 0, y: 0 }
                },
                onPopupClose: callback
            });
        },

        runPopupRV:function(callback){
            this.spawnPopup(dl.EntityPopupRewardVideo, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0.5, y: 0.5 },
                    dockerOffset: { x: 0, y: 0 }
                },
                onPopupClose: callback
            });
        },

        runPopupGems:function(callback){
            this.spawnPopup(dl.EntityPopupGems, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0.5, y: 0.5 },
                    dockerOffset: { x: 0, y: 0 }
                },
                onPopupClose: callback
            });
        },

        runPopupShop:function(callback){
            this.spawnPopup(dl.EntityPopupShop, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0.5, y: 0.5 },
                    dockerOffset: { x: 0, y: 0 }
                },
                onPopupClose: callback
            });
        },

        rewardVideo: function () {
            ig.game.disableEntities();
            this.runPopupRV(function () {
                ig.game.enableEntities();
            }.bind(this));
        },

        showGemsWindow: function () {
            ig.game.disableEntities();
            this.runPopupGems(function () {
                ig.game.enableEntities();
            }.bind(this));
        },

        showShopWindow: function () {
            ig.game.disableEntities();
            this.runPopupShop(function () {
                ig.game.enableEntities();
            }.bind(this));
        },

        selectPassword: function (callback) {
            this.spawnPopup(dl.EntityPopupSelectPassword, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0.5, y: 0.5 },
                    dockerOffset: { x: 0, y: 0 }
                },
                onPopupClose: callback
            });
        },

        randomMatch: function () {
            
            ig.game.disableEntities();
            // this.selectMaxPlayer(function () {
                // if (ig.game.client.roomMaxPlayer){
            this.selectArena(function () {
                ig.game.enableEntities();
                if (ig.game.client.roomArena!=0) {                    
                    if(ig.game.client.roomArena==4){
                        ig.game.client.roomMaxPlayer=0;
                        this.privateMatch();
                    }else{
                        ig.game.client.roomMaxPlayer=4;
                        this.enterRandomMatch();
                    }
                }
            }.bind(this));
                // }
                // ig.game.enableEntities();                    
          // }.bind(this));
            ig.game.client.saveGameData();
        },
        privateMatch: function () {
            this.selectPassword(function () {
                if (ig.game.client.roomPassword) {
                    if(ig.game.client.roomMaxPlayer && ig.game.client.roomMaxPlayer>1){
                        this.enterPrivateMatch()
                    }else{
                        this.selectMaxPlayer(function () {
                            this.enterPrivateMatch()
                        }.bind(this));
                    }
                }
            }.bind(this));
        },

        enterRandomMatch: function () {
            this.matching(function () {
                // ig.game.director.jumpTo(LevelMatching);
                ig.game.director.loadLevel(3);
            }.bind(this));
        },

        enterPrivateMatch: function () {
            this.matching(function () {
                ig.game.director.jumpTo(LevelMatchingPrivate);
                // this.tweenOut(function () {
                //     ig.game.director.jumpTo(LevelMatchingPrivate);
                // }.bind(this));
            }.bind(this));
        },

        matching: function (callback) {
            this.matchingPopup = this.spawnEntity(dl.EntityMatchingPopup, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0.5, y: 0.5 },
                    dockerOffset: { x: 0, y: 0 }
                },
                onMatchingSuccess: callback
            });
        }

        // spawnResultPopup: function () {
        //     this.spawnEntity(dl.EntityPopupGameResult, {
        //         c_DockerComponent: {
        //             dockerObject: dl.game.camera,
        //             dockerPercent: { x: 0.5, y: 0.5 },
        //             dockerOffset: { x: 0, y: 0 }
        //         }
        //     });
        // }
    };
});
