ig.module(
    'dl.templates.network-entities.entity-button-auto-start'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'dl.templates.entities.buttons.entity-button-image',
    'dl.templates.entities.entity-text'
).defines(function () {
    'use strict';

    dl.EntityButtonAutoStart = dl.Entity
        .extend(dl.MixinDocker)
        .extend({
            postInit: function () {
                this.parent();

                var xx=window.innerWidth;
                var yy=window.innerHeight;

                if(ig.ua.mobile && !ig.ua.iPad){
                    if(xx<yy){ //portrait
                        var bt1fontsize=60;

                    }else{  //landscape              
                        var bt1fontsize=60;
                    }
                }else{
                    var bt1fontsize=40;
                }        

                this.text = this.spawnEntity(dl.EntityText, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 1, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    anchor: { x: 1, y: 0.5 },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                        fontSize: bt1fontsize
                    },
                    text: _STRINGS.NETWORK.MATCHING_AUTO_START
                });

                this.button = this.spawnEntity(dl.EntityButtonAutoStart_Button, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    anchor: { x: 0, y: 0.5 },
                    onButtonClicked: this.onButtonClicked.bind(this)
                });

                this.calculateSize();
            },

            calculateSize: function () {
                this.setSize(
                    this.text.size.x + this.button.size.x + 10,
                    Math.max(this.text.size.y, this.button.size.y)
                );
            },

            setEnable: function (enable) {
                this.button.setEnable(enable);
            },

            onButtonClicked: function (newState) {
                if (!ig.game.client) return;
                if (!ig.game.client.clientGameRoom) return;

                ig.game.client.clientGameRoom.sendAutoStartStateChange(newState);
            },


            onEntitySpawn: function (entity) {
                entity._useParentScale = true;

                return entity;
            },

            tweenIn: function (time) {
                dl.TweenTemplate.fadeIn(this, time);
            },

            tweenOut: function (time) {
                dl.TweenTemplate.fadeOut(this, time);
            }
        });

    dl.EntityButtonAutoStart_Button = dl.Entity
        .extend(dl.MixinDocker)
        .extend(dl.MixinButton)
        .extend(dl.MixinAnimationSheet)
        .extend(dl.MixinButtonScaleEffect)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this._useAnimationSheetAsSize = true;
                this.image = dl.preload['button-check'];
                this.currentState = false;

                return this;
            },

            postInit: function () {
                this.parent();
                this.setState(this.currentState);
                this.onButtonClicked(true);
                this.onPointerReleased();
            },

            _initAnimationSheetComponent: function () {
                this.c_AnimationSheetComponent.updateProperties({
                    _animationSheetImage: this.image,
                    _animationSheetRow: 1,
                    _animationSheetCol: 2,
                    _setupDefaultAnimation: function () {
                        this.addAnimation('off', 0, [0], true);
                        this.addAnimation('on', 0, [1], true);
                        this.setAnimation('off');
                    }
                });
            },

            updateImage: function (image) {
                if (!image) return;

                this.c_AnimationSheetComponent.updateProperties({
                    _animationSheetImage: this.image,
                    _animationSheetRow: 1,
                    _animationSheetCol: 2,
                    _setupDefaultAnimation: function () {
                        this.addAnimation('off', 0, [0], true);
                        this.addAnimation('on', 0, [1], true);
                        this.setAnimation('off');
                    }
                });
            },

            _updateAnimation: function (newState) {
                if (newState) {
                    this.c_AnimationSheetComponent.setAnimation('on');
                } else {
                    this.c_AnimationSheetComponent.setAnimation('off');
                }
            },

            setState: function (isOn) {
                this.currentState = isOn;
                this._updateAnimation(isOn);
            },

            onPointerReleased: function () {
                this.setState(!this.currentState);
                this.onButtonClicked(this.currentState);
            }
        });

    // Enable cache
    dl.enableCacheCanvas(dl.EntityButtonAutoStart);
});
