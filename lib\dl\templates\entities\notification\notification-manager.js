/**
 * Usage:
 * ig.game.notificationManager.spawnNotification("Notification Text");
 */

ig.module(
    'dl.templates.entities.notification.notification-manager'
).requires(
    'dl.templates.entities.notification.entity-notification-image-background'
).defines(function () {
    'use strict';

    dl.NotificationManager = function (entityManager) {
        var self = {};

        self.init = function (entityManager) {
            self.entityManager = entityManager;

            self.MAX_NOTIFICATION = 3;

            self.notificationStack = [];
            self.delayNotificationStack = [];

            self.nextNotificationTime = Date.now();

            self.NEXT_NOTIFICATION_DELAY = dl.EntityNotification.APPEAR_TIME;
        };

        self.reset = function () {
            self.notificationStack = [];
            self.delayNotificationStack = [];

            self.nextNotificationTime = Date.now();
        };

        self.update = function () {
            self.updateNotification();
        };

        self._spawnNotification = function (notificationData) {
            var newNotification = self.entityManager.spawnEntity(dl.EntityNotification, {
                orientationData: {
                    landscape: {
                        c_DockerComponent: {
                            dockerObject: dl.game.camera,
                            dockerPercent: { x: 0.5, y: 0.5 },
                            dockerOffset: { x: 0, y: 0 }
                        }
                    },
                    portrait: {
                        c_DockerComponent: {
                            dockerObject: dl.game.camera,
                            dockerPercent: { x: 0.5, y: 0.25 },
                            dockerOffset: { x: 0, y: 0 }
                        }
                    }
                },
                c_TextComponent: {
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'WHITE'),
                    fontSize: 50,
                    fontFamily: dl.configs.getConfig('FONT', 'SOURCE_SANS').name
                },
                text: notificationData.notificationText,
                bgColor: notificationData.notificationSettings.bgColor
            });

            self.pushNewNotification(newNotification);
            return newNotification;
        };

        self.spawnNotification = function (notificationText, notificationSettings) {
            if (typeof notificationSettings === 'undefined') notificationSettings = {};
            if (typeof notificationSettings.bgColor === 'undefined') notificationSettings.bgColor = dl.configs.NOTIFICATION_COLORS.NEUTRAL;
            self.delayNotificationStack.push({
                notificationAcceptTime: Date.now(),
                notificationText: notificationText,
                notificationSettings: notificationSettings
            });

            self.updateNotification();
        };

        self.updateNotification = function () {
            var timeNow = Date.now();
            if ((timeNow - self.nextNotificationTime) >= 0) {
                var notificationData = self.delayNotificationStack.shift();
                if (!notificationData) return;

                self._spawnNotification(notificationData);
                self.nextNotificationTime = timeNow + self.NEXT_NOTIFICATION_DELAY;
            }
        };

        self.pushNewNotification = function (newNotification) {
            self.notificationStack.unshift(newNotification);
            self.notificationStack = self.notificationStack.filter(function (entity) {
                return !entity.killed;
            });

            if (!self.notificationStack.length) return;

            for (var i = 0; i < self.notificationStack.length; i++) {
                var notification = self.notificationStack[i];

                notification.updateNotificationIndex(i);
                if (i >= self.MAX_NOTIFICATION) {
                    notification.tweenOut();
                }
            }

            newNotification.ready();
        };

        self.init(entityManager);
        return self;
    };
});
