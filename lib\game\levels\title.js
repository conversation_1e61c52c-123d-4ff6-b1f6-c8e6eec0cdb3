ig.module('game.levels.title')
.requires(
    'dl.templates.entities.entity-level',
    'dl.network.mixins.home',
    'dl.templates.entities.backgrounds.entity-color-background',
    'dl.templates.entities.buttons.entity-button-fullscreen',
    'dl.templates.entities.buttons.entity-button-sound',
    'dl.templates.entities.entity-image',
    'dl.templates.entities.buttons.entity-button-image-text',
    'dl.templates.network-entities.name-and-avatar.entity-name-avatar-input',
    'dl.templates.network-entities.entity-server-status',
    'dl.templates.network-entities.entity-popup-select-server',
    'game.entities.upgrade',
    'game.entities.controller-rv'   ,
    'dl.templates.network-entities.entity-popup-gems',
    'dl.templates.network-entities.entity-popup-shop'
)
.defines(function () {
    LevelTitle = {
        entities: [{
            type: 'EntityTitleController',
            x: 0,
            y: 0
        }],
        layer: [],
        useCustomLib: true
    };

    EntityTitleController = dl.EntityLevel
        .extend(MixinNetworkHome)
        .extend({
        upgradeName:['Military Monday','Tactical Tuesday','War Machine Wednesday','Trouble Thursday','Full Attack Friday','Slugfest Saturday','Sabotage Sunday'],
        server:0,
        code:'',
        postInit: function () {
            this.parent();

            this.initEntities();

            if(ig.game.defaultServer==10){
                // this.checkConnection();                
                ig.game.popupStatus = true;
                dl.scene.spawnPopup(dl.EntityPopupSelectServer);
            }else{
                
                SERVER_CONFIGS.SERVER_IP = SERVER_CONFIGS.SERVER_IPS[ig.game.defaultServer];
                dl.game.serverIP=SERVER_CONFIGS.SERVER_IPS[ig.game.defaultServer];                
                this.checkConnection();                                    
            }

            ig.game.client.roomMaxPlayer=0;
            ig.game.title = this;
            ig.game.minimalTweenFlag = false;
            window.gameStart = false;

        },
        initEntities: function () {
            this.initBackgroundAndTitle();
            this.initSubButtons();
            this.initMainButtons();
            this.initNameAvatar();
            this.initServerStatus();
            this.initCoins();
            // if(!ig.ua.mobile){
                setTimeout(function(){
                    this.initAchievmentDot();                
                }.bind(this),1000);                
            // }
        },
        initAchievmentDot:function(){
            var centerX=dl.game.camera.size.x+100;
            var centerY=dl.game.camera.size.y+100;
            this.notifdot=ig.game.spawnEntityBackward(ig.AchievementNotificationDot, centerX, centerY, { zIndex: this.btReward.zIndex });            
        },
        initBackgroundAndTitle: function () {

            var xx=window.innerWidth;
            var yy=window.innerHeight;

            if(ig.ua.mobile && !ig.ua.iPad){
                if(xx<yy){ //portrait
                    var titlepos={x:0.5,y:0.17};                                    
                }else{  //landscape              
                    var titlepos={x:0.5,y:0.2};
                }
            }else{
                var titlepos={x:0.5,y:0.2};
            }      



            this.background = this.spawnEntity(dl.EntityColorBackground, {});
            this.title = this.spawnEntity(dl.EntityImage, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: titlepos,
                    dockerOffset: { x: 0, y: 0 }
                },
                image: dl.preload.title
            });

            this.coin = this.spawnEntity(dl.EntityImage, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0, y: 0 },
                    dockerOffset: { x: 30, y: 20 }
                },
                image: dl.preload['coin'],
                c_AnimationSheetComponent: {
                    _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                    _size: {x:100,y:100}
                },
                anchor: { x: 0, y: 0 }
            });

            this.gems = this.spawnEntity(dl.EntityImage, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0, y: 0 },
                    dockerOffset: { x: 45, y: 150 }
                },
                image: dl.preload['gems3'],
                c_AnimationSheetComponent: {
                    _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                    _size: {x:70,y:100}
                },
                anchor: { x: 0, y: 0 }
            });

            this.allianceIcon = this.spawnEntity(dl.EntityImage, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0, y: 0 },
                    dockerOffset: { x: 30, y: 300 }
                },
                image: dl.preload['iconNoAlliance'],
                c_AnimationSheetComponent: {
                    _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                    _size: {x:110,y:80}
                },
                anchor: { x: 0, y: 0 }
            });

        },

        initSubButtons: function () {

            if(ig.ua.mobile && !ig.ua.iPad){

                this.btnFullscreen = this.spawnEntity(dl.EntityButtonFullScreen, {
                    c_DockerComponent: {
                        dockerObject: dl.game.camera,
                        dockerPercent: { x: 1, y: 0 },
                        dockerOffset: { x: -15, y: 15 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:180,y:180}
                    },
                    anchor: { x: 1, y: 0 }
                });

                this.btnSound = this.spawnEntity(dl.EntityButtonSound, {
                    c_DockerComponent: {
                        dockerObject: dl.game.camera,
                        dockerPercent: { x: 0.96, y: 0 },
                        dockerOffset: { x: 15, y: 15 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:180,y:180}
                    },
                    anchor: { x: 0, y: 0 }
                });

            }else{

                this.btnFullscreen = this.spawnEntity(dl.EntityButtonFullScreen, {
                    c_DockerComponent: {
                        dockerObject: dl.game.camera,
                        dockerPercent: { x: 1, y: 0 },
                        dockerOffset: { x: -15, y: 15 }
                    },
                    anchor: { x: 1, y: 0 },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: { x: 80, y: 80 }
                    },
                    });

                this.btnSound = this.spawnEntity(dl.EntityButtonSound, {
                    c_DockerComponent: {
                        dockerObject: dl.game.camera,
                        dockerPercent: { x: 0.94, y: 0 },
                        dockerOffset: { x: 15, y: 15 }
                    },
                    anchor: { x: 0, y: 0 }
                });

            }


            this.utcbg = this.spawnEntity(dl.EntityButtonImageText, {
                _useParentScale: true,
                c_DockerComponent: {
                    dockerObject: this.btnFullscreen,
                    dockerPercent: { x: 1, y: 1 },
                    dockerOffset: { x:  0, y: 100 }
                },
                image: dl.preload['utcbox'+ig.game.today],
                c_AnimationSheetComponent: {
                    _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                    _size: {x:600,y:200}
                },
                anchor: { x: 1, y: 0 },
                text: '',
                onPointerOver:function(){
                    if(this.utcEvent){
                        this.utcEvent.c_TextComponent.fontSize=34;
                    } 
                }.bind(this),
                onPointerLeave:function(){
                    if(this.utcEvent){
                        this.utcEvent.c_TextComponent.fontSize=36;
                    }                     
                }.bind(this),
                onPointerFirstClick:function(){
                    if(this.utcEvent){
                        this.utcEvent.c_TextComponent.fontSize=32;
                    }                                         
                }.bind(this),
                onButtonClicked: function () {

                    if(ig.game.disableBtStatus) return;
                    ig.game.disableEntities();
                    ig.game.showUpgradeStatus=false;
                    var reward = ig.game.spawnEntityBackward(ig.AchievementPopup);
                }.bind(this)
            });

            this.utcDate = this.spawnEntity(dl.EntityText, {
                _useParentScale: true,
                c_DockerComponent: {
                    dockerObject: this.utcbg,
                    dockerPercent: { x: 1, y: 0 },
                    dockerOffset: { x: 0, y: -10 }
                },
                c_TextComponent: {
                    textAlign:'center',
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                    fontSize: 46,
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                },
                anchor: { x: 1, y: 1 },
                text:'',
            });



            var nameIndex=ig.game.today-1;
            if(nameIndex<0) nameIndex=6;
            this.utcEvent = this.spawnEntity(dl.EntityText, {
                _useParentScale: true,
                c_DockerComponent: {
                    dockerObject: this.utcbg,
                    dockerPercent: { x: 0.5, y: 1 },
                    dockerOffset: { x: 0, y: -20 }
                },
                c_TextComponent: {
                    textAlign:'center',
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                    fontSize: 36,
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'WHITE'),
                },
                anchor: { x: 0.5, y: 1 },
                text:'',
            });

            this.btnShowUTC = this.spawnEntity(dl.EntityButtonImageText, {
                _useParentScale: true,
                c_DockerComponent: {
                    dockerObject: this.utcbg,
                    dockerPercent: {x:0,y:0.5},
                    dockerOffset: {x:-10,y:0}
                },
                anchor: {x:1,y:0.5},
                c_AnimationSheetComponent: {
                    _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                    _size: {x:40,y:110}
                },
                c_TextComponent: {
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                    fontSize: 40,
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name
                },
                image: dl.preload['btnright'],
                text: '',
                statusPos:0,
                onButtonClicked: function () {
                    if(ig.game.disableBtStatus) return;
                    if(this.btnShowUTC.statusPos==0){
                        this.utcbg.createTween()
                            .to({
                                // c_DockerComponent: {dockerPercent:{x:0.76}} 
                                anchor:{x:0} 
                            }, 700, {
                                easing: ig.Tween.Easing.Cubic.EaseOut,
                                onPropertiesChanged: function () {
                                    this.updatePos();
                                }.bind(this.utcbg)
                            })
                            .start({
                                onCompleteTween: function () {
                                    this.statusPos = 1;
                                    this.updateImage(dl.preload['btnleft']);
                                }.bind(this.btnShowUTC)
                        });
                    }else{
                        this.utcbg.createTween()
                            .to({
                                // c_DockerComponent: {dockerPercent:{x:1}} 
                                anchor:{x:1} 
                            }, 700, {
                                easing: ig.Tween.Easing.Cubic.EaseOut,
                                onPropertiesChanged: function () {
                                    this.updatePos();
                                }.bind(this.utcbg)
                            })
                            .start({
                                onCompleteTween: function () {
                                    this.statusPos = 0;
                                    this.updateImage(dl.preload['btnright']);
                                }.bind(this.btnShowUTC)
                        });                        
                    }                

                }.bind(this)
            });

            if(ig.game.showFPS){            
                this.fps = this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: dl.game.camera,
                        dockerPercent: { x: 0, y: 0.3 },
                        dockerOffset: { x: 50, y: 0 }
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                        fontSize: 40
                    },
                    anchor:{x:0,y:0},
                    text:ig.system.fps.toFixed()
                });
            }
        },

        initMainButtons: function () {

            var xx=window.innerWidth;
            var yy=window.innerHeight;

            if(ig.ua.mobile && !ig.ua.iPad){

                if(xx<yy){ //portrait
                    var offset = 400;
                    var bt1size={x:700,y:180};
                    var bt1fontsize=54;
                    var bt2size={x:700,y:180};
                    var bt2fontsize=54;
                    var bt3size={x:700,y:180};
                    var bt3fontsize=54;
                    var bt4size={x:700,y:180};
                    var bt4fontsize=54;
                    var bt5size={x:300,y:100};
                    var bt5fontsize=40;

                    var bt1dockerPercent= { x: 0.5, y: 0.65 };
                    var bt1dockerOffset= { x: 0, y: 0 };

                    var bt2dockerPercent= { x: 0.5, y: 0.73 };
                    var bt2dockerOffset= { x: 0, y: 0 };

                    var bt3dockerPercent= { x: 0.5, y: 0.768 };
                    var bt3dockerOffset= { x: 0, y: 0 };

                    var bt4dockerPercent= { x: 0.5, y: 0.84 };
                    var bt4dockerOffset= { x: 0, y: 0 };

                    var bt5dockerPercent= { x: 0.5, y: 0.91 };
                    var bt5dockerOffset= { x: -offset, y: 0 };

                    var bt6dockerPercent= { x: 0.5, y: 0.94 };
                    var bt6dockerOffset= { x: offset, y: 0 };

                }else{  //landscape              
                    var offset = 400;
                    var bt1size={x:700,y:180};
                    var bt1fontsize=54;
                    var bt2size={x:700,y:180};
                    var bt2fontsize=54;
                    var bt3size={x:700,y:180};
                    var bt3fontsize=54;
                    var bt4size={x:700,y:180};
                    var bt4fontsize=54;

                    var bt5size={x:400,y:120};
                    var bt5fontsize=44;

                    var bt1dockerPercent= { x: 0.5, y: 0.75 };
                    var bt1dockerOffset= { x: -offset, y: 0 };

                    var bt2dockerPercent= { x: 0.5, y: 0.75 };
                    var bt2dockerOffset= { x: offset, y: 0 };

                    var bt3dockerPercent= { x: 0.5, y: 0.83 };
                    var bt3dockerOffset= { x: offset, y: 0 };

                    var bt4dockerPercent= { x: 0.5, y: 0.83 };
                    var bt4dockerOffset= { x: -offset, y: 0 };

                    var bt5dockerPercent= { x: 0.5, y: 0.91 };
                    var bt5dockerOffset= { x: -offset, y: 0 };

                    var bt6dockerPercent= { x: 0.5, y: 0.94 };
                    var bt6dockerOffset= { x: offset, y: 0 };

                }

            }else{
                var offset = 400;
                var bt1size={x:580,y:121};
                var bt1fontsize=40;
                var bt2size={x:580,y:121};
                var bt2fontsize=40;
                var bt3size={x:580,y:121};
                var bt3fontsize=40;
                var bt4size={x:580,y:121};
                var bt4fontsize=40;

                var bt5size={x:300,y:70};
                var bt5fontsize=36;

                var bt1dockerPercent= { x: 0.5, y: 0.75 };
                var bt1dockerOffset= { x: -offset, y: 0 };

                var bt2dockerPercent= { x: 0.5, y: 0.75 };
                var bt2dockerOffset= { x: offset, y: 0 };

                var bt3dockerPercent= { x: 0.5, y: 0.83 };
                var bt3dockerOffset= { x: offset, y: 0 };

                var bt4dockerPercent= { x: 0.5, y: 0.83 };
                var bt4dockerOffset= { x: -offset, y: 0 };

                var bt5dockerPercent= { x: 0.5, y: 0.91 };
                var bt5dockerOffset= { x: -offset, y: 0 };

                var bt6dockerPercent= { x: 0.5, y: 0.94 };
                var bt6dockerOffset= { x: -offset, y: 0 };

            }            


            this.btnRandom = this.spawnEntity(dl.EntityButtonImageText, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: bt1dockerPercent,
                    dockerOffset: bt1dockerOffset
                },
                c_AnimationSheetComponent: {
                    _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                    _size: bt1size
                },
                c_TextComponent: {
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                    fontSize: bt1fontsize,
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name
                },
                image: dl.preload['button-green'],
                text: _STRINGS.Button.Play,
                onButtonClicked: function () {
                    if(ig.game.disableBtStatus) return;
                    if (!this.checkConnection()) return;
                    // this.notifdot.tempAlpha=this.notifdot.alpha;
                    // this.notifdot.alpha=0;
                    ig.game.matchingAllianceData=[];
                    this.onCheckNameSuccess = this.randomMatch.bind(this);
                    if (!this.checkName()) return;

                }.bind(this)
            });
         

            this.btLeaderboard = this.spawnEntity(dl.EntityButtonImageText, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: bt2dockerPercent,
                    dockerOffset: bt2dockerOffset
                },
                c_AnimationSheetComponent: {
                    _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                    _size: bt2size
                },
                c_TextComponent: {
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                    fontSize: bt2fontsize,
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name
                },
                image: dl.preload['button-green'],
                text: _STRINGS.Button.Leaderboard,
                onButtonClicked: function () {
                    if(ig.game.disableBtStatus) return;
                    try {
                        ig.svas.showLeaderboard();
                    }catch(e){}

                }.bind(this)
            });

            this.btReward = this.spawnEntity(dl.EntityButtonImageText, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: bt4dockerPercent,
                    dockerOffset: bt4dockerOffset
                },
                c_AnimationSheetComponent: {
                    _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                    _size: bt4size
                },
                c_TextComponent: {
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                    fontSize: bt4fontsize,
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name
                },
                image: dl.preload['button-green'],
                text: 'DAILY EVENT',
                onPointerOver:function(){
                    if(this.circleRed){
                        this.circleRed.c_DockerComponent.dockerOffset={ x: -20, y: 20 };
                        this.circleRedText.c_DockerComponent.dockerOffset={ x: -20, y: 20 };
                    } 
                }.bind(this),
                onPointerLeave:function(){
                    if(this.circleRed){
                        this.circleRed.c_DockerComponent.dockerOffset={ x: -10, y: 10 };
                        this.circleRedText.c_DockerComponent.dockerOffset={ x: -10, y: 10 };
                    }                     
                }.bind(this),
                onPointerFirstClick:function(){
                    if(this.circleRed){
                        this.circleRed.c_DockerComponent.dockerOffset={ x: -25, y: 25 };
                        this.circleRedText.c_DockerComponent.dockerOffset={ x: -25, y: 25 };
                    }                                         
                }.bind(this),
                onButtonClicked: function () {
                    if(ig.game.disableBtStatus) return;

                    ig.game.disableEntities();
                    ig.game.showUpgradeStatus=false;
                    var reward = ig.game.spawnEntityBackward(ig.AchievementPopup);
                }.bind(this)
            });
            window.btReward = this.btReward;

            this.circleRed = this.spawnEntity(dl.EntityImage, {
                _useParentScale: true,
                c_DockerComponent: {
                    dockerObject: this.btReward,
                    dockerPercent: { x: 1, y: 0 },
                    dockerOffset: { x: -10, y: 10 }
                },
                anchor: { x: 0.5, y: 0.5 },
                c_AnimationSheetComponent: {
                    _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                    _size: {x:66,y:66}
                },
                image: dl.preload['circle-red'],
                visible:true,
            });

            this.circleRedText = this.spawnEntity(dl.EntityText, {
                c_DockerComponent: {
                    dockerObject: this.btReward,
                    dockerPercent: { x: 1, y: 0 },
                    dockerOffset: { x: -10, y: 10 }
                },
                anchor: { x: 0.5, y: 0.5 },
                c_TextComponent: {
                    textAlign:'center',
                    fontSize: 30,
                    fillStyle:'#ffffff',
                    fontFamily: dl.configs.getConfig('FONT', 'default').name,
                },
                text:'',
            });



          
            this.btRv = this.spawnEntity(dl.EntityButtonImageText, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: bt3dockerPercent,
                    dockerOffset: bt3dockerOffset
                },
                c_AnimationSheetComponent: {
                    _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                    _size: bt4size
                },
                c_TextComponent: {
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                    fontSize: bt4fontsize,
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name
                },
                image: dl.preload['button-red'],
                text: 'GET MORE COINS',
                onPointerOver:function(){
                    if(this.coinRv){
                        this.coinRv.c_AnimationSheetComponent._size={x:60,y:60};
                    } 
                }.bind(this),
                onPointerLeave:function(){
                    if(this.coinRv){
                        this.coinRv.c_AnimationSheetComponent._size={x:70,y:70};
                    }                     
                }.bind(this),
                onPointerFirstClick:function(){
                    if(this.coinRv){
                        this.coinRv.c_AnimationSheetComponent._size={x:50,y:50};
                    }                                         
                }.bind(this),
                onButtonClicked: function () {
                    if(this.coinRv){
                        this.coinRv.c_AnimationSheetComponent._size={x:70,y:70};
                    }                     
                    if(ig.game.disableBtStatus) return;
                    if (!this.checkConnection()) return;
                    // this.notifdot.tempAlpha=this.notifdot.alpha;
                    // this.notifdot.alpha=0;
                    this.rewardVideo();
                    // this.onCheckNameSuccess = this.randomMatch.bind(this);
                    if (!this.checkName()) return;
                }.bind(this)
            });            

            this.coinRv = this.spawnEntity(dl.EntityImage, {
                _useParentScale: true,
                c_DockerComponent: {
                    dockerObject: this.btRv,
                    dockerPercent: { x: 0.88, y: 0.5 },
                    dockerOffset: { x: -20, y: 0 }
                },
                anchor: { x: 0, y: 0.5 },
                c_AnimationSheetComponent: {
                    _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                    _size: {x:70,y:70}
                },
                image: dl.preload['coin']
            });


            this.btGems = this.spawnEntity(dl.EntityButtonImageText, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: bt6dockerPercent,
                    dockerOffset: bt6dockerOffset
                },
                c_AnimationSheetComponent: {
                    _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                    _size: bt4size
                },
                c_TextComponent: {
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                    fontSize: bt4fontsize,
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name
                },
                image: dl.preload['button-red'],
                text: 'GET MORE GEMS',
                onPointerOver:function(){
                    if(this.gemsIcon){
                        this.gemsIcon.c_AnimationSheetComponent._size={x:60,y:70};
                    } 
                }.bind(this),
                onPointerLeave:function(){
                    if(this.gemsIcon){
                        this.gemsIcon.c_AnimationSheetComponent._size={x:70,y:80};
                    }                     
                }.bind(this),
                onPointerFirstClick:function(){
                    if(this.gemsIcon){
                        this.gemsIcon.c_AnimationSheetComponent._size={x:50,y:60};
                    }                                         
                }.bind(this),
                onButtonClicked: function () {
                    if(this.gemsIcon){
                        this.gemsIcon.c_AnimationSheetComponent._size={x:70,y:80};
                    }                     
                    if(ig.game.disableBtStatus) return;
                    if (!this.checkConnection()) return;
                    this.showGemsWindow();
                }.bind(this)
            });

            this.gemsIcon = this.spawnEntity(dl.EntityImage, {
                _useParentScale: true,
                c_DockerComponent: {
                    dockerObject: this.btGems,
                    dockerPercent: { x: 0.86, y: 0.5 },
                    dockerOffset: { x: -10, y: 0 }
                },
                anchor: { x: 0, y: 0.5 },
                c_AnimationSheetComponent: {
                    _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                    _size: {x:70,y:80}
                },
                image: dl.preload['gems3']
            });


            this.btShop = this.spawnEntity(dl.EntityButtonImageText, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: bt6dockerPercent,
                    dockerOffset: bt6dockerOffset
                },
                c_AnimationSheetComponent: {
                    _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                    _size: bt4size
                },
                c_TextComponent: {
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                    fontSize: bt4fontsize,
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name
                },
                image: dl.preload['button-green'],
                text: 'SHOP',
                onButtonClicked: function () {
                    if(ig.game.disableBtStatus) return;
                    if (!this.checkConnection()) return;
                    this.showShopWindow();
                }.bind(this)
            });


            this.btAlliance = this.spawnEntity(dl.EntityButtonImageText, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: {x:0,y:0},
                    dockerOffset: {x:160,y:340}
                },
                c_AnimationSheetComponent: {
                    _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                    _size: bt5size
                },
                c_TextComponent: {
                    textAlign:'left',
                    fillStyle: "#ffffff",
                    fontSize: bt5fontsize,
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                },
                anchor: { x: 0, y: 0.5 },
                image: dl.preload['button-white-small'],
                text: '+ Alliance',
                onButtonClicked: function () {
                    if(ig.game.disableBtStatus) return;
                    try {
                        ig.alliance.showMainWindow();
                    }catch(e){}
                }.bind(this)
            });


        },
        updateMenuPos:function(){


            var xx=window.innerWidth;
            var yy=window.innerHeight;

            if(ig.ua.mobile && !ig.ua.iPad){

                if(xx<yy){ //portrait
                    var offset = 400;

                    var titlepos={x:0.5,y:0.17};
                    var avatarpos={x:0.5,y:0.35};


                    var bt1size={x:700,y:180};
                    var bt1fontsize=54;
                    var bt2size={x:700,y:180};
                    var bt2fontsize=54;
                    var bt3size={x:700,y:180};
                    var bt3fontsize=54;
                    var bt4size={x:300,y:100};
                    var bt4fontsize=40;

                    var bt1dockerPercent= { x: 0.5, y: 0.5 };
                    var bt1dockerOffset= { x: 0, y: 0 };

                    var bt2dockerPercent= { x: 0.5, y: 0.57 };
                    var bt2dockerOffset= { x: 0, y: 0 };


                    var bt4dockerPercent= { x: 0.5, y: 0.64 };
                    var bt4dockerOffset= { x: 0, y: 0 };

                    var bt5dockerPercent= { x: 0.5, y: 0.71 };
                    var bt5dockerOffset= { x: 0, y: 0 };

                    var bt6dockerPercent= { x: 0.5, y: 0.78 };
                    var bt6dockerOffset= { x: 0, y: 0 };

                    var bt7dockerPercent= { x: 0.5, y: 0.85 };
                    var bt7dockerOffset= { x: 0, y: 0 };


                    var bt3dockerPercent= { x: 0.5, y: 0.95 };
                    var bt3dockerOffset= { x: 0, y: 0 };

                    var btFullScreendockerPercent= { x: 1, y: 0 };
                    var btFullScreendockerOffset= { x: -420, y: 20 };

                    var svasCoinsPercent= { x: 0, y: 0 };
                    var svasCoinsOffset= { x: 160, y: 30 };

                    var svasGemsPercent= { x: 0, y: 0 };
                    var svasGemsOffset= { x: 160, y: 150 };

                }else{  //landscape              
                    var offset = 380;

                    var titlepos={x:0.5,y:0.2};
                    var avatarpos={x:0.5,y:0.5};

                    var bt1size={x:700,y:180};
                    var bt1fontsize=54;
                    var bt2size={x:700,y:180};
                    var bt2fontsize=54;
                    var bt3size={x:700,y:180};
                    var bt3fontsize=54;
                    var bt4size={x:400,y:120};
                    var bt4fontsize=44;

                    var bt1dockerPercent= { x: 0.5, y: 0.70 };
                    var bt1dockerOffset= { x: -offset, y: 0 };

                    var bt2dockerPercent= { x: 0.5, y: 0.70 };
                    var bt2dockerOffset= { x: offset, y: 0 };


                    var bt4dockerPercent= { x: 0.5, y: 0.81 };
                    var bt4dockerOffset= { x: -offset, y: 0 };

                    var bt5dockerPercent= { x: 0.5, y: 0.81 };
                    var bt5dockerOffset= { x: offset, y: 0 };


                    var bt6dockerPercent= { x: 0.5, y: 0.92 };
                    var bt6dockerOffset= { x: -offset, y: 0 };

                    var bt7dockerPercent= { x: 0.5, y: 0.92 };
                    var bt7dockerOffset= { x: offset, y: 0 };



                    var bt3dockerPercent= { x: 0, y: 1 };
                    var bt3dockerOffset= { x: offset/2+170, y: -10 };

                    var btFullScreendockerPercent= { x: 1, y: 0 };
                    var btFullScreendockerOffset= { x: -460, y: 20 };

                    var svasCoinsPercent= { x: 0, y: 0 };
                    var svasCoinsOffset= { x: 160, y: 40 };

                    var svasGemsPercent= { x: 0, y: 0 };
                    var svasGemsOffset= { x: 160, y: 160 };
                }


            }else{
                var offset = 350;

                var titlepos={x:0.5,y:0.2};
                var avatarpos={x:0.5,y:0.5};

                var bt1size={x:580,y:121};
                var bt1fontsize=40;
                var bt2size={x:580,y:121};
                var bt2fontsize=40;
                var bt3size={x:580,y:121};
                var bt3fontsize=40;
                var bt4size={x:580,y:121};
                var bt4fontsize=40;

                var bt1dockerPercent= { x: 0.5, y: 0.70 };
                var bt1dockerOffset= { x: -offset, y: 0 };

                var bt2dockerPercent= { x: 0.5, y: 0.70 };
                var bt2dockerOffset= { x: offset, y: 0 };


                var bt4dockerPercent= { x: 0.5, y: 0.78 };
                var bt4dockerOffset= { x: -offset, y: 0 };

                var bt5dockerPercent= { x: 0.5, y: 0.78 };
                var bt5dockerOffset= { x: offset, y: 0 };

                var bt6dockerPercent= { x: 0.5, y: 0.86 };
                var bt6dockerOffset= { x: -offset, y: 0 };

                var bt7dockerPercent= { x: 0.5, y: 0.86 };
                var bt7dockerOffset= { x: offset, y: 0 };


                var bt3dockerPercent= { x: 0, y: 1 };
                var bt3dockerOffset= { x: offset/2+130, y: -10 };

                var btFullScreendockerPercent= { x: 1, y: 0 };
                var btFullScreendockerOffset= { x: -220, y: 20 };

                var svasCoinsPercent= { x: 0, y: 0 };
                var svasCoinsOffset= { x: 160, y: 40 };

                var svasGemsPercent= { x: 0, y: 0 };
                var svasGemsOffset= { x: 160, y: 160 };
            }      

            if(this.title) this.title.c_DockerComponent.dockerPercent=titlepos;
            if(this.nameAvatar) this.nameAvatar.c_DockerComponent.dockerPercent=avatarpos;
            

            if(this.svasCoins) this.svasCoins.c_DockerComponent.dockerPercent=svasCoinsPercent;
            if(this.svasCoins) this.svasCoins.c_DockerComponent.dockerOffset=svasCoinsOffset;
            if(this.svasCoins) this.svasCoins.updatePos();

            if(this.svasGems) this.svasGems.c_DockerComponent.dockerPercent=svasGemsPercent;
            if(this.svasGems) this.svasGems.c_DockerComponent.dockerOffset=svasGemsOffset;
            if(this.svasGems) this.svasGems.updatePos();

            if(this.btnSound) this.btnSound.c_DockerComponent.dockerPercent=btFullScreendockerPercent;
            if(this.btnSound) this.btnSound.c_DockerComponent.dockerOffset=btFullScreendockerOffset;
            if(this.btnSound) this.btnSound.updatePos();


            if(this.btnRandom) this.btnRandom.c_DockerComponent.dockerPercent=bt1dockerPercent;
            if(this.btnRandom) this.btnRandom.c_DockerComponent.dockerOffset=bt1dockerOffset;
            if(this.btnRandom) this.btnRandom.updatePos();

            if(this.btLeaderboard) this.btLeaderboard.c_DockerComponent.dockerPercent=bt2dockerPercent;
            if(this.btLeaderboard) this.btLeaderboard.c_DockerComponent.dockerOffset=bt2dockerOffset;
            if(this.btLeaderboard) this.btLeaderboard.updatePos();


            if(this.btReward) this.btReward.c_DockerComponent.dockerPercent=bt4dockerPercent;
            if(this.btReward) this.btReward.c_DockerComponent.dockerOffset=bt4dockerOffset;
            if(this.btReward) this.btReward.updatePos();

            if(this.btRv) this.btRv.c_DockerComponent.dockerPercent=bt6dockerPercent;
            if(this.btRv) this.btRv.c_DockerComponent.dockerOffset=bt6dockerOffset;
            if(this.btRv) this.btRv.updatePos();

            if(this.btGems) this.btGems.c_DockerComponent.dockerPercent=bt7dockerPercent;
            if(this.btGems) this.btGems.c_DockerComponent.dockerOffset=bt7dockerOffset;
            if(this.btGems) this.btGems.updatePos();

            if(this.btShop) this.btShop.c_DockerComponent.dockerPercent=bt5dockerPercent;
            if(this.btShop) this.btShop.c_DockerComponent.dockerOffset=bt5dockerOffset;
            if(this.btShop) this.btShop.updatePos();


            if(this.btnChangeServer) this.btnChangeServer.c_DockerComponent.dockerPercent=bt3dockerPercent;
            if(this.btnChangeServer) this.btnChangeServer.c_DockerComponent.dockerOffset=bt3dockerOffset;
            if(this.btnChangeServer) this.btnChangeServer.updatePos();

            if(ig.Alliance && ig.game.svasData.allianceIcon!=null){                
                if(this.allianceIcon) this.allianceIcon.updateImage(dl.preload['allianceIcon'+ig.game.svasData.allianceIcon]);
                if(this.btAlliance) this.btAlliance.updateText(ig.game.svasData.allianceName);
            }else{
                if(this.allianceIcon) this.allianceIcon.updateImage(dl.preload['iconNoAlliance']);
                if(this.btAlliance) this.btAlliance.updateText('+ Alliance');                
            }

        },
        passStatus:true,
        checkRoomPassword:function(){


            if(this.passStatus && ig.game.client.roomPassword!="" && !ig.game.isHost && ig.game.client.connectedToServer && !ig.game.registerWaitStatus){
                setTimeout(function(){
                    if(ig.game.svasData.nickname==''){
                        this.showNotification("please login/register to join the battle!!!");
                        setTimeout(function(){
                            ig.game.registerWaitStatus=true;
                            ig.svas.ready();
                        }.bind(this),1500);
                    }else{
                       if(ig.game.svasData.coins<1500){
                            this.showNotification("You don't have enough coins to join the battle!!!");
                            ig.game.disableBtStatus = false;
                            ig.game.registerWaitStatus = false;
                       }else{
                            ig.game.client.roomArena = 4;
                            this.enterPrivateMatch();                        
                       }
                    }
                }.bind(this),2000);
                this.passStatus=false;
            }
        },
        showNotification:function(text){
            dl.scene.spawnNotification({
                c_DockerComponent: {
                        dockerObject: dl.game.camera,
                        dockerPercent: { x: 0.5, y: 0.1},
                        dockerOffset: { x: 0, y: 0 }
                    },
                notificationDrawConfigs: {
                    contentConfigs: [{
                            type: 'text',
                            text: text,
                            fillStyle: dl.configs.TEXT_COLOR.BUTTON_TEXT,
                            fontSize: 46,
                            fontFamily: dl.configs.FONT.SOURCE_SANS.name
                        }],
                    backgroundConfigs: {
                        lineWidth: 2,
                        fillStyle: dl.configs.NOTIFICATION_COLORS.PLAYER_1,
                        strokeStyle: dl.configs.NOTIFICATION_COLORS.PLAYER_1,
                        box: { width: 800, height: 90, round: 10, padding: { x: 100, y: 5 } }
                    },
                }
            });
        },
        update:function(){
            this.parent();
            this.checkRoomPassword();
            this.updateMenuPos();
            // this.updateServerData();
            this.checkPlayerExist();

            if(ig.game.showFPS){            
                this.fps.updateText(ig.system.fps.toFixed()+' fps');
            }
            if(this.notifdot && this.circleRedText) {
                if(this.notifdot.value==0){
                    var val = '';
                }else{
                    var val = this.notifdot.value;
                }
                this.circleRedText.updateText(val);
                if(this.circleRed) this.circleRed.visible = this.notifdot.visible;
            }else{
                if(this.circleRed) this.circleRed.visible = false;                
            }

            if(ig.game.svas.userInfo.nickname){
                this.svasCoins.updateText(ig.game.svasData.coins.toLocaleString());
                this.svasGems.updateText(ig.game.svasData.gems.toLocaleString());
                ig.game.balance=ig.game.svasData.coins;
                ig.game.gemsbalance=ig.game.svasData.gems;
            }

            var nameIndex=ig.game.today-1;
            if(nameIndex<0) nameIndex=6;
            if(ig.game.dayText2==''){
                var txt='';
            }else{
                var txt=this.upgradeName[nameIndex];
            }
            this.utcEvent.updateText(txt);
            this.utcDate.updateText(ig.game.utcTextSync);
            this.utcbg.updateImage(dl.preload['utcbox'+ig.game.today]);
        },
        checkPlayerExist:function(){
            if(window.gamemanager && window.gamemanager.players){
                window.gamemanager.clearAllData();
                window.gamemanager.players=null;
            }            
        },
        delay:0,
        updateServerData:function(){
            this.delay +=1;
            if(this.delay>=50){
                this.delay = 0;
                if(this.serverProp) this.serverProp.kill();

                if(ig.ua.mobile && !ig.ua.iPad){
                    var bt1size={x:700,y:180};
                    var bt1fontsize=60;
                }else{
                    var bt1size={x:580,y:121};
                    var bt1fontsize=40;
                }            

                var serverText = _STRINGS.NETWORK.SERVER_STATUS_PING + ': ' + ig.game.client.networkClient.latencyAverage.toString() + '<br>' +
                                    _STRINGS.NETWORK.SERVER_STATUS_ONLINE + ': ' +ig.game.client.serverUserCount.toString() + '<br>' +
                                    window.buildversion;


                this.serverProp = this.spawnEntity(dl.EntityText, {
                    c_DockerComponent: {
                        dockerObject: this.btnChangeServer,
                        dockerPercent: { x: 0, y: 0 },
                        dockerOffset: { x: 10, y: -10 }
                    },
                    c_TextComponent: {
                        textAlign:'left',
                        fontSize: bt1fontsize,
                    },
                    anchor: { x: 0, y: 1 },
                    text:serverText
                });
            }            
        },
        initNameAvatar: function () {            
            var xx=window.innerWidth;
            var yy=window.innerHeight;

            if(ig.ua.mobile && !ig.ua.iPad){
                if(xx<yy){ //portrait
                    var avatarpos={x:0.5,y:0.35};                                    
                }else{  //landscape              
                    var avatarpos={x:0.5,y:0.5};
                }
            }else{
                var avatarpos={x:0.5,y:0.5};
            }      

            this.nameAvatar = this.spawnEntity(dl.EntityNameAvatarInput, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: avatarpos,
                    dockerOffset: { x: 0, y: 0 }
                }
            });

            ig.game.nameAvatar = this.nameAvatar;
        },
        initCoins: function () {

            this.svasCoins = this.spawnEntity(dl.EntityText, {
                orientationData: {
                    portrait: {
                        _orientationScale: { x: 1.2, y: 1.2 }
                    },
                    landscape: {
                        _orientationScale: { x: 1, y: 1 }
                    }
                },
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0.054, y: 0.022 },
                    dockerOffset: { x: 0, y: 0 }
                },
                c_TextComponent: {
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                    fontSize: 60,
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                    textAlign:'left',
                },
                anchor: { x: 0, y: 0 },
                text: ''
            });

            this.svasGems = this.spawnEntity(dl.EntityText, {
                orientationData: {
                    portrait: {
                        _orientationScale: { x: 1.2, y: 1.2 }
                    },
                    landscape: {
                        _orientationScale: { x: 1, y: 1 }
                    }
                },
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0.054, y: 0.022 },
                    dockerOffset: { x: 0, y: 0 }
                },
                c_TextComponent: {
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                    fontSize: 60,
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                    textAlign:'left',
                },
                anchor: { x: 0, y: 0 },
                text: ''
            });

            this.getSvasData();
        },

        getSvasData:function(){
            if(ig.game.svas.userInfo.id && ig.game.svas.userInfo.id!=0 && ig.game.svas.userInfo.id!='' && 
                ig.game.client.networkClient &&
                ig.game.client.networkClient.socket ){
                ig.game.client.requestSvasData(ig.game.svas.userInfo.id);
            }else if(ig.game.svas.userInfo.id==0){
                ig.game.client.resetSvasData();
            }else{
                setTimeout(function(){
                    var userinfo=ig.game.io.storage.get(ig.game.svas.settings.localStorageKey);
                    if(!userinfo || userinfo.id==null || userinfo.id==''){
                        ig.game.svas.userInfo.id=0;
                    }else{
                        ig.game.svas.userInfo.id=userinfo.id;
                        ig.game.svas.userInfo.nickname=userinfo.nickname;
                        ig.game.svas.userInfo.pincode=userinfo.pincode;                        
                    }
                    this.getSvasData();                    
                }.bind(this),1000);
            }

        },
        initServerStatus: function () {

            if(ig.ua.mobile && !ig.ua.iPad){
                var bt1size={x:700,y:180};
                var bt1fontsize=60;
            }else{
                var bt1size={x:580,y:121};
                var bt1fontsize=40;
            }            

            this.btnChangeServer = this.spawnEntity(dl.EntityButtonImageText, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0, y: 0 },
                    dockerOffset: { x: 0, y: 0 }
                },
                c_AnimationSheetComponent: {
                    _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                    _size: bt1size
                },
                anchor: { x: 0.5, y: 1 },
                c_TextComponent: {
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                    fontSize: bt1fontsize,
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name
                },
                image: dl.preload['button-green'],
                text: _STRINGS.NETWORK.SELECT_SERVER,
                onButtonClicked: function () {
                    if(ig.game.disableBtStatus) return;
                    // ig.game.title.notifdot.tempAlpha=ig.game.title.notifdot.alpha;
                    // ig.game.title.notifdot.alpha=0;
                    ig.game.popupStatus = true;
                    dl.scene.spawnPopup(dl.EntityPopupSelectServer);
                }
            });

        },

        tweenIn: function (callback, minimalTween) {
            var time = dl.configs.getConfig('HOME_LEVEL_TWEEN_TIME', 'IN');

            if (!minimalTween) {
                dl.TweenTemplate.fadeIn(this.title, time);

                this.nameAvatar.tweenIn(time);

                dl.TweenTemplate.fadeIn(this.btnFullscreen, time);
                dl.TweenTemplate.fadeIn(this.btLeaderboard, time);
                dl.TweenTemplate.fadeIn(this.btnSound, time);
            }

            dl.TweenTemplate.fadeIn(this.btnRandom, time, callback);
            dl.TweenTemplate.fadeIn(this.btnPrivate, time);
            dl.TweenTemplate.fadeIn(this.btnChangeServer, time);
        },

        tweenOut: function (callback, minimalTween) {
            this.btnRandom.setEnable(false);
            this.btnPrivate.setEnable(false);
            this.btnChangeServer.setEnable(false);
            if (this.btnFullscreen) this.btnFullscreen.setEnable(false);
            if (this.btLeaderboard) this.btLeaderboard.setEnable(false);
            this.btnSound.setEnable(false);

            var time = dl.configs.getConfig('HOME_LEVEL_TWEEN_TIME', 'OUT');
            if (!minimalTween) {
                dl.TweenTemplate.fadeOut(this.title, time);

                this.nameAvatar.tweenOut(time);

                if (this.btLeaderboard) dl.TweenTemplate.fadeOut(this.btLeaderboard, time);
                if (this.btnFullscreen) dl.TweenTemplate.fadeOut(this.btnFullscreen, time);
                dl.TweenTemplate.fadeOut(this.btnSound, time);
            }

            dl.TweenTemplate.fadeOut(this.btnRandom, time, callback);
            dl.TweenTemplate.fadeOut(this.btnPrivate, time);
            dl.TweenTemplate.fadeOut(this.btnChangeServer, time);
        }
    });
});
