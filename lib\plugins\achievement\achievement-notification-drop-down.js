ig.module('plugins.achievement.achievement-notification-drop-down')
    .requires(
        'plugins.achievement.achievement-game-object'
    )
    .defines(function () {
        ig.AchievementNotificationDropDown = ig.AchievementSimpleButton.extend({

            zIndex: 9999,
            name: "achievementNotification",
            drawAsRect: true,
            forceDraw: true,
            anchorX: 0,
            anchorY: 0,
            align: "left",
            valign: "top",
            icon: null,
            oriX: 0,
            oriY: 0,
            destY: 0,
            clickNotificationCooldown: 0,

            usePressedTween: false,

            onClickNotification: null,
            showDuration: 1,
            isShowing: false,

            init: function (x, y, settings) {
                this.parent(x, y, settings);

                this.rectColor = ig.Achievement.notificationDropDown.color;
                this.textColor = ig.Achievement.notificationDropDown.textColor;
                this.width = ig.Achievement.notificationDropDown.width;
                this.height = ig.Achievement.notificationDropDown.height;
                this.font = ig.Achievement.notificationDropDown.font;
                this.lineSpacing = ig.Achievement.notificationDropDown.lineSpacing;

                this.offsetX = -this.width / 2 + this.height;
                this.offsetY = -this.height / 2 + ig.Achievement.notificationDropDown.padding + ig.Achievement.notificationDropDown.textOffsetY

                this.visible = false;
                this.onClickNotification = new ig.AchievementSignal();
                this.onClicked.add(this.onClickThis, this);

            },

            onClickThis: function () {
                if (!this.isShowing) return;
                if (this.clickNotificationCooldown > 0) return;
                this.clickNotificationCooldown = this.showDuration + this.entryDuration + this.exitDuration;
                this.onClickNotification.dispatch();
            },

            show: function (id) {
                if (this.isShowing) return;
                this.visible = true;
                var data = ig.Achievement.getAchievementData(id);
                this.icon = data.icon
                this.text = ig.Achievement.strings.unlocked + "\n" + data.name;
                this.isShowing = true
                this.currentId = id;
                this.showDuration = ig.Achievement.notificationDropDown.showDuration;
                this.entryDuration = 0.5;
                this.exitDuration = 0.5;
                this.anchorX = 0;
                this.anchorY = 0;

                if (ig.responsive) {
                    this.anchorType = ig.Achievement.notificationDropDown.placement;
                    switch (ig.Achievement.notificationDropDown.placement) {
                        case "top-left":
                            this.oriX = 0;
                            this.oriY = -this.height;
                            this.destX = this.oriX;
                            this.destY = 0;
                            break;
                        case "top-center":
                            this.oriX = 0;
                            this.oriY = -this.height;
                            this.destX = this.oriX;
                            this.destY = 0;
                            this.anchorX = 0.5;
                            break;
                        case "top-right":
                            this.oriX = -this.width;
                            this.oriY = -this.height;
                            this.destX = this.oriX;
                            this.destY = 0;
                            break;
                        case "middle-left":
                            this.oriX = 0;
                            this.oriY = 0;
                            this.destX = this.width;
                            this.destY = this.oriY;
                            this.anchorX = 1;
                            this.anchorY = 0.5;
                            break;
                        case "middle-right":
                            this.oriX = 0;
                            this.oriY = 0;
                            this.destX = -this.width;
                            this.destY = this.oriY;
                            this.anchorX = 0;
                            this.anchorY = 0.5;
                            break;
                        case "bottom-left":
                            this.oriX = 0;
                            this.oriY = 0;
                            this.destX = this.oriX;
                            this.destY = -this.height;
                            break;
                        case "bottom-center":
                            this.oriX = 0;
                            this.oriY = 0;
                            this.destX = this.oriX;
                            this.destY = -this.height;
                            this.anchorX = 0.5;
                            break;
                        case "bottom-right":
                            this.oriX = -this.width;
                            this.oriY = 0;
                            this.destX = this.oriX;
                            this.destY = -this.height;
                            break;
                    }

                    this.anchoredPositionX = this.oriX
                    this.anchoredPositionY = this.oriY
                } else {
                    switch (ig.Achievement.notificationDropDown.placement) {
                        case "top-left":
                            this.oriX = 0;
                            this.oriY = -this.height;
                            this.destX = this.oriX;
                            this.destY = 0;
                            break;
                        case "top-center":
                            this.oriX = ig.system.width / 2;
                            this.oriY = -this.height;
                            this.destX = this.oriX;
                            this.destY = 0;
                            this.anchorX = 0.5;
                            break;
                        case "top-right":
                            this.oriX = ig.system.width - this.width;
                            this.oriY = -this.height;
                            this.destX = this.oriX;
                            this.destY = 0;
                            break;
                        case "middle-left":
                            this.oriX = 0;
                            this.oriY = ig.system.height / 2;
                            this.destX = this.width;
                            this.destY = this.oriY;
                            this.anchorX = 1;
                            this.anchorY = 0.5;
                            break;
                        case "middle-right":
                            this.oriX = ig.system.width;
                            this.oriY = ig.system.height / 2;
                            this.destX = ig.system.width - this.width;
                            this.destY = this.oriY;
                            this.anchorX = 0;
                            this.anchorY = 0.5;
                            break;
                        case "bottom-left":
                            this.oriX = 0;
                            this.oriY = ig.system.height;
                            this.destX = this.oriX;
                            this.destY = ig.system.height - this.height;
                            break;
                        case "bottom-center":
                            this.oriX = ig.system.width / 2;
                            this.oriY = ig.system.height;
                            this.destX = this.oriX;
                            this.destY = ig.system.height - this.height;
                            this.anchorX = 0.5;
                            break;
                        case "bottom-right":
                            this.oriX = ig.system.width - this.width;
                            this.oriY = ig.system.height;
                            this.destX = this.oriX;
                            this.destY = ig.system.height - this.height;
                            break;
                    }
                    this.pos.x = this.oriX;
                    this.pos.y = this.oriY;
                }
            },

            update: function () {
                this.parent();
                this.clickNotificationCooldown -= ig.system.tick;
                if (this.isShowing) {
                    if (this.entryDuration > 0) {
                        this.entryDuration -= ig.system.tick;
                        if (ig.responsive) {
                            this.anchoredPositionX += (this.destX - this.anchoredPositionX) / 10;
                            this.anchoredPositionY += (this.destY - this.anchoredPositionY) / 10;
                        } else {
                            this.pos.x += (this.destX - this.pos.x) / 10;
                            this.pos.y += (this.destY - this.pos.y) / 10;
                        }
                    } else {
                        this.showDuration -= ig.system.tick;
                        if (this.showDuration < 0) {
                            this.exitDuration -= ig.system.tick;
                            if (ig.responsive) {
                                this.anchoredPositionX += (this.oriX - this.anchoredPositionX) / 10;
                                this.anchoredPositionY += (this.oriY - this.anchoredPositionY) / 10;
                            } else {
                                this.pos.x += (this.oriX - this.pos.x) / 10;
                                this.pos.y += (this.oriY - this.pos.y) / 10;
                            }

                            if (this.exitDuration < 0) {
                                this.isShowing = false;
                                this.visible = false;
                                ig.Achievement.data.achievements[this.currentId].popupShown = true;
                                ig.Achievement.saveData();
                                this.currentId = -1
                            }
                        }
                    }
                }
            },

            drawObject: function (x, y) {
                this.parent(x, y)

                if (this.icon) {
                    var ctx = ig.system.context;
                    ctx.save();
                    // this.icon.draw(ig.Achievement.notificationDropDown.padding, ig.Achievement.notificationDropDown.padding)
                    var iconX = ig.Achievement.notificationDropDown.padding + x +20;
                    var iconY = ig.Achievement.notificationDropDown.padding + y;
                    var iconSize = this.height - ig.Achievement.notificationDropDown.padding * 2;
                    if (this.icon.drawImageCtx) {
                        this.icon.drawImageCtx(ctx, 0, 0, this.icon.width, this.icon.height, iconX, iconY, iconSize, iconSize)
                    } else {
                        ctx.drawImage(this.icon.data, 0, 0, this.icon.width, this.icon.height, iconX, iconY, iconSize, iconSize)
                    }
                    ctx.restore();
                }
            },

        });
    });
