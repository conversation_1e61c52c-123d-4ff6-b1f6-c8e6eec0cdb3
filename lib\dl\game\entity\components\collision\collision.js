ig.module(
    'dl.game.entity.components.collision.collision'
).requires(
    'dl.game.entity.components.component',
    'dl.game.entity.components.collision.helpers.point',
    'dl.game.entity.components.collision.helpers.segment',
    'dl.game.entity.components.collision.helpers.rectangle',
    'dl.game.entity.components.collision.helpers.circle'
).defines(function () {
    'use strict';

    dl.CollisionComponent = dl.EntityComponent.extend({
        staticInstantiate: function (entity) {
            this.parent(entity);
            this._componentName = 'dl_CollisionComponent';

            this.collisionEnable = true;
            this.collisionTag = dl.CollisionComponent.TAGS.DEFAULT;
            this.collisionTargetTag = dl.CollisionComponent.TAGS.DEFAULT;
            this._collisionType = dl.CollisionComponent.TYPES.DEFAULT;

            this.collisionBoundingBox = {
                xMin: 0,
                yMin: 0,
                xMax: 0,
                yMax: 0
            };

            // bind events
            this.entity.onEvent('positionChanged', this.update.bind(this));

            return this;
        },

        draw: function (ctx) {
            if (dl.debug) {
                if (dl.debug.DRAW_COLLISION) this.drawCollision(dl.debug.getContext());
                if (dl.debug.drawCollisionBoundingBox) dl.debug.drawCollisionBoundingBox(this);
            }
        },

        touches: function (other) {
            dl.warnImplement(this.constructor.name);
        },

        onCollision: function (other) {
            dl.warnImplement(this.constructor.name);
        },

        drawCollision: function (ctx) {
            dl.warnImplement(this.constructor.name);
        },

        updateBoundingBox: function () {
            this.collisionBoundingBox.xMin = this.entity.pos.x - this.entity.size.x * 0.5;
            this.collisionBoundingBox.yMin = this.entity.pos.x - this.entity.size.y * 0.5;
            this.collisionBoundingBox.xMax = this.entity.pos.x + this.entity.size.x * 0.5;
            this.collisionBoundingBox.yMax = this.entity.pos.x + this.entity.size.y * 0.5;
        }
    });

    dl.CollisionComponent.TAGS = {
        DEFAULT: 0,
        BLOCK_INPUT: 1,
        POINTER: 2,
        POINTER_LISTENER: 4
    };

    dl.CollisionComponent.TYPES = {
        DEFAULT: 0,
        RECTANGLE: 1,
        CIRCLE: 2,
        POINT: 4,
        POLYGON: 8
    };

    dl.CollisionComponent.GetCollisionTag = function (name) {
        if (!dl.check.isDefined(dl.CollisionComponent.TAGS[name])) {
            dl.CollisionComponent.TAGS[name] = Math.pow(2, Object.keys(dl.CollisionComponent.TAGS).length - 1);
        }

        return dl.CollisionComponent.TAGS[name];
    };
});
