/**
 * Created by <PERSON><PERSON>
 * Store game bullet information
 */

var NetworkGameBullet = function () {
    var self = {};

    self.init = function () {
        self.playerId = 0;
        self.bulletType = 0;

        self.moveTime = 1000;

        self.direction = { x: 0, y: 0 };
        self.startPos = { x: 0, y: 0 };
        self.startTime = 0;
        self.currentPos = { x: 0, y: 0 };

        self.serverUpdateTime = 0;

        self.isAlive = true;
        self.isMoving = true;

        return self;
    };

    self.packData = function () {
        var data = {};

        data.playerId = self.playerId;
        data.bulletType = self.bulletType;

        data.moveTime = self.moveTime;

        data.direction = self.direction;
        data.startPos = self.startPos;
        data.startTime = self.startTime;
        data.currentPos = self.currentPos;

        data.serverUpdateTime = self.serverUpdateTime;

        data.isAlive = self.isAlive;
        data.isMoving = self.isMoving;

        return data;
    };

    self.importData = function (data) {
        if (!data) return;

        if (typeof data.playerId !== 'undefined') self.playerId = data.playerId;
        if (typeof data.bulletType !== 'undefined') self.bulletType = data.bulletType;

        if (typeof data.moveTime !== 'undefined') self.moveTime = data.moveTime;

        if (typeof data.direction !== 'undefined') self.direction = data.direction;
        if (typeof data.startPos !== 'undefined') self.startPos = data.startPos;
        if (typeof data.startTime !== 'undefined') self.startTime = data.startTime;
        if (typeof data.currentPos !== 'undefined') self.currentPos = data.currentPos;

        if (typeof data.serverUpdateTime !== 'undefined') self.serverUpdateTime = data.serverUpdateTime;

        if (typeof data.isAlive !== 'undefined') self.isAlive = data.isAlive;
        if (typeof data.isMoving !== 'undefined') self.isMoving = data.isMoving;
    };

    return self.init();
};

NetworkGameBullet.interpolate = function (start, end, delta) {
    var result = new NetworkGameBullet();
    result.importData(start);

    result.serverUpdateTime = start.serverUpdateTime + (end.serverUpdateTime - start.serverUpdateTime) * delta;

    return result;
};

/**
 * Export module
 */
if (typeof module !== 'undefined') {
    module.exports.NetworkGameBullet = NetworkGameBullet;
}
