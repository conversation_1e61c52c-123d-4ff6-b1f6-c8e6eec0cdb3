ig.module(
    'game.levels.instruction'
).requires(
    'dl.templates.entities.entity-level',
    'dl.templates.entities.backgrounds.entity-color-background',
    'dl.templates.entities.entity-text',
    'dl.templates.network-entities.entity-count-down-timer-text',
    'game.dl-entities.instructions-map'
).defines(function () {
    LevelInstruction = {
        entities: [
            { type: 'EntityInstructionController', x: 0, y: 0 }
        ],
        layer: [],
        useCustomLib: true
    };

    EntityInstructionController = dl.EntityLevel.extend({
        postInit: function () {
            this.parent();
            // ig.system.fps=dl.FPS_INTERVAL;

            this.initBackgroundAndTitle();
            this.initCountDownTimerText();
            this.initSteps();
            this.initMap();

            ig.game.instructionLevel = this;
            // this.tweenIn();
        },

        initBackgroundAndTitle: function () {
            this.background = this.spawnEntity(dl.EntityColorBackground, {});

            this.title = this.spawnEntity(dl.EntityText, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0.5, y: 0.08 },
                    dockerOffset: { x: 0, y: 0 }
                },
                c_TextComponent: {
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                    fontSize: 80
                },
                text: _STRINGS.NETWORK.INSTRUCTION_LEVEL_TITLE
            });
        },

        initCountDownTimerText: function () {
            this.countDownTimerText = this.spawnEntity(dl.EntityCountDownTimerText, {
                orientationData: {
                    portrait: {
                        _scale: { x: 0.5, y: 0.5 }
                    },
                    landscape: {
                        _scale: { x: 1, y: 1 }
                    }
                },
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0.5, y: 0.85 },
                    dockerOffset: { x: 0, y: 0 }
                },
                c_TextComponent: {
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'GRAY')
                },
                onTimerExpired: function () {

                }.bind(this)
            });
        },

        initSteps: function () {
            if(ig.ua.mobile && !ig.ua.iPad){
                var fontSize = 55;
                var offsetY = 100;

            }else{

                var fontSize = 46;
                var offsetY = 90;
            }
            this.stepContainer = this.spawnEntity(dl.EntityImage, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0.5, y: 0.2 },
                    dockerOffset: { x: 0, y: 0 }
                },
                image: dl.preload.instructions.stepContainer
            });

            this.step1 = this.spawnEntity(dl.EntityText, {
                c_DockerComponent: {
                    dockerObject: this.stepContainer,
                    dockerPercent: { x: 0.5, y: 0.13 },
                    dockerOffset: { x: 0, y: 0 }
                },
                anchor: { x: 0.5, y: 0 },
                c_TextComponent: {
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                    fontSize: fontSize,
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT')
                },
                text: _STRINGS.NETWORK.INSTRUCTION_LEVEL_STEP1
            });

            this.step2 = this.spawnEntity(dl.EntityText, {
                c_DockerComponent: {
                    dockerObject: this.step1,
                    dockerPercent: { x: 0, y: 1 },
                    dockerOffset: { x: 0, y: offsetY }
                },
                anchor: { x: 0, y: 1 },
                c_TextComponent: {
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                    fontSize: fontSize,
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT')
                },
                text: _STRINGS.NETWORK.INSTRUCTION_LEVEL_STEP2
            });

            this.step3 = this.spawnEntity(dl.EntityText, {
                c_DockerComponent: {
                    dockerObject: this.step2,
                    dockerPercent: { x: 0, y: 1 },
                    dockerOffset: { x: 0, y: offsetY }
                },
                anchor: { x: 0, y: 1 },
                c_TextComponent: {
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                    fontSize: fontSize,
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT')
                },
                text: _STRINGS.NETWORK.INSTRUCTION_LEVEL_STEP3
            });

            this.step4 = this.spawnEntity(dl.EntityText, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0.5, y: 0.80 },
                    dockerOffset: { x: 0, y: 0 }
                },
                anchor: { x: 0.5, y: 1 },
                c_TextComponent: {
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                    fontSize: fontSize,
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'PLAYER_1')
                },
                text: _STRINGS.NETWORK.INSTRUCTION_LEVEL_STEP4
            });
        },

        initMap: function () {
            this.map = this.spawnEntity(dl.EntityInstructionsMap, {
                c_DockerComponent: {
                    dockerObject: this,
                    dockerPercent: { x: 0.5, y: 0.5 },
                    dockerOffset: { x: 0, y: 0 }
                }
            });
        },

        startTimer: function (startTime, duration) {
            this.countDownTimerText.startTimer(startTime, duration);
        },

        tweenIn: function (callback) {
            var time = dl.configs.getConfig('INSTRUCTION_LEVEL_TWEEN_TIME', 'IN');

            dl.TweenTemplate.fadeIn(this.title, time, callback);
            dl.TweenTemplate.fadeIn(this.countDownTimerText, time);
            dl.TweenTemplate.fadeIn(this.step1, time);
            dl.TweenTemplate.fadeIn(this.step2, time);
            dl.TweenTemplate.fadeIn(this.step3, time);
            dl.TweenTemplate.fadeIn(this.step4, time);
            this.map.tweenIn(time);
        },

        tweenOut: function (callback) {
                var time = dl.configs.getConfig('INSTRUCTION_LEVEL_TWEEN_TIME', 'OUT');
                // dl.TweenTemplate.fadeOut(this.title, time, callback);
                // dl.TweenTemplate.fadeOut(this.countDownTimerText, time);
                // dl.TweenTemplate.fadeOut(this.step1, time);
                // dl.TweenTemplate.fadeOut(this.step2, time);
                // dl.TweenTemplate.fadeOut(this.step3, time);
                // dl.TweenTemplate.fadeOut(this.step4, time);
                this.map.tweenOut(time);

                this.stepContainer.kill();
                this.title.kill();
                this.countDownTimerText.kill();
                this.step1.kill();
                this.step2.kill();
                this.step3.kill();
                this.step4.kill();

                callback();

        }
    });
});
