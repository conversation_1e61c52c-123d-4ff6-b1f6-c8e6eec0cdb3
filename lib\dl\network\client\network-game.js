/**
 * Created by <PERSON><PERSON>
 * Handle NetworkGame on client side
 */
ig.module(
    'dl.network.client.network-game'
).requires(
    'dl.network.client.network-game-event-handler'
).defines(function () {
    NetworkGame = ig.Class.extend({
        init: function (room) {
            this.room = room;

            this.networkGameDataStack = [];
            this.networkGameData = new NetworkGameData();
            this.current_NetworkGameData = new NetworkGameData();
            this.pendingEvents = [];

            this.clientStartTime = 0;
        },
        update: function () {
            this.handlePendingEvent();
            this.updateCurrentNetworkGameData();
        },

        // ----------------
        // Start event's handler functions
        // ----------------
        addPendingEvent: function (event) {
            this.pendingEvents.push(event);
        },

        handlePendingEvent: function () {
            var unhandledPendingEvent = [];
            for (var i = 0, iLength = this.pendingEvents.length; i < iLength; i++) {
                var event = this.pendingEvents[i];
                if (!this.handleEvent(event)) {
                    unhandledPendingEvent.push(event);
                }
            }

            this.pendingEvents = unhandledPendingEvent;
        },

        handleEvent: function (event) {
            if (!event) return true;

            // update networkGameData
            this.updateNetworkGameData(event);

            return NetworkGameEventHandler.handleEvent(this, event);
        },

        sendGameEvent: function (event) {
            event.senderPlayerId = ig.game.client.playerId;
            this.room.sendClientEvent(RoomEvent.EVENT_TYPE.CLIENT_GAME_EVENT, event.packData());
        },

        // ----------------
        // End event's handler functions
        // ----------------

        // ----------------
        // Start Utilities functions
        // ----------------
        getServerTime: function () {
            return this.networkGameData.serverStartTime + (Date.now() - this.clientStartTime) - NETWORK_GAME_CONFIGS.RENDER_DELAY;
        },

        updateNetworkGameData: function (event) {
            if (!event.networkGameData) return;

            this.networkGameDataStack.push(event.networkGameData);
            if (this.networkGameDataStack.length >= 100) {
                this.networkGameDataStack.shift();
            }

            this.networkGameData.importData(event.networkGameData);
            delete event.networkGameData;
        },

        updateCurrentNetworkGameData: function () {


            var serverTime = this.getServerTime();
            var baseIndex = this.getBaseNetworkGameDataIndex(serverTime);

            if (baseIndex < 0) {
                this.current_NetworkGameData.importData(this.networkGameDataStack[this.networkGameDataStack.length - 1]);
            } else {
                if (baseIndex == (this.networkGameDataStack.length - 1)) {
                    this.current_NetworkGameData.importData(this.networkGameDataStack[baseIndex]);
                    // TODO: handle high ping
                } else {
                    var base = this.networkGameDataStack[baseIndex];
                    var next = this.networkGameDataStack[baseIndex + 1];
                    var deltaTime = (serverTime - base.lastServerUpdateTime) / (next.lastServerUpdateTime - base.lastServerUpdateTime);
                    var networkGameData = NetworkGameData.interpolate(base, next, deltaTime);

                    this.current_NetworkGameData.importData(networkGameData);
                }
            }
            // log(baseIndex, this.networkGameData.lastServerUpdateTime, serverTime, this.networkGameData.serverStartTime, this.networkGameData.lastServerUpdateTime - serverTime)
            NetworkGameEventHandler.onNetworkGameDataUpdated(this);



        },

        getBaseNetworkGameDataIndex: function (serverTime) {
            for (var i = this.networkGameDataStack.length - 1; i > 0; i--) {
                if (this.networkGameDataStack[i].lastServerUpdateTime <= serverTime) {
                    return i;
                }
            }

            return -1;
        },

        isMyPlayerId: function (playerId) {
            return playerId == this.room.client.playerId;
        },

        isMyTurn: function () {
            var currentPlayerInTurn = this.networkGameData.playerList[this.networkGameData.currentTurn];
            if (!currentPlayerInTurn) return false;

            return this.isMyPlayerId(currentPlayerInTurn.playerId);
        }
        // ----------------
        // End Utilities functions
        // ----------------
    });
});
