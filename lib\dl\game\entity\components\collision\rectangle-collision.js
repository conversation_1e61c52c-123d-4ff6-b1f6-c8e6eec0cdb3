ig.module(
    'dl.game.entity.components.collision.rectangle-collision'
).requires(
    'dl.game.entity.components.collision.collision',
    'dl.game.entity.components.collision.helpers.rectangle'
).defines(function () {
    'use strict';

    dl.RectangleCollision = dl.CollisionComponent.extend({
        staticInstantiate: function (entity) {
            this.parent(entity);

            this._collisionType = dl.CollisionComponent.TYPES.RECTANGLE;
            this._collisionSize; // = { x: 0, y: 0 };
            this._collisionOffset = { x: 0, y: 0 };

            this.size = { x: 0, y: 0 };
            this.offset = { x: 0, y: 0 };
            this.pos = { x: 0, y: 0 };

            // bind events
            this.entity.onEvent('positionChanged', this.updateAll.bind(this));

            return this;
        },

        onPropertiesChanged: function (properties) {
            this.updateAll();
        },

        updateAll: function () {
            this.updateSize();
            this.updateOffset();
            this.updatePos();
            this.updateBoundingBox();
        },

        updateSize: function () {
            if (this._collisionSize) {
                this.size.x = this._collisionSize.x * this.entity.scale.x;
                this.size.y = this._collisionSize.y * this.entity.scale.y;
            } else {
                this.size.x = this.entity.size.x;
                this.size.y = this.entity.size.y;
            }
        },

        updateOffset: function () {
            this.offset.x = this._collisionOffset.x * this.entity.scale.x;
            this.offset.y = this._collisionOffset.y * this.entity.scale.y;
        },

        updatePos: function () {
            this.pos.x = this.entity.pos.x + this.offset.x;
            this.pos.y = this.entity.pos.y + this.offset.y;
        },

        updateBoundingBox: function () {
            this.collisionBoundingBox.xMin = this.pos.x - this.size.x * 0.5;
            this.collisionBoundingBox.yMin = this.pos.y - this.size.y * 0.5;
            this.collisionBoundingBox.xMax = this.pos.x + this.size.x * 0.5;
            this.collisionBoundingBox.yMax = this.pos.y + this.size.y * 0.5;
        },

        touches: function (other) {
            switch (other._collisionType) {
                case dl.CollisionComponent.TYPES.POINT: {
                    return dl.CollisionHelpers_Rectangle.point(
                        this.pos.x - this.size.x * 0.5,
                        this.pos.y - this.size.y * 0.5,
                        this.size.x,
                        this.size.y,
                        other.pos.x,
                        other.pos.y
                    );
                } break;
                case dl.CollisionComponent.TYPES.RECTANGLE: {
                    return dl.CollisionHelpers_Rectangle.rectangle(
                        this.pos.x - this.size.x * 0.5,
                        this.pos.y - this.size.y * 0.5,
                        this.size.x,
                        this.size.y,
                        other.pos.x - other.size.x * 0.5,
                        other.pos.y - other.size.y * 0.5,
                        other.size.x,
                        other.size.y
                    );
                } break;
                case dl.CollisionComponent.TYPES.CIRCLE: {
                    return dl.CollisionHelpers_Rectangle.circle(
                        this.pos.x - this.size.x * 0.5,
                        this.pos.y - this.size.y * 0.5,
                        this.size.x,
                        this.size.y,
                        other.pos.x,
                        other.pos.y,
                        other.radius
                    );
                } break;
                default: {
                    dl.warn('Undefined type [' + other._collisionType + ']');
                } break;
            }
            return false;
        },

        drawCollision: function (ctx) {
            ctx.save();
            ctx.fillStyle = 'green';
            ctx.globalAlpha = 0.5;
            ctx.fillRect(
                this.pos.x - this.size.x * 0.5,
                this.pos.y - this.size.y * 0.5,
                this.size.x,
                this.size.y);
            ctx.restore();
        }
    });
});
