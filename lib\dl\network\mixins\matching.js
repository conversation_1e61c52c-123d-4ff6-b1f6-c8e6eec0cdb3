/**
 * Created by <PERSON><PERSON>
 * Usage:
 * dl.Entity.extend(MixinNetworkMatching);
 */
ig.module(
    'dl.network.mixins.matching'
).requires(

).defines(function () {
    MixinNetworkMatching = {
        staticInstantiate: function () {
            this.parent();

            this.roomStarted = false;

            return this;
        },

        postInit: function () {
            this.parent();

            this.addCallbacks();
            this.forceRoomUpdate();
        },

        addCallbacks: function () {
            if (!ig.game.client) return;
            if (!ig.game.client.clientGameRoom) return;

            ig.game.client.clientGameRoom.onRoomUpdateCallback = this.onRoomUpdate.bind(this);
            ig.game.client.clientGameRoom.onRoomStartCallback = this.onRoomStart.bind(this);
            ig.game.client.clientGameRoom.onRoomStartedCallback = this.onRoomStarted.bind(this);
        },

        checkAllReady: function () {
            if (!ig.game.client) return false;
            if (!ig.game.client.clientGameRoom) return false;

            var roomData = ig.game.client.clientGameRoom;
            if (roomData.roomMaxPlayer > roomData.playerList.length) return false;

            for (var i = 0; i < roomData.playerList.length; i++) {
                var playerData = roomData.playerList[i];

                if (!playerData.playerReady) return false;
            }

            return true;
        },

        onPlayerReady: function () {
            if (!ig.game.client) return;
            if (!ig.game.client.clientGameRoom) return;

            ig.game.client.clientGameRoom.sendPlayerReady();
        },

        onPlayerExit: function () {
            if (!ig.game.client) return;

            ig.game.client.cancelRequestRoom();
        },

        onAutoStartStateChange: function (newState) {
            if (!ig.game.client) return;
            if (!ig.game.client.clientGameRoom) return;

            ig.game.client.clientGameRoom.sendAutoStartStateChange(newState);
        },

        onRoomUpdate: function (data) {
            // to be implement in parent class
            // dl.log("onRoomUpdate", data);
            try{
                this.parent(data);
            }catch(e){}
        },

        onRoomStart: function (data) {
            // to be implement in parent class
            dl.log('onRoomStart', data);
            this.parent(data);
        },

        onRoomStarted: function (data) {
            // to be implement in parent class
            dl.log('onRoomStarted', data);
            this.parent(data);
        },

        forceRoomUpdate: function () {
            if (!ig.game.client) return;
            if (!ig.game.client.clientGameRoom) return;

            this.onRoomUpdate(ig.game.client.clientGameRoom);
        }
    };
});
