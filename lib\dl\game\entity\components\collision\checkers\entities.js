ig.module(
    'dl.game.entity.components.collision.checkers.entities'
).requires(
    'dl.dl'
).defines(function () {
    'use strict';

    dl.EntitiesCollisionChecker = {
        checkCollisions: function (entities) {
            // Using spatial hash.
            var hash = {};
            var cellSize = 64;

            this._checkCollisionRecursive(entities, hash, cellSize);
        },

        _checkCollisionRecursive: function (entities, hash, cellSize) {
            for (var entityIndex = 0; entityIndex < entities.length; entityIndex++) {
                var entity = entities[entityIndex];
                this._checkCollisionRecursiveEntity(entity, hash, cellSize);

                if (dl.check.isArray(entity._entities)) {
                    this._checkCollisionRecursive(entity._entities, hash, cellSize);
                }
            }
        },

        _checkCollisionRecursiveEntity: function (entity, hash, cellSize) {
            // Skip if game paused
            if (dl.game.gamePaused && !entity.skipGamePause) return;

            // Skip if no collision components
            if(entity && entity.getComponents){
                var collisionComponents = entity.getComponents('dl_CollisionComponent');
                if (!collisionComponents) return;

                for (var componentIndex = 0; componentIndex < collisionComponents.length; componentIndex++) {
                    var collision = collisionComponents[componentIndex];

                    if (!collision) continue;
                    if (!collision.collisionEnable) continue;
                    if (!collision.collisionTag && !collision.collisionTargetTag) continue;
                    // skip collision flag, for pointer
                    if (dl.Pointer && collision.collisionTag) {
                        if ((dl.Pointer.COLLISION_TARGET_TAG & collision.collisionTag) == collision.collisionTag) {
                            continue;
                        }
                    }

                    var checked = {};
                        var xMin = Math.floor(collision.collisionBoundingBox.xMin / cellSize);
                        var yMin = Math.floor(collision.collisionBoundingBox.yMin / cellSize);
                        var xMax = Math.floor(collision.collisionBoundingBox.xMax / cellSize) + 1;
                        var yMax = Math.floor(collision.collisionBoundingBox.yMax / cellSize) + 1;

                    for (var x = xMin; x < xMax; x++) {
                        for (var y = yMin; y < yMax; y++) {
                            // Current cell is empty - create it and insert!
                            if (!hash[x]) {
                                hash[x] = {};
                                hash[x][y] = [collision];
                            } else {
                                if (!hash[x][y]) {
                                    hash[x][y] = [collision];
                                } else {
                                    // Check against each entity in this cell, then insert
                                    var cell = hash[x][y];
                                    for (var c = 0; c < cell.length; c++) {
                                        // Intersects and wasn't already checked?
                                        if (collision.touches(cell[c]) &&
                                            !checked[cell[c].entity._entityId]) {
                                            checked[cell[c].entity._entityId] = true;
                                            this._checkCollisionPair(collision, cell[c]);
                                        }
                                    }
                                    cell.push(collision);
                                }
                            }
                        } // end for y size
                    } // end for x size
                } // end for collision components
            }
        },

        _checkCollisionPair: function (aCollision, bCollision) {
            if (bCollision.collisionTargetTag & aCollision.collisionTag) {
                bCollision.onCollision(aCollision);
            }

            if (aCollision.collisionTargetTag & bCollision.collisionTag) {
                aCollision.onCollision(bCollision);
            }
        }
    };
});
