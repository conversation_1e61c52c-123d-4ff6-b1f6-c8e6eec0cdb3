/**
 * Created by <PERSON><PERSON>
 * Defining player name configs
 * Check bad words
 */

if (typeof require !== 'undefined') {
    Utilities = require('./utilities.js').Utilities;
    BAD_WORD_REGEX1 = require('badwords-list').regex;
    BAD_WORD_REGEX2 = require('french-badwords-list').regex;
    BAD_WORD_REGEX3 = require('italian-badwords-list').regex;
    BAD_WORD_REGEX4 = require('filipino-badwords-list').regex;
    BAD_WORD_REGEX5 = require('vietnamese-badwords').regex;
}

var NameAndAvatar = {};

// name max length
NameAndAvatar.NAME_MAX_LENGTH = 9;
// total avatar available
NameAndAvatar.TOTAL_AVATAR = 16;
// pre-defined bot names
NameAndAvatar.BOT_NAMES_MALE_1 = [
        '<PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','bubbles','C-3<PERSON>','cody95','Davi<PERSON>','<PERSON>aco<PERSON>','E<PERSON>','eric108','F3<PERSON><PERSON>','GLaD<PERSON>','HAL-9000','james07','<PERSON>5','<PERSON><PERSON><PERSON><PERSON>','micha<PERSON>','nich0<PERSON>','Ru<PERSON>eLL','s<PERSON>','<PERSON><PERSON><PERSON>','s<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>-<PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>r','<PERSON>','Con','Cornelius','Cuddy','Cyrus','Richard','Derrick','Barney','Robert','Dyer','Nathaniel','Fate','Cleophas','Fred','Frederick','Constance','Eugene','Gus','August','Harold','Henry','Philadelphia','Hamilton','Damaris','Hank','Harry','Theodosia','Edie','Isaac','Jack','Jackson','John','Jacob','Jeremy','Jerry','Gerald','Frankie','Frances','Flora','Hezekiah','Kit','Christopher','Larry','Lawrence','Malcolm','Nate','Edwin','Edward','Newton','Ollie','Oliver','Patrick','Aquilla','Quill','Zachariah','Tad','Ming','Azariah','Ismael','Virgil','Theo','William','Wiley','Jemima','Joan','Phil','Zaha','Song','Dijk','Dipsi','Karim','Paul','Whyne','Ridwan','Kamil'
    ];

NameAndAvatar.BOT_NAMES_MALE_2 = [  
        '','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','_XY','_~','@#@','*_*','Red','Green','Eagle','Lion','Alpha','Skull','Claw','Vyro','Jaxx','Raze','Kolt','Tysk','Fyze','Kael','Jynx','Xyre','Daxx','Lyrk','Vayn','Gryn','Pyke','Zyle','Klyv','Wynn','Haze','Tyce','Ryze','Jask','Xyon','Vann','Kryz','Fydo','Raxx','Zayn','Kren','Tazx','Jaxo','Zyre','Vion','Kyrn','Xylo','Daxo','Lyxx','Vyxen','Grynne','Pylt','Zayz','Kryx','Fyrs','Rydo','Jyde','Xyze','Vyze','Kylt','Wyze','Hyls','Tygo','Ralx','Jyce','Xynt','Valt','Kyre','Zolx','Kory','Fynt','Rynx','Jyve','Xyso','Vazt','Kyls','Wyzk','Haxx','Tyze','Rysk','Jyke','Zylex','Kryne','Fyzeo','Ryth','Xyko','Venz','Kylz','Wysk','Hydo','Tyxen','Raxo','Jyvek','Xynx','Vayz','Kylk','Wyzeo','Fynx','Ralz','Jynt','Xyva','Vyxor','Kryso','Zayl','Fylox','Ryve','Jyxen','Xyloz','Vanz','Kyze','Wyrk'
    ];

NameAndAvatar.BOT_NAMES_FEMALE_1 = [
        'amanda19','ashley91','Ava','caroline','DIANE','EVE','jeanxo','Jessica','JoycE','julie404','KAT13','Kira','KITT','lisa','nic0le','nich0las','REbEcCa','rose','Siri','tinacook','WALL-E','Zorro','Abigail','Adelaide','Alice','Barbara','Becky','Cager','Bella','Isabella','Chan','Elizabeth','Beth','Cuddy','Betty','Sabrina','Carrie','Cassie','Cassandra','Cindy','Cynthia','Luciana','Fanny','Clara','Clarinda','Connie','Crece','Dolly','Dorothy','Ellen','Eleanor','Marietta','Jude','Kit','Virginia','Harriet','Hermione','Jenny','Jane','Janet','Rias','Judy','Sandy','Julie','Julia','Kate','Kathleen','Christina','Ming','Kitty','Angeline','Olive','Charlotte','Lollie','Amanda','Mary','Margaret','Martha','Amelia','Mini','Anna','Agnes','Helen','Nancy','Rana','Ratna','Monica','Irene','Cecily','Susan','Susannah','Natalie','Dipsi','Lala','Riawan',
    ];

NameAndAvatar.BOT_NAMES_FEMALE_2 = [
        '','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','_XY','_~','@#@','*_*','Red','Green','Blue','Moon','Alpha','Angel','Lyrae','Zylia','Fyre','Xena','Kaela','Tyra','Ryla','Jyra','Vyna','Wyla','Zora','Hyla','Kyra','Dely','Fyla','Vara','Glyn','Xora','Myla','Zyla','Kira','Pyra','Ryna','Syra','Tyla','Vyla','Wyna','Zyla','Ayla','Brea','Crya','Dyla','Eyla','Frya','Gyla','Hyla','Ilya','Jyla','Kyla','Lyla','Myra','Nyla','Orya','Pyxa','Qyla','Raya','Syxa','Tyla','Ulya','Vrya','Wyla','Xyla','Ylia','Zyda','Alyx','Byla','Cyla','Drea','Erya','Fyla','Gyla','Hyla','Inyx','Jyla','Kora','Lyla','Myxa','Nyla','Oyla','Pyxa','Qyla','Ryla','Syla','Tyla','Ulya','Vyla','Wyla','Xyla','Ysra','Zyra','Ayla','Bree','Cryx','Dyla','Eira','Fyra','Glyn','Hyla','Irya','Jyra','Kira','Lyla','Myla','Nyra','Oryx','Pyra','Qyra','Ryxa','Syra','Tyxa'
    ];

NameAndAvatar.randomName = function (avatarId) {
    if(avatarId<=8){
        var index1 = Utilities.randomInt(0, NameAndAvatar.BOT_NAMES_MALE_1.length - 1);
        var index2 = Utilities.randomInt(0, NameAndAvatar.BOT_NAMES_MALE_2.length - 1);
        var index3 = Utilities.randomInt(0, 9);
        var number='';
        var show=[0,0,0,0,0,0,0,0,1,1];
        if(show[index3]==1){
            number=Utilities.randomInt(10, 99);
        }
        
        return NameAndAvatar.BOT_NAMES_MALE_1[index1]+NameAndAvatar.BOT_NAMES_MALE_2[index2]+number;
    }else{
        var index1 = Utilities.randomInt(0, NameAndAvatar.BOT_NAMES_FEMALE_1.length - 1);
        var index2 = Utilities.randomInt(0, NameAndAvatar.BOT_NAMES_FEMALE_2.length - 1);
        var index3 = Utilities.randomInt(0, 9);
        var number='';
        var show=[0,0,0,0,0,0,0,0,1,1];
        if(show[index3]==1){
            number=Utilities.randomInt(10, 99);
        }
        return NameAndAvatar.BOT_NAMES_FEMALE_1[index1]+NameAndAvatar.BOT_NAMES_FEMALE_2[index2]+number;
    }
};


NameAndAvatar.randomAvatar = function () {
    return Utilities.randomInt(0, NameAndAvatar.TOTAL_AVATAR - 1);
};

NameAndAvatar.RETURN_CODE = {
    VALID: 0,
    INVALID: 1,
    EMPTY: 2,
    TOO_MUCH_LENGTH: 3,
    CONTAIN_INVALID_CHARACTER: 4,
    BAD_WORD: 5
};

NameAndAvatar.customFilter = /\b(4r5e|5h1t|5hit|a55|anal|anus|ar5e|arrse|arse|ass|ass-fucker|asses|assfucker|assfukka|asshole|assholes|asswhole|a_s_s|b!tch|b00bs|b17ch|b1tch|ballbag|balls|ballsack|bastard|beastial|beastiality|bellend|bestial|bestiality|bi\+ch|biatch|bitch|bitcher|bitchers|bitches|bitchin|bitching|bloody|blow job|blowjob|blowjobs|boiolas|bollock|bollok|boner|boob|boobs|booobs|boooobs|booooobs|booooooobs|breasts|buceta|bugger|bum|bunny fucker|butt|butthole|buttmuch|buttplug|c0ck|c0cksucker|carpet muncher|cawk|chink|cipa|cl1t|clit|clitoris|clits|cnut|cock|cock-sucker|cockface|cockhead|cockmunch|cockmuncher|cocks|cocksuck|cocksucked|cocksucker|cocksucking|cocksucks|cocksuka|cocksukka|cok|cokmuncher|coksucka|coon|cox|crap|cum|cummer|cumming|cums|cumshot|cunilingus|cunillingus|cunnilingus|cunt|cuntlick|cuntlicker|cuntlicking|cunts|cyalis|cyberfuc|cyberfuck|cyberfucked|cyberfucker|cyberfuckers|cyberfucking|d1ck|damn|dick|dickhead|dildo|dildos|dink|dinks|dirsa|dlck|dog-fucker|doggin|dogging|donkeyribber|doosh|duche|dyke|ejaculate|ejaculated|ejaculates|ejaculating|ejaculatings|ejaculation|ejakulate|f u c k|f u c k e r|f4nny|fag|fagging|faggitt|faggot|faggs|fagot|fagots|fags|fanny|fannyflaps|fannyfucker|fanyy|fatass|fcuk|fcuker|fcuking|feck|fecker|felching|fellate|fellatio|fingerfuck|fingerfucked|fingerfucker|fingerfuckers|fingerfucking|fingerfucks|fistfuck|fistfucked|fistfucker|fistfuckers|fistfucking|fistfuckings|fistfucks|flange|fook|fooker|fuck|fucka|fucked|fucker|fuckers|fuckhead|fuckheads|fuckin|fucking|fuckings|fuckingshitmotherfucker|fuckme|fucks|fuckwhit|fuckwit|fudge packer|fudgepacker|fuk|fuker|fukker|fukkin|fuks|fukwhit|fukwit|fux|fux0r|f_u_c_k|gangbang|gangbanged|gangbangs|gaylord|gaysex|goatse|God|god-dam|god-damned|goddamn|goddamned|hardcoresex|hell|heshe|hoar|hoare|hoer|homo|hore|horniest|horny|hotsex|jack-off|jackoff|jap|jerk-off|jism|jiz|jizm|jizz|kawk|knob|knobead|knobed|knobend|knobhead|knobjocky|knobjokey|kock|kondum|kondums|kum|kummer|kumming|kums|kunilingus|l3i\+ch|l3itch|labia|lust|lusting|m0f0|m0fo|m45terbate|ma5terb8|ma5terbate|masochist|master-bate|masterb8|masterbat*|masterbat3|masterbate|masterbation|masterbations|masturbate|mo-fo|mof0|mofo|mothafuck|mothafucka|mothafuckas|mothafuckaz|mothafucked|mothafucker|mothafuckers|mothafuckin|mothafucking|mothafuckings|mothafucks|mother fucker|motherfuck|motherfucked|motherfucker|motherfuckers|motherfuckin|motherfucking|motherfuckings|motherfuckka|motherfucks|muff|mutha|muthafecker|muthafuckker|muther|mutherfucker|n1gga|n1gger|nazi|nigg3r|nigg4h|nigga|niggah|niggas|niggaz|nigger|niggers|nob|nob jokey|nobhead|nobjocky|nobjokey|numbnuts|nutsack|orgasim|orgasims|orgasm|orgasms|p0rn|pawn|pecker|penis|penisfucker|phonesex|phuck|phuk|phuked|phuking|phukked|phukking|phuks|phuq|pigfucker|pimpis|piss|pissed|pisser|pissers|pisses|pissflaps|pissin|pissing|pissoff|poop|porn|porno|pornography|pornos|prick|pricks|pron|pube|pusse|pussi|pussies|pussy|pussys|rectum|retard|rimjaw|rimming|s hit|s.o.b.|sadist|schlong|screwing|scroat|scrote|scrotum|semen|sex|sh!\+|sh!t|sh1t|sh\*t|shag|shagger|shaggin|shagging|shemale|shi\+|shit|shitdick|shite|shited|shitey|shitfuck|shitfull|shithead|shiting|shitings|shits|shitted|shitter|shitters|shitting|shittings|shitty|skank|slut|sluts|smegma|smut|snatch|son-of-a-bitch|spac|spunk|s_h_i_t|t1tt1e5|t1tties|teets|teez|testical|testicle|tit|titfuck|tits|titt|tittie5|tittiefucker|titties|tittyfuck|tittywank|titwank|tosser|turd|tw4t|twat|twathead|twatty|twunt|twunter|v14gra|v1gra|vagina|viagra|vulva|w00se|wang|wank|wanker|wanky|whoar|whore|willies|willy|xrated|xxx|f*ck|anus|arse|arsehole|ass|ass-hat|ass-jabber|ass-pirate|assbag|assbandit|assbanger|assbite|assclown|asscock|asscracker|assess|assface|assfuck|assfucker|assgoblin|asshat|asshead|asshole|asshopper|assjacker|asslick|asslickerlicker|assmonkey|assmunch|assmuncher|assnigger|asspirate|assshit|assshole|asssucker|asswad|asswipe|axwound|bampot|bastard|beaner|bitch|bitchass|bitches|bitchtits|bitchy|blow job|blowjob|bollocks|bollox|boner|brotherfucker|bullshit|bumblefuck|butt plug|butt-pirate|buttfucka|buttfucker|camel toe|carpetmuncher|chesticle|chinc|chink|choad|chode|clit|clitface|clitfuck|clitweasel|clusterfuck|cock|cockass|cockbite|cockburger|cockface|cockfucker|cockhead|cockjockey|cockknoker|cockmaster|cockmongler|cockmongruel|cockmonkey|cockmuncher|cocknose|cocknugget|cockshit|cocksmith|cocksmoke|cocksmoker|cocksniffer|cocksucker|cockwaffle|coochie|coochy|coon|cooter|cracker|cum - semen|cumbubble|cumdumpster|cumguzzler|cumjockey|cumslut|cumtart|cunnie|cunnilingus|cunt|cuntass|cuntface|cunthole|cuntlicker|cuntrag|cuntslut|dago|damn|deggo|dick|dick-sneeze|dickbag|dickbeaters|dickface|dickfuck|dickfucker|dickhead|dickhole|dickjuice|dickmilk|dickmonger|dicks|dickslap|dicksucker|dicksucking|dicktickler|dickwad|dickweasel|dickweed|dickwod|dike|dildo|dipshit|docking|doochbag|dookie|douche|douche-fag|douchebag|douchewaffle|dumass|dumb ass|dumbass|dumbfuck|dumbshit|dumshit|dyke|fag|fagbag|fagfucker|faggit|faggot|faggotcock|fagnut|fagtard|fatass|fellatio|feltch|flamer|fuck|fuckass|fuckbag|fuckboy|fuckbrain|fuckbutt|fuckbutter|fucked|fucker|fuckersucker|fuckface|fuckhead|fuckhole|fuckin|fucking|fucknose|fucknut|fucknutt|fuckoff|fucks|fuckstick|fucktard|fucktart|fuckup|fuckwad|fuckwit|fuckwitt|fudgepacker|gay|gayass|gaybob|gaydo|gayfuck|gayfuckist|gaylord|gaytard|gaywad|goddamn|goddamnit|gooch|gook|gringo|guido|handjob|hard on|heeb|hell|ho|hoe|homo|homodumbshit|honkey|humping|jackass|jagoff|jap|jerk off|jerkass|jigaboo|jizz|jungle bunny|junglebunny|kike|kooch|kootch|kraut|kunt|kyke|lameass|lardass|lesbian|lesbo|lezzie|masturbate|mcfagget|mick|minge|mothafucka|mothafuckin|motherfucker|motherfucking|muff|muffdiver|munging|nazi|negro|nigaboo|nigga|nigger|niggerish|niggerss|niglet child|nignog|nut sack|nutsack|paki|panooch|pecker|peckerhead|penis|penisbanger|penisfucker|penispuffer|piss|pissed|pissed off|pissflaps|polesmoker|pollock|poon|poonani|poonany|poontang|porch monkey|porchmonkey|prick|punanny|punta|pussiess|pussy|pussylicking|puto|queef|queer|queerbait|queerhole|renob|rimjob|ruski|sand nigger|sandnigger|schlong|scrote|shit|shitass|shitbag|shitbagger|shitbrains|shitbreath|shitcanned|shitcunt|shitdick|shitface|shitfaced|shithead|shithole|shithouse|shitspitter|shitstain|shitter|shittiest|shitting|shitty|shiz|shiznit|skank|skeet|skullfuck|slut|slutbag|smeg|snatch|spic|spick|splooge|spook|suckass|tard|testicle|thundercunt|tit|titfuck|tits|tittyfuck|twat|twatlips|twats|twatwaffle|uglyfuck|unclefucker|va-j-j|vag|vagina|vajayjay|vjayjay|wank|wankjob|wetback|whore|whorebag|whoreface|wop)\b/i;

NameAndAvatar.checkWithRegex = function (word, regex) {
    if (regex instanceof RegExp) {
        regex.lastIndex = 0;
        if (regex.test(word)) {
            return true;
        }
    }

    return false;
};

NameAndAvatar.validatePlayerName = function (name) {
    if (name == null) return NameAndAvatar.RETURN_CODE.INVALID;
    if (typeof name === 'undefined') return NameAndAvatar.RETURN_CODE.INVALID;
    if (typeof name !== 'string') return NameAndAvatar.RETURN_CODE.INVALID;
    if (name == '') return NameAndAvatar.RETURN_CODE.EMPTY;

    /**
     * Check length
     */
    if (name.length > NameAndAvatar.NAME_LENGTH) return NameAndAvatar.RETURN_CODE.TOO_MUCH_LENGTH;

    /**
     * Check with regular expression.
     */
    var regex = new RegExp('^[A-Za-z0-9-]*$');
    var test = regex.test(name);
    if (!test) {
        return NameAndAvatar.RETURN_CODE.CONTAIN_INVALID_CHARACTER;
    }

    /**
     * Bad words filter
     */
    if (NameAndAvatar.checkWithRegex(name, NameAndAvatar.customFilter))
        { return NameAndAvatar.RETURN_CODE.BAD_WORD; }

    if (typeof BAD_WORD_REGEX1 !== 'undefined') {
        if (NameAndAvatar.checkWithRegex(name, BAD_WORD_REGEX1))
            { return NameAndAvatar.RETURN_CODE.BAD_WORD; }
    }
    if (typeof BAD_WORD_REGEX2 !== 'undefined') {
        if (NameAndAvatar.checkWithRegex(name, BAD_WORD_REGEX1))
            { return NameAndAvatar.RETURN_CODE.BAD_WORD; }
    }
    if (typeof BAD_WORD_REGEX3 !== 'undefined') {
        if (NameAndAvatar.checkWithRegex(name, BAD_WORD_REGEX1))
            { return NameAndAvatar.RETURN_CODE.BAD_WORD; }
    }
    if (typeof BAD_WORD_REGEX4 !== 'undefined') {
        if (NameAndAvatar.checkWithRegex(name, BAD_WORD_REGEX1))
            { return NameAndAvatar.RETURN_CODE.BAD_WORD; }
    }
    if (typeof BAD_WORD_REGEX5 !== 'undefined') {
        if (NameAndAvatar.checkWithRegex(name, BAD_WORD_REGEX1))
            { return NameAndAvatar.RETURN_CODE.BAD_WORD; }
    }
    return NameAndAvatar.RETURN_CODE.VALID;
};

/**
 * Export module
 */
if (typeof module !== 'undefined') {
    module.exports.NameAndAvatar = NameAndAvatar;
}
