ig.module(
    'dl.templates.mixins.button-effect'
).requires(
    'dl.templates.mixins.button'
).defines(function () {
    'use strict';

    dl.MixinButtonScaleEffect = {
        staticInstantiate: function () {
            this.parent();

            this.useScaleEffect = true;
            this._buttonScaleConfigs = {
                base: { x: 1, y: 1 },
                over: { x: 0.95, y: 0.95 },
                click: { x: 0.9, y: 0.9 }
            };

            return this;
        },

        getButtonScale: function () {
            if (this.canFunction()) {
                if (this.__firstClicked) {
                    return this._buttonScaleConfigs.click;
                }

                if (this.__pointerOver) {
                    return this._buttonScaleConfigs.over;
                }
            }

            return this._buttonScaleConfigs.base;
        },

        draw: function (ctx) {
            ctx.save();
            if (this.useScaleEffect) {
                var buttonScale = this.getButtonScale();
                ctx.translate(this.pos.x, this.pos.y);
                ctx.scale(buttonScale.x, buttonScale.y);
                ctx.translate(-this.pos.x, -this.pos.y);
            }
            this.parent(ctx);
            ctx.restore();
        }
    };

    dl.MixinButtonBrightnessEffect = {
        staticInstantiate: function () {
            this.parent();

            this.useBrightnessEffect = true;
            this._buttonBrightnessConfigs = {
                base: 1,
                over: 1.1,
                click: 1.2,
                disable: 0.75
            };

            return this;
        },

        getButtonBrightness: function () {
            if (!this.canFunction()) {
                return this._buttonBrightnessConfigs.disable;
            }

            if (this.__firstClicked) {
                return this._buttonBrightnessConfigs.click;
            }

            if (this.__pointerOver) {
                return this._buttonBrightnessConfigs.over;
            }

            return this._buttonBrightnessConfigs.base;
        },

        draw: function (ctx) {
            ctx.save();
            if (this.useBrightnessEffect) {
                ctx.filter = 'brightness(' + this.getButtonBrightness() + ')';
            }
            this.parent(ctx);
            ctx.restore();
        }
    };
});
