/**
 * Created by <PERSON><PERSON>
 * Network Game utilities functions
 */
var NetworkGameUtilities = {};

NetworkGameUtilities.reCalculatePlayerRank = function (networkGame) {
    if (!networkGame) return null;
    if (!networkGame.networkGameData) return null;

    // create sort array
    var sortArr = [];
    var player = null;
    for (var i = 0; i < networkGame.networkGameData.playerList.length; i++) {
        player = networkGame.networkGameData.playerList[i];
        sortArr.push({
            playerId: player.playerId,
            playerScore: player.playerScore
        });
    }

    // do sort
    sortArr = sortArr.sort(function (playerA, playerB) {
        return playerB.playerScore - playerA.playerScore;
    });

    // convert to object
    var sortObject = {};
    for (var i = 0; i < sortArr.length; i++) {
        sortObject[sortArr[i].playerId] = i;
    }

    // update rank
    var player2 = null;
    for (var i = 0; i < networkGame.networkGameData.playerList.length; i++) {
        player2 = networkGame.networkGameData.playerList[i];
        player2.playerRank = sortObject[player2.playerId];
    }

    return sortObject;
};

NetworkGameUtilities.aabbCheck = function (aabb1, aabb2) {
    if (aabb1.x + aabb1.w > aabb2.x &&
        aabb1.x < aabb2.x + aabb2.w &&
        aabb1.y + aabb1.h > aabb2.y &&
        aabb1.y < aabb2.y + aabb2.h) {
        return true;
    }
    return false;
};

/**
 * Export module
 */
if (typeof module !== 'undefined') {
    module.exports.NetworkGameUtilities = NetworkGameUtilities;
}
