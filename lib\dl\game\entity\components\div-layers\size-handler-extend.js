ig.module(
    'dl.game.entity.components.div-layers.size-handler-extend'
).requires(
    'plugins.handlers.size-handler'
).defines(function () {
    'use strict';

    ig.SizeHandler.inject({
        resizeLayersDiv: function () {
            var canvas = ig.domHandler.getElementById('#canvas');
            var offsets = ig.domHandler.getOffsets(canvas);
            var offsetLeft = offsets.left;
            var offsetTop = offsets.top;
            var aspectRatioMin = Math.min(ig.sizeHandler.scaleRatioMultiplier.x, ig.sizeHandler.scaleRatioMultiplier.y);

            for (var key in this.dynamicClickableEntityDivs) {
                var div = ig.domHandler.getElementById('#' + key);

                var posX = this.dynamicClickableEntityDivs[key].entity_pos_x;
                var posY = this.dynamicClickableEntityDivs[key].entity_pos_y;
                var sizeX = this.dynamicClickableEntityDivs[key].width;
                var sizeY = this.dynamicClickableEntityDivs[key].height;

                var divleft = Math.floor(offsetLeft + posX * this.scaleRatioMultiplier.x) + 'px';
                var divtop = Math.floor(offsetTop + posY * this.scaleRatioMultiplier.y) + 'px';
                var divwidth = Math.floor(sizeX * this.scaleRatioMultiplier.x) + 'px';
                var divheight = Math.floor(sizeY * this.scaleRatioMultiplier.y) + 'px';

                ig.domHandler.css(div
                    , {
                        float: 'left',
                         position: 'absolute',
                         left: divleft,
                         top: divtop,
                         width: divwidth,
                         height: divheight,
                         'z-index': 3
                    }
                );

                if (this.dynamicClickableEntityDivs[key]['font-size']) {
                    var fontSize = this.dynamicClickableEntityDivs[key]['font-size'];
                    ig.domHandler.css(div, { 'font-size': (fontSize * aspectRatioMin) + 'px' });
                }
            }
        }
    });
});
