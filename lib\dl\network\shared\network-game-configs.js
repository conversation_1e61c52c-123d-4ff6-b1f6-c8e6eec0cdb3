/**
 * Created by <PERSON><PERSON>
 * Defining network game configs
 */

var NETWORK_GAME_CONFIGS = {};

// Network game states
NETWORK_GAME_CONFIGS.NETWORK_GAME_STATE = {
    INITIALIZING: 0,
    INSTRUCTION: 1,
    COUNT_DOWN: 2,
    GA<PERSON>: 3,
    <PERSON>ND<PERSON>: 4
};

// transition time between level in client side
NETWORK_GAME_CONFIGS.LEVEL_TRANSITION_TIME = 2000;

// time waiting in instruction level
NETWORK_GAME_CONFIGS.INSTRUCTION_TIME = 11000;

// time count down in game level
NETWORK_GAME_CONFIGS.COUNT_DOWN_TIME = 3000;

// time game in game level
NETWORK_GAME_CONFIGS.GAME_TIME = 60000;

// render delay time
NETWORK_GAME_CONFIGS.RENDER_DELAY = 240;

/* ----- Start custom properties ------ */
NETWORK_GAME_CONFIGS.PLAYER_SOLDIER_INITIAL_COUNT = 15;
// Min-max
NETWORK_GAME_CONFIGS.NEUTRAL_SOLDIER_RANGE = [5, 15];

NETWORK_GAME_CONFIGS.BUILDING_SOLDIER_INITIAL_COUNT = 35;

NETWORK_GAME_CONFIGS.SOLDIER_INCREMENT_TIME = 1000;

NETWORK_GAME_CONFIGS.SOLDIER_MOVEMENT_SPEED = 90;
// in degrees
NETWORK_GAME_CONFIGS.ANGLE_INTERVAL = 22;

NETWORK_GAME_CONFIGS.DISTANCE_FROM_SOURCE = 60;

NETWORK_GAME_CONFIGS.CAPITAL_SIZE = 80;

NETWORK_GAME_CONFIGS.SOLDIER_SIZE = 20;

NETWORK_GAME_CONFIGS.CAPITAL_RESUME_TIME = 1000;

NETWORK_GAME_CONFIGS.SHIELD_DURATION = 15000;
// Note: This must ALMOST match the total tween time in game-map-capital > activateMissile.
NETWORK_GAME_CONFIGS.MISSILE_SILO_TIMER = 1750;

NETWORK_GAME_CONFIGS.BUILDINGS = [
    {
        BLDG_NAME: 'Fort',
        BLDG_CODE: 'FORT',
        BLDG_DESC: 'This building generates 2 soldiers per second',
        EFFECT: 2
    },
    {
        BLDG_NAME: 'Weather Dome',
        BLDG_CODE: 'WD',
        BLDG_DESC: 'Once captured, all enemy nations lose 20 soldiers<br>via a weather storm',
        EFFECT: 20
    },
    {
        BLDG_NAME: 'Missile Silo',
        BLDG_CODE: 'MS',
        BLDG_DESC: 'Once captured, a missile will be launched<br>into one enemy nation with the most soldiers,<br>killing all soldiers upon impact',
        EFFECT: 0
    },
    {
        BLDG_NAME: 'Propaganda Tower',
        BLDG_CODE: 'PT',
        BLDG_DESC: 'Once captured, all enemy soldiers<br>will train at 50% rate',
        /* duration is in milliseconds */
        DURATION: 10000
    }
];

/* ------ End custom properties ------- */

/* ----- Start debug properties ------ */
NETWORK_GAME_CONFIGS.SKIP_TIME = !true;
if (NETWORK_GAME_CONFIGS.SKIP_TIME) {
    // transition time between level in client side
    NETWORK_GAME_CONFIGS.LEVEL_TRANSITION_TIME = 100;
    // time waiting in instruction level
    NETWORK_GAME_CONFIGS.INSTRUCTION_TIME = 1000;
    // time count down in game level
    NETWORK_GAME_CONFIGS.COUNT_DOWN_TIME = 1000;
    // time game in game level
    NETWORK_GAME_CONFIGS.GAME_TIME = 60000;
}
/* ------ End debug properties ------- */

/**
 * Export module
 */
if (typeof module !== 'undefined') {
    module.exports.NETWORK_GAME_CONFIGS = NETWORK_GAME_CONFIGS;
}
