/**
 * Created by <PERSON><PERSON>
 * Responsive plugins mode to support both portrait and landscape
 */
ig.module(
    'plugins.responsive.responsive-plugin-v2'
).requires(
    'impact.system',
    'impact.entity',
    'plugins.handlers.size-handler'
).defines(function () {
    ig.SizeHandler.inject({
        resize: function () {
            this.parent();
        },

        checkOrientation: function () {
            // var ratio = window.innerHeight / window.innerWidth;
            // if (ratio >= (16 / 9)) {
            //     this.isPortraitOrientation = true;
            // } else {
            //     this.isPortraitOrientation = false;
            // }

            if (window.innerHeight > window.innerWidth) {
                this.isPortraitOrientation = true;
            } else {
                this.isPortraitOrientation = false;
            }
        },

        sizeCalcs: function () {
            if (!this.originalResolutionDesktop &&
                !this.originalResolutionMobile) {
                this.originalResolutionDesktop = new Vector2(this.desktop.actualResolution.x, this.desktop.actualResolution.y);
                this.originalResolutionMobile = new Vector2(this.mobile.actualResolution.x, this.mobile.actualResolution.y);
            }
            var innerWidth = window.innerWidth;
            var innerHeight = window.innerHeight;

            this.checkOrientation();
            if (this.isPortraitOrientation) {
                this.originalResolution = new Vector2(this.originalResolutionMobile.x, this.originalResolutionMobile.y);
            } else {
                this.originalResolution = new Vector2(this.originalResolutionDesktop.x, this.originalResolutionDesktop.y);
            }

            // calculate
            var newAspectRatio = innerWidth / innerHeight;
            var originalAspectRatio = this.originalResolution.x / this.originalResolution.y;
            var newWidth = 0;
            var newHeight = 0;
            this.windowSize = new Vector2(innerWidth, innerHeight);
            if (newAspectRatio < originalAspectRatio) {
                // if need to fill vertically
                newWidth = this.originalResolution.x;
                newHeight = newWidth / newAspectRatio;
                ig.responsive.scaleX = 1;
                ig.responsive.scaleY = newHeight / this.originalResolution.y;
            } else {
                // if need to fill horizontally
                newHeight = this.originalResolution.y;
                newWidth = newHeight * newAspectRatio;
                ig.responsive.scaleX = newWidth / this.originalResolution.x;
                ig.responsive.scaleY = 1;
            }

            this.scaleRatioMultiplier = new Vector2(innerWidth / newWidth, innerHeight / newHeight);
            this.desktop.actualResolution = new Vector2(newWidth, newHeight);
            this.mobile.actualResolution = new Vector2(newWidth, newHeight);
            this.desktop.actualSize = new Vector2(innerWidth, innerHeight);
            this.mobile.actualSize = new Vector2(innerWidth, innerHeight);

            ig.responsive.originalWidth = this.originalResolution.x;
            ig.responsive.originalHeight = this.originalResolution.y;
            ig.responsive.width = newWidth;
            ig.responsive.height = newHeight;
            ig.responsive.halfWidth = newWidth / 2;
            ig.responsive.halfHeight = newHeight / 2;
            ig.responsive.fillScale = Math.max(ig.responsive.scaleX, ig.responsive.scaleY);
        },

        resizeLayers: function (width, height) {
            ig.system.resize(ig.sizeHandler.desktop.actualResolution.x, ig.sizeHandler.desktop.actualResolution.y);
            for (var index = 0; index < this.coreDivsToResize.length; index++) {
                var elem = ig.domHandler.getElementById(this.coreDivsToResize[index]);

                var l = Math.floor(((ig.sizeHandler.windowSize.x / 2) - (ig.sizeHandler.desktop.actualSize.x / 2)));
                var t = Math.floor(((ig.sizeHandler.windowSize.y / 2) - (ig.sizeHandler.desktop.actualSize.y / 2)));
                if (l < 0) l = 0;
                if (t < 0) t = 0;
                ig.domHandler.resizeOffset(elem, Math.floor(ig.sizeHandler.desktop.actualSize.x), Math.floor(ig.sizeHandler.desktop.actualSize.y), l, t);
            }

            for (var key in this.adsToResize) {
                var keyDiv = ig.domHandler.getElementById('#' + key);
                var keyBox = ig.domHandler.getElementById('#' + key + '-Box');

                var divLeft = (window.innerWidth - this.adsToResize[key]['box-width']) / 2 + 'px';
                var divTop = (window.innerHeight - this.adsToResize[key]['box-height']) / 2 + 'px';

                if (keyDiv) {
                    ig.domHandler.css(keyDiv, { width: window.innerWidth, height: window.innerHeight });
                }
                if (keyBox) {
                    ig.domHandler.css(keyBox, { left: divLeft, top: divTop });
                }
            }

            var canvas = ig.domHandler.getElementById('#canvas');
            var offsets = ig.domHandler.getOffsets(canvas);
            var offsetLeft = offsets.left;
            var offsetTop = offsets.top;
            var aspectRatioMin = Math.min(ig.sizeHandler.scaleRatioMultiplier.x, ig.sizeHandler.scaleRatioMultiplier.y);

            for (var key in this.dynamicClickableEntityDivs) {
                var div = ig.domHandler.getElementById('#' + key);

                var posX = this.dynamicClickableEntityDivs[key].entity_pos_x;
                var posY = this.dynamicClickableEntityDivs[key].entity_pos_y;
                var sizeX = this.dynamicClickableEntityDivs[key].width;
                var sizeY = this.dynamicClickableEntityDivs[key].height;

                var divleft = Math.floor(offsetLeft + posX * this.scaleRatioMultiplier.x) + 'px';
                var divtop = Math.floor(offsetTop + posY * this.scaleRatioMultiplier.y) + 'px';
                var divwidth = Math.floor(sizeX * this.scaleRatioMultiplier.x) + 'px';
                var divheight = Math.floor(sizeY * this.scaleRatioMultiplier.y) + 'px';

                ig.domHandler.css(div
                    , {
                        float: 'left',
                         position: 'absolute',
                         left: divleft,
                         top: divtop,
                         width: divwidth,
                         height: divheight,
                         'z-index': 3
                    }
                );

                if (this.dynamicClickableEntityDivs[key]['font-size']) {
                    var fontSize = this.dynamicClickableEntityDivs[key]['font-size'];
                    ig.domHandler.css(div, { 'font-size': (fontSize * aspectRatioMin) + 'px' });
                }
            }

            $('#ajaxbar').width(this.windowSize.x);
            $('#ajaxbar').height(this.windowSize.y);
        },

        handleOrientate: function () {
            this.checkOrientation();

            if (!ig.game) return;

            if (ig.game.updateOrientation) {
                ig.game.updateOrientation();
            }
        },

        reorient: function () {
            this.handleOrientate();

            if (!ig.ua.mobile) {
                this.resize();
            } else {
                this.resize();
                this.resizeAds();
            }

            if (ig.game) {
                ig.game.update();
                ig.game.draw();
            }
        }
    });
});
