ig.module(
    'dl.game.entity.orientation'
).requires(
    'dl.game.entity.components.draw.text'
).defines(function () {
    'use strict';

    /**
     * @example
        orientationData:{
            portrait: {

            },
            landscape: {

            }
        }
     */
    dl.EntityMixinOrientation = {
        staticInstantiate: function () {
            this.parent();

            this.orientationData = null;
            this.currentOrientationData = null;

            this._orientationScale = { x: 1, y: 1 };

            return this;
        },

        postInit: function () {
            this.parent();

            this.updateOrientation();
        },

        updateOrientation: function () {
            this.parent();

            if (!this.orientationData) return;
            if (ig.sizeHandler.isPortraitOrientation) {
                this.currentOrientationData = this.orientationData.portrait;
            } else {
                this.currentOrientationData = this.orientationData.landscape;
            }

            this.updateOrientationData();
            this.onUpdateOrientation();
        },

        updateOrientationData: function () {
            if (!this.currentOrientationData) return;

            for (var propertyName in this.currentOrientationData) {
                var property = this.currentOrientationData[propertyName];
                switch (true) {
                    case this[propertyName] instanceof dl.EntityComponent: {
                        this[propertyName].updateProperties(property);
                    } break;
                    case propertyName == 'image': {
                        this.image = property;
                        this.updateImage(this.image);
                    } break;
                    case propertyName == '_orientationScale': {
                        this._orientationScale = {
                            x: property.x,
                            y: property.y
                        };

                        this.updateScale();
                    } break;
                }
            }
        },

        onUpdateOrientation: function () {
            // implement
        },

        updateBaseScale: function () {
            this.scale.x = this._scale.x * this._orientationScale.x;
            this.scale.y = this._scale.y * this._orientationScale.y;
        }
    };
});
