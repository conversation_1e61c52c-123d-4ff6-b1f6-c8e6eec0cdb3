ig.module(
    'dl.utils.canvas'
).defines(function () {
    'use strict';

    dl.canvas = {
        toImage: function (canvas) {
            var dataURL = canvas.toDataURL('image/png');
            var newTab = window.open('about:blank', 'image from canvas');
            newTab.document.write("<img src='" + dataURL + "' alt='from canvas'/>");
        },
        clearCanvas: function (canvas) {
            var ctx = canvas.getContext('2d');
            // ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.save();
            ctx.resetTransform();
            ctx.fillStyle = 'grey';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.restore();
        },
        resetData: function (data) {
            this.ctx = ig.system.context;
            this.systemScale = ig.system.scale;

            this.alpha = 1;
            this.fillStyle = '#000000';
            this.strokeStyle = '#000000';
            this.stroke = true;
            this.fill = true;
            this.roundRadius = { tl: 0, tr: 0, br: 0, bl: 0 };
            this.lineWidth = 2;

            this.fontSize = 12;
            this.fontStyle = dl.configs.getConfig('FONT', 'default', 'name');
            this.fontSizeMultiply = dl.configs.getConfig('FONT', 'default', 'pixel_per_size');
            this.fontLineOffset = dl.configs.getConfig('FONT', 'default', 'lineOffset');
            this.fontHeightOffset = dl.configs.getConfig('FONT', 'default', 'heightOffset');

            this.fontWeight = 'normal'; // [normal, bold, italic]
            this.textAlign = 'center'; // "center|end|left|right|start";
            this.textBaseline = 'middle'; // "alphabetic|top|hanging|middle|ideographic|bottom";
            this.maxWidth = undefined;
            this.maxHeight = undefined;

            this.angle = 0;
            this.radius = 10;

            // image
            this.imageDrawScale = { x: 1, y: 1 };
            this.imageDrawAngle = 0;
            this.imageDrawPercent = { x: 1, y: 1 };
            this.imageDrawAlign = 'left'; // "center|left";
            this.imageFillWidth = false;
            this.imageFillHeight = false;

            // shadow
            this.shadowColor = 'rgba(0,0,0,0.75)';
            this.shadowOffsetX = 0;
            this.shadowOffsetY = 0;
        },
        handleData: function (data) {
            this.resetData();
            for (var propertyName in data) {
                var propertyData = data[propertyName];
                switch (propertyName) {
                    case 'ctx': { this.ctx = propertyData; } break;
                    case 'systemScale': { this.systemScale = propertyData; } break;
                    case 'alpha': { this.alpha = propertyData; } break;
                    case 'fillStyle': { this.fillStyle = propertyData; } break;
                    case 'strokeStyle': { this.strokeStyle = propertyData; } break;
                    case 'stroke': { this.stroke = propertyData; } break;
                    case 'fill': { this.fill = propertyData; } break;
                    case 'lineWidth': { this.lineWidth = propertyData; } break;
                    case 'roundRadius': {
                        this.roundRadius = {
                            tl: propertyData,
                            tr: propertyData,
                            br: propertyData,
                            bl: propertyData
                        };
                    } break;

                    case 'fontWeight': { this.fontWeight = propertyData; } break;
                    case 'fontSize': { this.fontSize = propertyData; } break;
                    case 'fontStyle': { this.fontStyle = propertyData; } break;
                    case 'textAlign': { this.textAlign = propertyData; } break;
                    case 'textBaseline': { this.textBaseline = propertyData; } break;
                    case 'maxWidth': { this.maxWidth = propertyData; } break;
                    case 'maxHeight': { this.maxHeight = propertyData; } break;
                    case 'angle': { this.angle = propertyData; } break;
                    case 'radius': { this.radius = propertyData; } break;

                    case 'shadowColor': { this.shadowColor = propertyData; } break;
                    case 'shadowOffsetX': { this.shadowOffsetX = propertyData; } break;
                    case 'shadowOffsetY': { this.shadowOffsetY = propertyData; } break;

                    case 'imageDrawScale': { this.imageDrawScale = propertyData; } break;
                    case 'imageDrawAngle': { this.imageDrawAngle = propertyData; } break;
                    case 'imageDrawPercent': { this.imageDrawPercent = propertyData; } break;
                    case 'imageDrawAlign': { this.imageDrawAlign = propertyData; } break;
                    case 'imageFillWidth': { this.imageFillWidth = propertyData; } break;
                    case 'imageFillHeight': { this.imageFillHeight = propertyData; } break;
                }
            }
        },
        checkPackerPlugin: function (image) {
            var baseImageData = {
                data: image.data,
                x: 0,
                y: 0,
                width: image.width,
                height: image.height
            };

            if (typeof ig.packer === 'undefined') return baseImageData;

            if (ig.packer.isImageInAtlas(image.path)) {
                if (!image.atlasImage) {
                    image.atlasImage = ig.packer.getAtlasImage(image.path);
                    image.frameData = ig.packer.getFrameData(image.path);
                    image.width = image.frameData.frame.w;
                    image.height = image.frameData.frame.h;
                }
                baseImageData = {
                    data: image.atlasImage,
                    x: image.frameData.frame.x,
                    y: image.frameData.frame.y,
                    width: image.width,
                    height: image.height
                };
            }
            return baseImageData;
        },
        drawImage: function (ctx, image, x, y, data) {
            if (!image.loaded) return;

            this.handleData(data);
            ctx.save();

            var imageData = {
                data: image.data,
                x: 0,
                y: 0,
                width: image.width,
                height: image.height
            };
            imageData = this.checkPackerPlugin(image);

            /**
             * Transform
             */
            var originX = x + (imageData.width / 2);
            var originY = y + (imageData.height / 2);
            ctx.translate(originX * this.systemScale, originY * this.systemScale);
            ctx.scale(this.imageDrawScale.x, this.imageDrawScale.y);
            ctx.rotate(this.imageDrawAngle);
            ctx.translate(-originX * this.systemScale, -originY * this.systemScale);

            var drawWidth = imageData.width;
            var drawHeight = imageData.height;
            if (this.imageFillWidth) {
                drawWidth = this.imageFillWidth;
            }
            if (this.imageFillHeight) {
                drawHeight = this.imageFillHeight;
            }

            switch (this.imageDrawAlign) {
                case 'left': {
                    ctx.drawImage(
                        imageData.data,
                        imageData.x, imageData.y,
                        imageData.width * this.imageDrawPercent.x,
                        imageData.height * this.imageDrawPercent.y,
                        x,
                        y,
                        drawWidth * this.imageDrawPercent.x,
                        drawHeight * this.imageDrawPercent.y
                    );
                } break;
                case 'center': {
                    var startPosOffsetPercent = {
                        x: imageData.width * (1 - this.imageDrawPercent.x) * 0.5,
                        y: imageData.height * (1 - this.imageDrawPercent.y) * 0.5
                    };
                    ctx.drawImage(
                        imageData.data,
                        imageData.x + startPosOffsetPercent.x,
                        imageData.y + startPosOffsetPercent.y,
                        imageData.width * this.imageDrawPercent.x,
                        imageData.height * this.imageDrawPercent.y,
                        x + startPosOffsetPercent.x,
                        y + startPosOffsetPercent.y,
                        drawWidth * this.imageDrawPercent.x,
                        drawHeight * this.imageDrawPercent.y
                    );
                } break;
            }

            ctx.restore();
        },
        drawFullscreenImage: function (ctx, image, skipRow, skipCol) {
            if (!image) return;
            if (!image.loaded) return;

            var imageWidth = image.width;
            var imageHeight = image.height;
            if ((typeof (imageWidth) !== 'number') ||
                (typeof (imageHeight) !== 'number')) {
                dl.error('invalid image size', imageWidth, imageHeight);
                return;
            }

            var maxCol = Math.ceil(ig.system.width / imageWidth);
            if (!(maxCol % 2)) {
                maxCol += 1;
            }
            if (skipCol) maxCol = 1;

            var maxRow = Math.ceil(ig.system.height / imageHeight);
            if (!(maxRow % 2)) {
                maxRow += 1;
            }
            if (skipRow) maxRow = 1;

            var centerPoint = { x: ig.system.width * 0.5, y: ig.system.height * 0.5 };
            var drawStartPos = {
                x: centerPoint.x - maxCol * imageWidth * 0.5,
                y: centerPoint.y - maxRow * imageHeight * 0.5
            };

            for (var row = 0; row < maxRow; row++) {
                for (var col = 0; col < maxCol; col++) {
                    this.drawImage(ctx,
                        image,
                        Math.round(drawStartPos.x + imageWidth * col),
                        Math.round(drawStartPos.y + imageHeight * row)
                    );
                }
            }
        }
    };
});
