ig.module(
    'game.dl-entities.emoji-bar'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.button',
    'dl.templates.mixins.animation-sheet'
).defines(function () {
    dl.EntityNetworkEmojiBar = dl.Entity
        .extend(dl.MixinDocker)
        .extend({
            staticInstantiate: function () {
                this.parent();

                return this;
            },

            postInit: function () {
                this.parent();

                this.initButtons();
            },

            initButtons: function () {

                var xx=window.innerWidth;
                var yy=window.innerHeight;

                if(ig.ua.mobile && !ig.ua.iPad){

                    if(yy>xx){
                        var maxEmoji = {
                            x: 6,
                            y: 1
                        };


                        var emojiSize = {
                            x: 90 * this.scale.x*2,
                            y: 90 * this.scale.y*2
                        };

                        var startOffset = {
                            x: -emojiSize.x * (maxEmoji.x - 1) * 0.5,
                            y: -emojiSize.y * (maxEmoji.y - 1) * 0.5
                        };

                    }else{

                        var maxEmoji = {
                            x: 1,
                            y: 6
                        };


                        var emojiSize = {
                            x: 90 * this.scale.x*2,
                            y: 90 * this.scale.y*2
                        };

                        var startOffset = {
                            x: -emojiSize.x * (maxEmoji.x - 1) * 0.5,
                            y: -emojiSize.y * (maxEmoji.y - 1) * 0.5
                        };

                    }


                }else{

                    var maxEmoji = {
                        x: 1,
                        y: 6
                    };

                    var emojiSize = {
                        x: 80 * this.scale.x,
                        y: 80 * this.scale.y
                    };

                    var startOffset = {
                        x: -emojiSize.x * (maxEmoji.x - 1) * 0.5,
                        y: -emojiSize.y * (maxEmoji.y - 1) * 0.5
                    };
                }



                this.emojiButtons = [];
                for (var i = 0, iLength = maxEmoji.y; i < iLength; i++) {
                    for (var j = 0, jLength = maxEmoji.x; j < jLength; j++) {
                        var index = i * maxEmoji.x + j;
                        var newEmojiButton = this.spawnEntity(dl.EntityNetworkEmojiButton, {
                            c_DockerComponent: {
                                dockerObject: this,
                                dockerPercent: { x: 0.5, y: 0.5 },
                                dockerOffset: {
                                    x: startOffset.x + emojiSize.x * j,
                                    y: startOffset.y + emojiSize.y * i
                                },
                            },
                            c_AnimationSheetComponent: {
                                _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                                _size: emojiSize
                            },
                            emojiIndex: index,
                            onButtonClicked: this.onEmojiButtonClicked.bind(this, index),
                            emojiSize:emojiSize,
                        });

                        this.emojiButtons.push(newEmojiButton);
                    }
                }
            },

            updateButtonPos:function(){
                var xx=window.innerWidth;
                var yy=window.innerHeight;
                
                if(ig.ua.mobile && !ig.ua.iPad){

                    if(yy>xx){
                        var maxEmoji = {
                            x: 6,
                            y: 1
                        };


                        var emojiSize = {
                            x: 90 * this.scale.x*2,
                            y: 90 * this.scale.y*2
                        };

                        var startOffset = {
                            x: -emojiSize.x * (maxEmoji.x - 1) * 0.5,
                            y: -emojiSize.y * (maxEmoji.y - 1) * 0.5
                        };

                    }else{

                        var maxEmoji = {
                            x: 1,
                            y: 6
                        };


                        var emojiSize = {
                            x: 90 * this.scale.x*2,
                            y: 90 * this.scale.y*2
                        };

                        var startOffset = {
                            x: -emojiSize.x * (maxEmoji.x - 1) * 0.5,
                            y: -emojiSize.y * (maxEmoji.y - 1) * 0.5
                        };

                    }


                }else{

                    var maxEmoji = {
                        x: 1,
                        y: 6
                    };

                    var emojiSize = {
                        x: 80 * this.scale.x,
                        y: 80 * this.scale.y
                    };

                    var startOffset = {
                        x: -emojiSize.x * (maxEmoji.x - 1) * 0.5,
                        y: -emojiSize.y * (maxEmoji.y - 1) * 0.5
                    };
                }

                var index=0;
                for (var i = 0, iLength = maxEmoji.y; i < iLength; i++) {
                    for (var j = 0, jLength = maxEmoji.x; j < jLength; j++) {
                        var dockerOffset= {
                                    x: startOffset.x + emojiSize.x * j,
                                    y: startOffset.y + emojiSize.y * i
                                };

                        this.emojiButtons[index].c_DockerComponent.dockerOffset= dockerOffset;
                        this.emojiButtons[index].updatePos();
                        index +=1;
                    }
                }



            },

            onEmojiButtonClicked: function (emojiIndex) {

                if (ig.game.client && ig.game.client.clientGameRoom) {
                    ig.game.game.playersPanel.spawnBubble(emojiIndex, ig.game.client.playerId);
                    ig.game.client.clientGameRoom.sendEmoji(emojiIndex);
                    ig.Achievement.incrementProgress(1, 1);
                    setTimeout(function(){
                        ig.game.client.saveGameData();
                    }.bind(this),500);
                } else {
                    this.onSendEmoji({
                        emojiIndex: emojiIndex,
                        playerId: ig.game.client.playerId
                    });
                }
            },
            onSendEmoji: function (info) {
                if(ig.game.client.playerId!=info.playerId){
                    ig.game.game.playersPanel.spawnBubble(info.emojiIndex, info.playerId);
                }
            }
        });

    dl.EntityNetworkEmojiButton = dl.Entity
        .extend(dl.MixinDocker)
        .extend(dl.MixinButton)
        .extend(dl.MixinButtonScaleEffect)
        .extend(dl.MixinAnimationSheet)
        .extend({
            emojiIndex: 0,
            playerName: '',
            imgEmoji:[
                        new ig.Image('media/graphics/sprites/game/emoji1.png'),
                        new ig.Image('media/graphics/sprites/game/emoji2.png'),
                        new ig.Image('media/graphics/sprites/game/emoji3.png'),
                        new ig.Image('media/graphics/sprites/game/emoji4.png'),
                        new ig.Image('media/graphics/sprites/game/emoji5.png'),
                        new ig.Image('media/graphics/sprites/game/emoji6.png')
                     ],
            staticInstantiate: function () {
                this.parent();

                this._useAnimationSheetAsSize = true;
                this.image = dl.preload.emoji;
                return this;
            },

            postInit: function () {
                this.parent();
                this.c_AnimationSheetComponent.setAnimation('emoji_' + this.emojiIndex);
            },

            _initAnimationSheetComponent: function () {
                this.c_AnimationSheetComponent.updateProperties({
                    _animationSheetImage: this.image,
                    _animationSheetRow: 1,
                    _animationSheetCol: 6,
                    _setupDefaultAnimation: function () {
                        this.addAnimation('emoji_0', 0, [0], true);
                        this.addAnimation('emoji_1', 0, [1], true);
                        this.addAnimation('emoji_2', 0, [2], true);
                        this.addAnimation('emoji_3', 0, [3], true);
                        this.addAnimation('emoji_4', 0, [4], true);
                        this.addAnimation('emoji_5', 0, [5], true);
                    }
                });
            },

            onPointerReleased: function () {
                this.onButtonClicked();
            },

            draw: function (ctx) {
                ctx.save();
                ctx.globalAlpha = this.alpha;
                this.parent(ctx);
                ctx.restore();

                // var xx=window.innerWidth;
                // var yy=window.innerHeight;
                
                // if(ig.ua.mobile && !ig.ua.iPad){                
                //     this.imgEmoji[this.emojiIndex].drawImage(
                //         0,
                //         0,
                //         this.imgEmoji[this.emojiIndex].width,
                //         this.imgEmoji[this.emojiIndex].height,
                //         this.pos.x-(this.emojiSize.x-20)/2,
                //         this.pos.y-(this.emojiSize.y-20)/2,
                //         this.emojiSize.x-20,
                //         this.emojiSize.y-20
                //     );
                // }
            }
        });

    dl.EntityNetworkEmojiIcon = dl.Entity
        .extend(dl.MixinDocker)
        .extend(dl.MixinAnimationSheet)
        .extend({
            emojiIndex: 0,
            playerName: '',
            staticInstantiate: function () {
                this.parent();

                this._useAnimationSheetAsSize = true;
                this.image = dl.preload.emoji;
                return this;
            },

            postInit: function () {
                this.parent();
                this.c_AnimationSheetComponent.setAnimation('emoji_' + this.emojiIndex);
            },

            _initAnimationSheetComponent: function () {
                this.c_AnimationSheetComponent.updateProperties({
                    _animationSheetImage: this.image,
                    _animationSheetRow: 1,
                    _animationSheetCol: 6,
                    _setupDefaultAnimation: function () {
                        this.addAnimation('emoji_0', 0, [0], true);
                        this.addAnimation('emoji_1', 0, [1], true);
                        this.addAnimation('emoji_2', 0, [2], true);
                        this.addAnimation('emoji_3', 0, [3], true);
                        this.addAnimation('emoji_4', 0, [4], true);
                        this.addAnimation('emoji_5', 0, [5], true);
                    }
                });
            },

            onPointerReleased: function () {
            },

            draw: function (ctx) {
                ctx.save();
                ctx.globalAlpha = this.alpha;
                this.parent(ctx);
                ctx.restore();
            }
        });
});
