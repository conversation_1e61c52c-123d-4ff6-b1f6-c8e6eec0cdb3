ig.module(
    'dl.data.configs'
).requires(
    'dl.dl'
).defines(function () {
    'use strict';

    dl.configs = {};
    dl.configs.getConfig = function () {
        var currentObject = this;

        var settingString = '';
        for (var i = 0; i < arguments.length; i++) {
            var arg = arguments[i];
            currentObject = currentObject[arg];
            settingString += '.' + arg;
            if (typeof currentObject === 'undefined') {
                dl.error("Can't get setting: " + settingString);
                return '';
            }
        }
        return currentObject;
    };

    /**
     * Custom configs
     */
    dl.configs.FONT = {
        default: {
            name: 'montserrat-regular',
            pixel_per_size: 1,
            lineOffset: 0.1,
            heightOffset: 0.05
        },
        bold: {
            name: 'montserrat-bold',
            pixel_per_size: 1,
            lineOffset: 0.1,
            heightOffset: 0.05
        },
        SOURCE_SANS: {
            name: 'sourcesanspro-bold',
            pixel_per_size: 1,
            lineOffset: 0.1,
            heightOffset: 0.05
        }
    };

    dl.configs.TEXT_COLOR = {
        DEFAULT: 'black',
        INPUT_TEXT: 'black',
        BUTTON_TEXT: 'white',
        GRAY: '#454545',
        WHITE: 'white',
        PLAYER_1: '#FE6A6A',
        PLAYER_2: '#608DD2',
        PLAYER_3: '#25BB76',
        PLAYER_4: '#FDCB5A',
        NEUTRAL: '#D1CFD1'
    };

    dl.configs.NOTIFICATION_COLORS = {
        PLAYER_1: '#f16164',
        PLAYER_2: '#6084cd',
        PLAYER_3: '#43b46a',
        PLAYER_4: '#f6c552',
        NEUTRAL: '#D1CFD1'
    };

    dl.configs.DIRECTION = {
        DEFAULT: 0,
        LEFT: 2,
        RIGHT: 4,
        UP: 8,
        DOWN: 16
    };
    // in seconds
    dl.configs.SHIELD_DURATION = 15;
    // in milliseconds
    dl.configs.BLINK_INTERVAL = 750;

    dl.configs.HOME_LEVEL_TWEEN_TIME = { IN: 1000, OUT: 600 };
    dl.configs.MATCHING_LEVEL_TWEEN_TIME = { IN: 1000, OUT: 750 };
    dl.configs.INSTRUCTION_LEVEL_TWEEN_TIME = { IN: 1000, OUT: 750 };
    dl.configs.GAME_LEVEL_TWEEN_TIME = { IN: 1000, OUT: 750 };
    dl.configs.GAMEOVER_LEVEL_TWEEN_TIME = { IN: 1000, OUT: 750 };
    dl.configs.FAST_POPUP_TWEEN_TIME = { IN: 400, OUT: 200 };
    dl.configs.POPUP_TWEEN_TIME = { IN: 500, OUT: 250 };

    dl.configs.TEXT_CHANGE_TWEEN_TIME = 500;

    dl.configs.SKIP_TIME = !true;
    if (dl.configs.SKIP_TIME) {
        dl.configs.HOME_LEVEL_TWEEN_TIME = { IN: 150, OUT: 75 };
        dl.configs.MATCHING_LEVEL_TWEEN_TIME = { IN: 100, OUT: 75 };
        dl.configs.INSTRUCTION_LEVEL_TWEEN_TIME = { IN: 100, OUT: 75 };
        dl.configs.GAME_LEVEL_TWEEN_TIME = { IN: 100, OUT: 75 };
        dl.configs.GAMEOVER_LEVEL_TWEEN_TIME = { IN: 100, OUT: 75 };
        dl.configs.FAST_POPUP_TWEEN_TIME = { IN: 40, OUT: 20 };
        dl.configs.POPUP_TWEEN_TIME = { IN: 50, OUT: 25 };

        dl.configs.TEXT_CHANGE_TWEEN_TIME = 50;
    };
});
