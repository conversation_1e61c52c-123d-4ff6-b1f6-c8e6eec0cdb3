ig.module(
    'game.dl-entities.instructions-map'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker'
).defines(function () {
    dl.EntityInstructionsMap = dl.Entity
        .extend(dl.MixinDocker)
        .extend({
            postInit: function () {
                this.parent();
                this.content = this.spawnEntity(dl.EntityInstructionsMap_Content, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    image: dl.preload.instructions.map
                });
            },

            tweenIn: function (time) {
                this.content.tweenIn(time);
            },

            tweenOut: function (time) {
                this.content.tweenOut(time);
            },

            update: function () {
                this.parent();
            }
        });

    dl.EntityInstructionsMap_Content = dl.Entity
        .extend(dl.MixinDocker)
        .extend({
            postInit: function () {
                this.parent();
                this.dragAngle = 0;
                this.currentDrag = 0;
                this.maxDrag = 99999;
                this.minDrag = 150;
                this.initMap();
                this.initCapitals();
                this.initGuideArrow();
                this.initHand();
            },

            initMap: function () {
                this.map = this.spawnEntity(dl.EntityImage, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    image: dl.preload.instructions.map
                });
            },

            initCapitals: function () {
                this.playerCapital = this.spawnEntity(dl.EntityInstructionsMapCapital, {
                    c_DockerComponent: {
                        dockerObject: this.map,
                        dockerPercent: { x: 0.25, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    }
                });

                this.playerCapital.setImage();

                this.enemyCapital = this.spawnEntity(dl.EntityInstructionsMapCapital, {
                    c_DockerComponent: {
                        dockerObject: this.map,
                        dockerPercent: { x: 0.75, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    }
                });
            },

            initGuideArrow: function () {
                this.arrow = this.spawnEntity(dl.EntityInstructionsMapGuideArrow, {
                    c_DockerComponent: {
                        dockerObject: this.playerCapital,
                        dockerPercent: { x: 0.5, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    }
                });
            },

            initHand: function () {
                this.hand = this.spawnEntity(dl.EntityImage, {
                    c_DockerComponent: {
                        dockerObject: this.playerCapital,
                        dockerPercent: { x: 0, y: 0.5 },
                        dockerOffset: { x: -10, y: 0 }
                    },
                    alpha: 0,
                    anchor: { x: 0, y: 0 },
                    image: dl.preload.instructions.hand
                });
                this.tweenMove();                
            },

            tweenMove: function () {
                this.tweenInInstance = this.hand.createTween()
                    .to({
                        alpha: 1
                    }, 800, {
                        easing: dl.TweenEasing.Linear.EaseNone
                    })
                    .to({
                        pos: {
                            x: this.enemyCapital.pos.x + this.hand.size.x * 0.25,
                            y: this.enemyCapital.pos.y + this.hand.size.y * 0.5
                        }
                    }, 800, {
                        easing: dl.TweenEasing.Linear.EaseNone,
                        onPropertiesChanged: function () {
                            this.calculateDrag();
                        }.bind(this)
                    })
                    .to({
                        alpha: 0
                    }, 800, {
                        easing: dl.TweenEasing.Linear.EaseNone,
                        onPropertiesChanged: function () {
                            this.arrow.alpha = this.hand.alpha;
                        }.bind(this)
                    })
                    .start({
                        repeat: true,
                        onCompleteTween: function () {
                            this.updatePos();
                            this.dragAngle = 0;
                            this.currentDrag = 0;
                            this.arrow.dragAngle = this.dragAngle;
                            this.arrow.currentDrag = this.currentDrag;
                            this.arrow.alpha = 1;
                        }.bind(this)
                    });
            },

            tweenIn: function (time) {
                // dl.TweenTemplate.fadeIn(this.map, time, this.tweenMove.bind(this));
                // dl.TweenTemplate.fadeIn(this.playerCapital, time);
                // dl.TweenTemplate.fadeIn(this.enemyCapital, time);
            },

            tweenOut: function (time) {
                // dl.TweenTemplate.fadeOut(this.map, time);
                // dl.TweenTemplate.fadeOut(this.playerCapital, time);
                // dl.TweenTemplate.fadeOut(this.enemyCapital, time);
                // dl.TweenTemplate.fadeOut(this.hand, time);
                // dl.TweenTemplate.fadeOut(this.arrow, time);

                this.map.kill();
                this.playerCapital.kill();
                this.enemyCapital.kill();
                this.hand.kill();
                this.arrow.kill();

            },

            calculateDrag: function () {
                var firstPosition = this.playerCapital.pos;
                var currentPosition = this.hand.pos;
                if (Utilities.distanceBetweenTwoPoints(firstPosition, currentPosition) < this.maxDrag) {
                    if (Utilities.distanceBetweenTwoPoints(firstPosition, currentPosition) > this.minDrag) {
                        this.currentDrag = Utilities.distanceBetweenTwoPoints(firstPosition, currentPosition);
                    } else {
                        this.currentDrag = 0;
                    }
                } else {
                    this.currentDrag = this.maxDrag;
                }
                this.dragAngle = Utilities.calcAngle(
                    firstPosition.x, firstPosition.y,
                    currentPosition.x - this.hand.size.x * 0.5, currentPosition.y - this.hand.size.y * 0.5,
                    true
                );

                this.arrow.dragAngle = this.dragAngle;
                this.arrow.currentDrag = this.currentDrag;
            },

            update: function () {
                this.parent();
            }
        });

    dl.EntityInstructionsMapCapital = dl.Entity
        .extend(dl.MixinDocker)
        .extend({
            staticInstantiate: function () {
                this.parent();
                this.size = { x: 80, y: 80 };
                this.color = '#3d3d3d';
                this.diameter = 0.5 * (this.size.x + this.size.y);
                this.radius = 0.5 * this.diameter;
                this.center = { x: 0, y: 0 };
                // this.isActive = true;
            },

            postInit: function () {
                this.parent();
            },

            setImage: function () {
                this.capitalImage = this.spawnEntity(dl.EntityImage, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    image: dl.preload['game-player-info'].icons[0]
                });
            },

            update: function () {
                this.parent();
                this.center.x = this.pos.x + this.size.x * 0.5;
                this.center.y = this.pos.y + this.size.y * 0.5;
            },

            draw: function (ctx) {
                this.parent(ctx);
                ctx.save();
                // if (this.isActive) {
                //     ctx.shadowOffsetX = 0;
                //     ctx.shadowOffsetY = 0;
                //     ctx.shadowColor = 'white';
                //     ctx.shadowBlur = 30;
                // }
                if (!this.capitalImage) {
                    ctx.translate(this.pos.x, this.pos.y);
                    ctx.beginPath();
                    ctx.fillStyle = this.color;
                    ctx.arc(
                        0,
                        0,
                        this.radius,
                        0,
                        360
                    );
                    ctx.fill();
                }
                ctx.restore();
            }
        });

    dl.EntityInstructionsMapGuideArrow = dl.Entity
        .extend(dl.MixinDocker)
        .extend({
            staticInstantiate: function () {
                this.parent();
                this.arrowImage = dl.preload['guide-arrows'].player1;
                this.arrowStick = dl.preload['guide-arrows-stick'].player1;
                this.maxDrag = 99999;
                this.minDrag = 150;
                return this;
            },

            postInit: function () {
                this.parent();
            },

            update: function () {
                this.parent();
            },

            draw: function (ctx) {
                this.parent(ctx);
                if (this.currentDrag > this.minDrag) {
                    ctx.save();
                    ctx.translate(this.pos.x, this.pos.y);
                    ctx.shadowOffsetX = 0;
                    ctx.shadowOffsetY = 0;
                    ctx.shadowColor = 'white';
                    ctx.shadowBlur = 30;
                    ctx.rotate(this.dragAngle);
                    this.arrowStick.drawImage(
                        this.radius,
                        -this.arrowStick.height * 0.5,
                        this.currentDrag-40,
                        this.arrowStick.height
                    );

                    ctx.fillStyle="#a92c35";
                    ctx.beginPath();
                    ctx.arc(0,0, 20, 0, 2 * Math.PI);
                    ctx.fill();
                    ctx.stroke();

                    this.arrowImage.drawImage(
                        this.currentDrag-50,
                        -this.arrowStick.height/2-10,
                        50,
                        this.arrowImage.height
                    );

                    ctx.restore();
                }
            }
        });
    // Enable cache
    // dl.enableCacheCanvas(dl.EntityGameMapContinent);
});
