ig.module(
    'dl.templates.entities.buttons.entity-button-fullscreen'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.div-layer',
    'dl.templates.mixins.button',
    'dl.templates.mixins.animation-sheet',
    'dl.templates.mixins.button-effect'
).defines(function () {
    'use strict';

    dl.EntityButtonFullScreen = dl.Entity
        .extend(dl.MixinDocker)
        .extend(dl.MixinDivLayer)
        .extend(dl.MixinButton)
        .extend(dl.MixinAnimationSheet)
        .extend(dl.MixinButtonScaleEffect)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this._useAnimationSheetAsSize = true;
                this.image = dl.preload['button-fullscreen'];

                return this;
            },

            postInit: function () {
                this.parent();

                this.updateFullscreen(ig.Fullscreen.isFullscreen());

                window.fullscreenButton = this;
                window.fullscreenButton.onEvent('updateFullscreen', this.updateFullscreen.bind(this));
            },

            _initDivLayerComponent: function () {
                this.c_DivLayerComponent = this.addComponent(dl.DivLayerComponent, {
                    callback: 'ig.Fullscreen.toggleFullscreen();',
                    divLayerName: 'fullscreen'
                });
            },

            _initAnimationSheetComponent: function () {
                this.c_AnimationSheetComponent.updateProperties({
                    _animationSheetImage: this.image,
                    _animationSheetRow: 1,
                    _animationSheetCol: 2,
                    _setupDefaultAnimation: function () {
                        this.addAnimation('off', 0, [0], true);
                        this.addAnimation('on', 0, [1], true);
                        this.setAnimation('off');
                    }
                });
            },

            updateImage: function (image) {
                if (!image) return;

                this.c_AnimationSheetComponent.updateProperties({
                    _animationSheetImage: this.image,
                    _animationSheetRow: 1,
                    _animationSheetCol: 2,
                    _setupDefaultAnimation: function () {
                        this.addAnimation('off', 0, [0], true);
                        this.addAnimation('on', 0, [1], true);
                        this.setAnimation('off');
                    }
                });
            },

            _updateAnimation: function (newState) {
                if (newState) {
                    this.c_AnimationSheetComponent.setAnimation('on');
                } else {
                    this.c_AnimationSheetComponent.setAnimation('off');
                }
            },

            updateFullscreen: function (isFullscreen) {
                this._updateAnimation(isFullscreen);
            }
        });

    // Enable cache
    dl.enableCacheCanvas(dl.EntityButtonFullScreen);
});
