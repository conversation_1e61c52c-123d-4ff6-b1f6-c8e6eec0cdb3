ig.module('plugins.achievement.achievement-game-object')
    .requires(
        'impact.entity'
    )
    .defines(function () {

        ig.AchievementGameObject = ig.Entity.extend({
            idleSheetInfo: null,
            singleFrameImage: null,
            visible: true,
            alpha: 1,
            zIndex: 1000,

            drawAsRect: false,
            drawAsOutline: false,
            outlineWidth: 1,
            rectColor: "#FFFFFF",
            outlineColor: "#000000",

            entryDelay: 0,
            entryDuration: 0.25,
            entryType: null,

            exitDelay: 0,
            exitDuration: 0.4,
            exitType: null,

            killOnExit: true,

            scaleX: 1,
            scaleY: 1,
            angle: 0,
            anchorX: 0,
            anchorY: 0,
            forceDraw: false,
            width: 32,
            height: 32,

            onInputDown: null,
            onInputUp: null,
            onClicked: null,

            hasTouchInside: false,
            inputEnabled: false,
            isClicking: false,

            boundLeft: 0,
            boundRight: 0,
            boundTop: 0,
            boundBottom: 0,
            isFinishEntering: false,
            fixedOnCamera: false,

            anchorType: "default",

            timedLife: -999,

            size: {
                x: 32,
                y: 32,
            },

            init: function (x, y, settings) {
                if (!ig.AchievementTouch.hasInitialized) ig.AchievementTouch.init();
                this.onInputDown = new ig.AchievementSignal();
                this.onInputUp = new ig.AchievementSignal();
                this.onClicked = new ig.AchievementSignal();
                if (settings['singleFrameImage']) {
                    this.singleFrameImage = settings['singleFrameImage'];
                } else if (settings['image']) {
                    this.singleFrameImage = settings['image'];
                    this.image = this.singleFrameImage;
                }

                if (this.singleFrameImage != null) {
                    this.idleSheetInfo = {
                        sheetImage: this.singleFrameImage,
                        sheetX: 1,
                        sheetY: 1,
                    }

                    this.setSpriteSheet("idle");
                    this.setSize("idle");
                    this.addAnimation("idle", "idleSheet", 1, [0], false, true);
                }
                else if (this.idleSheetInfo != null) {
                    this.setSpriteSheet("idle");
                    this.setSize("idle");
                }

                this.parent(x, y, settings);
                if (settings.visible === false) this.visible = false;
                if (!settings.anchorType) this.anchorType = "default";
                else this.anchorType = settings.anchorType;
                this.enter();
            },

            swapImage: function (image) {
                this.idleSheetInfo.sheetImage = image;
                this.idleSheet.image = image;
            },

            onFinishEntering: function () {

            },

            enter: function () {
                this.visible = true;

                if (ig.responsive) {
                    var point = ig.responsive.toAnchor(this.anchoredPositionX, this.anchoredPositionY, this.anchorType);
                    this.pos.x = point.x;
                    this.pos.y = point.y;
                }

                if (this.entryType != null) {
                    this[this.entryType]();
                    this.delayedCall(this.entryDelay + this.entryDuration, function () {
                        this.onFinishEntering();
                        this.isFinishEntering = true;
                    }.bind(this));
                } else {
                    this.isFinishEntering = true;
                    this.onFinishEntering();
                }
            },

            exit: function () {
                if (this.exitType != null) {
                    this[this.exitType]();
                } else {
                    this.alpha0();
                }
                this.delayedCall(this.exitDuration + this.exitDelay, function () { this.checkKill() }.bind(this))
            },

            setSpriteSheet: function (animationName) {
                this[animationName + "Sheet"] = new ig.AnimationSheet(
                    this[animationName + "SheetInfo"].sheetImage.path,
                    this[animationName + "SheetInfo"].sheetImage.width / this[animationName + "SheetInfo"].sheetX,
                    this[animationName + "SheetInfo"].sheetImage.height / this[animationName + "SheetInfo"].sheetY
                );
            },

            setSize: function (animationName) {
                this.width = this[animationName + "SheetInfo"].sheetImage.width / this[animationName + "SheetInfo"].sheetX;
                this.height = this[animationName + "SheetInfo"].sheetImage.height / this[animationName + "SheetInfo"].sheetY;
            },

            addAnimation: function (animationName, sheetName, delay, frames, loop, setAsCurrentAnim) {
                this[animationName] = new ig.Animation(this[sheetName], delay, frames, !loop);
                if (setAsCurrentAnim == true) {
                    this.currentAnim = this[animationName];
                }
            },

            makeFramesArray: function (start, finish, yoyo) {
                var arr = [];

                for (var i = start; i <= finish; i++) {
                    arr.push(i);
                }

                if (yoyo) {
                    for (var i = finish; i >= start; i--) {
                        arr.push(i);
                    }
                }
                return arr;
            },

            draw: function () {
                if (this.alpha < 0.01) {
                    return;
                }
                if (this.visible && (this.forceDraw || this.currentAnim || this.drawAsRect || this.drawAsOutline)) {
                    var ctx = ig.system.context;
                    ctx.textBaseline = "Alphabetic";
                    ctx.save();

                    var translateX = 0;
                    var translateY = 0;

                    if (this.fixedOnCamera) {
                        translateX = ig.system.getDrawPos(this.pos.x.round() - this.offset.x);
                        translateY = ig.system.getDrawPos(this.pos.y.round() - this.offset.y);
                    } else {
                        translateX = ig.system.getDrawPos(this.pos.x.round() - this.offset.x - ig.game.screen.x);
                        translateY = ig.system.getDrawPos(this.pos.y.round() - this.offset.y - ig.game.screen.y);
                    }

                    ctx.translate(translateX, translateY);
                    if (this.scaleX != 1 || this.scaleY != 1) ctx.scale(this.scaleX, this.scaleY);


                    var drawX = -this.anchorX * this.width;
                    var drawY = -this.anchorY * this.height

                    if (this.shakeValue > 0) {
                        this.shakeValue -= ig.system.tick * this.shakeReductionMultiplier;
                        drawX += ig.AchievementRandom.float(-this.shakeValue, this.shakeValue);
                        drawY += ig.AchievementRandom.float(-this.shakeValue, this.shakeValue);
                    }

                    this.drawObject(drawX, drawY);
                    ctx.restore();
                }
            },

            drawObject: function (x, y) {
                var ctx = ig.system.context;
                ctx.textBaseline = "Alphabetic";
                var color = ig.hexToRgb(this.rectColor);
                if (this.drawAsRect) {
                    if (this.alpha < 1) {
                        ctx.fillStyle = "rgba(" + color.r + "," + color.g + "," + color.b + "," + this.alpha + ")";
                    } else {
                        ctx.fillStyle = color.hex;
                    }
                    ctx.fillRect(x, y, this.width, this.height);
                }
                var color = ig.hexToRgb(this.outlineColor);
                if (this.drawAsOutline) {
                    ctx.lineWidth = this.outlineWidth;
                    if (this.alpha < 1) {
                        ctx.strokeStyle = "rgba(" + color.r + "," + color.g + "," + color.b + "," + this.alpha + ")";
                    } else {
                        ctx.strokeStyle = color.hex;
                    }
                    ctx.strokeRect(x, y, this.width, this.height);
                }
                else if (this.currentAnim != null) {
                    this.currentAnim.alpha = this.alpha;
                    this.currentAnim.draw(x, y);
                }
            },

            update: function () {
                if (ig.game.entities) this.parent();
                if (this.inputEnabled && this.isFinishEntering && this.visible && this.alpha > 0.01) {
                    var hasTouch = false;
                    this.hasTouchInside = false;
                    for (var i = 0; i < ig.multitouchInput.touches.length; i++) {
                        hasTouch = true;
                        var t = ig.multitouchInput.touches[i];
                        this.processInput(t.x, t.y);

                    }

                    for (var i = 0; i < ig.AchievementTouch.touches.length; i++) {
                        hasTouch = true;
                        var t = ig.AchievementTouch.touches[i];
                        this.processInput(t.x, t.y);

                    }

                    if (!hasTouch && ig.input.state("click")) {
                        var px = ig.AchievementTouch.mouseX;
                        var py = ig.AchievementTouch.mouseY;
                        if (px != 0 && py != 0) {
                            this.processInput(px, py);
                            hasTouch = true;
                        }
                    }

                    if (hasTouch && !this.isClicking) {

                    }

                    if (!hasTouch) {
                        if (this.isClicking) {
                            //User is releasing mouse/tap after click on this button
                            this.onInputUp.dispatch(this);
                            this.onClicked.dispatch(this);
                        }
                        this.isClicking = false;
                    } else if (!this.hasTouchInside) {
                        this.isClicking = false;
                    }
                }

                if (this.timedLife != -999 && this.isFinishEntering) {
                    this.timedLife -= ig.system.tick;
                    if (this.timedLife <= 0) {
                        this.timedLife = -999;
                        this.exit();
                    }
                }
            },

            processInput: function (x, y) {
                if (this.isInsideBounds(x, y)) {
                    this.hasTouchInside = true;
                    if (!this.isClicking) {
                        this.isClicking = true;
                        //User begin clicking/tapping on this button
                        this.onInputDown.dispatch(this);
                    }
                }
            },

            isInsideBounds: function (x, y) {
                var w = this.scaleX * this.width;
                var h = this.scaleY * this.height;
                this.boundLeft = this.pos.x - w * this.anchorX;
                this.boundTop = this.pos.y - h * this.anchorY;
                this.boundRight = this.boundLeft + w;
                this.boundBottom = this.boundTop + h;

                if (this.boundLeft > this.boundRight) {
                    var temp = this.boundLeft;
                    this.boundLeft = this.boundRight;
                    this.boundRight = temp;
                }

                if (this.boundTop > this.boundBottom) {
                    var temp = this.boundTop;
                    this.boundTop = this.boundBottom;
                    this.boundBottom = temp;
                }

                if (x < this.boundLeft) return false;
                if (y < this.boundTop) return false;
                if (x > this.boundRight) return false;
                if (y > this.boundBottom) return false;


                return true;
            },

            getBounds: function () {
                if (!this._bounds) this._bounds = {};
                var bounds = this._bounds;
                var w = this.scaleX * this.width;
                var h = this.scaleY * this.height;
                bounds.left = this.pos.x - w * this.anchorX;
                bounds.top = this.pos.y - h * this.anchorY;
                bounds.right = bounds.left + w;
                bounds.bottom = bounds.top + h;
                bounds.width = Math.abs(w);
                bounds.height = Math.abs(h);

                if (bounds.left > bounds.right) {
                    var temp = bounds.left;
                    bounds.left = bounds.right;
                    bounds.right = temp;
                }

                if (bounds.top > bounds.bottom) {
                    var temp = bounds.top;
                    bounds.top = bounds.bottom;
                    bounds.bottom = temp;
                }
                bounds.x = bounds.left;
                bounds.y = bounds.top;

                return bounds;
            },

            isCollidingWith: function (obj) {
                var bounds = this.getBounds();
                var otherBounds = obj.getBounds();
                if (bounds.right < otherBounds.left) return false;
                if (bounds.left > otherBounds.right) return false;
                if (bounds.bottom < otherBounds.top) return false;
                if (bounds.top > otherBounds.bottom) return false;
                return true;
            },

            shakeValue: 0,
            shakeReductionMultiplier: 0,

            shake: function (value, reductionMultiplier) {
                if (!value) value = 5;
                if (!reductionMultiplier) reductionMultiplier = 10;
                this.shakeValue = value;
                this.shakeReductionMultiplier = reductionMultiplier;
            },

            //=============================================================================
            //Transition template
            //=============================================================================

            fadeIn: function () {
                // var alphaTarget = this.alpha;
                // this.alpha = 0;
                // this.tween({ alpha: alphaTarget }, this.entryDuration, { delay: this.entryDelay, easing: ig.Tween.Easing.Quadratic.EaseOut }).start();

                this.offsetY = -1500;
                this.tween({ offsetY: 0 }, 0.6, { easing: ig.Tween.Easing.Back.EaseOut }).start();

            },

            fadeOut: function () {
                // this.alpha=0;
                this.tween({ alpha: 0 }, this.exitDuration, { delay: this.exitDelay, easing: ig.Tween.Easing.Quadratic.EaseOut }).start();
                // // this.offsetY = -1500;
                // this.tween({ offsetY: -1500 }, 0.4, { delay: this.exitDelay,easing: ig.Tween.Easing.Back.EaseIn }).start();

            },

            alpha0: function () {
                this.alpha = 0;
            },

            alpha1: function () {
                this.alpha = 1;
            },

            checkKill: function () {
                if (this.killOnExit) this.kill();
            }

        });

        //###
        ig.AchievementTextRenderer = ig.Class.extend({
            text: "",
            font: "48px Arial",
            color: "#000000",
            alpha: 1,
            align: "start",
            offsetX: 0,
            offsetY: 0,
            lineSpacing: 1,
            valign: "default",

            enableShadow: false,
            shadowColor: "#000000",
            shadowOffsetX: 0,
            shadowOffsetY: 4,
            shadowAlpha: 1,

            outlineWeight: 0,
            outlineColor: "#000000",
            outlineCap: "square",
            outlineJoin: "bevel",

            _previousText: "",
            _multiline: false,
            _lines: [],
            _lineHeightInPixel: 0,

            init: function (param) { },

            draw: function (x, y) {
                if (!(x !== undefined)) x = 0;
                if (!(y !== undefined)) y = 0;

                if (this.enableShadow) {
                    this.drawText(x + this.shadowOffsetX, y + this.shadowOffsetY, this.shadowAlpha * this.alpha, this.shadowColor, this.shadowColor)
                    this.drawText(x, y, this.alpha, this.color, this.outlineColor)
                } else {
                    this.drawText(x, y, this.alpha, this.color, this.outlineColor)
                }
            },

            drawText: function (x, y, alpha, color, outlineColor) {
                if (this.text.length > 0) {
                    if (this._previousTextString != this.text) {
                        this._previousTextString = this.text;
                        this._lines = this.text.split(/\r?\n/);
                        if (this._lines.length > 1) {
                            this._multiline = true;
                        } else {
                            this._multiline = false;
                        }
                        this._lineHeightInPixel = parseInt(this.font.split("px")[0].split(" ").pop()) * this.lineSpacing;
                        if (isNaN(this._lineHeightInPixel)) console.error("'" + this.font + "' is a wrong font string format, error when rendering " + this.text)
                    }

                    var ctx = ig.system.context;
                    ctx.textBaseline = "Alphabetic";
                    ctx.save();
                    ctx.font = this.font;
                    ctx.textAlign = this.align;
                    ctx.lineWidth = this.outlineWeight * 2;
                    ctx.lineCap = this.outlineCap;
                    ctx.lineJoin = this.outlineJoin;
                    ctx.miterLimit = this.outlineWeight * 2;

                    if (alpha < 1) {
                        var rgb = ig.hexToRgb(color)
                        ctx.fillStyle = "rgba(" + rgb.r + "," + rgb.g + "," + rgb.b + "," + alpha + ")";

                        rgb = ig.hexToRgb(outlineColor)
                        ctx.strokeStyle = "rgba(" + rgb.r + "," + rgb.g + "," + rgb.b + "," + alpha + ")";
                    } else {
                        ctx.fillStyle = color;
                        ctx.strokeStyle = outlineColor;
                    }

                    var xpos = x + this.offsetX;
                    var ypos = y + this.offsetY;
                    ctx.textBaseline = "Alphabetic";

                    if (this._multiline) {
                        //TODO: apply multiline with various alignment
                        var startLine = ypos;
                        if (this.valign == "center") {
                            startLine = ypos - (this._lines.length - 1) * this._lineHeightInPixel / 2;
                        }

                        for (var index = 0; index < this._lines.length; index++) {
                            var line = this._lines[index];
                            if (this.outlineWeight > 0) ctx.strokeText(line, xpos, startLine + index * this._lineHeightInPixel);
                            ctx.fillText(line, xpos, startLine + index * this._lineHeightInPixel);
                        }
                    } else {
                        if (this.valign == "center") ypos += this._lineHeightInPixel / 2
                        if (this.outlineWeight > 0) ctx.strokeText(this.text, xpos, ypos);
                        ctx.fillText(this.text, xpos, ypos);
                    }
                    ctx.restore();
                }
            },

            measureTextWidth: function () {
                var ctx = ig.system.context;
                ctx.save();
                ctx.font = this.font;
                ctx.textAlign = "left";
                ctx.textBaseline = "Alphabetic";
                var measure = ctx.measureText(this.text);
                ctx.restore();
                return measure.width;
            }
        });

        //###
        ig.AchievementTextField = ig.AchievementGameObject.extend({
            text: "",
            font: "48px Arial",
            color: "#FFFFFF",
            align: "start",
            offsetX: 0,
            offsetY: 0,
            lineSpacing: 1,
            valign: "top",

            textRenderer: null,
            forceDraw: true,

            enableShadow: false,
            shadowColor: "#000000",
            shadowOffsetX: 0,
            shadowOffsetY: 4,
            shadowAlpha: 0.4,

            outlineWeight: 0,
            outlineColor: "#000000",
            outlineCap: "square",
            outlineJoin: "bevel",

            zIndex: 7000,
            init: function (x, y, settings) {
                this.parent(x, y, settings);
                this.textRenderer = new ig.AchievementTextRenderer();
            },

            drawObject: function (x, y) {
                this.parent(x, y)
                if (this.visible) {
                    this.applyProperties();
                    this.textRenderer.draw(x, y);
                }
            },


            applyProperties: function () {
                this.textRenderer.text = this.text;
                this.textRenderer.font = this.font;
                this.textRenderer.color = this.color;
                this.textRenderer.alpha = this.alpha;
                this.textRenderer.align = this.align;
                this.textRenderer.offsetX = this.offsetX;
                this.textRenderer.offsetY = this.offsetY;
                this.textRenderer.lineSpacing = this.lineSpacing;
                this.textRenderer.valign = this.valign;

                this.textRenderer.enableShadow = this.enableShadow;
                this.textRenderer.shadowAlpha = this.shadowAlpha;
                this.textRenderer.shadowColor = this.shadowColor;
                this.textRenderer.shadowOffsetX = this.shadowOffsetX;
                this.textRenderer.shadowOffsetY = this.shadowOffsetY;

                this.textRenderer.outlineWeight = this.outlineWeight;
                this.textRenderer.outlineColor = this.outlineColor;
                this.textRenderer.outlineCap = this.outlineCap;
                this.textRenderer.outlineJoin = this.outlineJoin;
            },

            getTextWidth: function () {
                this.applyProperties();
                return this.textRenderer.measureTextWidth();
            }
        });

        //###
        ig.AchievementSimpleButton = ig.AchievementGameObject.extend({
            originalX: 0,
            originalY: 0,
            transitionInDelay: 0,
            zIndex: 7000,

            text: "",
            font: "48px Arial",
            textColor: "#FFFFFF",
            align: "center",
            offsetX: 0,
            offsetY: 0,

            anchorX: 0.5,
            anchorY: 0.5,

            lineSpacing: 1,
            valign: "center",

            enableShadow: false,
            shadowColor: "#000000",
            shadowOffsetX: 0,
            shadowOffsetY: 4,
            shadowAlpha: 0.4,

            outlineWeight: 0,
            outlineColor: "#000000",
            outlineCap: "square",
            outlineJoin: "bevel",

            normalScale: 1,

            usePressedTween: true,

            textRenderer: null,
            forceDraw: true,

            prevText: "",

            init: function (x, y, settings) {
                this.parent(x, y, settings);
                this.textRenderer = new ig.AchievementTextRenderer();
            },

            update: function () {
                this.parent();
                if (this.usePressedTween) {
                    if (this.visible) {
                        var targetScale = 0.9 * this.normalScale;
                        var targetScaleX = 0.9 * this.normalScale;
                        var targetScaleY = 0.9 * this.normalScale;
                        if (this.hasTouchInside) targetScale = 0.9 * this.normalScale;
                        else targetScale = this.normalScale;

                        if (this.scaleX < 0) targetScaleX = -1 * targetScale;
                        else targetScaleX = targetScale;

                        if (this.scaleY < 0) targetScaleY = -1 * targetScale;
                        else targetScaleY = targetScale;

                        this.scaleX += (targetScaleX - this.scaleX) / 3;
                        this.scaleY += (targetScaleY - this.scaleY) / 3;
                    }
                }
            },

            drawObject: function (x, y) {
                this.parent(x, y);
                if (this.visible) {
                    this.textRenderer.text = this.text;
                    this.textRenderer.font = this.font;
                    this.textRenderer.color = this.textColor;
                    this.textRenderer.alpha = this.alpha;
                    this.textRenderer.align = this.align;
                    this.textRenderer.offsetX = this.offsetX;
                    this.textRenderer.offsetY = this.offsetY;
                    this.textRenderer.lineSpacing = this.lineSpacing;
                    this.textRenderer.valign = this.valign;

                    this.textRenderer.enableShadow = this.enableShadow;
                    this.textRenderer.shadowAlpha = this.shadowAlpha;
                    this.textRenderer.shadowColor = this.shadowColor;
                    this.textRenderer.shadowOffsetX = this.shadowOffsetX;
                    this.textRenderer.shadowOffsetY = this.shadowOffsetY;

                    this.textRenderer.outlineWeight = this.outlineWeight;
                    this.textRenderer.outlineColor = this.outlineColor;
                    this.textRenderer.outlineCap = this.outlineCap;
                    this.textRenderer.outlineJoin = this.outlineJoin;

                    if (this.prevText != this.text) {
                        this.prevText = this.text;
                        var ctx = ig.system.context;
                        ctx.save();
                        ctx.font = this.font;
                        var metrics = ctx.measureText(this.text);
                        var fontsize = parseInt(this.font.split("px")[0].split(" ").pop());
                        this.centerYOffset = (fontsize / 2) - metrics.actualBoundingBoxAscent;
                        ctx.restore();
                    }

                    this.textRenderer.draw(x + this.width / 2, y + this.height / 2 + this.centerYOffset);
                }
            },

            onFinishEntering: function () {
                this.inputEnabled = true;
            }
        });

        ig.AchievementParticleIn = ig.AchievementGameObject.extend({
            zIndex: 9999,

            forceDraw: true,

            targetX: 0,
            targetY: 0,
            hasSpawned: false,
            fadeAway: false,
            fillColor: "#ffffff",

            init: function (x, y, settings) {
                var r = 180;
                this.parent(x + ig.AchievementRandom.float(-r, r) * 2, y + ig.AchievementRandom.float(-r, r) * 2, settings);
                this.targetX = x;
                this.targetY = y;
                this.alpha = 0;
                this.width = this.height = ig.AchievementRandom.int(20, 30)
            },

            update: function () {
                this.parent();
                if (this.delay > 0) {
                    this.delay -= ig.system.tick;
                    return;
                }
                this.hasSpawned = true;
                var lerpFactor = 10;
                var limitDisappear = 100;
                if (ig.responsive) {
                    this.anchoredPositionX += (this.targetX - this.anchoredPositionX) / lerpFactor;
                    this.anchoredPositionY += (this.targetY - this.anchoredPositionY) / lerpFactor;
                    if (Math.abs(this.anchoredPositionX - this.targetX) < limitDisappear && Math.abs(this.anchoredPositionY - this.targetY) < limitDisappear) {
                        this.fadeAway = true;
                    }
                } else {
                    this.pos.x += (this.targetX - this.pos.x) / lerpFactor;
                    this.pos.y += (this.targetY - this.pos.y) / lerpFactor;
                    if (Math.abs(this.pos.x - this.targetX) < limitDisappear && Math.abs(this.pos.y - this.targetY) < limitDisappear) {
                        this.fadeAway = true;
                    }
                }

                if (this.fadeAway) {
                    this.alpha -= ig.system.tick * 5;
                    if (this.alpha <= 0) this.kill();
                } else {
                    if (this.alpha < 0.5) {
                        this.alpha += ig.system.tick * 10;
                    } else {
                        this.alpha = 0.5;
                    }

                }

            },

            drawObject: function (x, y) {
                this.parent(x, y);

                if (!this.hasSpawned) return;
                var ctx = ig.system.context;
                ctx.textBaseline = "Alphabetic";
                var color = ig.hexToRgb(this.fillColor);
                if (this.alpha < 1) {
                    ctx.fillStyle = "rgba(" + color.r + "," + color.g + "," + color.b + "," + this.alpha + ")";
                } else {
                    ctx.fillStyle = color.hex;
                }
                ctx.beginPath()
                ctx.arc(x, y, this.width, 0, 2 * Math.PI);
                ctx.fill();
                ctx.closePath()

            },
        });

        ig.AchievementParticleOut = ig.AchievementGameObject.extend({
            zIndex: 999,

            forceDraw: true,

            targetX: 0,
            targetY: 0,
            hasSpawned: false,
            fadeAway: false,
            fillColor: "#ffffff",

            init: function (x, y, settings) {
                var r = 180;
                var angle = Math.random() * Math.PI * 2;
                var tX = Math.cos(angle) * ig.AchievementRandom.int(r * 0.5, r * 2) * 1.5;
                var tY = Math.sin(angle) * ig.AchievementRandom.int(r * 0.5, r * 2) * 1.5;

                this.parent(x + tX / 2, y + tY / 2, settings);
                this.targetX = x + tX;
                this.targetY = y + tY;
                this.alpha = 0.5;
                this.width = this.height = ig.AchievementRandom.int(20, 30)
            },

            update: function () {
                this.parent();
                if (this.delay > 0) {
                    this.delay -= ig.system.tick;
                    return;
                }
                this.hasSpawned = true;
                var lerpFactor = 10;
                var limitDisappear = 10;
                if (ig.responsive) {
                    this.anchoredPositionX += (this.targetX - this.anchoredPositionX) / lerpFactor;
                    this.anchoredPositionY += (this.targetY - this.anchoredPositionY) / lerpFactor;
                    if (Math.abs(this.anchoredPositionX - this.targetX) > limitDisappear || Math.abs(this.anchoredPositionY - this.targetY) > limitDisappear) {
                        this.fadeAway = true;
                    }
                } else {
                    this.pos.x += (this.targetX - this.pos.x) / lerpFactor;
                    this.pos.y += (this.targetY - this.pos.y) / lerpFactor;
                    if (Math.abs(this.pos.x - this.targetX) > limitDisappear || Math.abs(this.pos.y - this.targetY) > limitDisappear) {
                        this.fadeAway = true;
                    }
                }

                if (this.fadeAway) {
                    this.alpha -= ig.system.tick;
                    if (this.alpha <= 0) this.kill();
                }

            },

            drawObject: function (x, y) {
                this.parent(x, y);

                if (!this.hasSpawned) return;
                var ctx = ig.system.context;
                var color = ig.hexToRgb(this.fillColor);
                if (this.alpha < 1) {
                    ctx.fillStyle = "rgba(" + color.r + "," + color.g + "," + color.b + "," + this.alpha + ")";
                } else {
                    ctx.fillStyle = color.hex;
                }
                ctx.beginPath()
                ctx.arc(x, y, this.width, 0, 2 * Math.PI);
                ctx.fill();
                ctx.closePath()

            },
        });

        ig.hexToRgb = function (hex) {
            if (!ig.hexToRgbTable) ig.hexToRgbTable = {};
            if (ig.hexToRgbTable[hex]) return ig.hexToRgbTable[hex];

            var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            if (result) {
                ig.hexToRgbTable[hex] = {
                    r: parseInt(result[1], 16),
                    g: parseInt(result[2], 16),
                    b: parseInt(result[3], 16),
                    hex: hex
                }

                return ig.hexToRgbTable[hex];
            }
            console.log("cannot convert to rgb from hex :", hex)
            return null;
        };

        ig.AchievementSignal = ig.Class.extend({

            signalListeners: null,
            id: 0,

            init: function (param) {
                this.id = ++ig.AchievementSignal._lastId;
                this.signalListeners = [];
            },

            addOnce: function (signalListener, signalContext) {
                this.signalListeners.push({ signalListener: signalListener, signalContext: signalContext, isOnce: true });
            },

            add: function (signalListener, signalContext) {
                this.signalListeners.push({ signalListener: signalListener, signalContext: signalContext, isOnce: false });
            },

            dispatch: function () {
                var i = 0;
                while (i < this.signalListeners.length) {
                    var obj = this.signalListeners[i];
                    obj.signalListener.apply(obj.signalContext, arguments);
                    if (obj.isOnce) {
                        this.signalListeners.splice(i, 1);
                    } else {
                        i++;
                    }
                }
            },

            clear: function () {
                this.signalListeners = [];
            }

        });

        ig.AchievementSignal._lastId = 0;

        ig.AchievementTouch = {
            hasInitialized: false,
            touches: [],
            mouseX: 0,
            mouseY: 0,
            init: function () {
                if (ig.ua.touchDevice) {
                    // Standard
                    if (window.navigator.msPointerEnabled) {
                        // MS pointer events
                        ig.system.canvas.addEventListener('MSPointerDown', this.touchDown.bind(this), false);
                        ig.system.canvas.addEventListener('MSPointerUp', this.touchUp.bind(this), false);
                        ig.system.canvas.addEventListener('MSPointerMove', this.touchMove.bind(this), false);

                        ig.system.canvas.style.msContentZooming = "none";
                        ig.system.canvas.style.msTouchAction = 'none';
                    }

                    // Standard
                    ig.system.canvas.addEventListener('touchstart', this.touchDown.bind(this), false);
                    ig.system.canvas.addEventListener('touchend', this.touchUp.bind(this), false);
                    ig.system.canvas.addEventListener('touchmove', this.touchMove.bind(this), false);
                }
                ig.system.canvas.addEventListener('mousemove', this.mouseMove.bind(this), false);
                this.hasInitialized = true;
            },

            mouseMove: function (event) {
                var internalWidth = parseInt(ig.system.canvas.offsetWidth) || ig.system.realWidth;
                var internalHeight = parseInt(ig.system.canvas.offsetHeight) || ig.system.realHeight;

                this.scaleX = ig.system.scale * (internalWidth / ig.system.realWidth);
                this.scaleY = ig.system.scale * (internalHeight / ig.system.realHeight);

                var pos = { left: 0, top: 0 };
                if (ig.system.canvas.getBoundingClientRect) {
                    pos = ig.system.canvas.getBoundingClientRect();
                }
                this.mouseX = (event.clientX - pos.left) / this.scaleX;
                this.mouseY = (event.clientY - pos.top) / this.scaleY;
            },

            touchDown: function (event) {
                this.processTouch(event.touches);
            },

            touchUp: function (event) {
                this.processTouch(event.touches);
            },

            touchMove: function (event) {
                this.processTouch(event.touches);
            },

            processTouch: function (touches) {
                var internalWidth = parseInt(ig.system.canvas.offsetWidth) || ig.system.realWidth;
                var internalHeight = parseInt(ig.system.canvas.offsetHeight) || ig.system.realHeight;

                this.scaleX = ig.system.scale * (internalWidth / ig.system.realWidth);
                this.scaleY = ig.system.scale * (internalHeight / ig.system.realHeight);

                var pos = { left: 0, top: 0 };
                if (ig.system.canvas.getBoundingClientRect) {
                    pos = ig.system.canvas.getBoundingClientRect();
                }
                this.touches = [];
                for (var i = 0; i < touches.length; i++) {
                    var touch = touches[i];
                    var touchX = (touch.clientX - pos.left) / this.scaleX;
                    var touchY = (touch.clientY - pos.top) / this.scaleY;
                    this.touches.push({ x: touchX, y: touchY })
                }
            }
        }

        ig.Entity.prototype.delayedCall = function (duration, onComplete, autoStart) {
            if (autoStart === undefined) {
                autoStart = true;
            }
            var tween = new ig.Tween(this, {}, duration, { onComplete: onComplete });
            this.tweens.push(tween);
            if (autoStart) {
                tween.start();
            }
            return tween;
        };

        ig.AchievementRandom = {
            /**
             * random a boolean value
             */
            chance: function (chance) {
                return Math.random() < chance;
            },

            /**
             * random a boolean value
             */
            bool: function () {
                return Math.random() < 0.5;
            },

            /**
             * random an integer value 
             * @param from
             * @param to
             */
            int: function (from, to) {
                return from + Math.floor(((to - from + 1) * Math.random()));
            },

            /**
             * random a float value
             * @param from
             * @param to
             */
            float: function (from, to) {
                return from + ((to - from) * Math.random());
            },

            /**
             * random a string
             * @param length
             * @param charactersToUse optional parameter, string contained characters to use, default: "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
             */
            string: function (length, charactersToUse) {
                if (charactersToUse === undefined) charactersToUse = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
                var str = "";
                for (var i = 0; i < length; i++) {
                    str += charactersToUse.charAt(Random.int(0, charactersToUse.length - 1));
                }
                return str;
            },

            /**
             * choose an item from an array randomly
             * @param arr
             */
            fromArray: function (arr) {
                return (arr != null && arr.length > 0) ? arr[this.int(0, arr.length - 1)] : null;
            },

            /**
             * shuffle an array
             * @param arr
             */
            shuffle: function (arr) {
                if (arr != null) {
                    for (var i = 0; i < arr.length; i++) {
                        var j = this.int(0, arr.length - 1);
                        var a = arr[i];
                        var b = arr[j];
                        arr[i] = b;
                        arr[j] = a;
                    }
                }
                return arr;
            }
        };
    });