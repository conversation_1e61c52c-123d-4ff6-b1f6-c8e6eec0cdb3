ig.module(
    'dl.templates.entities.entity-level'
).requires(
    'dl.game.entity',
    'dl-plugins.notification.notification-mixin',
    'dl-plugins.chat-bubble.chat-bubble-mixin'
).defines(function () {
    'use strict';

    dl.EntityLevel = dl.Entity
        .extend(dl.Notification.Mixin)
        .extend(dl.ChatBubble.Mixin)
        .extend({
            /**
             * to use with base impact engine when loadLevel
             */
            ready: function () {
                dl.scene = this;
                dl.game.currentLevel = this;

                this.postInit();
            },

            spawnPopup: function (popupClass, settings) {
                if (this.popup && !this.popup.killed) {
                    // ig.game.enableEntities();
                    console.warn('Already have popup');
                    return null;
                }

                this.popup = this.spawnEntity(popupClass, settings);

                return this.popup;
            },

            tweenIn: function (callback) {
                if (dl.check.isFunction(callback)) callback();
            },

            tweenOut: function (callback) {
                if (dl.check.isFunction(callback)) callback();
            }
        });
});
