ig.module(
    'dl.game.entity.components.draw.animation-sheet'
).requires(
    'dl.game.entity.components.draw.draw',
    'impact.image'
).defines(function () {
    'use strict';

    /**
     * Draw animation sheets
     * @example:
     *  Init component:
            this.c_AnimationSheetComponent = this.addComponent(dl.AnimationSheetComponent);
        *  Define in class:
            this.c_AnimationSheetComponent.updateProperties({
                _drawType: dl.AnimationSheetComponent.DRAW_SIZE.RAW,

                _animationSheetImage: null,
                _animationSheetRow: 1,
                _animationSheetCol: 1,

                _size: { x: 16, y: 16 },
                _docker: { x: 0.5, y: 0.5 },
                _anchor: { x: 0.5, y: 0.5 },
                _offset: { x: 0, y: 0 }
            });
        *  In settings:
            c_AnimationSheetComponent: {
                _drawType: dl.AnimationSheetComponent.DRAW_SIZE.RAW,

                _animationSheetImage: null,
                _animationSheetRow: 1,
                _animationSheetCol: 1,

                _size: { x: 16, y: 16 },
                _docker: { x: 0.5, y: 0.5 },
                _anchor: { x: 0.5, y: 0.5 },
                _offset: { x: 0, y: 0 }
            }
        */

    dl.AnimationSheetComponent = dl.EntityDrawComponent.extend({
        staticInstantiate: function (entity) {
            this.parent(entity);

            // Draw
            this._drawType = dl.AnimationSheetComponent.DRAW_SIZE.RAW;

            // Animation sheet
            this._animationSheetImage = null;
            this._animationSheetRow = 1;
            this._animationSheetCol = 1;
            this._killOnStop = false;

            this.animations = [];
            this.currentAnimation = null;

            this.__currentSheet = null;
            this.__nextAnimation = null;

            return this;
        },

        setComponentName: function () {
            this._componentName = 'dl_AnimationSheetComponent';
        },

        lateUpdate: function () {
            if (this.__nextAnimation) {
                this._setAnimation(this.__nextAnimation.rewind());
                this.__nextAnimation = null;
            }

            if (this.currentAnimation) {
                if (this.currentAnimation.updateFrame()) {
                    this.triggerDrawChanged();
                }
                if (this.currentAnimation._loopCount > 0 && this._killOnStop) {
                    this.entity.kill();
                }
            }

            this.parent();
        },

        draw: function (ctx) {
            if (!this.currentAnimation) return;

            ctx.save();

            // translate to center entity
            ctx.translate(this.entity.pos.x, this.entity.pos.y);
            // scale
            ctx.translate(this.pos.x, this.pos.y);
            ctx.rotate(this.entity.angle);
            ctx.translate(-this.pos.x, -this.pos.y);
            // draw
            this.currentAnimation.drawFrame(ctx,
                this.pos.x - this.size.x * 0.5,
                this.pos.y - this.size.y * 0.5,
                this.size.x,
                this.size.y);

            ctx.restore();

            if (dl.debug) {
                if (dl.debug.increaseDrawCall) dl.debug.increaseDrawCall();
                if (dl.debug.drawComponent) dl.debug.drawComponent(this);
            }
        },

        onPropertiesChanged: function (properties) {
            if (properties) {
                if (properties._animationSheetImage) {
                    this.setupAnimationSheet();
                    this._setupDefaultAnimation();
                }

                // force size change
                if (properties._drawType ||

                    properties._animationSheetImage ||

                    properties._size ||
                    properties._docker ||
                    properties._anchor ||
                    properties._offset) {
                    this.reposition();
                }
            }

            this.requestReposition();
        },

        setupAnimationSheet: function () {
            if (!(this._animationSheetImage instanceof ig.Image)) {
                dl.warn('Invalid Image [' + image + ']');
                return;
            }
            if (!dl.check.isNumber(this._animationSheetRow)) {
                dl.warn('Invalid row [' + this._animationSheetRow + ']');
                row = 1;
            }
            if (!dl.check.isNumber(this._animationSheetCol)) {
                dl.warn('Invalid col [' + this._animationSheetCol + ']');
                col = 1;
            }

            this.__currentSheet = new dl.AnimationSheetComponent.Sheet(
                this._animationSheetImage.path,
                this._animationSheetImage.width / this._animationSheetCol,
                this._animationSheetImage.height / this._animationSheetRow
            );
        },

        _setupDefaultAnimation: function () {
            var frames = this._animationSheetRow * this._animationSheetCol;

            if (frames > 1) {
                this.addAnimation('default', 200, this.generateSequence(frames));
            } else {
                this.addAnimation('default', 0, [0], true);
            }

            this.setAnimation('default');
        },

        generateSequence: function (frames) {
            return Array.from(Array(frames).keys());
        },

        addAnimation: function (name, frameTime, sequence, stop) {
            if (!this.__currentSheet) {
                dl.error('No __currentSheet to add the animation ' + name + ' to.');
                return;
            }

            this.animations[name] = new dl.AnimationSheetComponent.Animation(
                this.__currentSheet,
                frameTime,
                sequence,
                stop);

            return this.animations[name];
        },

        _setAnimation: function (newAnimation) {
            this.currentAnimation = newAnimation;

            this.triggerDrawChanged();
            this.requestReposition();
        },

        setAnimation: function (name) {
            this.__nextAnimation = this.animations[name];

            if (!dl.check.isDefined(this.__nextAnimation)) {
                dl.warn('No animation named [' + name + ']');
                this.__nextAnimation = null;
                return false;
            }

            if (!dl.check.isDefined(this.currentAnimation)) {
                this._setAnimation(this.__nextAnimation.rewind());

                return true;
            }

            if (this.__nextAnimation == this.currentAnimation) {
                this.__nextAnimation = null;
                return false;
            }

            return true;
        },

        updateCurrentAnimation: function (data) {
            if (!dl.check.isDefined(this.currentAnimation)) return;
            this.currentAnimation.updateData(data);
        },

        checkCurrentAnimation: function (name) {
            var checkAnimation = this.animations[name];

            if (!dl.check.isDefined(checkAnimation)) {
                return false;
            }

            if (!dl.check.isDefined(this.currentAnimation)) {
                return false;
            }

            if (this.checkAnimation == this.currentAnimation) {
                return true;
            }

            return false;
        },

        updateSize: function () {
            switch (this._drawType) {
                case dl.AnimationSheetComponent.DRAW_SIZE.RAW:
                default: {
                    if (this.currentAnimation) {
                        this.size.x = this.currentAnimation._sheet.frameWidth * this.entity.scale.x;
                        this.size.y = this.currentAnimation._sheet.frameHeight * this.entity.scale.y;
                    } else {
                        if (this.__currentSheet) {
                            this.size.x = this.__currentSheet.frameWidth * this.entity.scale.x;
                            this.size.y = this.__currentSheet.frameHeight * this.entity.scale.y;
                        }
                    }
                } break;

                case dl.AnimationSheetComponent.DRAW_SIZE.ENTITY: {
                    this.size.x = this.entity.size.x;
                    this.size.y = this.entity.size.y;
                } break;

                case dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM: {
                    this.size.x = this._size.x * this.entity.scale.x;
                    this.size.y = this._size.y * this.entity.scale.y;
                } break;
            }

            this.parent();
        }
    });

    /**
     * Sheet class
     * Contain sheet info
     */
    dl.AnimationSheetComponent.Sheet = ig.Class.extend({
        staticInstantiate: function (path, frameWidth, frameHeight) {
            this.frameWidth = frameWidth;
            this.frameHeight = frameHeight;

            this.sheetImage = new ig.Image(path);

            return this;
        },

        drawTile: function (context, tile, x, y, width, height) {
            var imageData = dl.canvas.checkPackerPlugin(this.sheetImage);

            var sourceImageStartPos = {
                x: imageData.x + (Math.floor(tile * this.frameWidth) % this.sheetImage.width),
                y: imageData.y + (Math.floor(tile * this.frameWidth / this.sheetImage.width) * this.frameHeight)
            };

            context.drawImage(
                imageData.data,
                sourceImageStartPos.x, sourceImageStartPos.y,
                this.frameWidth, this.frameHeight,
                x, y,
                width, height
            );
        }
    });

    /**
     * Animation class
     * Handle animation
     */
    dl.AnimationSheetComponent.Animation = ig.Class.extend({
        staticInstantiate: function (sheet, frameTime, sequence, stop) {
            this._sheet = sheet;
            this._frameTime = frameTime || 1;
            this._sequence = sequence;
            this._stop = !!stop;

            this.__timer = new dl.GameTimer();
            this._loopCount = 0;
            this._frame = 0;
            this._tile = this._sequence[0];
            this._lastTile = this._tile;

            this.useGlobalTime = false;
            this.startGlobalTime = 0;
            this.currentGlobalTime = 0;

            return this;
        },

        rewind: function () {
            this.__timer.start();
            this._loopCount = 0;
            this._frame = 0;
            this._tile = this._sequence[0];
            this._lastTile = this._tile;

            return this;
        },

        updateData: function (data) {
            if (!data) return;

            if (typeof data.useGlobalTime !== 'undefined') {
                this.useGlobalTime = data.useGlobalTime;
            } else {
                this.currentGlobalTime = false;
            }

            if (typeof data.startGlobalTime !== 'undefined') {
                this.startGlobalTime = data.startGlobalTime || 0;
            } else {
                this.startGlobalTime = 0;
            }

            if (typeof data.currentGlobalTime !== 'undefined') {
                this.currentGlobalTime = data.currentGlobalTime;
            } else {
                this.currentGlobalTime = 0;
            }
        },

        updateFrame: function () {
            var deltaTime = this.__timer.delta();
            if (this.useGlobalTime) {
                deltaTime = this.currentGlobalTime - this.startGlobalTime;
            }

            var frameTotal = Math.floor(deltaTime / this._frameTime);
            this._loopCount = Math.floor(frameTotal / this._sequence.length);

            if (this._stop && this._loopCount > 0) {
                this._frame = this._sequence.length - 1;
            } else {
                this._frame = frameTotal % this._sequence.length;
            }

            this._lastTile = this._tile;
            this._tile = this._sequence[this._frame];

            return this._lastTile != this._tile;
        },

        drawFrame: function (context, x, y, width, height) {
            this._sheet.drawTile(context, this._tile, x, y, width, height);
        }
    });

    dl.AnimationSheetComponent.DRAW_SIZE = {
        RAW: 0,
        ENTITY: 1, // note: shouldn't update entity size
        CUSTOM: 2
    };
});
