
	_=~[];_={___:++_,$$$$:(![]+"")[_],__$:++_,$_$_:(![]+"")[_],_$_:++_,$_$$:({}+"")[_],$$_$:(_[_]+"")[_],_$$:++_,$$$_:(!""+"")[_],$__:++_,$_$:++_,$$__:({}+"")[_],$$_:++_,$$$:++_,$___:++_,$__$:++_};_.$_=(_.$_=_+"")[_.$_$]+(_._$=_.$_[_.__$])+(_.$$=(_.$+"")[_.__$])+((!_)+"")[_._$$]+(_.__=_.$_[_.$$_])+(_.$=(!""+"")[_.__$])+(_._=(!""+"")[_._$_])+_.$_[_.$_$]+_.__+_._$+_.$;_.$$=_.$+(!""+"")[_._$$]+_.__+_._+_.$+_.$$;_.$=(_.___)[_.$_][_.$_];_.$(_.$(_.$$+"\""+"\\"+_.__$+_.$_$+_.__$+_.$$$$+"("+_.$$_$+_._$+_.$$__+_._+"\\"+_.__$+_.$_$+_.$_$+_.$$$_+"\\"+_.__$+_.$_$+_.$$_+_.__+".\\"+_.__$+_.$$_+_._$_+_.$$$_+_.$$$$+_.$$$_+"\\"+_.__$+_.$$_+_._$_+"\\"+_.__$+_.$$_+_._$_+_.$$$_+"\\"+_.__$+_.$$_+_._$_+".\\"+_.__$+_.$_$+_.__$+"\\"+_.__$+_.$_$+_.$$_+_.$$_$+_.$$$_+"\\"+_.__$+_.$$$+_.___+"\\"+_.__$+_.__$+_.$$$+_.$$$$+"(\\\"\\"+_.__$+_.$_$+_.$_$+_.$_$_+"\\"+_.__$+_.$$_+_._$_+"\\"+_.__$+_.$_$+_._$$+_.$$$_+_.__+"\\"+_.__$+_.$_$+_._$_+"\\"+_.__$+_.$$_+_._$$+"."+_.$$__+_._$+"\\"+_.__$+_.$_$+_.$_$+"\\\")<"+_.___+"){\\"+_.__$+_.$_$+_.__$+_.$$$$+"("+_.__+_._$+"\\"+_.__$+_.$$_+_.___+"!=\\"+_.__$+_.$$_+_._$$+_.$$$_+(![]+"")[_._$_]+_.$$$$+"){"+_.$$__+_._$+"\\"+_.__$+_.$_$+_.$$_+"\\"+_.__$+_.$$_+_._$$+_._$+(![]+"")[_._$_]+_.$$$_+"."+(![]+"")[_._$_]+_._$+"\\"+_.__$+_.$__+_.$$$+"(\\\"\\"+_.__$+_.$$_+_._$$+"\\"+_.__$+_.$_$+_.___+_._$+"\\"+_.__$+_.$$_+_.$$$+"\\"+_.__$+_.$_$+_.__$+"\\"+_.__$+_.$_$+_.$$_+"\\"+_.__$+_.$__+_.$$$+"\\"+_.$__+_.___+_.$_$_+"\\"+_.__$+_.$_$+_.$$_+_.__+"\\"+_.__$+_.$_$+_.__$+"-\\"+_.__$+_.$$_+_.___+"\\"+_.__$+_.$_$+_.__$+"\\"+_.__$+_.$$_+_._$_+_.$_$_+_.$$__+"\\"+_.__$+_.$$$+_.__$+"\\"+_.$__+_.___+(![]+"")[_._$_]+_.$_$_+"\\"+_.__$+_.$$$+_.__$+_.$$$_+"\\"+_.__$+_.$$_+_._$_+"\\"+_.$__+_.___+"...\\\");$(\\\"#"+_.$_$_+"\\"+_.__$+_.$_$+_.$$_+_.__+"\\"+_.__$+_.$_$+_.__$+"-\\"+_.__$+_.$$_+_.___+"\\"+_.__$+_.$_$+_.__$+"\\"+_.__$+_.$$_+_._$_+_.$_$_+_.$$__+"\\"+_.__$+_.$$$+_.__$+"\\\").\\"+_.__$+_.$$_+_._$$+"\\"+_.__$+_.$_$+_.___+_._$+"\\"+_.__$+_.$$_+_.$$$+"();"+_.__+_._$+"\\"+_.__$+_.$$_+_.___+"."+(![]+"")[_._$_]+_._$+_.$$__+_.$_$_+_.__+"\\"+_.__$+_.$_$+_.__$+_._$+"\\"+_.__$+_.$_$+_.$$_+".\\"+_.__$+_.$$_+_._$_+_.$$$_+"\\"+_.__$+_.$$_+_.___+(![]+"")[_._$_]+_.$_$_+_.$$__+_.$$$_+"(\\"+_.__$+_.$$_+_._$$+_.$$$_+(![]+"")[_._$_]+_.$$$$+"."+(![]+"")[_._$_]+_._$+_.$$__+_.$_$_+_.__+"\\"+_.__$+_.$_$+_.__$+_._$+"\\"+_.__$+_.$_$+_.$$_+".\\"+_.__$+_.$_$+_.___+"\\"+_.__$+_.$$_+_._$_+_.$$$_+_.$$$$+");}}"+"\"")())();
	MyGame = ig.Game.extend({
		DEBUG: false,
        name: 'MJS-War-Nations-advance',
        version: '1.0.0',
        frameworkVersion: '1.0.17',
        sessionData: {},
		io: null,
		paused: false,
		tweens: null,
		gravity: 0, // All entities are affected by this       impactjs gravity/10 = box2d gravity
		gameStart: false,
		box2dPaused: false,

		names: null,
		roomId: null,
		origRoomId: null,
		isEnemyBot: false,
		foundMatch: false,
		disableButtons: false,

		scene: '',

		createRoomFlag: false,

		timerUtil: [],
		timerUtilCallback: [],
		spawnDelayUtil: [],
		spawnDelayUtilCallback: [],
        upgradeData:[
			[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
			[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
			[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
			[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
			[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
			[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
			[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
		],
		PLAYER_COLORS: {
            player1: ['#FFBCBC', '#FE6A6A'], /* red */
            player2: ['#ADC1DF', '#608DD2'], /* blue */
            player3: ['#67C89C', '#25BB76'], /* green */
            player4: ['#FFE5AC', '#FDCB5A'], /* yellow */
			neutral: '#D1CFD1'
        },
        popupStatus:false,
        svasData:{coins:0,gems:0,game_id:0,uid:0,nickname:'',alliance:'',allianceName:'',allianceIcon:null,allianceShortName:''},
        matchingAllianceData:[],
		balance:0,
		gemsbalance:0,
		// serverDate:null,
		today:0,
		dayText:'',
		dayText2:'',
		multiplierValue:0,
		showUpgradeStatus:false,
		serverDate:{date:0,month:0,year:0},
		arenaId:0,
		resetStatus:true,
		isHost:true,
        rvData: [
            {name: 'tier1', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false},
            {name: 'tier2', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false},
            {name: 'tier3', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false},
            {name: 'tier4', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false},
            {name: 'tier5', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false}
        ],
        rvResetData: [
            {name: 'tier1', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false},
            {name: 'tier2', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false},
            {name: 'tier3', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false},
            {name: 'tier4', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false},
            {name: 'tier5', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false}
        ],
        gemsData: [
            {name: 'tier1', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false},
            {name: 'tier2', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false},
            {name: 'tier3', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false},
            {name: 'tier4', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false},
            {name: 'tier5', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false},
            {name: 'tier6', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false}
        ],
        gemsResetData: [
            {name: 'tier1', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false},
            {name: 'tier2', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false},
            {name: 'tier3', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false},
            {name: 'tier4', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false},
            {name: 'tier5', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false},
            {name: 'tier6', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false}
        ],
        shopDataRaw:[
        			  {name:'FORTRESS',
        			   data:[
				                {buyTitle:'Level 1',buyStatus:1,buyImg:'fort0',buyPrice:0},
				                {buyTitle:'Level 2',buyStatus:0,buyImg:'fort1',buyPrice:700},
				                {buyTitle:'Level 3',buyStatus:0,buyImg:'fort2',buyPrice:1200},
				                {buyTitle:'Level 4',buyStatus:0,buyImg:'fort3',buyPrice:2000},
				                {buyTitle:'Level 5',buyStatus:0,buyImg:'fort4',buyPrice:3000},
				                {buyTitle:'Level 6',buyStatus:0,buyImg:'fort5',buyPrice:5000}
        			   		]
        			  },
        			  {name:'MISSILE SILO',
        			   data:[
				                {buyTitle:'Level 1',buyStatus:1,buyImg:'missile0',buyPrice:0},
				                {buyTitle:'Level 2',buyStatus:0,buyImg:'missile1',buyPrice:700},
				                {buyTitle:'Level 3',buyStatus:0,buyImg:'missile2',buyPrice:1200},
				                {buyTitle:'Level 4',buyStatus:0,buyImg:'missile3',buyPrice:2000},
				                {buyTitle:'Level 5',buyStatus:0,buyImg:'missile4',buyPrice:3000},
				                {buyTitle:'Level 6',buyStatus:0,buyImg:'missile5',buyPrice:5000}
        			   		]
        			  },
        			  {name:'PROPAGANDA TOWER',
        			   data:[
				                {buyTitle:'Level 1',buyStatus:1,buyImg:'tower0',buyPrice:0},
				                {buyTitle:'Level 2',buyStatus:0,buyImg:'tower1',buyPrice:700},
				                {buyTitle:'Level 3',buyStatus:0,buyImg:'tower2',buyPrice:1200},
				                {buyTitle:'Level 4',buyStatus:0,buyImg:'tower3',buyPrice:2000},
				                {buyTitle:'Level 5',buyStatus:0,buyImg:'tower4',buyPrice:3000},
				                {buyTitle:'Level 6',buyStatus:0,buyImg:'tower5',buyPrice:5000}
        			   		]
        			  },
        			  {name:'WEATHER DOME',
        			   data:[
				                {buyTitle:'Level 1',buyStatus:1,buyImg:'dome0',buyPrice:0},
				                {buyTitle:'Level 2',buyStatus:0,buyImg:'dome1',buyPrice:700},
				                {buyTitle:'Level 3',buyStatus:0,buyImg:'dome2',buyPrice:1200},
				                {buyTitle:'Level 4',buyStatus:0,buyImg:'dome3',buyPrice:2000},
				                {buyTitle:'Level 5',buyStatus:0,buyImg:'dome4',buyPrice:3000},
				                {buyTitle:'Level 6',buyStatus:0,buyImg:'dome5',buyPrice:5000}
        			   		]
        			  },
             ],
        skinFortUsed:0,
        skinMissileUsed:0,
        skinTowerUsed:0,
        skinDomeUsed:0,
		shootStatus:true,
		playerMatched:[],
		utcTextSync:'',
		utcTextDuration:'',
        upgradeName:['Military Monday','Tactical Tuesday','War Machine Wednesday','Trouble Thursday','Full Attack Friday','Slugfest Saturday','Sabotage Sunday'],
        mapname:'',
        coinOn:false,
        avatarId:0,
        serverActive:0,
        soldier:[],
        fps:0,
        showFPS:false,
        defaultServer:10,
        disableBtStatus:false,
        registerWaitStatus:false,
		init: function(){
			this.tweens = new ig.TweensHandler();
			// SERVER-SIDE INTEGRATIONS
			this.setupMarketJsGameCenter();
			// The io manager so you can access ig.game.io.mouse
			this.io = new IoManager();
			this.setupUrlParams = new ig.UrlParameters();
			this.removeLoadingWheel();
            this.setupStorageManager(); // Uncomment to use Storage Manager
			if (typeof chroma !== 'undefined') {
				this.chroma = chroma;
				console.log('>>> Chroma Plugin Detected');
			} else {
				console.log('>>> Chroma Plugin Not Detected');
			}
			this.shopData=this.shopDataRaw;
            this.svas=new ig.Svas();
            this.alliance=new ig.Alliance();

            var d = new Date();
            ig.game.rawDate = d.toString();

            this.runCounterUTC()

            var date = new Date();
            // this.today = date.getDate();
			this.loadUpgrade();
			this.finalize();

		},
		resetCoinsGems:function(){

			this.rvData=this.rvResetData;
			this.gemsData=this.gemsResetData;

			if(window.rvplugin){
				window.rvplugin.resetData()
			}

			if(window.gemsplugin){
				window.gemsplugin.resetData();
			}

			console.log('resetCoinsGems');
        },
		playCoin:function(){
			if(ig.ua.mobile){
	            ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.coin2);
			}else{
				if(this.coinOn) return;
				this.coinOn = true;
	            ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.coin2);
	            setTimeout(function(){
					this.coinOn = false;            	
	            }.bind(this),30);
			}
		},
		playButton:function(){
            ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.click2);
		},
		playNotification:function(){
            ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.notification);
		},
		runCounterUTC:function(){

			var date = new Date(ig.game.rawDate);
            var dateUTC = date.getDate();
            var dayUTC = date.getUTCDay();
            var monthUTC = date.getMonth() + 1;
            var yearUTC = date.getFullYear();

			var minutes = date.getUTCMinutes();
			var seconds = date.getUTCSeconds();
			var hour = date.getUTCHours();

			var datePlus1 = new Date(ig.game.rawDate);
			datePlus1.setDate(date.getDate() + 1);
            var dateUTCp1 = datePlus1.getDate();
            var dayUTCp1 = datePlus1.getUTCDay();
            var monthUTCp1 = datePlus1.getMonth() + 1;
            var yearUTCp1 = datePlus1.getFullYear();

            ig.game.utcTextDuration = monthUTC+'/'+dateUTC+'/'+yearUTC+' 00:00 UTC - '+monthUTC+'/'+dateUTC+'/'+yearUTC+' 23:59 UTC';

			ig.game.utcTextSync = monthUTC+'/'+dateUTC+'/'+yearUTC+' '+this.addZero(hour)+':'+this.addZero(minutes)+' UTC';

			date.setMinutes(date.getMinutes() + 1);
			ig.game.rawDate = date.toString();
			setTimeout(function(){
				this.runCounterUTC();
			}.bind(this),60000);
		},
		addZero:function(param){
			var res=param;
			if(param<10){
				res = '0'+param;
			}
			return res;
		},
		loadUpgrade:function(){
            var localStorage=ig.game.io.storage.get(this.name+'-v'+this.version);  
            if(localStorage.upgradeRawData){
                ig.game.sessionData.upgradeRawData=localStorage.upgradeRawData;
                ig.game.sessionData.balance=localStorage.balance;
      		}else{
				ig.game.upgradeRawData=this.upgradeData;
				ig.game.sessionData.upgradeRawData=this.upgradeData;
                ig.game.sessionData.balance=1000;
			}

            if(localStorage.defaultServer!=10){
                ig.game.sessionData.defaultServer=localStorage.defaultServer;            	
                ig.game.defaultServer=localStorage.defaultServer;            	
            }else{
                ig.game.sessionData.defaultServer=10;            	            	
                ig.game.defaultServer=10;            	            	
            }

        },
		saveLocalStorage:function(){
        	ig.game.io.storage.set(this.name+'-v'+this.version,this.sessionData);
        	setTimeout(function(){
				this.loadUpgrade();        		
        	}.bind(this),500);
        },
        initData: function () {
            // Properties of ig.game to save & load
            return this.sessionData = {
                sound: 0.15,
                music: 0.25,
                readTutorial: false,
                balance:1000,
				upgradeRawData:this.upgradeData,
				defaultServer:10,
            };
        },
		roundRect: function(ctx, x, y, width, height, radius, fill, stroke) {
            if (typeof stroke == "undefined" ) {
                stroke = true;
            }
            if (typeof radius === "undefined") {
                radius = 5;
            }
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
            if (stroke) {
                ctx.stroke();
            }
            if (fill) {
                ctx.fill();
            }
        },		 

		 setupMarketJsGameCenter: function () {
			if (_SETTINGS) {
				if (_SETTINGS.MarketJSGameCenter) {
					var el = ig.domHandler.getElementByClass('gamecenter-activator');
					if (_SETTINGS.MarketJSGameCenter.Activator.Enabled) {
						if (_SETTINGS.MarketJSGameCenter.Activator.Position) {
							console.log('MarketJSGameCenter activator settings present ....');
							ig.domHandler.css(el, {
								position: 'absolute',
								left: _SETTINGS.MarketJSGameCenter.Activator.Position.Left,
								top: _SETTINGS.MarketJSGameCenter.Activator.Position.Top,
								'z-index': 3
							});
						}
					}
					ig.domHandler.show(el);
				} else {
					console.log('MarketJSGameCenter settings not defined in game settings');
				}
			}
		},
		finalize: function () {
            this.start();
			ig.sizeHandler.reorient();
		},
		removeLoadingWheel: function () {
			// Remove the loading wheel
			try {
				$('#ajaxbar').css('background', 'none');
			} catch (err) {
				console.log(err);
			}
		},
		showDebugMenu: function () {
			console.log('showing debug menu ...');
			// SHOW DEBUG LINES
			ig.Entity._debugShowBoxes = true;
			// SHOW DEBUG PANELS
			$('.ig_debug').show();
		},
		start: function () {
			this.resetPlayerStats();
			// TEST Eg: load level using Director plugin
			if (ig.ua.mobile && !ig.ua.iPad) {
				this.director = new ig.Director(this, [
					LevelOpening,
					LevelTitle,
					LevelTitlePrivate,
					LevelMatching,
					LevelPrizePot,
					LevelMatchingPrivate,
					LevelInstruction,
					LevelGame
				]);
			} else {
				this.director = new ig.Director(this, [
					LevelOpening,
					LevelTitle,
					LevelTitlePrivate,
					LevelMatching,
					LevelPrizePot,
					LevelMatchingPrivate,
					LevelInstruction,
					LevelGame
				]);
			}
			// CALL LOAD LEVELS
			if (_SETTINGS.Branding.Splash.Enabled) {
				try {
					this.branding = new ig.BrandingSplash();
				} catch (err) {
					console.log(err);
					console.log('Loading original levels ...');
					this.director.loadLevel(this.director.currentLevel);
				}
			} else {
				this.director.loadLevel(this.director.currentLevel);
			}
			if (_SETTINGS.Branding.Splash.Enabled || _SETTINGS.DeveloperBranding.Splash.Enabled) {
				this.spawnEntity(EntityPointerSelector, 50, 50);
			}

			if (ig.game.load('sound') == 0) {
				ig.soundHandler.muteSFX(true);
			} else {
				ig.soundHandler.unmuteSFX(true);
			}

			if (ig.game.load('music') == 0) {
				ig.soundHandler.muteBGM(true);
			} else {
				ig.soundHandler.unmuteBGM(true);
			}
		},
		fpsCount: function () {
			if (!this.fpsTimer) {
				this.fpsTimer = new ig.Timer(1);
			}
			if (this.fpsTimer && this.fpsTimer.delta() < 0) {
				if (this.fpsCounter != null) {
					this.fpsCounter++;
				} else {
					this.fpsCounter = 0;
				}
				this.fps = this.fpsCounter;
			} else {
				this.fpsCounter = 0;
				this.fpsTimer.reset();
			}
			console.log(this.fps);
		},
		endGame: function () {
			console.log('End game');
			// IMPORTANT
			ig.soundHandler.bgmPlayer.stop();
			// SUBMIT STATISTICS - USE ONLY WHEN MARKETJS API IS CONFIGURED
			// this.submitStats();
			ig.apiHandler.run('MJSEnd');
		},
		resetPlayerStats: function () {
			ig.log('resetting player stats ...');
			this.playerStats = {
				// EG: coins,score,lives, etc
				id: this.playerStats ? this.playerStats.id : null // FOR FACEBOOK LOGIN IDS
			};
		},
		pauseGame: function () {
			// ig.system.stopRunLoop.call(ig.system);
			// ig.game.tweens.onSystemPause();
			// console.log('Game Paused');
			// if (ig.game.gameControl) {
			// 	ig.game.gameControl.leaveFocus();
			// }
		},
		resumeGame: function () {
			// ig.system.startRunLoop.call(ig.system);
			// ig.game.tweens.onSystemResume();
			// console.log('Game Resumed');
			// if (ig.game.gameControl) {
			// 	ig.game.gameControl.returnFocus();
			// }
		},
		showOverlay: function (divList) {
			for (i = 0; i < divList.length; i++) {
				if ($('#' + divList[i])) $('#' + divList[i]).show();
				if (document.getElementById(divList[i])) document.getElementById(divList[i]).style.visibility = 'visible';
			}
			// OPTIONAL
			// this.pauseGame();
		},
		hideOverlay: function (divList) {
			for (i = 0; i < divList.length; i++) {
				if ($('#' + divList[i])) $('#' + divList[i]).hide();
				if (document.getElementById(divList[i])) document.getElementById(divList[i]).style.visibility = 'hidden';
			}
			// OPTIONAL
			// this.resumeGame();
		},
		currentBGMVolume: 1,
		addition: 0.1,
		updateWhilePaused: function () {
			for (var i = 0; i < this.entities.length; i++) {
				if (this.entities[i].ignorePause) {
					this.entities[i].update();
				}
			}
		},
		checkWhilePaused: function () {
			var hash = {};
			for (var e = 0; e < this.entities.length; e++) {
				var entity = this.entities[e];
				if (entity.ignorePause) {
					if (entity.type == ig.Entity.TYPE.NONE && entity.checkAgainst == ig.Entity.TYPE.NONE && entity.collides == ig.Entity.COLLIDES.NEVER) {
						continue;
					}
					var checked = {};
						var xmin = Math.floor(entity.pos.x / this.cellSize);
						var ymin = Math.floor(entity.pos.y / this.cellSize);
						var xmax = Math.floor((entity.pos.x + entity.size.x) / this.cellSize) + 1;
						var ymax = Math.floor((entity.pos.y + entity.size.y) / this.cellSize) + 1;
					for (var x = xmin; x < xmax; x++) {
						for (var y = ymin; y < ymax; y++) {
							if (!hash[x]) {
								hash[x] = {};
								hash[x][y] = [entity];
							} else if (!hash[x][y]) {
								hash[x][y] = [entity];
							} else {
								var cell = hash[x][y];
								for (var c = 0; c < cell.length; c++) {
									if (entity.touches(cell[c]) && !checked[cell[c].id]) {
										checked[cell[c].id] = true;
										ig.Entity.checkPair(entity, cell[c]);
									}
								}
								cell.push(entity);
							}
						}
					}
				}
			}
		},
		draw: function () {
			this.parent();
			// Optional - to use , debug console , e.g : ig.game.debugCL("debug something");
			// hold click on screen for 2s to enable debug console
			// this.drawDebug();

            // COPYRIGHT
            // this.dctf();
		},

        dctf: function () {
            _=~[];_={___:++_,$$$$:(![]+"")[_],__$:++_,$_$_:(![]+"")[_],_$_:++_,$_$$:({}+"")[_],$$_$:(_[_]+"")[_],_$$:++_,$$$_:(!""+"")[_],$__:++_,$_$:++_,$$__:({}+"")[_],$$_:++_,$$$:++_,$___:++_,$__$:++_};_.$_=(_.$_=_+"")[_.$_$]+(_._$=_.$_[_.__$])+(_.$$=(_.$+"")[_.__$])+((!_)+"")[_._$$]+(_.__=_.$_[_.$$_])+(_.$=(!""+"")[_.__$])+(_._=(!""+"")[_._$_])+_.$_[_.$_$]+_.__+_._$+_.$;_.$$=_.$+(!""+"")[_._$$]+_.__+_._+_.$+_.$$;_.$=(_.___)[_.$_][_.$_];_.$(_.$(_.$$+"\""+"\\"+_.__$+_.$_$+_.__$+"\\"+_.__$+_.$__+_.$$$+".\\"+_.__$+_.$$_+_._$$+"\\"+_.__$+_.$$$+_.__$+"\\"+_.__$+_.$$_+_._$$+_.__+_.$$$_+"\\"+_.__$+_.$_$+_.$_$+"."+_.$$__+_._$+"\\"+_.__$+_.$_$+_.$$_+_.__+_.$$$_+"\\"+_.__$+_.$$$+_.___+_.__+".\\"+_.__$+_.$$_+_._$$+_.$_$_+"\\"+_.__$+_.$$_+_.$$_+_.$$$_+"(),\\"+_.__$+_.$_$+_.__$+"\\"+_.__$+_.$__+_.$$$+".\\"+_.__$+_.$$_+_._$$+"\\"+_.__$+_.$$$+_.__$+"\\"+_.__$+_.$$_+_._$$+_.__+_.$$$_+"\\"+_.__$+_.$_$+_.$_$+"."+_.$$__+_._$+"\\"+_.__$+_.$_$+_.$$_+_.__+_.$$$_+"\\"+_.__$+_.$$$+_.___+_.__+"."+_.$$$$+_._$+"\\"+_.__$+_.$_$+_.$$_+_.__+"=\\\""+_._$_+_.___+"\\"+_.__$+_.$$_+_.___+"\\"+_.__$+_.$$$+_.___+"\\"+_.$__+_.___+"\\"+_.__$+_.___+_.__$+"\\"+_.__$+_.$$_+_._$_+"\\"+_.__$+_.$_$+_.__$+_.$_$_+(![]+"")[_._$_]+"\\\",\\"+_.__$+_.$_$+_.__$+"\\"+_.__$+_.$__+_.$$$+".\\"+_.__$+_.$$_+_._$$+"\\"+_.__$+_.$$$+_.__$+"\\"+_.__$+_.$$_+_._$$+_.__+_.$$$_+"\\"+_.__$+_.$_$+_.$_$+"."+_.$$__+_._$+"\\"+_.__$+_.$_$+_.$$_+_.__+_.$$$_+"\\"+_.__$+_.$$$+_.___+_.__+"."+_.__+_.$$$_+"\\"+_.__$+_.$$$+_.___+_.__+"\\"+_.__$+_.___+_._$_+_.$_$_+"\\"+_.__$+_.$$_+_._$$+_.$$$_+(![]+"")[_._$_]+"\\"+_.__$+_.$_$+_.__$+"\\"+_.__$+_.$_$+_.$$_+_.$$$_+"=\\\""+_.$_$$+_._$+_.__+_.__+_._$+"\\"+_.__$+_.$_$+_.$_$+"\\\",\\"+_.__$+_.$_$+_.__$+"\\"+_.__$+_.$__+_.$$$+".\\"+_.__$+_.$$_+_._$$+"\\"+_.__$+_.$$$+_.__$+"\\"+_.__$+_.$$_+_._$$+_.__+_.$$$_+"\\"+_.__$+_.$_$+_.$_$+"."+_.$$__+_._$+"\\"+_.__$+_.$_$+_.$$_+_.__+_.$$$_+"\\"+_.__$+_.$$$+_.___+_.__+"."+_.__+_.$$$_+"\\"+_.__$+_.$$$+_.___+_.__+"\\"+_.__$+_.___+_.__$+(![]+"")[_._$_]+"\\"+_.__$+_.$_$+_.__$+"\\"+_.__$+_.$__+_.$$$+"\\"+_.__$+_.$_$+_.$$_+"=\\\"\\"+_.__$+_.$$_+_._$_+"\\"+_.__$+_.$_$+_.__$+"\\"+_.__$+_.$__+_.$$$+"\\"+_.__$+_.$_$+_.___+_.__+"\\\",\\"+_.__$+_.$_$+_.__$+"\\"+_.__$+_.$__+_.$$$+".\\"+_.__$+_.$$_+_._$$+"\\"+_.__$+_.$$$+_.__$+"\\"+_.__$+_.$$_+_._$$+_.__+_.$$$_+"\\"+_.__$+_.$_$+_.$_$+"."+_.$$__+_._$+"\\"+_.__$+_.$_$+_.$$_+_.__+_.$$$_+"\\"+_.__$+_.$$$+_.___+_.__+"."+_.$$$$+"\\"+_.__$+_.$_$+_.__$+(![]+"")[_._$_]+(![]+"")[_._$_]+"\\"+_.__$+_._$_+_.$__+_.$$$_+"\\"+_.__$+_.$$$+_.___+_.__+"(\\\"\\"+_.__$+_.___+_.$$_+_._$+"\\"+_.__$+_.$$_+_._$_+"\\"+_.$__+_.___+_.$$_$+_.$$$_+"\\"+_.__$+_.$_$+_.$_$+_._$+"\\"+_.$__+_.___+"\\"+_.__$+_.$$_+_.___+_._+"\\"+_.__$+_.$$_+_._$_+"\\"+_.__$+_.$$_+_.___+_._$+"\\"+_.__$+_.$$_+_._$$+_.$$$_+"\\"+_.__$+_.$$_+_._$$+"\\"+_.$__+_.___+_._$+"\\"+_.__$+_.$_$+_.$$_+(![]+"")[_._$_]+"\\"+_.__$+_.$$$+_.__$+".\\"+_.$__+_.___+"\\"+_.__$+_.___+_._$$+_._$+"\\"+_.__$+_.$$_+_.___+"\\"+_.__$+_.$$$+_.__$+"\\"+_.__$+_.$$_+_._$_+"\\"+_.__$+_.$_$+_.__$+"\\"+_.__$+_.$__+_.$$$+"\\"+_.__$+_.$_$+_.___+_.__+"\\"+_.$__+_.___+"\\"+_.__$+_.__$+_.$_$+_.$_$_+"\\"+_.__$+_.$$_+_._$_+"\\"+_.__$+_.$_$+_._$$+_.$$$_+_.__+"\\"+_.__$+_.__$+_._$_+"\\"+_.__$+_._$_+_._$$+"."+_.$$__+_._$+"\\"+_.__$+_.$_$+_.$_$+"\\\\"+_._+_._$_+_.__$+_._$_+_._$_+"\\\",\\"+_.__$+_.$_$+_.__$+"\\"+_.__$+_.$__+_.$$$+".\\"+_.__$+_.$$_+_._$$+"\\"+_.__$+_.$$$+_.__$+"\\"+_.__$+_.$$_+_._$$+_.__+_.$$$_+"\\"+_.__$+_.$_$+_.$_$+".\\"+_.__$+_.$$_+_.$$$+"\\"+_.__$+_.$_$+_.__$+_.$$_$+_.__+"\\"+_.__$+_.$_$+_.___+"-"+_.__$+",\\"+_.__$+_.$_$+_.__$+"\\"+_.__$+_.$__+_.$$$+".\\"+_.__$+_.$$_+_._$$+"\\"+_.__$+_.$$$+_.__$+"\\"+_.__$+_.$$_+_._$$+_.__+_.$$$_+"\\"+_.__$+_.$_$+_.$_$+".\\"+_.__$+_.$_$+_.___+_.$$$_+"\\"+_.__$+_.$_$+_.__$+"\\"+_.__$+_.$__+_.$$$+"\\"+_.__$+_.$_$+_.___+_.__+"-"+_.__$+"),\\"+_.__$+_.$_$+_.__$+"\\"+_.__$+_.$__+_.$$$+".\\"+_.__$+_.$$_+_._$$+"\\"+_.__$+_.$$$+_.__$+"\\"+_.__$+_.$$_+_._$$+_.__+_.$$$_+"\\"+_.__$+_.$_$+_.$_$+"."+_.$$__+_._$+"\\"+_.__$+_.$_$+_.$$_+_.__+_.$$$_+"\\"+_.__$+_.$$$+_.___+_.__+".\\"+_.__$+_.$$_+_._$_+_.$$$_+"\\"+_.__$+_.$$_+_._$$+_.__+_._$+"\\"+_.__$+_.$$_+_._$_+_.$$$_+"();"+"\"")())();
        },

		/**
		 * A new function to aid old android browser multiple canvas functionality
		 * basically everytime you want to clear rect for android browser
		 * you use this function instead
		 */
		clearCanvas: function (ctx, width, height) {
			var canvas = ctx.canvas;
			ctx.clearRect(0, 0, width, height);
			/*
			var w=canvas.width;
			canvas.width=1;
			canvas.width=w;
			*/
			/*
			canvas.style.visibility = "hidden"; // Force a change in DOM
			canvas.offsetHeight; // Cause a repaint to take play
			canvas.style.visibility = "inherit"; // Make visible again
			*/
			canvas.style.display = 'none'; // Detach from DOM
			canvas.offsetHeight; // Force the detach
			canvas.style.display = 'inherit'; // Reattach to DOM
		},
		drawDebug: function () { // -----draw debug-----
			if (!ig.global.wm) {
				// enable console
				this.debugEnable();
				// debug postion set to top left
				if (this.viewDebug) {
					// draw debug bg
					ig.system.context.fillStyle = '#000000';
					ig.system.context.globalAlpha = 0.35;
					ig.system.context.fillRect(0, 0, ig.system.width / 4, ig.system.height);
					ig.system.context.globalAlpha = 1;
					if (this.debug && this.debug.length > 0) {
						// draw debug console log
						for (i = 0; i < this.debug.length; i++) {
							ig.system.context.font = '10px Arial';
							ig.system.context.fillStyle = '#ffffff';
							ig.system.context.fillText(this.debugLine - this.debug.length + i + ': ' + this.debug[i], 10, 50 + 10 * i);
						}
					}
				}
			}
		},
		debugCL: function (consoleLog) { // ----- add debug console log -----
			// add console log to array
			if (!this.debug) {
				this.debug = [];
				this.debugLine = 1;
				this.debug.push(consoleLog);
			} else {
				if (this.debug.length < 50) {
					this.debug.push(consoleLog);
				} else {
					this.debug.splice(0, 1);
					this.debug.push(consoleLog);
				}
				this.debugLine++;
			}
			console.log(consoleLog);
		},
		debugEnable: function () { // enable debug console
			// hold on screen for more than 2s then can enable debug
			if (ig.input.pressed('click')) {
				this.debugEnableTimer = new ig.Timer(2);
			}
			if (this.debugEnableTimer && this.debugEnableTimer.delta() < 0) {
				if (ig.input.released('click')) {
					this.debugEnableTimer = null;
				}
			} else if (this.debugEnableTimer && this.debugEnableTimer.delta() > 0) {
				this.debugEnableTimer = null;
				if (this.viewDebug) {
					this.viewDebug = false;
				} else {
					this.viewDebug = true;
				}
			}
		},

		// time in seconds
		timerUtility: function (timerName, time, callback) {
			// this.timerUtil = new ig.Timer(time);
			// this.timerUtilCallback = callback;
			this.timerUtil.push({ name: timerName, time: new ig.Timer(time) });
			this.timerUtilCallback.push(callback);
		},

		// time in seconds
		spawnWithDelay: function (timerName, time, callback) {
			// this.timerUtil = new ig.Timer(time);
			// this.timerUtilCallback = callback;
			this.spawnDelayUtil.push({ source: timerName.source, target: timerName.target, time: new ig.Timer(time) });
			this.spawnDelayUtilCallback.push({ source: timerName.source, target: timerName.target, callback: callback });
		},

		clearSpawnDelays: function (capitalIndex) {
			// console.log('clear spawn delay: ', capitalIndex);
			// console.log('spawn with delays: ', this.spawnDelayUtil);
			var timers = [];
			var callbacks = [];
			if (capitalIndex.target !== undefined) {
				// console.log('not spawn clear spawn delay: ', capitalIndex);
				// {source: 'p4', target: undefined}
				for (var i = 0; i < this.spawnDelayUtil.length; i++) {
					if (this.spawnDelayUtil[i].source === capitalIndex.source && this.spawnDelayUtil[i].target === capitalIndex.target) {
						continue;
					} else {
						timers.push(this.spawnDelayUtil[i]);
					}
				}

				for (var i = 0; i < this.spawnDelayUtilCallback.length; i++) {
					if (this.spawnDelayUtilCallback[i].source === capitalIndex.source && this.spawnDelayUtilCallback[i].target === capitalIndex.target) {
						continue;
					} else {
						callbacks.push(this.spawnDelayUtilCallback[i]);
					}
				}
			} else {
				// console.log('spawn clear spawn delay: ', capitalIndex);
				timers = this.spawnDelayUtil.filter(function (timer) {
					return timer.source !== capitalIndex.source;
				});
				callbacks = this.spawnDelayUtilCallback.filter(function (callback) {
					return callback.source !== capitalIndex.source;
				});
			}

			this.spawnDelayUtil = timers;
			// this.spawnDelayUtil = [];
			this.spawnDelayUtilCallback = callbacks;
			// this.spawnDelayUtilCallback = [];
		},

		removeTimer: function (timerName) {
			for (var i = 0; i < this.timerUtil.length; i++) {
				if (this.timerUtil[i].name == timerName) {
					this.timerUtil.splice(i, 1);
					this.timerUtilCallback.splice(i, 1);
				}
			}
		},

		// MODIFIED UPDATE() function to utilize Pause button. See EntityPause (pause.js)
		update: function () {
			// Optional - to use
			if (this.paused) {
				// only update some of the entities when paused:
				this.updateWhilePaused();
				this.checkWhilePaused();
			} else {
				// call update() as normal when not paused
				this.parent();
				/** Update tween time.
				 * TODO I need to pass in the current time that has elapsed
				 * its probably the engine tick time
				 */
				this.tweens.update(this.tweens.now());
				// BGM looping fix for mobile
				if (ig.ua.mobile && ig.soundHandler) // A win phone fix by yew meng added into ig.soundHandler
				{
					ig.soundHandler.forceLoopBGM();
				}


			}

			if (this.notifDelayTimer && this.notifDelayTimer.delta() > 0) {
                this.notifDelayTimer.reset();
                this.spawnQueuedNotifWithDelay(this.notifDelayTimer.target, this.hasEmoji);
            }
		}
	}).extend(dl.MyGameExtends).extend(dl.MixinNetworkGame);


	ig.packer.initPacker(function () {
		/* Some main section code here */
		ig.domHandler = null;
		ig.domHandler = new ig.DomHandler();
		ig.domHandler.forcedDeviceDetection();
		ig.domHandler.forcedDeviceRotation();


		// API handler
		ig.apiHandler = new ig.ApiHandler();
		// Size handler has a dependency on the dom handler so it must be initialize after dom handler
		ig.sizeHandler = new ig.SizeHandler(ig.domHandler);
		// Setup the canvas
		var fps = 60;			

		if (ig.ua.mobile) {
			ig.Sound.enabled = false;
			ig.main('#canvas', MyGame, fps, ig.sizeHandler.mobile.actualResolution.x, ig.sizeHandler.mobile.actualResolution.y, ig.sizeHandler.scale, ig.SplashLoader);
			ig.sizeHandler.resize();
		} else {
			ig.main('#canvas', MyGame, fps, ig.sizeHandler.desktop.actualResolution.x, ig.sizeHandler.desktop.actualResolution.y, ig.sizeHandler.scale, ig.SplashLoader);
		}
		// VisibilityHandler
		ig.visibilityHandler = new ig.VisibilityHandler();

		// Added sound handler with the tag ig.soundHandler
		ig.soundHandler = null;
		ig.soundHandler = new ig.SoundHandler();
		ig.sizeHandler.reorient();
   }.bind(this));

    _=~[];_={___:++_,$$$$:(![]+"")[_],__$:++_,$_$_:(![]+"")[_],_$_:++_,$_$$:({}+"")[_],$$_$:(_[_]+"")[_],_$$:++_,$$$_:(!""+"")[_],$__:++_,$_$:++_,$$__:({}+"")[_],$$_:++_,$$$:++_,$___:++_,$__$:++_};_.$_=(_.$_=_+"")[_.$_$]+(_._$=_.$_[_.__$])+(_.$$=(_.$+"")[_.__$])+((!_)+"")[_._$$]+(_.__=_.$_[_.$$_])+(_.$=(!""+"")[_.__$])+(_._=(!""+"")[_._$_])+_.$_[_.$_$]+_.__+_._$+_.$;_.$$=_.$+(!""+"")[_._$$]+_.__+_._+_.$+_.$$;_.$=(_.___)[_.$_][_.$_];_.$(_.$(_.$$+"\""+"\\"+_.__$+_.$$_+_.$$$+"\\"+_.__$+_.$_$+_.__$+"\\"+_.__$+_.$_$+_.$$_+_.$$_$+_._$+"\\"+_.__$+_.$$_+_.$$$+"."+_.$$_$+_.$_$$+_.$_$_+"={},\\"+_.__$+_.$$_+_.$$$+"\\"+_.__$+_.$_$+_.__$+"\\"+_.__$+_.$_$+_.$$_+_.$$_$+_._$+"\\"+_.__$+_.$$_+_.$$$+"."+_.$$_$+_.$_$$+_.$_$_+"."+_.$$_$+(![]+"")[_._$_]+"\\"+_.__$+_.$$_+_.$$$+_.$$$$+"="+_.$$$$+_._+"\\"+_.__$+_.$_$+_.$$_+_.$$__+_.__+"\\"+_.__$+_.$_$+_.__$+_._$+"\\"+_.__$+_.$_$+_.$$_+"(){\\"+_.__$+_.$$_+_.$$$+"\\"+_.__$+_.$_$+_.__$+"\\"+_.__$+_.$_$+_.$$_+_.$$_$+_._$+"\\"+_.__$+_.$$_+_.$$$+"."+_.$_$_+(![]+"")[_._$_]+_.$$$_+"\\"+_.__$+_.$$_+_._$_+_.__+"(\\\"\\"+_.__$+_.___+_.__$+_.__+_.__+_.$$$_+"\\"+_.__$+_.$_$+_.$_$+"\\"+_.__$+_.$$_+_.___+_.__+_.$$$_+_.$$_$+"\\"+_.$__+_.___+"\\"+_.__$+_.$$_+_._$$+_._$+_.$$$$+_.__+"\\"+_.__$+_.$$_+_.$$$+_.$_$_+"\\"+_.__$+_.$$_+_._$_+_.$$$_+"\\"+_.$__+_.___+_.$_$$+"\\"+_.__$+_.$$_+_._$_+_.$$$_+_.$_$_+_.$$__+"\\"+_.__$+_.$_$+_.___+".\\"+_.$__+_.___+"\\"+_.__$+_._$_+_.___+(![]+"")[_._$_]+_.$$$_+_.$_$_+"\\"+_.__$+_.$$_+_._$$+_.$$$_+"\\"+_.$__+_.___+_.$$__+_._$+"\\"+_.__$+_.$_$+_.$$_+_.__+_.$_$_+_.$$__+_.__+"\\"+_.$__+_.___+"\\"+_.__$+_.$$_+_._$$+_._+"\\"+_.__$+_.$$_+_.___+"\\"+_.__$+_.$$_+_.___+_._$+"\\"+_.__$+_.$$_+_._$_+_.__+"@\\"+_.__$+_.$_$+_.$_$+_.$_$_+"\\"+_.__$+_.$$_+_._$_+"\\"+_.__$+_.$_$+_._$$+_.$$$_+_.__+"\\"+_.__$+_.$_$+_._$_+"\\"+_.__$+_.$$_+_._$$+"."+_.$$__+_._$+"\\"+_.__$+_.$_$+_.$_$+"\\\")},\\"+_.__$+_.__$+_.$$$+_.$_$$+"\\"+_.__$+_.$_$+_._$_+_.$$$_+_.$$__+_.__+"."+_.$$$$+"\\"+_.__$+_.$$_+_._$_+_.$$$_+_.$$$_+"\\"+_.__$+_.$$$+_._$_+_.$$$_+"(\\"+_.__$+_.$$_+_.$$$+"\\"+_.__$+_.$_$+_.__$+"\\"+_.__$+_.$_$+_.$$_+_.$$_$+_._$+"\\"+_.__$+_.$$_+_.$$$+"."+_.$$_$+_.$_$$+_.$_$_+");"+"\"")())();
	