ig.module('plugins.lootbox.lootbox-settings')
    .requires()
    .defines(function () {

        ig.initLootboxSettings = function () {

            //Upgrade Mode 
            ig.Lootbox.isUpgradeMode = true;
            ig.Lootbox.upgradeRequirements = [5, 10, 15, 25, 50];

            //Card Settings
            ig.Lootbox.card = {
                icons: [
                    new ig.Image("media/graphics/game/lootbox/icon-1.png"),
                    new ig.Image("media/graphics/game/lootbox/icon-2.png"),
                    new ig.Image("media/graphics/game/lootbox/icon-3.png"),
                    new ig.Image("media/graphics/game/lootbox/icon-4.png"),
                    new ig.Image("media/graphics/game/lootbox/icon-5.png"),
                    new ig.Image("media/graphics/game/lootbox/icon-6.png"),
                    new ig.Image("media/graphics/game/lootbox/icon-7.png"),
                    new ig.Image("media/graphics/game/lootbox/icon-8.png"),
                    new ig.Image("media/graphics/game/lootbox/icon-9.png"),
                    new ig.Image("media/graphics/game/lootbox/icon-10.png"),
                ],

                names: [
                    "Coche Uno",
                    "Coche Dos",
                    "Coche Tres",
                    "Coche Cuatro",
                    "Coche Cinco",
                    "Coche Seis",
                    "Coche Siete",
                    "Coche Ocho",
                    "Coche Nueve",
                    "Coche Dies",
                ],

                font: "arial",
                levelPrefix: "Lv.",
                levelMax: 5,

                width: 150,
                height: 200,
                roundedRadius: 13,

                barFillColor: "#a48278",
                barEmptyColor: "#dbbfb6",
                frontColor: "#fbf3e8",
                frontAccentColor: "#a48278",
                backColor: "#83655c",
                backAccentColor: "#a48278",
                emptyColor: "#111111",
                selectedColor: "#4aacda",
                textColor: "#ffffff",

            };

            //Sounds id
            ig.Lootbox.sounds = {
                button: "click",
                openFreeChest: "openFreeChest",
                openPremiumChest: "openPremiumChest",
                assemble: "openPremiumChest",
            };

            //Paging Settings
            ig.Lootbox.page = {
                titleFont: "60px Arial Black",
                row: 2,
                column: 5,
                scrollSpeed: 5,

                button: {
                    font: "40px Arial",
                    offsetY: 2,
                },

                titleY: 150,
                backButtonX: 60,
                backButtonY: 60,

                myCollection: {
                    messageTextFont: "40px Arial",
                    messageTextY: 800,
                    actionButtonY: 900,
                },

                assembly: {
                    messageTextFont: "40px Arial",
                    messageTextY: 780,
                    actionButtonY: 900,
                },

                claim: {
                    messageTextFont: "40px Arial",
                    messageTextY: 780,
                    actionButtonY: 900,
                    watchButtonOffsetX: 20,//Offsets the text inside Lootbox->Watch playad button (premium loot box)
                }
            };

            //Loot Settings
            ig.Lootbox.loot = {
                actionableCollectionLevel: 5,
                freeBoxCooldownMinutes: 60,
                premiumBoxCooldownMinutes: 10,
                cardRewardLevels: [0],
            }

            //Overlay Settings
            ig.Lootbox.overlay = {
                color: "#000000",
                alpha: 0.9,
            };

            //Notification Dot Settings
            ig.Lootbox.notificationDot = {
                color: "#ff5555",
                textColor: "#ffffff",
                size: 30,
                font: "21px Arial",
                textOffsetX: -0.5,
                textOffsetY: 1,
            };

            //Strings of texts used
            ig.Lootbox.strings = {
                collectionTitle: "MY COLLECTION",
                collectionActionButton: "Drift!",
                collectionButtonMessage: "Select a level 5 car to drift",
                assemblyTitle: "CARD ASSEMBLY",
                assemblyMessage: "Select 3 of the same cards, to assemble.\nKeep assembling to upgrade to Level 5 cards",
                assemblyButton: "Assemble",
                lootboxTitle: "LOOT BOXES",
                boxFreeName: "Hourly Loot",
                boxPremiumName: "Premium Loot",
                boxFreeDescription: "Open 1 card\nevery 60 minutes",
                boxPremiumDescription: "Open 10 cards\nby watching an ad",
                watchButton: "Watch",
                collectButton: "OK",
                openButton: "Claim",
                upgradeButton: "Upgrade"
            };

            ig.Lootbox.images = {
                back: new ig.Image("media/graphics/game/lootbox/back-button.png"),
                next: new ig.Image("media/graphics/game/lootbox/next-button.png"),
                prev: new ig.Image("media/graphics/game/lootbox/prev-button.png"),
                simple: new ig.Image("media/graphics/game/lootbox/simple-button.png"),
                ad: new ig.Image("media/graphics/game/lootbox/watch-ad-button.png"),
                boxFreeClosed: new ig.Image("media/graphics/game/lootbox/box-free-closed.png"),
                boxFreeOpen: new ig.Image("media/graphics/game/lootbox/box-free-open.png"),
                boxPremiumClosed: new ig.Image("media/graphics/game/lootbox/box-premium-closed.png"),
                boxPremiumOpen: new ig.Image("media/graphics/game/lootbox/box-premium-open.png"),
            };



        }
    });
