ig.module(
    'dl.templates.network-entities.entity-popup-connecting'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.popup',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.animation-sheet',
    'dl.templates.mixins.text'
).defines(function () {
    'use strict';

    dl.EntityConnectingPopup = dl.Entity
        .extend(dl.MixinPopup)
        .extend(dl.MixinDocker)
        .extend({
            postInit: function () {
                this.parent();

                this.content = this.spawnEntity(dl.EntityConnectingPopup_Content, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    }
                });
            }
        });

    dl.EntityConnectingPopup_Content = dl.Entity
        .extend(dl.MixinDocker)
        .extend(dl.MixinAnimationSheet)
        .extend(dl.MixinText)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this.image = dl.preload['popup-background-small'];

                this.timer = null;
                this.SPEED_MULTIPLY = 1;
                // DEBUG
                if (dl.debug && dl.debug.SKIP_TIME) {
                    this.SPEED_MULTIPLY = 0.1;
                }

                this.TWEEN_IN_TIME = 400 * this.SPEED_MULTIPLY;
                this.TWEEN_OUT_TIME = 400 * this.SPEED_MULTIPLY;
                this.TIME_OUT_TIME = 5000 * this.SPEED_MULTIPLY;
                this.NOTIFY_TIME = 1000 * this.SPEED_MULTIPLY;

                this.STATES = {
                    INIT: 0,
                    CHECK: 1,
                    CONNECT: 2,
                    CONNECTING: 3,
                    TIME_OUT: 4,
                    CONNECTED: 5,
                    ERROR: 6,
                    CLOSE: 7
                };
                this.currentState = -1;

                return this;
            },

            postInit: function () {
                this.parent();

                this.switchState(this.STATES.INIT);

                this.tweenIn(function () {
                    this.switchState(this.STATES.CHECK);
                }.bind(this));
            },

            _initTextComponent: function () {
                this.c_TextComponent.updateProperties({
                    fontSize: 36,
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT')
                });
            },

            update: function () {
                this.parent();
                this.updateState();
            },

            checkConnection: function () {
                if (!dl.game) return false;
                if (!dl.game.networkClient) return false;

                return dl.game.networkClient.checkConnection();
            },

            connectToServer: function () {
                dl.game.networkClient.connect(
                    function () {
                        console.log('Request to connect success');
                        if (this.timer) this.timer.kill();
                        this.timer = new dl.GameTimer(this.TIME_OUT_TIME);
                    }.bind(this),
                    function () {
                        console.log('Request to connect fail');
                        this.switchState(this.STATES.ERROR);
                    }.bind(this)
                );
            },

            switchState: function (newState) {
                this.currentState = newState;
                switch (newState) {
                    case this.STATES.INIT:
                        this.updateText(_STRINGS.NETWORK.CONNECTING_POPUP_CHECKING);
                        break;
                    case this.STATES.CHECK:
                        if (!this.checkConnection()) {
                            this.switchState(this.STATES.CONNECT);
                        } else {
                            this.switchState(this.STATES.CLOSE);
                        }
                        break;
                    case this.STATES.CONNECT:
                        if (this.timer) this.timer.kill();
                        this.timer = new dl.GameTimer(this.NOTIFY_TIME);
                        this.updateText(_STRINGS.NETWORK.CONNECTING_POPUP_CONNECTING);
                        break;

                    case this.STATES.CONNECTING:
                        this.connectToServer();
                        this.updateText(_STRINGS.NETWORK.CONNECTING_POPUP_CONNECTING);
                        break;

                    case this.STATES.ERROR:
                        if (this.timer) this.timer.kill();
                        this.timer = new dl.GameTimer(this.NOTIFY_TIME);
                        this.updateText(_STRINGS.NETWORK.CONNECTING_POPUP_ERROR);
                        break;

                    case this.STATES.TIME_OUT:
                        if (this.timer) this.timer.kill();
                        this.timer = new dl.GameTimer(this.NOTIFY_TIME);
                        this.updateText(_STRINGS.NETWORK.CONNECTING_POPUP_TIME_OUT);
                        ig.game.svas.hideLoader();
                        ig.game.popupStatus = true;
                        dl.scene.spawnPopup(dl.EntityPopupSelectServer);
                        break;

                    case this.STATES.CONNECTED:
                        if (this.timer) this.timer.kill();
                        this.timer = new dl.GameTimer(this.NOTIFY_TIME);
                        this.updateText(_STRINGS.NETWORK.CONNECTING_POPUP_CONNECTED);
                        break;

                    case this.STATES.CLOSE:
                        this.tweenOut(function () {
                            // console.log('popup close');
                            ig.game.popupStatus = false;
                            ig.game.nameAvatar.nameInput.tweenIn(100);

                            this.kill();
                        }.bind(this.parentInstance));
                        break;
                }
            },

            updateState: function () {
                switch (this.currentState) {
                    case this.STATES.CONNECT:
                        if (!this.timer) return;

                        if (this.timer.delta() >= 0) {
                            this.switchState(this.STATES.CONNECTING);
                        }
                        break;

                    case this.STATES.CONNECTING:
                        if (!this.timer) return;

                        if (this.timer.delta() >= 0) {
                            this.timer = null;
                            this.switchState(this.STATES.TIME_OUT);
                        } else {
                            if (this.checkConnection()) {
                                this.switchState(this.STATES.CONNECTED);
                            }
                        }
                        break;

                    case this.STATES.ERROR:
                        if (!this.timer) return;

                        if (this.timer.delta() >= 0) {
                            this.switchState(this.STATES.CLOSE);
                        }
                        break;

                    case this.STATES.TIME_OUT:
                        if (!this.timer) return;

                        if (this.timer.delta() >= 0) {
                            this.switchState(this.STATES.CONNECT);
                        }
                        break;

                    case this.STATES.CONNECTED:
                        if (!this.timer) return;

                        if (this.timer.delta() >= 0) {
                            this.switchState(this.STATES.CLOSE);
                        }
                        break;
                }
            },

            tweenIn: function (callback) {
                dl.TweenTemplate.moveInTop(this, this.TWEEN_IN_TIME, callback);
            },

            tweenOut: function (callback) {
                dl.TweenTemplate.moveOutTop(this, this.TWEEN_OUT_TIME, callback);
            }
        });
});
