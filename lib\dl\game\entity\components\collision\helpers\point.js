ig.module(
    'dl.game.entity.components.collision.helpers.point'
).defines(function () {
    'use strict';

    dl.CollisionHelpers_Point = {
        /**
         * point-point collision
         * @param {number} x1 x of first point
         * @param {number} y1 y of first point
         * @param {number} x2 x of second point
         * @param {number} y2 y of second point
         * @returns {boolean}
         */
        point: function (x1, y1, x2, y2) {
            return x1 == x2 &&
                y1 == y2;
        },

        /**
         * point-rectangle collision
         * @param {number} px of point
         * @param {number} py of point
         * @param {number} rx top-left corner of rectangle
         * @param {number} ry top-left corner of rectangle
         * @param {number} rw width of rectangle
         * @param {number} rh height of rectangle
         * @return {boolean}
         */
        rectangle: function (px, py, rx, ry, rw, rh) {
            return (px > rx &&
                px < rx + rw &&
                py > ry &&
                py < ry + rh);
        },

        /**
         * point-circle collision
         * @param {number} px of point
         * @param {number} py of point
         * @param {number} cx center x of circle
         * @param {number} cy center y of circle
         * @param {number} cr radius of circle
         * @return {boolean}
         */
        circle: function (px, py, cx, cy, cr) {
            var tx = cx - px;
            var ty = cy - py;
            return tx * tx + ty * ty <= cr * cr;
        },

        /**
         * point-polygon collision
         * @param {number} px of point
         * @param {number} py of point
         * @param {array} polygon array of point({x:0,y:0}) in polygon
         * @returns
         */
        polygon: function (px, py, polygon) {
            var minX = polygon[0].x; var maxX = polygon[0].x;
            var minY = polygon[0].y; var maxY = polygon[0].y;
            for (var i = 1; i < polygon.length; i++) {
                var point = polygon[i];
                minX = Math.min(point.x, minX);
                maxX = Math.max(point.x, maxX);
                minY = Math.min(point.y, minY);
                maxY = Math.max(point.y, maxY);
            }

            if (px < minX || px > maxX || py < minY || py > maxY) {
                return false;
            }

            // https://wrf.ecse.rpi.edu/Research/Short_Notes/pnpoly.html
            var isInside = false;
            var i = 0;
            var j = polygon.length - 1;
            for (i, j; i < polygon.length; j = i++) {
                if ((polygon[i].y > py) != (polygon[j].y > py) &&
                    px < (polygon[j].x - polygon[i].x) * (py - polygon[i].y) / (polygon[j].y - polygon[i].y) + polygon[i].x) {
                    isInside = !isInside;
                }
            }

            return isInside;
        }
    };
});
