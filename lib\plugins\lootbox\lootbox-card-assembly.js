ig.module('plugins.lootbox.lootbox-card-assembly')
    .requires(
        'plugins.lootbox.lootbox-game-object'
    )
    .defines(function () {
        ig.LootboxCardAssembly = ig.LootboxGameObject.extend({

            zIndex: 9999,
            onBack: null,
            deck: null,
            title: null,
            actionButton: null,
            messageTextField: null,

            init: function (x, y, settings) {
                this.parent(x, y, settings);
                ig.Lootbox.loadData();
                this.forceDraw = true;
                this.onAction = new ig.LootboxSignal();
                this.onBack = new ig.LootboxSignal();

                ig.Lootbox.sortData();


                var pageW = ig.responsive ? ig.responsive.originalWidth : ig.system.width;
                var pageH = ig.responsive ? ig.responsive.originalHeight : ig.system.height;
                this.createDeck()

                this.title = ig.game.spawnEntityBackward(ig.LootboxTextField, pageW / 2, ig.Lootbox.page.titleY, { font: ig.Lootbox.page.titleFont, text: ig.Lootbox.strings.assemblyTitle, align: "center", color: "#ffffff", zIndex: 99999 });

                this.actionButton = ig.game.spawnEntityBackward(ig.LootboxSimpleButton, pageW / 2, ig.Lootbox.page.assembly.actionButtonY, {
                    image: ig.Lootbox.images.simple,
                    font: ig.Lootbox.page.button.font,
                    textColor: ig.Lootbox.page.button.textColor ? ig.Lootbox.page.button.textColor : "#ffffff",
                    offsetY: ig.Lootbox.page.button.offsetY,
                    text: ig.Lootbox.strings.assemblyButton,
                    zIndex: 99999
                });
                this.actionButton.onClicked.add(this.onClickAction, this)

                this.backButton = ig.game.spawnEntityBackward(ig.LootboxSimpleButton, ig.Lootbox.page.backButtonX, ig.Lootbox.page.backButtonY, { image: ig.Lootbox.images.back, zIndex: 99999 });
                this.backButton.onClicked.addOnce(this.onClickBack, this)

                this.messageTextField = ig.game.spawnEntityBackward(ig.LootboxTextField, pageW / 2, ig.Lootbox.page.assembly.messageTextY, { font: ig.Lootbox.page.assembly.messageTextFont, text: ig.Lootbox.strings.assemblyMessage, align: "center", color: "#ffffff", zIndex: 99999 });

                if (ig.responsive) {
                    this.backButton.anchorType = "top-left"
                }

                this.actionButton.visible = false;
                ig.game.sortEntitiesDeferred()
            },

            createDeck: function () {
                var collectionData = [];
                var count = {};

                for (var i = 0; i < ig.Lootbox.data.cards.length; i++) {
                    var cardData = ig.Lootbox.data.cards[i];
                    collectionData.push({ id: cardData.id, level: cardData.level });

                    var key = cardData.id + "-" + cardData.level;
                    if (!count[key]) count[key] = 0;
                    count[key]++;
                }

                for (var i = 0; i < collectionData.length; i++) {
                    var card = collectionData[i];
                    var key = card.id + "-" + card.level;
                    if (Object.hasOwnProperty.call(count, key) && count[key] >= 3) card.isMergeable = true;
                }

                console.log(collectionData);

                collectionData.sort(function (a, b) {
                    if (a.isMergeable == b.isMergeable) return 0;
                    if (a.isMergeable) return -1;
                    if (b.isMergeable) return 1;
                    else return 0;
                });

                console.log(collectionData);




                this.deck = new ig.LootboxDeckDisplay(collectionData);
                this.deck.maxSelection = 3;
                this.deck.isSelectable = true;
                this.deck.onCardSelected.add(this.onClickCard, this);
            },

            onClickCard: function () {
                var cards = this.deck.getSelectedCards();
                if (cards.length < 3) {
                    this.actionButton.visible = false;
                } else {
                    if (
                        cards[0].level == cards[1].level && cards[0].level == cards[2].level &&
                        cards[0].id == cards[1].id && cards[0].id == cards[2].id
                    ) {
                        if (cards[0].level < ig.Lootbox.card.levelMax);
                        this.actionButton.visible = true;
                    }

                }
                ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList[ig.Lootbox.sounds.button]);
            },

            onClickAction: function () {
                this.backButton.inputEnabled = false;
                this.backButton.visible = false;

                var selectedCards = this.deck.getSelectedCards();

                if (selectedCards.length < 3) return;

                this.tweenedCards = [];

                this.deck.isSelectable = false;

                for (var i = 0; i < 100; i++) {
                    if (ig.responsive) ig.game.spawnEntityBackward(ig.LootboxParticleIn, 0, 0, { anchorType: "center", delay: i * 0.006 })
                    else ig.game.spawnEntityBackward(ig.LootboxParticleIn, ig.system.width * 0.5, ig.system.height * 0.5, { delay: i * 0.006 })
                }

                for (var i = 0; i < selectedCards.length; i++) {
                    var selectedCard = selectedCards[i];
                    selectedCard.isSelected = false;

                    this.mergedId = selectedCard.id;
                    this.mergedLevel = selectedCard.level;

                    var cardX = selectedCard.pos.x;
                    var cardY = selectedCard.pos.y;

                    selectedCard.id = -1;

                    if (ig.responsive) {
                        cardX = selectedCard.anchoredPositionX;
                        cardY = selectedCard.anchoredPositionY;
                    }

                    var card = ig.game.spawnEntityBackward(ig.LootboxCard, cardX, cardY, { id: this.mergedId, level: this.mergedLevel, zIndex: 999999, isSelected: true });
                    card.shake(3, 2)
                    this.tweenedCards.push(card);
                    if (ig.responsive) {
                        var targetX = ig.responsive.originalWidth * 0.25 + i * ig.responsive.originalWidth * 0.25;
                        var tween1 = card.tween({ anchorX: 0.5, anchorY: 0.5, scaleX: 1.4, scaleY: 1.4, anchoredPositionX: targetX, anchoredPositionY: ig.system.height * 0.5 }, 0.3, { easing: ig.Tween.Easing.Quadratic.EaseOut });
                        var tween2 = card.tween({ scaleX: 0.3, scaleY: 0.3, anchoredPositionX: ig.responsive.originalWidth * 0.5, anchoredPositionY: ig.responsive.originalHeight * 0.5 }, 0.4, { delay: 0.4, easing: ig.Tween.Easing.Back.EaseIn, onComplete: this.spawnNewCard.bind(this) });
                        tween1.chain(tween2);
                        tween1.start();
                    } else {
                        var targetX = ig.system.width * 0.25 + i * ig.system.width * 0.25;
                        var tween1 = card.tween({ anchorX: 0.5, anchorY: 0.5, scaleX: 1.4, scaleY: 1.4, pos: { x: targetX, y: ig.system.height * 0.5 } }, 0.3, { easing: ig.Tween.Easing.Quadratic.EaseOut });
                        var tween2 = card.tween({ scaleX: 0.3, scaleY: 0.3, pos: { x: ig.system.width * 0.5, y: ig.system.height * 0.5 } }, 0.4, { delay: 0.4, easing: ig.Tween.Easing.Back.EaseIn, onComplete: this.spawnNewCard.bind(this) });
                        tween1.chain(tween2);
                        tween1.start();
                    }
                }

                this.mergedX = selectedCards[0].pos.x;
                this.mergedY = selectedCards[0].pos.y;
                if (ig.responsive) {
                    this.mergedX = selectedCards[0].anchoredPositionX;
                    this.mergedY = selectedCards[0].anchoredPositionY;
                }
                this.actionButton.visible = false;

                ig.game.sortEntitiesDeferred();
                ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList[ig.Lootbox.sounds.button]);
            },

            spawnNewCard: function () {
                if (!this.tweenedCards) return;

                ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList[ig.Lootbox.sounds.assemble]);
                for (var i = 0; i < this.tweenedCards.length; i++) {
                    var tweenedCard = this.tweenedCards[i];
                    tweenedCard.exit();
                }
                var cardX = ig.system.width / 2;
                var cardY = ig.system.height / 2;
                if (ig.responsive) {
                    cardX = ig.responsive.originalWidth / 2;
                    cardY = ig.responsive.originalHeight / 2;
                }
                var card = ig.game.spawnEntityBackward(ig.LootboxCard, cardX, cardY, { id: this.mergedId, level: this.mergedLevel + 1, zIndex: 999999, isSelected: true, anchorX: 0.5, anchorY: 0.5, scaleX: 0.3, scaleY: 0.3 });

                card.shake(5, 5)
                for (var i = 0; i < 100; i++) {
                    if (ig.responsive) ig.game.spawnEntityBackward(ig.LootboxParticleOut, 0, 0, { anchorType: "center", delay: i * 0.002 })
                    else ig.game.spawnEntityBackward(ig.LootboxParticleOut, ig.system.width * 0.5, ig.system.height * 0.5, { delay: i * 0.002 })
                }

                var tween1 = card.tween({ scaleX: 2, scaleY: 2 }, 0.3, { easing: ig.Tween.Easing.Quadratic.EaseOut });

                var tween2;
                if (ig.responsive) {
                    tween2 = card.tween({ scaleX: 1, scaleY: 1, anchorX: 0, anchorY: 0, anchoredPositionX: this.mergedX, anchoredPositionY: this.mergedY }, 0.3, { delay: 1, easing: ig.Tween.Easing.Quadratic.EaseOut });
                } else {
                    tween2 = card.tween({ scaleX: 1, scaleY: 1, anchorX: 0, anchorY: 0, pos: { x: this.mergedX, y: this.mergedY } }, 0.3, { delay: 1, easing: ig.Tween.Easing.Quadratic.EaseOut });
                }

                var tween3 = card.tween({}, 0.1, {
                    onComplete: function () {
                        card.exit();
                        this.deck.exit();
                        this.createDeck();
                        this.backButton.inputEnabled = true;
                        this.backButton.visible = true;
                    }.bind(this)
                })

                tween1.chain(tween2);
                tween2.chain(tween3);
                tween1.start();

                var deleteCount = 0;
                for (var i = 0; i < ig.Lootbox.data.cards.length; i++) {
                    var cardData = ig.Lootbox.data.cards[i];
                    if (cardData.id == this.mergedId && cardData.level == this.mergedLevel && deleteCount < 3) {
                        deleteCount++;
                        ig.Lootbox.data.cards.splice(i, 1);
                        i--;
                    }
                }
                ig.Lootbox.data.cards.push({ id: this.mergedId, level: this.mergedLevel + 1 });
                ig.Lootbox.sortData();
                ig.Lootbox.saveData();
                this.tweenedCards = null;
            },

            onClickBack: function () {
                this.onBack.dispatch();
                this.exitAll();
                ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList[ig.Lootbox.sounds.button]);
            },

            exitAll: function () {

                this.deck.exit();
                this.backButton.exit();
                this.actionButton.exit();
                this.title.exit();
                this.messageTextField.exit();
                this.exit();
            },

            update: function () {
                this.parent();
                if (this.deck) {
                    var count = {};
                    for (var i = 0; i < ig.Lootbox.data.cards.length; i++) {
                        var card = ig.Lootbox.data.cards[i];
                        var key = card.id + "-" + card.level;
                        if (!count[key]) count[key] = 0;
                        count[key]++;
                    }

                    for (var i = 0; i < this.deck.cards.length; i++) {
                        var card = this.deck.cards[i];
                        var key = card.id + "-" + card.level;
                        if (Object.hasOwnProperty.call(count, key) && count[key] >= 3) card.isMergeable = true;
                        else card.isMergeable = false;
                    }

                    this.deck.update()
                }
            },

            draw: function () {

                var ctx = ig.system.context;
                ctx.save();
                ctx.fillStyle = ig.Lootbox.overlay.color;
                ctx.globalAlpha = ig.Lootbox.overlay.alpha;
                ctx.fillRect(0, 0, ig.system.width, ig.system.height);
                ctx.globalAlpha = 1;
                ctx.restore();

                this.parent();
            },

        });
    });
