// Create Namespace
dl.Notification.Factory = {};

ig.module(
    'dl-plugins.notification.notification-factory'
).requires(
    'dl-plugins.notification.factory.background',
    'dl-plugins.notification.factory.image',
    'dl-plugins.notification.factory.text'
).defines(function () {
    'use strict';

    dl.Notification.FactoryManager = {
        deepCopyAndMergerConfigs: function (baseConfigs, targetConfigs) {
            var configs = JSON.parse(JSON.stringify(baseConfigs));

            ig.merge(configs, targetConfigs);
            return configs;
        },

        generateCanvas: function (configs) {
            var canvasData = {
                configs: configs,
                canvas: null
            };

            // content
            if (canvasData.configs && canvasData.configs.contentConfigs) {
                for (var i = 0; i < canvasData.configs.contentConfigs.length; i++) {
                    var contentConfig = canvasData.configs.contentConfigs[i];

                    var newCanvasData = null;
                    switch (contentConfig.type) {
                        case 'image': {
                            newCanvasData = this.generateImage(contentConfig, canvasData.canvas);
                        } break;

                        case 'text': {
                            newCanvasData = this.generateText(contentConfig, canvasData.canvas);
                        } break;

                        default: { } break;
                    }

                    if (newCanvasData) {
                        canvasData.configs.contentConfigs[i] = newCanvasData.configs;
                        canvasData.canvas = newCanvasData.canvas;
                    }
                }
            }

            // background
            var newCanvasData = this.generateBackground(canvasData.configs.backgroundConfigs, canvasData.canvas);
            canvasData.configs.backgroundConfigs = newCanvasData.configs;
            canvasData.canvas = newCanvasData.canvas;

            return canvasData;
        },

        generateImage: function (configs, contentCanvas) {
            var imageConfigs = this.deepCopyAndMergerConfigs(dl.Notification.Factory.Image.DEFAULT_CONFIGS, configs);
            return dl.Notification.Factory.Image.generate(imageConfigs, contentCanvas);
        },

        generateText: function (configs, contentCanvas) {
            var textConfigs = this.deepCopyAndMergerConfigs(dl.Notification.Factory.Text.DEFAULT_CONFIGS, configs);
            return dl.Notification.Factory.Text.generate(textConfigs, contentCanvas);
        },

        generateBackground: function (configs, contentCanvas) {
            if (!configs) configs = {};

            var backgroundConfigs = this.deepCopyAndMergerConfigs(dl.Notification.Factory.Background.DEFAULT_CONFIGS, configs);
            return dl.Notification.Factory.Background.generate(backgroundConfigs, contentCanvas);
        }
    };
});
