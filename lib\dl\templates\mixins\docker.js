ig.module(
    'dl.templates.mixins.docker'
).requires(
    'dl.game.entity.components.position.docker'
).defines(function () {
    'use strict';

    dl.MixinDocker = {
        initComponents: function () {
            this.parent();
            this._initDockerComponent();
        },

        _initDockerComponent: function () {
            this.c_DockerComponent = this.addComponent(dl.DockerComponent, {
                dockerObject: dl.game.camera,
                dockerPercent: { x: 0.5, y: 0.5 },
                dockerOffset: { x: 0, y: 0 }
            });
        }
    };
});
