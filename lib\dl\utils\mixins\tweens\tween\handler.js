'use strict';

function _createForOfIteratorHelper (o, allowArrayLike) { var it = typeof Symbol !== 'undefined' && o[Symbol.iterator] || o['@@iterator']; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === 'number') { if (it) o = it; var i = 0; var F = function F () {}; return { s: F, n: function n () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e (_e) { throw _e; }, f: F }; } throw new TypeError('Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.'); } var normalCompletion = true; var didErr = false; var err; return { s: function s () { it = it.call(o); }, n: function n () { var step = it.next(); normalCompletion = step.done; return step; }, e: function e (_e2) { didErr = true; err = _e2; }, f: function f () { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }

function _unsupportedIterableToArray (o, minLen) { if (!o) return; if (typeof o === 'string') return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === 'Object' && o.constructor) n = o.constructor.name; if (n === 'Map' || n === 'Set') return Array.from(o); if (n === 'Arguments' || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray (arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }

function _classCallCheck (instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError('Cannot call a class as a function'); } }

function _defineProperties (target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ('value' in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }

function _createClass (Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, 'prototype', { writable: false }); return Constructor; }

ig.module('dl.utils.mixins.tweens.tween.handler').requires('dl.utils.mixins.tweens.tween.action').defines(function () {
  'use strict';

  dl.TweenHandler = /* #__PURE__ */(function () {
    function dl_TweenHandler (object) {
      _classCallCheck(this, dl_TweenHandler);

      this._object = object;
      this._tweenActions = [];
      this.__started = true;
      this.__completed = true;
      this.repeat = false;
      this.onCompleteTween = false;
    }

    _createClass(dl_TweenHandler, [{
      key: '_processSettings',
      value: function _processSettings (settings) {
        if (!settings) return;
        this.repeat = settings.repeat;
        this.onCompleteTween = settings.onCompleteTween;
      }
    }, {
      key: 'update',
      value: function update () {
        if (!this.__started) return false;
        if (this.__completed) return false;
        this.__completed = true;

        var _iterator = _createForOfIteratorHelper(this._tweenActions);
            var _step;

        try {
          for (_iterator.s(); !(_step = _iterator.n()).done;) {
            var tweenAction = _step.value;
            tweenAction.update();

            if (!tweenAction.isCompleted()) {
              this.__completed = false;
            }
          }
        } catch (err) {
          _iterator.e(err);
        } finally {
          _iterator.f();
        }

        if (this.__completed) {
          if (dl.check.isFunction(this.onCompleteTween)) {
            this.onCompleteTween();
          }

          if (this.repeat) {
            if (this.__isGlobal) {
              this.startGlobal();
            } else {
              this.start();
            }
          }
        }
      }
    }, {
      key: 'to',
      value: function to (properties, duration, settings) {
        if (!dl.check.isDefined(settings)) {
          settings = {};
        }

        var tweenAction = new dl.TweenAction(this._object, properties, duration, settings);

        this._tweenActions.push(tweenAction);

        return this;
      }
    }, {
      key: 'start',
      value: function start (settings) {
        this._processSettings(settings);

        this.__completed = false;
        this.__started = true;
        var duration = 0;

        var _iterator2 = _createForOfIteratorHelper(this._tweenActions);
            var _step2;

        try {
          for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
            var tweenAction = _step2.value;
            duration += tweenAction._duration;
            var newTimer = new dl.GameTimer(duration);
            tweenAction.start(newTimer);
          }
        } catch (err) {
          _iterator2.e(err);
        } finally {
          _iterator2.f();
        }
      }
    }, {
      key: 'startGlobal',
      value: function startGlobal (settings, startTime) {
        this._processSettings(settings);

        this.__completed = false;
        this.__started = true;
        this.__isGlobal = true;

        if (!dl.check.isDefined(startTime)) {
          startTime = Date.now();
        }

        var _iterator3 = _createForOfIteratorHelper(this._tweenActions);
            var _step3;

        try {
          for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {
            var tweenAction = _step3.value;
            var newTimer = new dl.GlobalTimer(startTime, tweenAction._duration);
            tweenAction.start(newTimer);
            startTime += tweenAction._duration;
          }
        } catch (err) {
          _iterator3.e(err);
        } finally {
          _iterator3.f();
        }
      }
    }, {
      key: 'pause',
      value: function pause () {
        if (!this.__started) return false;
        if (this.__completed) return false;

        var _iterator4 = _createForOfIteratorHelper(this._tweenActions);
            var _step4;

        try {
          for (_iterator4.s(); !(_step4 = _iterator4.n()).done;) {
            var tweenAction = _step4.value;
            tweenAction.pause();
          }
        } catch (err) {
          _iterator4.e(err);
        } finally {
          _iterator4.f();
        }
      }
    }, {
      key: 'resume',
      value: function resume () {
        if (!this.__started) return false;
        if (this.__completed) return false;

        var _iterator5 = _createForOfIteratorHelper(this._tweenActions);
            var _step5;

        try {
          for (_iterator5.s(); !(_step5 = _iterator5.n()).done;) {
            var tweenAction = _step5.value;
            tweenAction.resume();
          }
        } catch (err) {
          _iterator5.e(err);
        } finally {
          _iterator5.f();
        }
      }
    }, {
      key: 'stop',
      value: function stop (doCallback) {
        if (!this.__started) return false;
        if (this.__completed) return false;

        var _iterator6 = _createForOfIteratorHelper(this._tweenActions);
            var _step6;

        try {
          for (_iterator6.s(); !(_step6 = _iterator6.n()).done;) {
            var tweenAction = _step6.value;
            tweenAction.stop(doCallback);
          }
        } catch (err) {
          _iterator6.e(err);
        } finally {
          _iterator6.f();
        }

        if (dl.check.isFunction(this.onCompleteTween)) {
          if (doCallback) {
            this.onCompleteTween();
          }
        }
      }
    }, {
      key: 'finish',
      value: function finish (doCallback) {
        if (!this.__started) return false;
        if (this.__completed) return false;

        var _iterator7 = _createForOfIteratorHelper(this._tweenActions);
            var _step7;

        try {
          for (_iterator7.s(); !(_step7 = _iterator7.n()).done;) {
            var tweenAction = _step7.value;
            tweenAction.finish(doCallback);
          }
        } catch (err) {
          _iterator7.e(err);
        } finally {
          _iterator7.f();
        }

        this.__completed = true;

        if (dl.check.isFunction(this.onCompleteTween)) {
          if (doCallback) {
            this.onCompleteTween();
          }
        }
      }
    }, {
      key: 'isCompleted',
      value: function isCompleted () {
        if (!this.__started) return false;
        return this.__completed;
      }
    }]);

    return dl_TweenHandler;
  }());
});
