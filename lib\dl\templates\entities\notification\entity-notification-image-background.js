ig.module(
    'dl.templates.entities.notification.entity-notification-image-background'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.animation-sheet',
    'dl.templates.mixins.text'
).defines(function () {
    'use strict';

    dl.EntityNotification = dl.Entity
        .extend(dl.MixinDocker)
        .extend(dl.MixinAnimationSheet)
        .extend(dl.MixinText)
        .extend({
            staticInstantiate: function () {
                this.parent();

                // this.image = dl.preload['notification-background'];
                // this._useAnimationSheetAsSize = true;

                this.text = 'Notification Text';
                this.size = { x: 1290, y: 90 };
                this.notificationIndex = 0;
                this.setSize(this.size.x, this.size.y);
                dl.system.onEvent('sizeChanged', this.setSize.bind(this));

                this.init.apply(this, arguments);

                return this;
            },

            /**
             * to use with base impact
             */
            ready: function () {
                this.postInit();
            },

            postInit: function () {
                this.parent();

                this.tweenIn();
            },

            updateNotificationIndex: function (newIndex) {
                if (newIndex == this.notificationIndex) return;

                this.notificationIndex = newIndex;

                this.tweenUp();
            },

            tweenIn: function () {
                this._alpha = 0;
                this.updateAlpha();

                this._scale = { x: 0.25, y: 0.25 };
                this.updateAlpha();

                this.createTween()
                    // .to({}, dl.EntityNotification.MOVE_UP_TIME, {})
                    .to({
                        _alpha: 1,
                        _scale: { x: 1, y: 1 }
                    }, dl.EntityNotification.APPEAR_TIME, {
                        easing: dl.TweenEasing.Quintic.EaseOut,
                        onPropertiesChanged: function () {
                            this.updateAlpha();
                            this.updateScale();
                        }.bind(this)
                    })
                    .start({
                        onCompleteTween: function () {
                            this.tweenAlive();
                        }.bind(this)
                    });
            },

            tweenAlive: function () {
                this.createTween()
                    .to({}, dl.EntityNotification.ALIVE_TIME, {})
                    .start({
                        onCompleteTween: function () {
                            this.tweenOut();
                        }.bind(this)
                    });
            },

            tweenOut: function () {
                this.createTween()
                    .to({
                        _alpha: 0
                    }, dl.EntityNotification.DISAPPEAR_TIME, {
                        onPropertiesChanged: function () {
                            this.updateAlpha();
                        }.bind(this)
                    })
                    .start({
                        onCompleteTween: function () {
                            this.kill();
                        }.bind(this)
                    });
            },

            tweenUp: function () {
                if (this.tweenUpInstance) {
                    this.tweenUpInstance.finish();
                    this.tweenUpInstance = null;
                }

                this.tweenUpInstance = this.createTween()
                    .to({
                        c_DockerComponent: {
                            dockerOffset: { x: 0, y: this.notificationIndex * -dl.EntityNotification.OFFSET_Y }
                        }
                    }, dl.EntityNotification.MOVE_UP_TIME, {
                        onPropertiesChanged: function () {
                            this.updatePos();
                        }.bind(this)
                    })
                    .start({
                        onCompleteTween: function () {
                        }.bind(this)
                    });
            },

            roundRect: function (x, y, width, height, radius, fill, stroke) {
                var ctx = ig.system.context;

                if (typeof stroke === 'undefined') {
                    stroke = true;
                }
                if (typeof radius === 'undefined') {
                    radius = 5;
                }
                if (typeof radius === 'number') {
                    radius = { tl: radius, tr: radius, br: radius, bl: radius };
                } else {
                    var defaultRadius = { tl: 0, tr: 0, br: 0, bl: 0 };
                    for (var side in defaultRadius) {
                        radius[side] = radius[side] || defaultRadius[side];
                    }
                }
                ctx.beginPath();
                ctx.moveTo(x + radius.tl, y);
                ctx.lineTo(x + width - radius.tr, y);
                ctx.quadraticCurveTo(x + width, y, x + width, y + radius.tr);
                ctx.lineTo(x + width, y + height - radius.br);
                ctx.quadraticCurveTo(x + width, y + height, x + width - radius.br, y + height);
                ctx.lineTo(x + radius.bl, y + height);
                ctx.quadraticCurveTo(x, y + height, x, y + height - radius.bl);
                ctx.lineTo(x, y + radius.tl);
                ctx.quadraticCurveTo(x, y, x + radius.tl, y);
                ctx.closePath();
                if (fill) {
                    ctx.fill();
                }
                if (stroke) {
                    ctx.stroke();
                }
            },

            draw: function (ctx) {
                ctx.save();
                ctx.globalAlpha = 0.5;
                ctx.fillStyle = this.bgColor;
                this.roundRect(this.pos.x - this.size.x * 0.5, this.pos.y - this.size.y * 0.5, this.size.x, this.size.y, 10, true, false);
                ctx.restore();
                this.parent(ctx);
            }
        });

    dl.EntityNotification.OFFSET_Y = 110;
    dl.EntityNotification.APPEAR_TIME = 400;
    dl.EntityNotification.ALIVE_TIME = 2000;
    dl.EntityNotification.DISAPPEAR_TIME = 250;
    dl.EntityNotification.MOVE_UP_TIME = 100;
});
