ig.module(
    'dl.game.entity.components.div-layers.text-input-layer'
).requires(
    'dl.game.entity.components.component',
    'dl.game.entity.components.div-layers.size-handler-extend'
).defines(function () {
    'use strict';

    dl.InputTextComponent = dl.EntityComponent.extend({
        staticInstantiate: function (entity) {
            this.parent(entity);
            this._componentName = 'dl_InputTextComponent';

            // position and size
            this.size = { x: 16, y: 16 };
            this.pos = { x: 0, y: 0 };

            // div properties
            this.containerElement = null;
            this.textInputElement = null;
            this.divLayerName = 'InputTextLayerContainer';
            this.divInputLayerName = 'InputTextLayer';
            this.fontSize = 16;
            this.fontFamily = 'Arial';
            this.text = 'MMMMMMMMMMMM';
            this.textLength = 10;

            // bind events
            this.entity.onEvent('positionChanged', this.updateAll.bind(this));

            return this;
        },

        init: function (settings) {
            this.parent(settings);

            this.containerElement = ig.domHandler.getElementById('#' + this.divLayerName);
            this.textInputElement = ig.domHandler.getElementById('#' + this.divInputLayerName);
            if (this.containerElement &&
                this.textInputElement
            ) {
                ig.domHandler.show(this.containerElement);
                ig.domHandler.show(this.textInputElement);
            } else {
                this.createTextInput();
            }

            // clear text
            this.textInputElement.val(this.text);
            // bind event
            this.textInputElement.change(this.onTextChanged.bind(this));
        },

        updateProperties: function (properties) {
            this.parent(properties);

            // update div text
            if (properties.text) {
                if (this.textInputElement) {
                    this.textInputElement.val(properties.text);
                }
            }
        },

        update: function () {
            this.updateState();
            this.parent();
        },

        updateAll: function () {
            this.updateSize();
            this.updatePos();

            ig.sizeHandler.dynamicClickableEntityDivs[this.divLayerName] = {};
            ig.sizeHandler.dynamicClickableEntityDivs[this.divLayerName].width = this.size.x;
            ig.sizeHandler.dynamicClickableEntityDivs[this.divLayerName].height = this.size.y;
            ig.sizeHandler.dynamicClickableEntityDivs[this.divLayerName].entity_pos_x = this.pos.x - this.size.x * 0.5;
            ig.sizeHandler.dynamicClickableEntityDivs[this.divLayerName].entity_pos_y = this.pos.y - this.size.y * 0.5;

            ig.sizeHandler.dynamicClickableEntityDivs[this.divInputLayerName] = {};
            ig.sizeHandler.dynamicClickableEntityDivs[this.divInputLayerName]['font-size'] = this.fontSize;
            ig.sizeHandler.resizeLayersDiv();
        },

        updateSize: function () {
            var screeSize = dl.game.camera.getScreenSizeFromGameSize(this.entity.size.x, this.entity.size.y);
            this.size.x = Math.round(screeSize.x);
            this.size.y = Math.round(screeSize.y);
        },

        updatePos: function () {
            var screenPosition = dl.game.camera.getScreenPositionFromGamePosition(this.entity.pos.x, this.entity.pos.y);
            this.pos.x = Math.round(screenPosition.x);
            this.pos.y = Math.round(screenPosition.y);
        },

        createTextInput: function () {
            var div = ig.domHandler.create('div');
            ig.domHandler.attr(div, 'id', this.divLayerName);
            this.containerElement = div;

            var inputDiv = ig.domHandler.create('input');
            ig.domHandler.attr(inputDiv, 'id', this.divInputLayerName);
            inputDiv.blur(function (event) {
                event.stopPropagation();
            });
            this.textInputElement = inputDiv;

            var aspectRatioMin = Math.min(ig.sizeHandler.scaleRatioMultiplier.x, ig.sizeHandler.scaleRatioMultiplier.y);
            var fontSize = this.fontSize * aspectRatioMin;

            var canvas = ig.domHandler.getElementById('#canvas');
            var offsets = ig.domHandler.getOffsets(canvas);
            var offsetLeft = offsets.left;
            var offsetTop = offsets.top;

            var divleft = Math.floor(offsetLeft + this.pos.x * ig.sizeHandler.scaleRatioMultiplier.x) + 'px';
            var divtop = Math.floor(offsetTop + this.pos.y * ig.sizeHandler.scaleRatioMultiplier.y) + 'px';
            var divwidth = Math.floor(this.size.x * ig.sizeHandler.scaleRatioMultiplier.x) + 'px';
            var divheight = Math.floor(this.size.y * ig.sizeHandler.scaleRatioMultiplier.y) + 'px';

            ig.domHandler.css(div
                , {
                    float: 'left',
                     position: 'absolute',
                     left: divleft,
                     top: divtop,
                     width: divwidth,
                     height: divheight,
                     'z-index': 3
                }
            );

            ig.domHandler.css(inputDiv, {
                width: '100%',
                 height: '100%',
                //, margin:"0px"
                //, border:"0px"
                //, padding:"0px"
                 background: 'rgba(0,0,0,0)',
                 border: 'none',
                 outline: 'none',
                //, color:"rgba(0,0,0,0)"
                //, opacity:"0.00"
                 'text-align': 'center',
                 'font-family': this.fontFamily,
                 'font-size': fontSize + 'px',
                 'z-index': 3
            });

            inputDiv.attr('maxlength', this.textLength);
            inputDiv.attr('size', this.textLength);
            inputDiv.attr('autocomplete', 'off');
            inputDiv.attr('autocapitalize', 'off');
            inputDiv.attr('autocorrect', 'off');
            inputDiv.val(this.text);

            inputDiv.keyup(function (e) {
                if (e.keyCode == 13) {
                    this.textInputElement.blur();
                }
            }.bind(this));

            ig.domHandler.addEvent(div, 'mousemove', ig.input.mousemove.bind(ig.input), false);
            ig.domHandler.appendChild(div, inputDiv);
            ig.domHandler.appendToBody(div);

            this.updateAll();
        },

        updateState: function () {
            if (ig.input.released('click')) {
                this.textInputElement.blur();
            }
        },

        showDiv: function () {
            if (this.containerElement &&
                this.textInputElement
            ) {
                ig.domHandler.show(this.containerElement);
                ig.domHandler.show(this.textInputElement);
            }
        },

        hideDiv: function () {
            // console.log('hideDiv');
            if (this.containerElement &&
                this.textInputElement
            ) {
                ig.domHandler.hide(this.containerElement);
                ig.domHandler.hide(this.textInputElement);
            }
        },

        onTextChanged: function (event) {
            this.text = event.target.value;
            this.triggerEvent('textChanged', this.text);
        }
    });
});
