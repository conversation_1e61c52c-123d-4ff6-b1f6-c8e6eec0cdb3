ig.module(
    'dl.game.entity.components.collision.helpers.rectangle'
).requires(
    'dl.game.entity.components.collision.helpers.point',
    'dl.game.entity.components.collision.helpers.circle',
    'dl.game.entity.components.collision.helpers.polygon'
).defines(function () {
    'use strict';

    dl.CollisionHelpers_Rectangle = {
        /**
         * rectangle-point collision
         * @param {number} x top-left corner of rectangle
         * @param {number} y top-left corner of rectangle
         * @param {number} w width of rectangle
         * @param {number} h height of rectangle
         * @param {number} px of point
         * @param {number} py of point
         * @return {boolean}
         */
        point: function (x, y, w, h, px, py) {
            return dl.CollisionHelpers_Point.rectangle(px, py, x, y, w, h);
        },

        /**
         * rectangle-rectangle collision
         * @param {number} r1x top-left corner of rectangle 1
         * @param {number} r1y top-left corner of rectangle 1
         * @param {number} r1w width of rectangle 1
         * @param {number} r1h height of rectangle 1
         * @param {number} r2x top-left corner of rectangle 2
         * @param {number} r2y top-left corner of rectangle 2
         * @param {number} r2w width of rectangle 2
         * @param {number} r2h height of rectangle 2
         * @return {boolean}
         */
        rectangle: function (r1x, r1y, r1w, r1h, r2x, r2y, r2w, r2h) {
            return r1x <= r2x + r2w &&
                r2x <= r1x + r1w &&
                r1y <= r2y + r2h &&
                r2y <= r1y + r1h;
        },

        /**
         * rectangle-circle collision
         * @param {number} x top-left corner of rectangle
         * @param {number} y top-left corner of rectangle
         * @param {number} w width of rectangle
         * @param {number} h height of rectangle
         * @param {number} cx center x of circle
         * @param {number} cy center y of circle
         * @param {number} cr radius of circle
         * @return {boolean}
         */
        circle: function (x, y, w, h, cx, cy, cr) {
            return dl.CollisionHelpers_Circle.rectangle(cx, cy, cr, x, y, w, h);
        },

        /**
         * rectangle-polygon collision
         * @param {number} x top-left corner of rectangle
         * @param {number} y top-left corner of rectangle
         * @param {number} w width of rectangle
         * @param {number} h height of rectangle
         * @param {array} polygon array of point({x:0,y:0}) in polygon
         * @return {boolean}
         */
        polygon: function (x, y, w, h, polygon) {
            var rectPolygon = [
                { x: x, y: y },
                { x: x + w, y: y },
                { x: x + w, y: y + h },
                { x: x, y: y + h }
            ];
            return dl.CollisionHelpers_Polygon.polygon(polygon, rectPolygon);
        }
    };
});
