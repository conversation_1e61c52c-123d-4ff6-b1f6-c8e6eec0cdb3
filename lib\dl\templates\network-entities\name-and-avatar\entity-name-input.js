ig.module(
    'dl.templates.network-entities.name-and-avatar.entity-name-input'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.animation-sheet',
    'dl.templates.mixins.text-input-layer'
).defines(function () {
    'use strict';

    dl.EntityNameInput = dl.Entity
        .extend(dl.MixinDocker)
        .extend(dl.MixinAnimationSheet)
        .extend(dl.MixinInputTextLayer)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this._useAnimationSheetAsSize = true;

                this.image = dl.preload['name-avatar']['name-input'];
                this.text = ig.game.sessionData.playerName;

                return this;
            },

            postInit: function () {
                this.parent();
                this.updateData();
            },

            _initInputTextComponent: function () {

                if(ig.ua.mobile && !ig.ua.iPad){
                    var fontSize=60;
                }else{
                    var fontSize=40;
                }

                this.c_InputTextComponent = this.addComponent(dl.InputTextComponent, {
                    divLayerName: 'InputTextLayerContainer_PlayerName',
                    divInputLayerName: 'InputTextLayer_PlayerName',
                    text: this.text,
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'INPUT_TEXT'),
                    fontSize: fontSize,
                    textLength: 9,
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name
                });

                window.playerNameInput = this;
            },
            changeText:function(){ 
                ig.game.avatarId=NameAndAvatar.randomAvatar()
                var newText=NameAndAvatar.randomName(ig.game.avatarId);               
                this.c_InputTextComponent.updateProperties({
                    text: newText
                });
            },
            onTextChanged: function (newText) {
                ig.game.sessionData.playerName = newText;
                ig.game.saveAll();

                this.updateData();                    
            },
            updateData: function () {
                if(ig.game.svasData.nickname && ig.game.svasData.nickname!=''){
                    this.c_InputTextComponent.updateProperties({
                        text: ig.game.svasData.nickname
                    });
                }else{
                    this.c_InputTextComponent.updateProperties({
                        text: ig.game.sessionData.playerName
                    });

                }
            },
            tweenIn: function (time) {
                // this.hide();
                // console.log('in');
                if(this.nameText) this.nameText.kill();
                // dl.TweenTemplate.fadeIn(this, time);
            },

            tweenOut: function (time) {
                this.hide();
                // dl.TweenTemplate.fadeOut(this, time);
                // this.spawnText();
            },
            spawnText:function(){
                if(ig.ua.mobile && !ig.ua.iPad){
                    var fontSize=60;
                }else{
                    var fontSize=40;
                }
                
                this.nameText = this.spawnEntity(dl.EntityText, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    anchor: { x: 0.5, y: 0.5 },
                    c_TextComponent: {
                        fontSize: fontSize,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                        shadow: false,
                        textAlign:'center',
                        // shadowColor: 'black',
                        // shadowBlur: 4
                    },
                    text: ig.game.sessionData.playerName
                });

            },


        });

    // Enable cache
    dl.enableCacheCanvas(dl.EntityNameInput);
});
