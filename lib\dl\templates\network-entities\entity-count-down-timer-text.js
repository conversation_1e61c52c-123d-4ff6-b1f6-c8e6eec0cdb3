ig.module(
    'dl.templates.network-entities.entity-count-down-timer-text'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.text'
).defines(function () {
    'use strict';

    dl.EntityCountDownTimerText = dl.Entity
        .extend(dl.MixinDocker)
        .extend(dl.MixinText)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this._useTextAsSize = true;
                this._baseText = _STRINGS.NETWORK.INSTRUCTION_COUNTDOWN;

                this.timer = null;
                this.onTimerExpired = null;

                return this;
            },

            _initTextComponent: function () {
                this.c_TextComponent.updateProperties({
                    textAlign: 'center',
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                    fontSize: 60
                });
            },

            update: function () {
                this.parent();

                this.updateTimer();
            },

            updateTimer: function () {
                if (!this.timer) {
                    this.updateText('');
                } else {
                    if (this.timer.delta() <= 0) {
                        var timeLeft = Math.abs(Math.round(this.timer.delta() / 1000));
                        if(timeLeft>0 && timeLeft<=5){
                            this.updateText(this._baseText.replace('<replace>', timeLeft));
                        }else{
                            this.updateText('');
                        }
                    } else {
                        if (dl.check.isFunction(this.onTimerExpired)) {
                            this.onTimerExpired();
                        }
                        this.timer = null;
                    }
                }
            },

            startTimer: function (startTime, duration) {
                this.timer = new dl.GlobalTimer(startTime, duration);
            }
        });
});
