ig.module(
    'plugins.raycasting.ray-particle'
)
.requires(
    'impact.entity'
)
.defines(function () {
    EntityRayParticle = ig.Entity.extend({
        zIndex: 100,
        pos: new Vector2(0, 0),
        size: new Vector2(1, 1),
        dir: null,
        frogEntity: null,
        targetEntity: null,
        previousTargetedEntity: null,

        targetEntityBehind: null,
        previousTargetedEntityBehind: null,

        init: function (x, y, settings) {
            this.parent(x, y, settings);
            this.pos = {
                x: this.frogEntity.center.x,
                y: this.frogEntity.center.y
            };
            this.rays = [];
            this.padCoords = [];
            this.padCoords2 = [];

            this.gameControl = ig.game.getEntitiesByType(EntityGameControl)[0];
        },

        createRays: function () {
            for (var a = 90; a <= 90; a += 1) {
                // this.rays.push(ig.game.spawnEntity(EntityRay, this.pos.x, this.pos.y, {
                //     parentEntity: this,
                //     angle: degToRad(a)
                // }));
                this.rays.push(ig.game.spawnEntity(EntityRay, this.pos.x, this.pos.y, {
                    parentEntity: this,
                    angle: this.frogEntity.currentAnim.angle + degToRad(a)
                }));
            }

            // this.rays.push(ig.game.spawnEntity(EntityRay, this.pos.x, this.pos.y, {
            //     parentEntity: this,
            //     angle: this.frogEntity.currentAnim.angle
            // }));

            // this.rays.push(ig.game.spawnEntity(EntityRay, this.pos.x, this.pos.y, {
            //     parentEntity: this,
            //     angle: invertRadAngle(this.parentEntity.currentAnim.angle)
            // }));
        },

        updateRays: function () {
            for (var i = 0; i < this.rays.length; i++) {
                this.rays[i].kill();
            }
            this.rays = [];
            for (var a = 90; a <= 90; a += 1) {
                this.rays.push(ig.game.spawnEntity(EntityRay, this.pos.x, this.pos.y, {
                    parentEntity: this,
                    angle: this.frogEntity.currentAnim.angle + degToRad(a)
                }));
            }
        },

        filterPads: function () {
            var initialPads = this.gameControl.pads;
            var frogPad = this.frogEntity.padEntity;
            this.pads = initialPads.filter(function (padEntity) {
                if (padEntity.padNumber != frogPad.padNumber) {
                    if (distanceTo(padEntity.pos, frogPad.pos) < 400) {
                        return padEntity;
                    }
                }
            });

            // console.log(this.pads);
        },

        lookForPads: function () {
            var targetPad = null;
            this.padCoords = [];
            this.foundPad = false;
            this.targetEntity = null;
            this.previousTargetedEntity = null;
            for (var i = 0; i < this.rays.length; i++) {
                var ray = this.rays[i];
                var closest = null;
                var record = Infinity;
                for (var j = 0; j < this.pads.length; j++) {
                    for (var k = 0; k < this.pads[j].corners.length; k++) {
                        var prev = this.pads[j].corners[k - 1] || this.pads[j].corners[k];
                        var next = this.pads[j].corners[k + 1] || this.pads[j].corners[k];
                        var pt = ray.cast(prev, next);
                        if (pt) {
                            var d = pt.u;
                            if (d < record) {
                                record = d;
                                closest = pt;
                                targetPad = this.pads[j];
                            }
                        }
                    }
                }
                if (closest) {
                    this.foundPad = true;
                    this.padCoords[i] = {
                        x: closest.x,
                        y: closest.y,
                        padNumber: targetPad.padNumber
                    };
                    this.targetEntity = targetPad;
                    this.previousTargetedEntity = targetPad;
                }
            }
        },

        lookForPadsBehind: function () {
            var targetPad = null;
            // for (var i = 0; i < this.rays.length; i++) {
                var ray = this.rays[1];
                var closest = null;
                var record = Infinity;
                for (var j = 0; j < this.pads.length; j++) {
                    for (var k = 0; k < this.pads[j].corners.length; k++) {
                        var prev = this.pads[j].corners[k - 1] || this.pads[j].corners[k];
                        var next = this.pads[j].corners[k + 1] || this.pads[j].corners[k];
                        var pt = ray.cast2(prev, next);
                        if (pt) {
                            var d = pt.u;
                            if (d < record) {
                                record = d;
                                closest = pt;
                                targetPad = this.pads[j];
                            }
                        }
                    }
                }
                if (closest) {
                    this.foundPadBehind = true;
                    this.padCoords2[0] = {
                        x: closest.x,
                        y: closest.y,
                        padNumber: targetPad.padNumber
                    };
                    this.targetEntityBehind = targetPad;
                    this.previousTargetedEntityBehind = targetPad;
                } else {
                    this.foundPadBehind = false;
                    this.targetEntityBehind = null;
                    this.previousTargetedEntityBehind = null;
                    this.padCoords2[0] = {};
                }
            // }
        },

        kill: function () {
            this.parent();
            for (var i = 0; i < this.rays.length; i++) {
                this.rays[i].kill();
            }
        },

        drawRayToPad: function () {
            var ctx = ig.system.context;
            ctx.save();
            ctx.strokeStyle = '#ff0000';
            for (var i = 0; i < this.padCoords.length; i++) {
                if (ig.input.pressed('debug')) console.log(this.padCoords[i]);
                if (this.padCoords[i]) {
                    ctx.beginPath();
                    ctx.moveTo(this.pos.x, this.pos.y);
                    ctx.lineTo(this.padCoords[i].x, this.padCoords[i].y);
                    ctx.stroke();
                }
            }
            ctx.restore();
        },

        drawRayToPadBehind: function () {
            var ctx = ig.system.context;
            ctx.save();
            ctx.strokeStyle = '#ff0000';
            for (var i = 0; i < this.padCoords2.length; i++) {
                if (this.padCoords[i]) {
                    ctx.beginPath();
                    ctx.moveTo(this.pos.x, this.pos.y);
                    ctx.lineTo(this.padCoords2[i].x, this.padCoords2[i].y);
                    ctx.stroke();
                }
            }
            ctx.restore();
        },

        draw: function () {
            this.parent();

            // if (this.foundPad && this.padCoords.length != 0) {
                this.drawRayToPad();
            // }

            // if (this.foundPadBehind && this.padCoords2.length != 0) {
            //     this.drawRayToPadBehind();
            // }

            var ctx = ig.system.context;
            ctx.save();
            for (var i = 0; i < this.rays.length; i++) {
                this.rays[i].drawRay();
                // this.rays[1].drawRay();
            }
            ctx.restore();
        },

        update: function () {
            this.parent();
            this.updateRays();
            this.pos = {
                x: this.frogEntity.center.x,
                y: this.frogEntity.center.y
            };

            this.lookForPads();
            // this.lookForPadsBehind();
        }
    });
});
