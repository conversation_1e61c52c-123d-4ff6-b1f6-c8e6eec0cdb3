ig.module(
    'dl-plugins.notification.notification-manager'
).requires(
    'dl-plugins.notification.notification-entity'
).defines(function () {
    'use strict';

    dl.Notification.Manager = (function () {
        // constructor
        function NotificationManager (parentInstance) {
            this._parentInstance = parentInstance;

            this.notificationStack = [];
            this.delayingNotificationStack = [];
            this.nextNotificationTime = Date.now();

            // bound function
            this._parentInstance.spawnNotification = this.spawnNotification.bind(this);
        };

        // Static
        NotificationManager.MAX_NOTIFICATION = 3;
        NotificationManager.NOTIFICATION_ENTITY = dl.Notification.Entity;

        // Method
        NotificationManager.prototype.spawnNotification = function (notificationSettings) {

            // if(notificationSettings.notificationDrawConfigs && notificationSettings.notificationDrawConfigs.notif){
            //     ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.notification);
            // }
            this.delayingNotificationStack.push({
                notificationAcceptTime: Date.now(),
                notificationSettings: notificationSettings
            });

            this.updateNotification();
        };

        NotificationManager.prototype.pushNewNotification = function (newNotification) {
            // add
            this.notificationStack.unshift(newNotification);
            // clear
            this.notificationStack = this.notificationStack.filter(function (entity) {
                return !entity.killed;
            });

            if (!this.notificationStack.length) return;

            if(newNotification.notificationDrawConfigs.notifSfx){
                ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.notification);                
            }


            for (var i = 0; i < this.notificationStack.length; i++) {
                var notification = this.notificationStack[i];

                notification.updateNotificationIndex(i);
                if (i >= NotificationManager.MAX_NOTIFICATION) {
                    notification.hideNotification();
                }
            }

            newNotification.showNotification();
        };

        NotificationManager.prototype._spawnNotification = function (notificationData) {
            var newNotification = this._parentInstance.spawnEntity(NotificationManager.NOTIFICATION_ENTITY, notificationData.notificationSettings);
            this.pushNewNotification(newNotification);

            return newNotification;
        };

        NotificationManager.prototype.updateNotification = function () {
            var timeNow = Date.now();
            if (timeNow >= this.nextNotificationTime) {
                var notificationData = this.delayingNotificationStack.shift();
                if (!notificationData) return;

                this._spawnNotification(notificationData);
                this.nextNotificationTime = timeNow + NotificationManager.NOTIFICATION_ENTITY.MOVE_TIME + NotificationManager.NOTIFICATION_ENTITY.SHOW_TIME;
            }
        };

        NotificationManager.prototype.reset = function () {
            this.notificationStack = [];
            this.delayingNotificationStack = [];
            this.nextNotificationTime = Date.now();
        };

        NotificationManager.prototype.update = function () {
            this.updateNotification();
        };

        // return class
        return NotificationManager;
    }());
});
