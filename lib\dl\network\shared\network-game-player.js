/**
 * Created by <PERSON><PERSON>
 * Store game player information
 */

var NetworkGamePlayer = function (playerId) {
    var self = {};

    self.init = function (playerId) {
        // info
        self.playerId = playerId;
        self.playerIndex = 0;
        self.playerName = 'playerName';
        self.playerAvatarId = 0;
        self.playerSvasId = 0;
        self.playerAlliance = '';

        // state
        self.botFlag = false;
        self.isLeaved = false;
        self.eliminated = false;

        // score
        self.playerRank = 0;
        self.playerScore = 0;

        /* ----- Start custom properties ------ */
        self.playerMoveTime = 1000;

        self.startGridPos = { x: 0, y: 0 };
        self.endGridPos = { x: 0, y: 0 };
        self.startTime = 0;
        self.extraStartTime = 0; // time extra from last move

        self.currentDirection = null;
        self.nextDirection = null;

        self.serverUpdateTime = 0;

        self.currentState = NetworkGamePlayer.STATE.INITIALIZING;
        self.isAlive = false;
        self.isMoving = false;
        self.occupiedCapitals = [];
        /* ------ End custom properties ------- */

        return self;
    };

    self.packData = function () {
        var data = {};

        // info
        data.playerId = self.playerId;
        data.playerIndex = self.playerIndex;
        data.playerName = self.playerName;
        data.playerAvatarId = self.playerAvatarId;
        data.playerSvasId = self.playerSvasId;
        data.playerAlliance = self.playerAlliance;

        // state
        data.botFlag = self.botFlag;
        data.isLeaved = self.isLeaved;
        data.eliminated = self.eliminated;

        // score
        data.playerRank = self.playerRank;
        data.playerScore = self.playerScore;

        /* ----- Start custom properties ------ */
        data.playerMoveTime = self.playerMoveTime;

        data.startGridPos = self.startGridPos;
        data.endGridPos = self.endGridPos;
        data.startTime = self.startTime;
        data.extraStartTime = self.extraStartTime;

        data.currentDirection = self.currentDirection;
        data.nextDirection = self.nextDirection;

        data.serverUpdateTime = self.serverUpdateTime;

        data.currentState = self.currentState;
        data.isAlive = self.isAlive;
        data.isMoving = self.isMoving;
        data.occupiedCapitals = self.occupiedCapitals;
        /* ------ End custom properties ------- */

        return data;
    };

    self.importData = function (data) {
        if (!data) return;

        // info
        if (typeof data.playerId !== 'undefined') self.playerId = data.playerId;
        if (typeof data.playerIndex !== 'undefined') self.playerIndex = data.playerIndex;
        if (typeof data.playerName !== 'undefined') self.playerName = data.playerName;
        if (typeof data.playerAvatarId !== 'undefined') self.playerAvatarId = data.playerAvatarId;
        if (typeof data.playerSvasId !== 'undefined') self.playerSvasId = data.playerSvasId;
        if (typeof data.playerAlliance !== 'undefined') self.playerAlliance = data.playerAlliance;

        // state
        if (typeof data.botFlag !== 'undefined') self.botFlag = data.botFlag;
        if (typeof data.isLeaved !== 'undefined') self.isLeaved = data.isLeaved;
        if (typeof data.eliminated !== 'undefined') self.eliminated = data.eliminated;

        // score
        if (typeof data.playerRank !== 'undefined') self.playerRank = data.playerRank;
        if (typeof data.playerScore !== 'undefined') self.playerScore = data.playerScore;

        /* ----- Start custom properties ------ */
        if (typeof data.playerMoveTime !== 'undefined') self.playerMoveTime = data.playerMoveTime;

        if (typeof data.startGridPos !== 'undefined') self.startGridPos = data.startGridPos;
        if (typeof data.endGridPos !== 'undefined') self.endGridPos = data.endGridPos;
        if (typeof data.startTime !== 'undefined') self.startTime = data.startTime;
        if (typeof data.extraStartTime !== 'undefined') self.extraStartTime = data.extraStartTime;

        if (typeof data.currentDirection !== 'undefined') self.currentDirection = data.currentDirection;
        if (typeof data.nextDirection !== 'undefined') self.nextDirection = data.nextDirection;

        if (typeof data.serverUpdateTime !== 'undefined') self.serverUpdateTime = data.serverUpdateTime;

        if (typeof data.currentState !== 'undefined') self.currentState = data.currentState;
        if (typeof data.isAlive !== 'undefined') self.isAlive = data.isAlive;
        if (typeof data.isMoving !== 'undefined') self.isMoving = data.isMoving;
        if (typeof data.occupiedCapitals !== 'undefined') self.occupiedCapitals = data.occupiedCapitals;
        /* ------ End custom properties ------- */
    };

    return self.init(playerId);
};

NetworkGamePlayer.interpolate = function (start, end, delta) {
    var result = new NetworkGamePlayer();
    result.importData(start);

    result.serverUpdateTime = start.serverUpdateTime + (end.serverUpdateTime - start.serverUpdateTime) * delta;

    return result;
};

NetworkGamePlayer.STATE = {
    INITIALIZING: 0,
    MOVING: 1,
    MOVING_TO_CRASH: 2,
    CRASHED: 3
};

/**
 * Export module
 */
if (typeof module !== 'undefined') {
    module.exports.NetworkGamePlayer = NetworkGamePlayer;
}
