ig.module('game.entities.controller-rv')
.requires(
	'impact.entity'
)
.defines(function () {
	EntityControllerRV = ig.Entity.extend ({
		zIndex: 1100,
		
		init: function (x, y, settings){
			this.parent (x, y, settings);
		},
		
		update: function (){
			this.parent ();
			
			if (this.sampleRV_curDur > 0) {
				this.sampleRV_curDur -= ig.system.tick;
				
				if (this.sampleRV_curDur <= 0) {
					this.sample_rv_end ();
				}
			}
		},
		
		draw: function (){
			var ctx = ig.system.context;
			ctx.save ();
			
			if (this.sampleRV_curDur > 0) {
				ctx.globalAlpha = 0.25;
				ctx.fillStyle = '#000000';
				ctx.fillRect (0, 0, ig.system.width, ig.system.height);
				ctx.globalAlpha = 1;
				
				ctx.font = '100px montserrat-bold';
				ctx.fillStyle = '#FFFFFF';
				ctx.textAlign = 'center';
				ctx.fillText (
					'Test RV is playing...',
					ig.system.width / 2,
					ig.system.height / 2
				);
			}
			
			ctx.restore ();
		},
		
		sampleRV_dur: 4, sampleRV_curDur: 0,
		sampleRV_cbSuccess: null, sampleRV_cbFail: null,
		sample_rv_start: function (_callbackSuccess, _callbackFail){
			this.sampleRV_curDur = this.sampleRV_dur;			
			this.sampleRV_cbSuccess = _callbackSuccess;
			this.sampleRV_cbFail = _callbackFail;
		},
		
		sample_rv_end: function (){
			this.sampleRV_cbSuccess ();
		}
	});


	EntityControllerPopup = ig.Entity.extend ({
		
		main: null,
		
		img: null,
		icon: null,
		
		index: 0,
		
		posOffset: {x: 0, y: 0}, posOffset_orig: null,
		size: {x: 0, y: 0},
		objAlpha: 0,
		
		txt: '',
		dur: 0,
		
		init: function (x, y, settings) {
			this.parent (x, y, settings);
			
			if (!ig.responsive) {
				this.gameResolution = ((!ig.ua.mobile) ? ig.sizeHandler.desktop.actualResolution : ig.sizeHandler.mobile.actualResolution);
			}
			
			var _plugin 			= this.control;
			this.dur 				= _plugin.popup.dur;
			this.posOffset 			= _plugin.popup.posOffset;
			this.img 				= _plugin.popup.img;
			this.size				= _plugin.popup.size;
			
			this.posOffset_orig = {x: this.posOffset.x, y: this.posOffset.y};
			
			this.tween_in ();
		},
		
		tween_in: function (){
			this.posOffset = {x: this.posOffset_orig.x, y: this.posOffset_orig.y};
			this.posOffset.x -= 50;
			
			var _dur = 0.5;
			
			this.tween( {posOffset: {x: this.posOffset_orig.x, y: this.posOffset_orig.y}}, _dur, {
				easing: ig.Tween.Easing.Exponential.EaseOut
			}).start();
			
			this.tween( {objAlpha: 1}, _dur, {
				easing: ig.Tween.Easing.Exponential.EaseOut
			}).start();
			
			this.control.enable_buttons (false);
		},
		
		tween_out: function (){
			this.posOffset = {x: this.posOffset_orig.x, y: this.posOffset_orig.y};
			var _targ = {x: this.posOffset_orig.x + 50, y: this.posOffset_orig.y},
				_dur = 0.5;
			
			this.tween( {posOffset: _targ}, _dur, {
				easing: ig.Tween.Easing.Exponential.EaseOut,
				onComplete: function (){
					this.control.update_panel_buttons_status();
					// this.control.enable_buttons (true);					
					this.kill ();
				}.bind (this)
			}).start();
			
			this.tween( {objAlpha: 0}, _dur, {
				easing: ig.Tween.Easing.Exponential.EaseOut
			}).start();
		},
		
		update: function (){
			this.parent ();
			
			if (ig.responsive) {
				this.pos = {
					x: (ig.responsive.width / 2) - (this.size.x / 2) + this.posOffset.x,
					y: (ig.responsive.height / 2) - (this.size.y / 2) + this.posOffset.y
				};
			}else {
				this.pos = {
					x: (this.gameResolution.x / 2) - (this.size.x / 2) + this.posOffset.x,
					y: (this.gameResolution.y / 2) - (this.size.y / 2) + this.posOffset.y
				};
			}
			
			this.dur -= ig.system.tick;
			if (this.dur <= 0) {
				this.tween_out ();
			}
		},
		
		draw: function (){
			this.parent ();
			
			var ctx = ig.system.context;
			ctx.save();
			ctx.globalAlpha = this.objAlpha;


            ctx.fillStyle='#ffffff';            
            ctx.strokeStyle='#1e295c';//'#3b4e90';            
            ig.game.roundRect(ctx,this.pos.x,this.pos.y,this.size.x,this.size.y,10,true,true);
			
			// this.img.drawImage (
			// 	this.pos.x, this.pos.y,
			// 	this.size.x, this.size.y
			// );
			
			var _plugin = this.control;
			
			ctx.font = _plugin.popup.txtFont;
			ctx.fillStyle = _plugin.popup.txtFillStyle;
			ctx.textAlign = 'center';
			ctx.fillText (
				this.txt,
				this.pos.x + _plugin.popup.txtPosOffset.x,
				this.pos.y + _plugin.popup.txtPosOffset.y
			);
			
			ctx.globalAlpha = 1;
			ctx.restore ();
		}
	});
});