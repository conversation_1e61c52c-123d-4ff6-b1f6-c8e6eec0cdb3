ig.module(
    'game.dl-entities.game-map'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'game.dl-entities.game-map-nation',
    'game.dl-entities.game-map-capital',
    'game.dl-entities.game-map-guide-arrow',
    'game.dl-entities.game-soldier'
).defines(function () {
    dl.EntityGameMap = dl.Entity
        .extend(dl.MixinDocker)
        .extend({
        postInit: function () {
            this.parent();
            this.colorManager = ig.game.managers.color;
            this.gameManager = ig.game.managers.game;
            this.gameControl = ig.game.game;
            this.nations = [];
            this.capitals = [];
            this.initNations();
            this.initGameManager();
            this.initCapitals();
            this.initGuideArrow();
            this.colorManager.setCapitalsColor();
            this.colorManager.setNationsColor();
            window.gamemap = this;
        },

        initNations: function () {

            var xx=window.innerWidth;
            var yy=window.innerHeight;

            if(ig.ua.mobile && !ig.ua.iPad){
                var fontSize=80;
                if(xx<yy){
                    var dockerPercent={x:0.5,y:0.25};
                }else{
                    var dockerPercent={x:0.5,y:0.18};
                }
            }else{
                var fontSize=50;
                var dockerPercent={x:0.5,y:0.18};
            }

            ig.game.mapname = this.mapData.mapName;

            var nationList = this.mapData.nationData;
            for (var i = 0; i < nationList.length; i++) {
                this.nations.push(
                    this.spawnEntity(dl.EntityGameMapNation, {
                        c_DockerComponent: {
                            dockerObject: dl.game.camera,
                            dockerPercent: { x: 0.5, y: 0.5 },
                            dockerOffset: { x: 0, y: 0 }
                        },
                        nationData: nationList[i],
                        mapName:this.mapData.mapName,
                        seqNation:i,
                    })
                );
            }
            this.colorManager.nationList = this.nations;
        },

        initCapitals: function () {
            var capitalData = this.mapData.capitalData;
            for (var i = 0; i < capitalData.length; i++) {
                var pos = { x: 0, y: 0 };
                pos.x = -capitalData[i].mapDimension.width * 0.5 + capitalData[i].initialPoint.x;
                pos.y = -capitalData[i].mapDimension.height * 0.5 + capitalData[i].initialPoint.y;
                this.capitals.push(
                    this.spawnEntity(dl.EntityGameMapCapital, {
                        c_DockerComponent: {
                            dockerObject: dl.game.camera,
                            dockerPercent: { x: 0.5, y: 0.5 },
                            dockerOffset: { x: pos.x, y: pos.y }
                        },
                        capitalData: capitalData[i]
                    })
                );
                ig.game.capitals = this.capitals;
            }

            for (var i = 0; i < this.capitals.length; i++) {
                this.capitals[i].initGameManager();
            }

            this.gameManager.capitalList = this.capitals;
            this.gameManager.nationList = this.nations;
            this.colorManager.capitalList = this.capitals;

            for (var i = 0; i < this.capitals.length; i++) {
                this.capitals[i].initNationPlayer();
            }
        },

        initGuideArrow: function () {
            this.guideArrow = this.spawnEntity(dl.EntityGameMapGuideArrow, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0.5, y: 0.5 },
                    dockerOffset: { x: 0, y: 0 }
                },
                mapData: this.mapData
            });
        },

        initGameManager: function () {
            window.gameManager = ig.game.managers.game = this.gameManager = this.spawnEntity(dl.EntityGameManager, {});
            window.gameStart = true;
        },
        updateData: function (data) {
            this.mapData = data;
            if (this.gameManager) {
                this.gameManager.mapData = this.mapData;
            }
            if (this.colorManager) {
                this.colorManager.updateData(data);
                if (this.capitals.length > 0) {
                    this.colorManager.capitalList = this.capitals;
                }
                if (this.nations.length > 0) {
                    this.colorManager.nationList = this.nations;
                }
            }

        }
    });
});
