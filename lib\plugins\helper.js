ig.module('plugins.helper')
	.requires('impact.impact')
	.defines(function () {
		createVector = function (point) {
			return new Vector2(point.x, point.y);
		};

		Number.prototype.reverse = function (min, max) {
			return this.map(min, max, max, min);
		};

		randomRange = function (min, max) {
			return Math.floor(Math.random() * (max - min + 1) + min);
		};

		distance = function (a, b) {
			if ((!a.x || !a.y) && a.pos) {
				a = a.pos;
			}
			if ((!b.x || !b.y) && b.pos) {
				b = b.pos;
			}
			return Math.sqrt(Math.pow(a.x - b.x, 2) + Math.pow(a.y - b.y, 2));
		};

		aabbCheck = function (aabb1, aabb2) {
			if (
				aabb1.x + aabb1.w > aabb2.x &&
				aabb1.x < aabb2.x + aabb2.w &&
				aabb1.y + aabb1.h > aabb2.y &&
				aabb1.y < aabb2.y + aabb2.h
			) {
				return true;
			}
			return false;
		};

		drawEllipseWithBezierByCenter = function (cx, cy, w, h) {
			this.drawEllipseWithBezier(cx - w / 2.0, cy - h / 2.0, w, h);
		};

		drawEllipseWithBezier = function (x, y, w, h) {
			var ctx = ig.system.context;
			var kappa = 0.5522848;
				var ox = (w / 2) * kappa; // control point offset horizontal
				var oy = (h / 2) * kappa; // control point offset vertical
				var xe = x + w; // x-end
				var ye = y + h; // y-end
				var xm = x + w / 2; // x-middle
				var ym = y + h / 2; // y-middle
			// ctx.save();
			ctx.beginPath();
			ctx.moveTo(x, ym);
			ctx.bezierCurveTo(x, ym - oy, xm - ox, y, xm, y);
			ctx.bezierCurveTo(xm + ox, y, xe, ym - oy, xe, ym);
			ctx.bezierCurveTo(xe, ym + oy, xm + ox, ye, xm, ye);
			ctx.bezierCurveTo(xm - ox, ye, x, ym + oy, x, ym);
			ctx.closePath();
			// ctx.stroke();
			// ctx.fill();
			// ctx.restore();
		};

		spawnParticle = function (x, y, count) {
			for (var _c = 0; _c < count; _c++) ig.game.spawnEntity(EntityParticle, x, y);
		};

		roundRect = function (x, y, width, height, radius, fill, stroke) {
			var ctx = ig.system.context;

			if (typeof stroke === 'undefined') {
				stroke = true;
			}
			if (typeof radius === 'undefined') {
				radius = 5;
			}
			if (typeof radius === 'number') {
				radius = { tl: radius, tr: radius, br: radius, bl: radius };
			} else {
				var defaultRadius = { tl: 0, tr: 0, br: 0, bl: 0 };
				for (var side in defaultRadius) {
					radius[side] = radius[side] || defaultRadius[side];
				}
			}
			ctx.beginPath();
			ctx.moveTo(x + radius.tl, y);
			ctx.lineTo(x + width - radius.tr, y);
			ctx.quadraticCurveTo(x + width, y, x + width, y + radius.tr);
			ctx.lineTo(x + width, y + height - radius.br);
			ctx.quadraticCurveTo(x + width, y + height, x + width - radius.br, y + height);
			ctx.lineTo(x + radius.bl, y + height);
			ctx.quadraticCurveTo(x, y + height, x, y + height - radius.bl);
			ctx.lineTo(x, y + radius.tl);
			ctx.quadraticCurveTo(x, y, x + radius.tl, y);
			ctx.closePath();
			if (fill) {
				ctx.fill();
			}
			if (stroke) {
				ctx.stroke();
			}
		};
	});
