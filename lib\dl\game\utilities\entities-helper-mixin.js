ig.module(
    'dl.game.utilities.entities-helper-mixin'
).requires(
    'dl.game.utilities.object-pool'
).defines(function () {
    'use strict';

    dl.EntitiesHelperMixin = {
        staticInstantiate: function () {
            this.parent();

            this._entities = [];
            this._sortFunction = dl.EntitiesHelperMixin.SORT_ENTITIES_BY.zIndex;

            this._killedChild = false; // flag to notify a child killed in update
            this._zIndexChanged = false; // flag to notify zIndex changed in update

            return this;
        },

        update: function () {
            this.parent();

            for (var i = 0; i < this._entities.length; i++) {
                var entity = this._entities[i];
                if (entity.killed) continue;

                entity.update();
            }
        },

        lateUpdate: function () {
            this.parent();

            for (var i = 0; i < this._entities.length; i++) {
                var entity = this._entities[i];
                if (entity.killed) continue;

                entity.lateUpdate();
            }

            if (this._killedChild) {
                this.removeKilledEntitiesPool();
                this._killedChild = false;
            }

            if (this._zIndexChanged) {
                this.sortEntities();
                this._zIndexChanged = false;
            }
        },

        updateOrientation: function () {
            this.parent();

            for (var i = 0; i < this._entities.length; i++) {
                var entity = this._entities[i];
                if (entity.killed) continue;

                entity.updateOrientation();
            }
        },

        draw: function (ctx) {
            this.parent(ctx);

            for (var i = 0; i < this._entities.length; i++) {
                var entity = this._entities[i];
                if (entity.killed) continue;
                if (!entity.visible) continue;

                if (entity.alpha != 1) {
                    ctx.save();
                    ctx.globalAlpha = entity.alpha;
                    entity.draw(ctx);
                    ctx.restore();
                } else {
                    entity.draw(ctx);
                }

                if (dl.debug && dl.debug.drawEntity) {
                    dl.debug.drawEntity(entity);
                }
            }
        },

        kill: function () {
            for (var i = 0; i < this._entities.length; i++) {
                var entity = this._entities[i];
                entity.kill();
            }

            this.parent();
        },

        onEntitySpawn: function (entity) {

        },

        spawnEntity: function (entityClass, settings) {
            if (!dl.check.isDefined(settings)) {
                settings = {};
            }

            // addition
            settings.parentInstance = this;

            var entity = this._spawnEntityPool(entityClass, settings);
            // bind events
            entity.onEvent('killed', this.onChildKilled.bind(this));
            entity.onEvent('zIndexChanged', this.onChildZIndexChanged.bind(this));

            this._entities.push(entity);

            // trigger onEntitySpawn after add entity to parent and before entity postInit
            this.onEntitySpawn(entity);
            // trigger post init after add entity to parent
            entity.postInit();
            // trigger zIndex changed for new entities
            this.onChildZIndexChanged();

            return entity;
        },

        _spawnEntityPool: function (entityClass, settings) {
            var entity = dl.objectPool.getFromPool(entityClass);
            if (entity) {
                entity.reset(settings);
            } else {
                entity = new (entityClass)();
                entity.init(settings);
            }

            return entity;
        },

        sortEntities: function () {
            this._entities = this._entities.sort(this._sortFunction);
        },

        removeKilledEntitiesPool: function () {
            this._entities = this._entities.filter(function (entity) {
                if (entity._enablePool && entity.killed) {
                    dl.objectPool.putInPool(entity);
                }
                return !entity.killed;
            });
        },

        onChildKilled: function () {
            this._killedChild = true;
        },

        onChildZIndexChanged: function () {
            this._zIndexChanged = true;
        }
    };

    /**
     * Static properties for mixin
     */
    dl.EntitiesHelperMixin.SORT_ENTITIES_BY = {
        zIndex: function (a, b) {
            return a.zIndex - b.zIndex;
        }
    };
    dl.EntitiesHelperMixin.DEBUG_DRAW_SIZE = true;
});
