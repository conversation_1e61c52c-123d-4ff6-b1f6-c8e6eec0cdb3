ig.module(
    'dl.game.input-handler'
).requires(
    'dl.dl',
    'impact.input'
).defines(function () {
    dl.InputHandler = function () {
        var self = {};

        self.init = function () {
            self.pressInputData = {};
            self.pressingInputData = {};
        };

        self.update = function () {
            Object.keys(self.pressInputData).forEach(function (keycode) {
                var data = self.pressInputData[keycode];
                if (ig.input.pressed(data.keyName)) {
                    if (typeof (data.callback) === 'function')
                        { data.callback(); }
                }
            }.bind(this));

            Object.keys(self.pressingInputData).forEach(function (keycode) {
                var data = self.pressingInputData[keycode];
                if (ig.input.state(data.keyName)) {
                    if (typeof (data.callback) === 'function')
                        { data.callback(); }
                }
            }.bind(this));
        };

        self.addPressingInput = function (keyName, callback) {
            var keycode = ig.KEY[keyName];

            if (typeof keycode === 'undefined') throw 'Invalid key code!';
            if (typeof self.pressingInputData[keycode] !== 'undefined') throw 'Key code implemented!';

            ig.input.bind(keycode, keyName);
            self.pressingInputData[keycode] = {
                keyName: keyName,
                callback: callback
            };
        };

        self.addPressInput = function (keyName, callback) {
            var keycode = ig.KEY[keyName];

            if (typeof keycode === 'undefined') throw 'Invalid key code!';
            if (typeof self.pressingInputData[keycode] !== 'undefined') throw 'Key code implemented!';

            ig.input.bind(keycode, keyName);
            self.pressInputData[keycode] = {
                keyName: keyName,
                callback: callback
            };
        };

        self.init();
        return self;
    };
});
