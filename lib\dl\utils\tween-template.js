ig.module(
    'dl.utils.tween-template'
).defines(function () {
    'use strict';

    dl.TweenTemplate = {
        scaleIn: function (object, time, callback) {
            if (!time) time = 1000;

            object._alpha = 0;
            object.updateAlpha();

            object._scale = { x: 0.25, y: 0.25 };
            object.updateAlpha();

            object.createTween()
                .to({
                    _alpha: 1,
                    _scale: { x: 1, y: 1 }
                }, time, {
                    easing: dl.TweenEasing.Elastic.EaseOut,
                    onPropertiesChanged: function () {
                        this.updateAlpha();
                        this.updateScale();
                    }.bind(object)
                })
                .start({
                    onCompleteTween: function () {
                        if (dl.check.isFunction(callback)) callback();
                    }.bind(object)
                });
        },

        scaleOut: function (object, time, callback) {
            if (!time) time = 1000;
            object.createTween()
                .to({
                    _alpha: 0,
                    _scale: { x: 0.25, y: 0.25 }
                }, time, {
                    easing: dl.TweenEasing.Back.EaseIn,
                    onPropertiesChanged: function () {
                        this.updateAlpha();
                        this.updateScale();
                    }.bind(object)
                })
                .start({
                    onCompleteTween: function () {
                        if (dl.check.isFunction(callback)) callback();
                    }.bind(object)
                });
        },

        scaleOutIn: function (object, time, middleCallback, endCallback) {
            if (!time) time = 1000;

            object.createTween()
                .to({
                    _alpha: 0,
                    _scale: { x: 0.5, y: 0.5 }
                }, time * 0.5, {
                    easing: dl.TweenEasing.Sinusoidal.EaseIn,
                    onPropertiesChanged: function () {
                        this.updateAlpha();
                        this.updateScale();
                    }.bind(object),
                    onCompleteAction: function () {
                        if (dl.check.isFunction(middleCallback)) middleCallback();
                    }.bind(object)
                })
                .to({
                    _alpha: 1,
                    _scale: { x: 1, y: 1 }
                }, time * 0.5, {
                    easing: dl.TweenEasing.Sinusoidal.EaseOut,
                    onPropertiesChanged: function () {
                        this.updateAlpha();
                        this.updateScale();
                    }.bind(object)
                })
                .start({
                    onCompleteTween: function () {
                        if (dl.check.isFunction(endCallback)) endCallback();
                    }.bind(object)
                });
        },

        fadeIn: function (object, time, callback) {
            if (!time) time = 1000;

            object._alpha = 0;
            object.updateAlpha();

            object.createTween()
                .to({
                    _alpha: 1
                }, time, {
                    easing: dl.TweenEasing.Quadratic.EaseOut,
                    onPropertiesChanged: function () {
                        // if (object.cacheCanvasEnabled) this.cacheRequestRedraw();
                        this.updateAlpha();
                    }.bind(object)
                })
                .start({
                    onCompleteTween: function () {
                        if (dl.check.isFunction(callback)) callback();
                    }.bind(object)
                });
        },

        fadeOut: function (object, time, callback) {
            if (!time) time = 1000;

            object.createTween()
                .to({
                    _alpha: 0
                }, time, {
                    easing: dl.TweenEasing.Quadratic.EaseIn,
                    onPropertiesChanged: function () {
                        // if (object.cacheCanvasEnabled) this.cacheRequestRedraw();
                        this.updateAlpha();
                    }.bind(object)
                })
                .start({
                    onCompleteTween: function () {
                        if (dl.check.isFunction(callback)) callback();
                    }.bind(object)
                });
        },

        moveInTop: function (object, time, callback) {
            if (!time) time = 1000;

            object._alpha = 0;
            object.updateAlpha();

            var originalY = object.c_DockerComponent.dockerPercent.y;
            object.c_DockerComponent.dockerPercent.y = -1;

            object.createTween()
                .to({
                    _alpha: 1,
                    c_DockerComponent: { dockerPercent: { y: originalY } }
                }, time, {
                    easing: dl.TweenEasing.Back.EaseOut,
                    onPropertiesChanged: function () {
                        this.updateAlpha();
                        this.updatePos();
                    }.bind(object)
                })
                .start({
                    onCompleteTween: function () {
                        if (dl.check.isFunction(callback)) callback();
                    }.bind(object)
                });
        },

        moveOutTop: function (object, time, callback) {
            if (!time) time = 1000;

            object.createTween()
                .to({
                    _alpha: 0,
                    c_DockerComponent: { dockerPercent: { y: -1 } }
                }, time, {
                    easing: dl.TweenEasing.Back.EaseIn,
                    onPropertiesChanged: function () {
                        this.updateAlpha();
                        this.updatePos();
                    }.bind(object)
                })
                .start({
                    onCompleteTween: function () {
                        if (dl.check.isFunction(callback)) callback();
                    }.bind(object)
                });
        },

        blink: function (object, time) {
            if (!time) time = 1000;

            object.createTween()
                .to({
                    _alpha: 0
                }, time * 0.5, {
                    easing: dl.TweenEasing.Linear.EaseNone,
                    onPropertiesChanged: function () {
                        this.updateAlpha();
                        this.updateScale();
                    }.bind(object),
                    onCompleteAction: function () {
                    }.bind(object)
                })
                .to({
                    _alpha: 1,
                    _scale: { x: 1, y: 1 }
                }, time * 0.5, {
                    easing: dl.TweenEasing.Linear.EaseNone,
                    onPropertiesChanged: function () {
                        this.updateAlpha();
                        this.updateScale();
                    }.bind(object)
                })
                .start({
                    repeat: true,
                    onCompleteTween: function () {
                    }.bind(object)
                });
        },

        thundercloud: function (object, time, callback) {
            if (!time) time = 1000;

            object._alpha = 0;
            object.updateAlpha();

            var originalY = object.c_DockerComponent.dockerPercent.y;
            object.c_DockerComponent.dockerPercent.y = -0.85;

            object.createTween()
                .to({
                    _alpha: 1,
                    c_DockerComponent: { dockerPercent: { y: originalY } }
                }, time, {
                    easing: ig.Tween.Easing.Cubic.EaseOut, //dl.TweenEasing.Back.EaseOut,
                    onPropertiesChanged: function () {
                        this.updateAlpha();
                        this.updatePos();
                    }.bind(object)
                })
                .to({
                    _alpha: 1
                }, 1000, {
                    onPropertiesChanged: function () {
                        this.updateAlpha();
                    }.bind(object)
                })
                .to({
                    _alpha: 0
                }, 400, {
                    onPropertiesChanged: function () {
                        this.updateAlpha();
                    }.bind(object)
                })
                .start({
                    onCompleteTween: function () {
                        if (dl.check.isFunction(callback)) callback();
                        object.kill();
                    }.bind(object)
                });
        }
    };
});
