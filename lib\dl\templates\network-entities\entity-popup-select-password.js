ig.module(
    'dl.templates.network-entities.entity-popup-select-password'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.popup',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.animation-sheet',
    'dl.templates.mixins.text-input-layer',
    'dl.templates.mixins.text',
    'dl.templates.entities.buttons.entity-button-image-text',
    'dl.templates.entities.buttons.entity-button-image'
).defines(function () {
    'use strict';

    dl.EntityPasswordInput = dl.Entity
        .extend(dl.MixinDocker)
        .extend(dl.MixinAnimationSheet)
        .extend(dl.MixinInputTextLayer)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this._useAnimationSheetAsSize = true;

                this.image = dl.preload['room-code-input'];
                this.text = '';

                return this;
            },

            postInit: function () {
                this.parent();
            },

            _initInputTextComponent: function () {
                this.c_InputTextComponent = this.addComponent(dl.InputTextComponent, {
                    divLayerName: 'InputTextLayerContainer_Password',
                    divInputLayerName: 'InputTextLayer_Password',
                    text: this.text,
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'INPUT_TEXT'),
                    fontSize: 40,
                    textLength: 9,
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name
                });
            },

            onTextChanged: function (newText) {
                this.text = newText;
            }
        });

    dl.EntityPopupSelectPassword = dl.Entity
        .extend(dl.MixinPopup)
        .extend(dl.MixinDocker)
        .extend({
            postInit: function () {
                this.parent();

                this.content = this.spawnEntity(dl.EntityPopupSelectPassword_Content, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    }
                });
            }
        });

    dl.EntityPopupSelectPassword_Content = dl.Entity
        .extend(dl.MixinPopupContent)
        .extend(dl.MixinDocker)
        .extend(dl.MixinAnimationSheet)
        .extend(dl.MixinText)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this.image = dl.preload['popup-medium'];
                this._useAnimationSheetAsSize = true;

                this.text = _STRINGS.NETWORK.POPUP_SELECT_PASSWORD_TITLE;

                return this;
            },

            postInit: function () {
                this.parent();

                this.initInput();
                this.initButtons();

                this.tweenIn();
            },

            _initTextComponent: function () {
                this.c_TextComponent.updateProperties({
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                    textAlign: 'center', // [center|end|left|right|start];
                    textBaseline: 'middle', // [alphabetic|top|hanging|middle|ideographic|bottom];

                    fontSize: 46,
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name,

                    _docker: { x: 0.5, y: 0.2 },
                    _anchor: { x: 0.5, y: 0.5 },
                    _offset: { x: 0, y: 0 }
                });
            },

            initInput: function () {
                this.input = this.spawnEntity(dl.EntityPasswordInput, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.45 },
                        dockerOffset: { x: 0, y: 0 }
                    }
                });
            },

            initButtons: function () {
                this.btnCreate = this.spawnEntity(dl.EntityButtonImageText, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.75 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                        fontSize: 40,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    image: dl.preload['button-green'],
                    text: _STRINGS.NETWORK.POPUP_SELECT_PASSWORD_BUTTON_CONNECT,
                    onButtonClicked: function () {
                        ig.game.client.roomPassword = this.input.text;
                        this.checkPassword();
                    }.bind(this)
                });

                this.btnClose = this.spawnEntity(dl.EntityButtonImage, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 1, y: 0 },
                        dockerOffset: { x: -10, y: 10 }
                    },
                    anchor: { x: 1, y: 0 },
                    image: dl.preload['popup-button-close'],
                    onButtonClicked: function () {
                        ig.game.client.roomPassword = '';

                        this.tweenOut(function () {
                            this.kill();
                        }.bind(this.parentInstance));
                    }.bind(this)
                });
            },

            checkPassword: function () {
                ig.game.client.requestCheckPassword(this.onCheckPasswordCallback.bind(this));
            },

            onCheckPasswordCallback: function (data) {
                if (!data) return false;

                if (typeof RoomPassword === 'undefined') {
                    throw 'RoomPassword is undefined!';
                }

                switch (data.returnCode) {
                    case RoomPassword.RETURN_CODES.VALID: {
                        if(data.maxPlayer) ig.game.client.roomMaxPlayer=data.maxPlayer
                        this.onCheckPasswordSuccess();
                        return true;
                    } break;
                    case RoomPassword.RETURN_CODES.EMPTY: {
                        this.onCheckPasswordFail(_STRINGS.NETWORK.CHECK_PASSWORD_NOTIFICATION_EMPTY);
                    } break;
                    case RoomPassword.RETURN_CODES.TOO_MUCH_LENGTH: {
                        this.onCheckPasswordFail(_STRINGS.NETWORK.CHECK_PASSWORD_NOTIFICATION_TOO_MUCH_LENGTH);
                    } break;
                    case RoomPassword.RETURN_CODES.NOT_ENOUGH_LENGTH: {
                        this.onCheckPasswordFail(_STRINGS.NETWORK.CHECK_PASSWORD_NOTIFICATION_NOT_ENOUGH_LENGTH);
                    } break;
                    case RoomPassword.RETURN_CODES.CONTAIN_INVALID_CHARACTER: {
                        this.onCheckPasswordFail(_STRINGS.NETWORK.CHECK_PASSWORD_NOTIFICATION_CONTAIN_INVALID_CHARACTER);
                    } break;
                    case 411: {
                        this.onCheckPasswordFail('Battle room is full, please create another battle room!');                        
                    } break;
                    case RoomPassword.RETURN_CODES.INVALID: {
                        this.onCheckPasswordFail(_STRINGS.NETWORK.CHECK_PASSWORD_NOTIFICATION_INVALID);
                    } break;
                }
            },

            onCheckPasswordSuccess: function (text) {
                this.tweenOut(function () {
                    this.kill();
                }.bind(this.parentInstance));
            },

            onCheckPasswordFail: function (text) {
                dl.scene.spawnNotification({
                    notificationDrawConfigs: {
                        contentConfigs: [
                            {
                                type: 'text',
                                text: text,
                                fillStyle: dl.configs.TEXT_COLOR.WHITE,
                                fontSize: 46,
                                fontFamily: dl.configs.FONT.SOURCE_SANS.name
                            }],
                        backgroundConfigs: {
                            lineWidth: 2,
                            fillStyle: dl.configs.NOTIFICATION_COLORS.PLAYER_1,
                            strokeStyle: dl.configs.NOTIFICATION_COLORS.PLAYER_1,

                            box: { width: 800, height: 90, round: 10, padding: { x: 100, y: 5 } }
                        }
                    }
                });
            },

            tweenIn: function (callback) {
                this.parent(callback);
            },

            tweenOut: function (callback) {
                this.btnClose.setEnable(false);
                this.btnCreate.setEnable(false);
                this.input.hide();

                this.parent(callback);
            }
        });
});
