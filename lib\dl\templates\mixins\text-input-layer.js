ig.module(
    'dl.templates.mixins.text-input-layer'
).requires(
    'dl.game.entity.components.div-layers.text-input-layer'
).defines(function () {
    'use strict';

    dl.MixinInputTextLayer = {
        staticInstantiate: function () {
            this.parent();

            this.text = '';

            return this;
        },

        initComponents: function () {
            this.parent();

            this._initInputTextComponent();
            this.c_InputTextComponent.onEvent('textChanged', this.onTextChanged.bind(this));
        },

        _initInputTextComponent: function () {
            this.c_InputTextComponent = this.addComponent(dl.InputTextComponent, {
                divLayerName: 'InputTextLayerContainer_Name',
                divInputLayerName: 'InputTextLayer_Name',
                text: this.text,
                fillStyle: dl.configs.getConfig('TEXT_COLOR', 'INPUT_TEXT'),
                fontSize: 26
            });
        },

        onTextChanged: function (newText) {

        },

        show: function () {
            this.c_InputTextComponent.showDiv();
        },

        hide: function () {
            this.c_InputTextComponent.hideDiv();
        },

        disableEntityUnder: function () {
            this.parent();

            if (this.killed) return;

            this.hide();
        },

        enableEntityUnder: function () {
            this.parent();

            if (this.killed) return;

            this.show();
        },

        kill: function () {
            this.hide();
            this.parent();
        }
    };
});
