ig.module(
    'dl.templates.mixins.div-layer'
).requires(
    'dl.game.entity.components.div-layers.div-layer'
).defines(function () {
    'use strict';

    dl.MixinDivLayer = {
        initComponents: function () {
            this.parent();

            this._initDivLayerComponent();
        },

        _initDivLayerComponent: function () {
            this.c_DivLayerComponent = this.addComponent(dl.DivLayerComponent, {
                callback: null,
                divLayerName: 'divLayerName'
            });
        },

        disableEntityUnder: function () {
            this.parent();
            this.c_DivLayerComponent.hideDiv();
        },

        enableEntityUnder: function () {
            this.parent();
            this.c_DivLayerComponent.showDiv();
        }
    };
});
