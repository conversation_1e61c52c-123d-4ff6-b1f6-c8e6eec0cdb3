ig.module(
    'dl.templates.entities.backgrounds.entity-color-background'
).requires(
    'dl.game.entity'
).defines(function () {
    'use strict';

    dl.EntityColorBackground = dl.Entity.extend({
        staticInstantiate: function () {
            this.parent();

            this.fillColor = 'white';
            return this;
        },

        postInit: function () {
            dl.system.onEvent('sizeChanged', this.setSize.bind(this));
            this.setSize(dl.system.width, dl.system.height);
        },

        draw: function (ctx) {
            ctx.save();
            ctx.fillStyle = this.fillColor;
            ctx.fillRect(
                this.pos.x - this.size.x * 0.5,
                this.pos.y - this.size.y * 0.5,
                this.size.x,
                this.size.y);
            ctx.restore();
        }
    });

    // Enable cache
    dl.enableCacheCanvas(dl.EntityColorBackground);
});
