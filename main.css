html,body {
	background-color: #000;
	margin: 0;
	padding: 0;
	position: relative;
	font-family:"Arial";
	width:100%;
    touch-action: none;
    overflow: hidden;
}

#game {
	position: absolute;
	left: 0;
	top: 0;
	z-index: 0;
}

#ajaxbar{
	background:url('media/graphics/loading/ajax-loader.gif') center no-repeat;
}

.divlink:hover {
	cursor: pointer;
}


#canvas {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	-ms-interpolation-mode: nearest-neighbor;
	-webkit-transform: scale3d(1, 1, 1);
	z-index: 1;
}

#orientate {
	position: absolute;
	float:left;
	width:100%;
	height:100%;
	top: 0;
	left: 0;
	z-index: 10002;
	display:none;
}

#orientate img {
	position:absolute;
	float:left;
	width:100%;
	height:100%;
}
.play {
	position:absolute;
	float:left;
	width:100%;
	height:100%;
	z-index:1000;
	background-color:#fff;
	left:0;
	top:0;
	display:none;
}

.play img {
	position:absolute;
	float:left;
	width:100%;
	height:100%;
	z-index:1000;
	left:0;
	top:0;
}

#nohtml5 {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: #000;
	z-index: 1002;
	visibility:hidden;
}

#nohtml5 img {
	position: absolute;
	width: 100%;
	height: 100%;
}

#nohtml5-bubble{
	position: absolute;
	bottom: 20px;
	left: 50px;
	width: 380px;
	height: 100px;
	z-index: 1002;
	color:#000;
	background:rgba(255,255,255,0.75);

	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	border-radius: 10px;
}

#nohtml5-text{
	padding:10px;
}

.horizontal-seperator{
	height:10px;
	width:100%;
}

@font-face {
    font-family: 'montserrat';
    src: url('media/fonts/montserrat.woff');
    src: url('media/fonts/montserrat.woff') format('woff'),
         url('media/fonts/montserrat.ttf') format('truetype');
}

@font-face {
    font-family: 'montserrat-regular';
    src: url('media/fonts/montserrat-regular.woff');
    src: url('media/fonts/montserrat-regular.woff') format('woff'),
         url('media/fonts/montserrat-regular.ttf') format('truetype');
}

@font-face {
    font-family: 'montserrat-bold';
    src: url('media/fonts/montserrat-bold.woff');
    src: url('media/fonts/montserrat-bold.woff') format('woff'),
         url('media/fonts/montserrat-bold.ttf') format('truetype');
}


/* SVAS - reCAPTCHA */
.grecaptcha-badge { visibility: hidden; }
.svas-recaptcha-branding {
	color: #B4B4B4;
	font-size: 0.7em;
}

/* SVAS - LOADER */
.svas-loader-container {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 99999;
}

.svas-loader-spinner {
	color: official;
	display: inline-block;
	position: relative;
	width: 80px;
	height: 80px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	-webkit-transform: translate(-50%, -50%);
}

.svas-loader-spinner div {
	transform-origin: 40px 40px;
	animation: svas-loader-spinner 1.2s linear infinite;
}

.svas-loader-spinner div:after {
	content: " ";
	display: block;
	position: absolute;
	top: 3px;
	left: 37px;
	width: 6px;
	height: 18px;
	border-radius: 20%;
	background: #fff;
}

.svas-loader-spinner div:nth-child(1) {
	transform: rotate(0deg);
	animation-delay: -1.1s;
}

.svas-loader-spinner div:nth-child(2) {
	transform: rotate(30deg);
	animation-delay: -1s;
}

.svas-loader-spinner div:nth-child(3) {
	transform: rotate(60deg);
	animation-delay: -0.9s;
}

.svas-loader-spinner div:nth-child(4) {
	transform: rotate(90deg);
	animation-delay: -0.8s;
}

.svas-loader-spinner div:nth-child(5) {
	transform: rotate(120deg);
	animation-delay: -0.7s;
}

.svas-loader-spinner div:nth-child(6) {
	transform: rotate(150deg);
	animation-delay: -0.6s;
}

.svas-loader-spinner div:nth-child(7) {
	transform: rotate(180deg);
	animation-delay: -0.5s;
}

.svas-loader-spinner div:nth-child(8) {
	transform: rotate(210deg);
	animation-delay: -0.4s;
}

.svas-loader-spinner div:nth-child(9) {
	transform: rotate(240deg);
	animation-delay: -0.3s;
}

.svas-loader-spinner div:nth-child(10) {
	transform: rotate(270deg);
	animation-delay: -0.2s;
}

.svas-loader-spinner div:nth-child(11) {
	transform: rotate(300deg);
	animation-delay: -0.1s;
}

.svas-loader-spinner div:nth-child(12) {
	transform: rotate(330deg);
	animation-delay: 0s;
}

@keyframes svas-loader-spinner {
	0% {
		opacity: 1;
	}
	100% {
		opacity: 0;
	}
}

/* SVAS - COMMON */
.svas-a-link {
	text-decoration: underline; 
	cursor: pointer; 
	color: #007bff
}

.button-copy {
	opacity: 0.7;
}

.button-copy:hover {
	opacity: 1;
}

.button-copy:active {
	opacity: 1;
	width: 58px;
	height: 58px;
}


.svas-overlay-container {
    background: rgba(0,0,0,0.5);
    width: 100%;
    height: 100%;
    min-height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1003;
    display: block;
	user-select: none;
}

.svas-horizontal-separator {
	height:2em;
	width:100%;
    font-size: 1em;
}

.svas-horizontal-separator-small {
	height:1em;
	width:100%;
    font-size: 0.5em;
}

.svas-notification-valid {
	font-size:0.8em;
	color:#1bdc24;	
}

.svas-notification {
    display: block;
    font-size:0.8em;
	text-align: right;
}

.svas-notification-error {
	font-size:0.8em;
	color:#DC0000;
	margin-top:0.2em;
	margin-bottom:0.5em;
	display:block;
}

.svas-notification-tip {
	color:#0066dc;
	font-size:1.5em;
}

.svas-close-button {
	background:url('media/graphics/sprites/ui/popup-button-close.png') no-repeat;
	background-size: cover;
	position: absolute;
	right: 12px;
	top: 12px;
	width: 32px;
	height: 32px;
	opacity: 0.8;
}
.svas-close-button:hover {
	opacity: 1;
	cursor: pointer;
}
.svas-close-button:before, .svas-close-button:after {
	position: absolute;
	left: 15px;
	height: 33px;
	width: 2px;
	background-color: #656363;
}
.svas-close-button:before {
/*	-webkit-transform: rotate(45deg);
	-moz-transform: rotate(45deg);
	-ms-transform: rotate(45deg);
	-o-transform: rotate(45deg);
	transform: rotate(45deg);*/
}
.svas-close-button:after {
/*	-webkit-transform: rotate(45deg);
	-moz-transform: rotate(45deg);
	-ms-transform: rotate(45deg);
	-o-transform: rotate(45deg);
	transform: rotate(-45deg);*/
}

.svas-box {
	background: rgba(255, 255, 255	, 1);
	font-size: 1em;
	width: 80%;
	padding: 0.5em;
	overflow: auto;
	border-radius: 18px;
	-webkit-border-radius: 18px;
	-moz-border-radius: 18px;
	outline-style: solid;
    outline-color: #AAAAAA;
    outline-width: 4px;
    outline-offset: -4px;
    font-family: montserrat;
}

.svas-box-center {
	float: left;
	margin: auto;
	padding: auto;
	position: absolute;
	top: 50%;
	left: 50%;
	max-width: 500px;
	-webkit-transform: translate(-50%, -50%);
	-moz-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	-o-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
}

.svas-box-leaderboard {
	max-height: 90%;
	max-width :700px;
	overflow: none;
}

.svas-box-header {
	font-family: montserrat-bold;
	text-align: center;
	font-size: 1.5em;
	text-transform: uppercase;
	font-weight: bold;
	color: #F07328;
	margin: 0.5em 0.5em 0em 0.5em;
}

.svas-box-subheader {
	font-size: 0.8em;
	margin: 0.8em;
}

.svas-box-footer {
	font-size: 1.2em;
	text-align: right;
}

.svas-box-footer-leaderboard {
	margin: 0.5em;
	text-align: center;
}

.svas-box-contents {
	margin: 0.5em;
}

.svas-box-contents-highlight {
	color: #e43630;
}

.svas-box-contents-leaderboard {
	margin: 0.5em;
	height: 23em;
	max-height: 55vh;
	overflow-y: scroll;
}

.svas-box-form-input {
	margin-top: 0.2em;
	margin-bottom: 0.5em;
	width: 100%;
	font-size: 1.2em;
	border: 1px solid #ccc;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
	padding: 0.3em;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	-ms-box-sizing: border-box;
	-o-box-sizing: border-box;
	box-sizing: border-box;
}

.svas-submit-button {
	text-decoration: none;
	text-align: center;
	color: #ffffff;
	display: block;
	font-family: montserrat;
	font-weight: bold;
	border: none;
	background: #48c107;
	font-size: 1.7em;
	padding: 0.2em;
	min-width: 50%;
	max-width: 80%;
	margin-left: 50%;
	margin-bottom: 0.5em;
	-webkit-transform: translate(-50%, 0%);
	-moz-transform: translate(-50%, 0%);
	-ms-transform: translate(-50%, 0%);
	-o-transform: translate(-50%, 0%);
	transform: translate(-50%, 0%);
	border-style: solid;
	border-color: #60f410;
	border-width: 3px;
	float: left;
}
.svas-submit-button:hover {
	background: #60f410;
	cursor: pointer;
}

.svas-box-col-header {
	padding: 0 0.5em 0 0.45em;
	font-size: 1em;
	font-weight: 800;
}

.svas-box-username {
	padding: 0.5em;
}

.svas-box-score {
	padding: 0.5em;
}

.svas-box-header {
	font-family: montserrat-bold;
	text-align: center;
	font-size: 1.6em;
	text-transform: uppercase;
	font-weight: bold;
	color: #e43630;
	margin: 0.5em 0.5em 0em 0.5em;
}

.svas-column {
	display: block;
	float:left;
	margin: 0;
    padding: 0;
	height: 2em;
}
.svas-column:first-child { margin-left: 0; }

.svas-span-10-of-10 {
	width: 100%;
}

.svas-span-9-of-10 {
	width: 89.84%;
}

.svas-span-8-of-10 {
	width: 79.68%;
}

.svas-span-7-of-10 {
	width: 69.52%; 
}

.svas-span-6-of-10 {
	width: 59.36%; 
}

.svas-span-5-of-10 {
	width: 49.2%; 
}

.svas-span-4-of-10 {
	width: 39.04%; 
}

.svas-span-3-of-10 {
	width: 28.88%;
    display: -webkit-box;      /* OLD - iOS 6-, Safari 3.1-6 */
    display: -moz-box;         /* OLD - Firefox 19- (buggy but mostly works) */
    display: -ms-flexbox;      /* TWEENER - IE 10 */
    display: -webkit-flex;     /* NEW - Chrome */
    display: flex;             /* NEW, Spec - Opera 12.1, Firefox 20+ */
    justify-content: flex-end;
}

.svas-span-2-of-10 {
	width: 18.72%; 
}
.svas-span-2-of-10 .svas-box-score {
	padding-left: 1.4em !important;
}

.svas-span-1-of-10 {
	width: 8.56%; 
}

/*ALLIANCE*/

/* alliance - reCAPTCHA */
.alliance-recaptcha-branding {
	color: #B4B4B4;
	font-size: 0.7em;
}

/* alliance - LOADER */
.alliance-loader-container {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 99999;
}

.alliance-loader-spinner {
	color: official;
	display: inline-block;
	position: relative;
	width: 80px;
	height: 80px;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	-webkit-transform: translate(-50%, -50%);
}

.alliance-loader-spinner div {
	transform-origin: 40px 40px;
	animation: alliance-loader-spinner 1.2s linear infinite;
}

.alliance-loader-spinner div:after {
	content: " ";
	display: block;
	position: absolute;
	top: 3px;
	left: 37px;
	width: 6px;
	height: 18px;
	border-radius: 20%;
	background: #fff;
}

.alliance-loader-spinner div:nth-child(1) {
	transform: rotate(0deg);
	animation-delay: -1.1s;
}

.alliance-loader-spinner div:nth-child(2) {
	transform: rotate(30deg);
	animation-delay: -1s;
}

.alliance-loader-spinner div:nth-child(3) {
	transform: rotate(60deg);
	animation-delay: -0.9s;
}

.alliance-loader-spinner div:nth-child(4) {
	transform: rotate(90deg);
	animation-delay: -0.8s;
}

.alliance-loader-spinner div:nth-child(5) {
	transform: rotate(120deg);
	animation-delay: -0.7s;
}

.alliance-loader-spinner div:nth-child(6) {
	transform: rotate(150deg);
	animation-delay: -0.6s;
}

.alliance-loader-spinner div:nth-child(7) {
	transform: rotate(180deg);
	animation-delay: -0.5s;
}

.alliance-loader-spinner div:nth-child(8) {
	transform: rotate(210deg);
	animation-delay: -0.4s;
}

.alliance-loader-spinner div:nth-child(9) {
	transform: rotate(240deg);
	animation-delay: -0.3s;
}

.alliance-loader-spinner div:nth-child(10) {
	transform: rotate(270deg);
	animation-delay: -0.2s;
}

.alliance-loader-spinner div:nth-child(11) {
	transform: rotate(300deg);
	animation-delay: -0.1s;
}

.alliance-loader-spinner div:nth-child(12) {
	transform: rotate(330deg);
	animation-delay: 0s;
}

@keyframes alliance-loader-spinner {
	0% {
		opacity: 1;
	}
	100% {
		opacity: 0;
	}
}

/* alliance - COMMON */
.alliance-a-link {
	text-decoration: underline; 
	cursor: pointer; 
	color: #007bff
}

.alliance-overlay-container {
    background: rgba(0,0,0,0.5);
    width: 100%;
    height: 100%;
    min-height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1003;
    display: block;
	user-select: none;
}

.alliance-horizontal-separator {
	height:2em;
	width:100%;
    font-size: 1em;
}

.alliance-horizontal-separator-small {
	height:1em;
	width:100%;
    font-size: 0.5em;
}

.alliance-notification-valid {
	font-size:0.8em;
	color:#1bdc24;	
}

.alliance-notification {
    display: block;
    font-size:0.8em;
	text-align: right;
}

.alliance-notification-error {
	font-size:0.8em;
	color:#DC0000;
	margin-top:0.2em;
	margin-bottom:0.5em;
	display:block;
}

.alliance-notification-tip {
	color:#DCDC00;
	font-size:0.8em;
}

.alliance-close-button {
	background:url('media/graphics/sprites/ui/popup-button-close.png') no-repeat;
	background-size: cover;
	position: absolute;
	right: 0px;
	top: 0px;
	width: 32px;
	height: 32px;
	opacity: 0.8;
}
.alliance-close-button:hover {
	opacity: 1;
	cursor: pointer;
}
.alliance-close-button:before, .alliance-close-button:after {
	position: absolute;
	left: 15px;
	height: 33px;
	width: 2px;
	background-color: #656363;
}
.alliance-close-button:before {
/*	-webkit-transform: rotate(45deg);
	-moz-transform: rotate(45deg);
	-ms-transform: rotate(45deg);
	-o-transform: rotate(45deg);
	transform: rotate(45deg);*/
}
.alliance-close-button:after {
/*	-webkit-transform: rotate(45deg);
	-moz-transform: rotate(45deg);
	-ms-transform: rotate(45deg);
	-o-transform: rotate(45deg);
	transform: rotate(-45deg);*/
}


@media only screen and (min-width: 481px) {

	.alliance-box {
		background: rgba(255, 255, 255	, 1);
		font-size: 1em;
		width: 80%;
		padding: 0.5em;
		border-radius: 18px;
		-webkit-border-radius: 18px;
		-moz-border-radius: 18px;
		outline-style: solid;
	    outline-color: #AAAAAA;
	    outline-width: 4px;
	    outline-offset: -4px;
	    font-family: montserrat;
	}
	.alliance-box-center {
		margin: auto;
		padding: auto;
		position: absolute;
		top: 48%;
		left: 50%;
		max-width: 500px;
		-webkit-transform: translate(-50%, -50%);
		-moz-transform: translate(-50%, -50%);
		-ms-transform: translate(-50%, -50%);
		-o-transform: translate(-50%, -50%);
		transform: translate(-50%, -50%);
	}

}

@media only screen and (max-width: 480px) {

	.alliance-box {
		background: rgba(255, 255, 255	, 1);
		font-size: 1em;
		width: 86%;
		padding: 0.5em;
		border-radius: 18px;
		-webkit-border-radius: 18px;
		-moz-border-radius: 18px;
		outline-style: solid;
	    outline-color: #AAAAAA;
	    outline-width: 4px;
	    outline-offset: -4px;
	    font-family: montserrat;
	}

	.alliance-box-center {
		margin: auto;
		padding: auto;
		position: absolute;
		left: 3%;
		top: 10%;
		max-width: 500px;
		-webkit-transform: translate(-50%, -50%);
		-moz-transform: translate(-50%, -50%);
		-ms-transform: translate(-50%, -50%);
		-o-transform: translate(-50%, -50%);
		transform: translate(-50%, -50%);

		-webkit-transform: scale(0.8,0.8);
		-moz-transform: scale(0.8,0.8);
		-ms-transform: scale(0.8,0.8);
		-o-transform: scale(0.8,0.8);
		transform: scale(0.8,0.8);
	}

}



.alliance-box-notification {
	max-height: 90%;
	max-width :500px;
	text-align: center;	
	color: #ffffff;
	background: rgba(0, 0, 0	, 0.8);
	font-size: 1em;
	width: 80%;
	padding: 0.5em;
	border-radius: 5px;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	outline-style: solid;
    outline-color: #AAAAAA;
    outline-width: 2px;
    outline-offset: -2px;
    font-family: montserrat;

	float: left;
	margin: auto;
	padding: auto;
	position: absolute;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-moz-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	-o-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);

}

.alliance-notification-container {
    width: 100%;
    height: 100%;
    min-height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1003;
    display: block;
	user-select: none;
}


.alliance-box-leaderboard {
	max-height: 80%;
	max-width :800px;
	padding-left: 30px;	
}

.alliance-box-gift {
	max-height: 90%;
	max-width :600px;
	padding-left: 30px;	
}

.alliance-box-join-alliance {
	max-height: 90%;
	max-width :500px;
	padding-left: 30px;	
}

.alliance-box-create-new {
	max-height: 90%;
	max-width :600px;
	padding-left: 30px;	
}

.alliance-box-read {
	max-height: 90%;
	max-width :800px;
	padding-left: 30px;	
}

.alliance-box-header {
	font-family: montserrat-bold;
	text-align: center;
	font-size: 1.5em;
	text-transform: uppercase;
	font-weight: bold;
	color: #F07328;
	margin: 0.5em 0.5em 0em 0.5em;
}

.alliance-box-subheader {
	/*font-size: 0.8em;*/
	/*margin: 0.8em;*/
}

.alliance-box-footer {
	font-size: 1.2em;
	text-align: right;
}

.alliance-box-footer-leaderboard {
	margin: 0.5em;
	text-align: center;
}

.alliance-box-contents {
	margin: 0.5em;
}

.alliance-box-contents-highlight {
	color: #e43630;
}

.alliance-box-contents-leaderboard {

	margin-left: 0px;
	margin-right: 0px;
	margin-top: 0.5em;
	margin-bottom: 0.5em;

	height: 23em;
	max-height: 55vh;
	overflow-y: scroll;
}

.alliance-box-form-input {
	margin-top: 0.2em;
	margin-bottom: 0.5em;
	width: 100%;
	font-size: 1.2em;
	border: 1px solid #ccc;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
	padding: 0.3em;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	-ms-box-sizing: border-box;
	-o-box-sizing: border-box;
	box-sizing: border-box;
}

.alliance-submit-button {
	text-decoration: none;
	text-align: center;
	color: #ffffff;
	display: block;
	font-family: montserrat;
	font-weight: bold;
	border: none;
	background: #48c107;
	font-size: 1em;
	padding-left: 0.2em;
	padding-right: 0.2em;
	min-width: 30%;
	max-width: 31%;
	min-height: 30px;
	-webkit-transform: translate(-50%, 0%);
	-moz-transform: translate(-50%, 0%);
	-ms-transform: translate(-50%, 0%);
	-o-transform: translate(-50%, 0%);
	transform: translate(-50%, 0%);
	border-style: solid;
	border-color: #60f410;
	border-width: 2px;
	float: left;
	position: fixed;
}
.alliance-submit-button:hover {
	background: #60f410;
	cursor: pointer;
}

.alliance-create-button {
	text-decoration: none;
	text-align: center;
	color: #ffffff;
	display: block;
	font-family: montserrat;
	font-weight: bold;
	border: none;
	background: #48c107;
	font-size: 1em;
	padding-left: 0.2em;
	padding-right: 0.2em;
	padding-top: 5px;
	min-width: 31%;
	max-width: 32%;
	min-height: 22px;
	margin-left: 16%;
	margin-top: -2px;
	-webkit-transform: translate(-50%, 0%);
	-moz-transform: translate(-50%, 0%);
	-ms-transform: translate(-50%, 0%);
	-o-transform: translate(-50%, 0%);
	transform: translate(-50%, 0%);
	border-style: solid;
	border-color: #60f410;
	border-width: 2px;
	float: left;
	position: fixed;
}
.alliance-create-button:hover {
	background: #60f410;
	cursor: pointer;
}

.alliance-readform-button-disabled {
	text-decoration: none;
	text-align: center;
	color: #ffffff;
	display: block;
	font-family: montserrat;
	font-weight: bold;
	border: none;
	background: #aaaaaa;
	font-size: 1em;
	padding-left: 0.1em;
	padding-right: 0.1em;
	padding-top: 0.8em;
	padding-bottom: : 0em;
	min-width: 14%;
	max-width: 15%;
	min-height: 30px;
	margin-left: 8%;
	margin-top: 10px;
	-webkit-transform: translate(-50%, 0%);
	-moz-transform: translate(-50%, 0%);
	-ms-transform: translate(-50%, 0%);
	-o-transform: translate(-50%, 0%);
	transform: translate(-50%, 0%);
	border-style: solid;
	border-color: #808080;
	border-width: 2px;
	float: left;
	position: fixed;
}

.alliance-readform-button {
	text-decoration: none;
	text-align: center;
	color: #ffffff;
	display: block;
	font-family: montserrat;
	font-weight: bold;
	border: none;
	background: #48c107;
	font-size: 1em;
	padding-left: 0.1em;
	padding-right: 0.1em;
	padding-top: 0.8em;
	padding-bottom: : 0em;
	min-width: 14%;
	max-width: 15%;
	min-height: 30px;
	margin-left: 8%;
	margin-top: 10px;
	-webkit-transform: translate(-50%, 0%);
	-moz-transform: translate(-50%, 0%);
	-ms-transform: translate(-50%, 0%);
	-o-transform: translate(-50%, 0%);
	transform: translate(-50%, 0%);
	border-style: solid;
	border-color: #60f410;
	border-width: 2px;
	float: left;
	position: fixed;
}
.alliance-readform-button:hover {
	background: #60f410;
	cursor: pointer;
}


.alliance-readform-button-list-disabled {
	text-decoration: none;
	text-align: center;
	color: #ffffff;
	display: block;
	font-family: montserrat;
	font-weight: bold;
	border: none;
	background: #aaaaaa;
	font-size: 1em;
	padding-left: 0.1em;
	padding-right: 0.1em;
	padding-top: 0.8em;
	padding-bottom: : 0em;
	min-width: 10%;
	max-width: 12%;
	min-height: 30px;
	margin-left: 8%;
	margin-top: 10px;
	-webkit-transform: translate(-50%, 0%);
	-moz-transform: translate(-50%, 0%);
	-ms-transform: translate(-50%, 0%);
	-o-transform: translate(-50%, 0%);
	transform: translate(-50%, 0%);
	border-style: solid;
	border-color: #808080;
	border-width: 2px;
	float: left;
	position: fixed;
}

.alliance-readform-button-list {
	text-decoration: none;
	text-align: center;
	color: #ffffff;
	display: block;
	font-family: montserrat;
	font-weight: bold;
	border: none;
	background: #48c107;
	font-size: 1em;
	padding-left: 0.1em;
	padding-right: 0.1em;
	padding-top: 0.8em;
	padding-bottom: : 0em;
	min-width: 10%;
	max-width: 12%;
	min-height: 30px;
	margin-left: 8%;
	margin-top: 10px;
	-webkit-transform: translate(-50%, 0%);
	-moz-transform: translate(-50%, 0%);
	-ms-transform: translate(-50%, 0%);
	-o-transform: translate(-50%, 0%);
	transform: translate(-50%, 0%);
	border-style: solid;
	border-color: #60f410;
	border-width: 2px;
	float: left;
	position: fixed;
}
.alliance-readform-button-list:hover {
	background: #60f410;
	cursor: pointer;
}

@media only screen and (min-width: 481px) {
	.alliance-listing-order-button {
		text-decoration: none;
	    text-align: center;
	    color: #ffffff;
	    display: block;
	    font-family: montserrat;
	    font-weight: bold;
	    border: none;
	    background: #ff3f58;
	    font-size: 12px;
	    padding-left: 0.1em;
	    padding-right: 0.1em;
	    min-width: 22%;
	    max-width: 23%;
	    min-height: 23px;
	    margin-left: 10%;
	    margin-top: -1px;
	    -webkit-transform: translate(-50%, 0%);
	    -moz-transform: translate(-50%, 0%);
	    -ms-transform: translate(-50%, 0%);
	    -o-transform: translate(-50%, 0%);
	    transform: translate(-50%, 0%);
	    border-style: solid;
	    border-color: #fe7e79;
	    border-width: 2px;
	    float: left;
	    position: fixed;
	}
}

@media only screen and (max-width: 480px) {
	.alliance-listing-order-button {
		text-decoration: none;
	    text-align: center;
	    color: #ffffff;
	    display: block;
	    font-family: montserrat;
	    font-size: 12px;
	    border: none;
	    background: #ff3f58;
	    font-size: 1em;
	    padding-left: 0.1em;
	    padding-right: 0.1em;
	    min-width: 16%;
	    max-width: 18%;
	    min-height: 23px;
	    margin-left: 10%;
	    margin-top: -1px;
	    -webkit-transform: translate(-50%, 0%);
	    -moz-transform: translate(-50%, 0%);
	    -ms-transform: translate(-50%, 0%);
	    -o-transform: translate(-50%, 0%);
	    transform: translate(-50%, 0%);
	    border-style: solid;
	    border-color: #fe7e79;
	    border-width: 2px;
	    float: left;
	    position: fixed;
	}
}

.alliance-listing-order-button:hover {
	background: #da3046;
	cursor: pointer;
}

.alliance-claim-button {
	text-decoration: none;
	text-align: center;
	color: #ffffff;
	display: block;
	font-family: montserrat;
	font-weight: bold;
	border: none;
	background: #48c107;
	font-size: 14px;
	padding-left: 0.1em;
	padding-right: 0.1em;
	padding-top: 10px;
	padding-bottom: : 0em;
	min-width: 65px;
	min-height: 24px;
	margin-left: 65%;
	margin-top: 2px;
	-webkit-transform: translate(-50%, 0%);
	-moz-transform: translate(-50%, 0%);
	-ms-transform: translate(-50%, 0%);
	-o-transform: translate(-50%, 0%);
	transform: translate(-50%, 0%);
	border-style: solid;
	border-color: #60f410;
	border-width: 2px;
	float: left;
	position: relative;
}
.alliance-claim-button:hover {
	background: #60f410;
	cursor: pointer;
}
.alliance-claim-button:disabled {
	background: #505050;
	border-color: #aaaaaa;
}
@media only screen and (min-width: 481px) {
	.alliance-search-button {
		text-decoration: none;
		text-align: center;
		color: #ffffff;
		display: unset;
		font-family: montserrat;
		font-weight: bold;
		border: none;
		background: #48c107;
		font-size: 1em;
		padding-left: 0.2em;
		padding-right: 0.2em;
		padding-top: 0.6em;
		min-width: 30%;
		max-width: 40%;
		height: 26px;
		margin-left: 19%;
		-webkit-transform: translate(-50%, 0%);
		-moz-transform: translate(-50%, 0%);
		-ms-transform: translate(-50%, 0%);
		-o-transform: translate(-50%, 0%);
		transform: translate(-50%, 0%);
		border-style: solid;
		border-color: #60f410;
		border-width: 2px;
		float: left;
		position: fixed;
	}
}

@media only screen and (max-width: 480px) {
	.alliance-search-button {
		text-decoration: none;
		text-align: center;
		color: #ffffff;
		display: unset;
		font-family: montserrat;
		font-weight: bold;
		border: none;
		background: #48c107;
		font-size: 1em;
		padding-left: 0.2em;
		padding-right: 0.2em;
		padding-top: 0.6em;
		min-width: 23%;
		max-width: 24%;
		min-height: 18px;
		margin-left: 19%;
		-webkit-transform: translate(-50%, 0%);
		-moz-transform: translate(-50%, 0%);
		-ms-transform: translate(-50%, 0%);
		-o-transform: translate(-50%, 0%);
		transform: translate(-50%, 0%);
		border-style: solid;
		border-color: #60f410;
		border-width: 2px;
		float: left;
		position: fixed;
		margin-left: 54px;
	}
}


@media only screen and (max-width: 480px) {
	.banned-image-show{
		max-width: 50%;
		position: fixed;
		opacity: 0.3;
		left: 25%;	
		top: 50%;	
	}
	.banned-image-hide{
		max-width: 50%;
		position: fixed;
		opacity: 0;
		left: 25%;	
		top: 50%;	
	}
}

@media only screen and (min-width: 481px) {
	.banned-image-show{
		max-width: 50%;
		position: fixed;
		opacity: 0.3;
		left: 25%;	
		top: 25%;	
	}	
	.banned-image-hide{
		max-width: 50%;
		position: fixed;
		opacity: 0;
		left: 25%;	
		top: 25%;	
	}	
}

.alliance-search-button:hover {
	background: #60f410;
	cursor: pointer;
}


.alliance-nav-icon-button {
	width:16px;
	height:44px;
	margin-top:0px
}

.alliance-nav-icon-button:hover {
	width: 14px;
	height: 40px;
	margin-top:2px
}

@media only screen and (min-width: 481px) {
	.alliance-nav-icon-button-right{
		margin-left: 11px;		
	}
}

@media only screen and (max-width: 480px) {
	.alliance-nav-icon-button-right{
		margin-left: 48px;
	}
}


.header-color {
	background-color: #505050;
	color: #ffffff;
	padding-top: 8px;
    padding-bottom: 8px;
}

.alliance-box-col-header {
	padding: 0 0.5em 0 0.45em;    
	padding-left: 0.2em;
	padding-right: 0.2em;
	padding-top: 0.5em;
	padding-bottom: 0.5em;
	font-size: 0.9em;
	font-weight: 800;
}

.alliance-box-username {
	padding-left: 0.2em;
	padding-right: 0.2em;
	padding-top: 0.5em;
	padding-bottom: 0.5em;
}



@media only screen and (min-width: 481px) {
	.alliance-box-listdata {
		padding-left: 0.1em;
		padding-right: 0.1em;
		padding-top: 4px;
		padding-bottom: 0px;
	}
}

@media only screen and (max-width: 480px) {
	.alliance-box-listdata {
		padding-left: 0.1em;
		padding-right: 0.1em;
		padding-top: 4px;
		padding-bottom: 0px;
		font-size: 13px;
	}
}

.alliance-box-score {
	padding: 0.5em;
}

.alliance-box-header {
	font-family: montserrat-bold;
	text-align: center;
	font-size: 1.6em;
	font-weight: bold;
	color: #e43630;
	margin: 0.1em 0.5em 0.1em 0.5em;
}

.alliance-column {
	display: block;
	float:left;
	margin: 0;
    padding: 0;
	height: 2em;
}

.alliance-span-10-of-10 {
	width: 100%;
	vertical-align: middle;
}

.alliance-span-9-of-10 {
	width: 90%;
	vertical-align: middle;
}

.alliance-span-8-of-10 {
	width: 80%;
	vertical-align: middle;
}

.alliance-span-7-of-10 {
	width: 70%; 
	vertical-align: middle;
}

.alliance-span-6-of-10 {
	width: 60%; 
	vertical-align: middle;
}

.alliance-span-5-of-10 {
	width: 50%; 
	vertical-align: middle;
}

.alliance-span-4-of-10 {
	width: 40%; 
	vertical-align: middle;
}

.alliance-span-3-of-10 {
	width: 30%; 
	vertical-align: middle;
}

.alliance-span-2-of-10 {
	width: 20%; 
	vertical-align: middle;
}

.alliance-span-1-of-10 {
	width: 10%; 
	vertical-align: middle;
}

.alliance-span-five-percent {
	width: 5%; 
	vertical-align: middle;
}

.top-space {
	margin-top: 10px;
}

.top-space-password {
	margin-top: 30px;
}


/*
responsive grid system
*/


/* html5reset.css - 01/11/2011 */

html, body, div, span, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
abbr, address, cite, code,
del, dfn, em, img, ins, kbd, q, samp,
small, strong, sub, sup, var,
b, i,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, figcaption, figure, 
footer, header, hgroup, menu, nav, section, summary,
time, mark, audio, video {
    margin: 0;
    padding: 0;
    border: 0;
    outline: 0;
    font-size: 100%;
    vertical-align: baseline;
}

body {
    line-height: 1;
}

article,aside,details,figcaption,figure,
footer,header,hgroup,menu,nav,section { 
	display: block;
}

nav ul {
    list-style: none;
}

blockquote, q {
    quotes: none;
}

blockquote:before, blockquote:after,
q:before, q:after {
    content: '';
    content: none;
}

a {
    margin: 0;
    padding: 0;
    font-size: 100%;
    vertical-align: baseline;
    background: transparent;
}

/* change colours to suit your needs */
ins {
    background-color: #ff9;
    color: #000;
    text-decoration: none;
}

/* change colours to suit your needs */
mark {
    background-color: #ff9;
    color: #000; 
    font-style: italic;
    font-weight: bold;
}

del {
    text-decoration:  line-through;
}

abbr[title], dfn[title] {
    border-bottom: 1px dotted;
    cursor: help;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

/* change border colour to suit your needs */
hr {
    display: block;
    height: 1px;
    border: 0;   
    border-top: 1px solid #cccccc;
    margin: 1em 0;
    padding: 0;
}

input, select {
    vertical-align: middle;
}

/*  SECTIONS  ============================================================================= */

.section {
	clear: both;
	padding: 0px;
	margin: 0px;
}

/*  GROUPING  ============================================================================= */


.group:before,
.group:after {
    content:"";
    display:table;
}
.group:after {
    clear:both;
}
.group {
    zoom:1; /* For IE 6/7 (trigger hasLayout) */
}

/*  GRID COLUMN SETUP   ==================================================================== */

.col {
	display: block;
	float:left;
	margin: 4px -1px 4px 0px;
}

.col:first-child { margin-left: 6px; } /* all browsers except IE6 and lower */


/*  REMOVE MARGINS AS ALL GO FULL WIDTH AT 480 PIXELS */

@media only screen and (max-width: 480px) {
	.col { 
		margin: 1% -2px 1% 0%;
		font-size: 12px;
	}
}

/*  GRID OF TWELVE   ============================================================================= */

.span_12_of_12 {
	width: 100%;
}

.span_11_of_12 {
	width: 91.53%;
}

.span_10_of_12 {
	width: 83.06%;
}

.span_9_of_12 {
	width: 74.6%;
}

.span_8_of_12 {
	width: 66.13%;
}

.span_7_of_12 {
	width: 57.66%; 
}

.span_6_of_12 {
	width: 49.2%; 
}

.span_5_of_12 {
	width: 40.73%; 
}

.span_4_of_12 {
	width: 32.26%; 
}

.span_3_of_12 {
	width: 23.8%;
}

.span_2_of_12 {
	width: 15.33%; 
}

.span_1_of_12 {
	width: 6.86%; 
}

.top-space-text {
	padding-top: 5px; 
}


/*  GO FULL WIDTH AT LESS THAN 480 PIXELS */

@media only screen and (max-width: 480px) {
	/*.span_12_of_12 {
		width: 100%; 
	}
	.span_11_of_12 {
		width: 100%; 
	}
	.span_10_of_12 {
		width: 100%; 
	}
	.span_9_of_12 {
		width: 100%; 
	}
	.span_8_of_12 {
		width: 100%; 
	}
	.span_7_of_12 {
		width: 100%; 
	}
	.span_6_of_12 {
		width: 100%; 
	}
	.span_5_of_12 {
		width: 100%; 
	}
	.span_4_of_12 {
		width: 100%; 
	}
	.span_3_of_12 {
		width: 100%; 
	}
	.span_2_of_12 {
		width: 100%; 
	}
	.span_1_of_12 {
		width: 100%; 
	}*/

	.span_12_of_12 {
		width: 100%;
	}

	.span_11_of_12 {
		width: 91.53%;
	}

	.span_10_of_12 {
		width: 83.06%;
	}

	.span_9_of_12 {
		width: 74.6%;
	}

	.span_8_of_12 {
		width: 66.13%;
	}

	.span_7_of_12 {
		width: 57.66%; 
	}

	.span_6_of_12 {
		width: 49.2%; 
	}

	.span_5_of_12 {
		width: 40.73%; 
	}

	.span_4_of_12 {
		width: 32.26%; 
	}

	.span_3_of_12 {
		width: 23.8%;
	}

	.span_2_of_12 {
		width: 15%; 
	}

	.span_1_of_12 {
		width: 6.86%; 
	}

}

.textbackground {
	background-color: #dddddd;
}