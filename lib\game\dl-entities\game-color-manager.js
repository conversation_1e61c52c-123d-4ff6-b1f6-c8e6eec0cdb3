ig.module(
    'game.dl-entities.game-color-manager'
).requires(
    'dl.game.entity'
).defines(function () {
    dl.EntityGameColorManager = dl.Entity
        .extend({
            staticInstantiate: function () {
                this.parent();
                this.capitalList = [];
                this.nationList = [];
                this.colorList = {
                    player1: ['#FFBCBC', '#FE6A6A'], /* red */
                    player2: ['#ADC1DF', '#608DD2'], /* blue */
                    player3: ['#67C89C', '#25BB76'], /* green */
                    player4: ['#FFE5AC', '#FDCB5A'], /* yellow */
                    neutral: '#D1CFD1'
                };

                return this;
            },

            postInit: function () {
                this.parent();
                this.setCapitalsColor();
                this.setNationsColor();
            },

            setCapitalsColor: function () {
                if (!this.capitalList) return;
                if (!this.capitalList.length) return;

                for (var i = 0; i < this.capitalList.length; i++) {
                    var capital = this.capitalList[i];
                    if (capital.capitalData.assignedPlayer) {
                        // console.log('Capital have assigned player', capital.capitalData.capitalIndex);
                        var playerNumber = ig.game.client.clientGameRoom.getPlayerNumber(capital.capitalData.assignedPlayer.playerId);
                        var colorScale = this.identifyColorScale(playerNumber);
                        capital.setColor(colorScale(100).darken(2.5).hex());
                    }
                }
            },

            setNationsColor: function () {
                if (!this.nationList) return;
                if (!this.nationList.length) return;

                for (var i = 0; i < this.nationList.length; i++) {
                    var nation = this.nationList[i];
                    if (nation.nationData.assignedPlayer) {
                        // console.log('Capital have assigned player', nation.nationData.nationIndex);
                        var soldierCount = this.capitalList.find(function (capital) {
                            return capital.capitalData.capitalIndex === nation.nationData.nationIndex;
                        }).capitalData.soldierCount;
                        var playerNumber = ig.game.client.clientGameRoom.getPlayerNumber(nation.nationData.assignedPlayer.playerId);
                        var colorScale = this.identifyColorScale(playerNumber);
                        nation.setColor(colorScale(soldierCount).hex());
                    }
                }
            },

            updateCapitalColor: function (capital) {
                if (!capital) return;
                var gameManager = ig.game.managers.game;
                var playerNumber = gameManager.getPlayerNumber(capital.capitalData.assignedPlayer.playerId);
                var colorScale = this.identifyColorScale(playerNumber);
                capital.setColor(colorScale(100).darken(2.5).hex());
            },

            updateNationColor: function (nation) {
                if (!nation) return;

                // console.log('Nation Data', nation.nationData);
                var gameManager = ig.game.managers.game;
                var soldierCount = gameManager.findCapitalByNation(nation).capitalData.soldierCount;
                var playerNumber = gameManager.getPlayerNumber(nation.nationData.assignedPlayer.playerId);
                var colorScale = this.identifyColorScale(playerNumber);
                nation.setColor(colorScale(soldierCount).hex());
                // console.log(playerNumber, colorScale(soldierCount).hex());
            },

            updateNationColorByCapital: function (nation) {
                if (!nation) return;
                if (!nation.nationData) return;

                // console.log('Nation Data', nation.nationData);
                try {
                    var gameManager = ig.game.managers.game;
                    var soldierCount = nation.capital.capitalData.soldierCount;
                    var playerNumber = gameManager.getPlayerNumber(nation.nationData.assignedPlayer.playerId);
                    var colorScale = this.identifyColorScale(playerNumber);
                    nation.setColor(colorScale(soldierCount).hex());
                } catch (e) {}
                // console.log(playerNumber, colorScale(soldierCount).hex());
            },

            getSoldierColor: function (capital) {
                var gameManager = ig.game.managers.game;
                var playerNumber = gameManager.getPlayerNumber(capital.capitalData.assignedPlayer.playerId);
                var colorScale = this.identifyColorScale(playerNumber);
                return colorScale(100).darken(1).hex();
            },

            identifyColorScale: function (playerNum) {
                var colorScale = null;
                switch (playerNum) {
                    case 1:
                        colorScale = ig.game.chroma.scale([this.colorList.player1[0], this.colorList.player1[1]]).domain([0, 100]);
                        break;
                    case 2:
                        colorScale = ig.game.chroma.scale([this.colorList.player2[0], this.colorList.player2[1]]).domain([0, 100]);
                        break;
                    case 3:
                        colorScale = ig.game.chroma.scale([this.colorList.player3[0], this.colorList.player3[1]]).domain([0, 100]);
                        break;
                    case 4:
                        colorScale = ig.game.chroma.scale([this.colorList.player4[0], this.colorList.player4[1]]).domain([0, 100]);
                        break;
                    default:
                        colorScale = ig.game.chroma.scale([this.colorList.neutral, this.colorList.neutral]).domain([0, 100]);
                        break;
                }
                return colorScale;
            },

            update: function () {
                this.parent();
            },

            updateData: function (data) {
                if (!data) return;
            }
        });
});
