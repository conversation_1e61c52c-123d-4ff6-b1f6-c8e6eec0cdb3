ig.module(
    'dl.game.entity.components.position.docker'
).requires(
    'dl.game.entity.components.component'
).defines(function () {
    'use strict';

    /**
     * Reposition object base on another object position
     * @example:
     *  Init component:
            this.c_DockerComponent = this.addComponent(dl.DockerComponent);
     *  Define in class:
            this.cAnimationSheetComponent.updateProperties({
                dockerObject: dockerObject,
                dockerPercent: { x: 0.5, y: 0.5 },
                dockerOffset: { x: 0, y: 0 }
            });
     *  In settings:
            c_DockerComponent: {
                dockerObject: dockerObject,
                dockerPercent: { x: 0.5, y: 0.5 },
                dockerOffset: { x: 0, y: 0 }
            }
     */

    dl.DockerComponent = dl.EntityComponent.extend({
        staticInstantiate: function (entity) {
            this.parent(entity);

            // bound event
            this.boundUpdatePos = this.updatePos.bind(this);

            // properties
            this._dockerObject = null;
            Object.defineProperty(this, 'dockerObject',
                {
                    get: function () { return this._dockerObject; },
                    set: function (v) {
                        if (dl.check.isDefined(this._dockerObject)) {
                            if (dl.check.isFunction(this._dockerObject.offEvent)) {
                                this._dockerObject.offEvent('positionChanged', this.boundUpdatePos);
                            }
                        }

                        this._dockerObject = v;

                        this.updatePos();
                        // bind events
                        if (dl.check.isDefined(this._dockerObject)) {
                            if (dl.check.isFunction(this._dockerObject.onEvent)) {
                                this._dockerObject.onEvent('positionChanged', this.boundUpdatePos);
                            }
                        }
                    }
                });
            this.dockerPercent = { x: 0.5, y: 0.5 };
            this.dockerOffset = { x: 0, y: 0 };

            // calculated properties
            this.pos = { x: 0, y: 0 };
            this.overridingEntity();

            return this;
        },

        setComponentName: function () {
            this._componentName = 'dl_DockerComponent';
        },

        onPropertiesChanged: function (properties) {
            this.updatePos();
        },

        overridingEntity: function () {
            this.bound_Entity_UpdateBasePos = this.entity.updateBasePos.bind(this.entity);
            this.entity.updateBasePos = function () {
                if (this.updateDockerPos()) {
                    this.entity.pos.x = this.pos.x;
                    this.entity.pos.y = this.pos.y;
                } else {
                    this.bound_Entity_UpdateBasePos();
                }
            }.bind(this);
        },

        updatePos: function () {
            this.entity.updatePos();
        },

        updateDockerPos: function () {
            if (!this.enable) return false;
            if (!dl.check.isDefined(this.dockerObject)) return false;

            this.pos.x = this.dockerObject.pos.x +
                this.dockerObject.size.x * (this.dockerPercent.x - 0.5) +
                this.dockerOffset.x;

            this.pos.y = this.dockerObject.pos.y +
                this.dockerObject.size.y * (this.dockerPercent.y - 0.5) +
                this.dockerOffset.y;

            return true;
        }
    });
});
