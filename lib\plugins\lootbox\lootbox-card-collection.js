ig.module('plugins.lootbox.lootbox-card-collection')
    .requires(
        'plugins.lootbox.lootbox-game-object'
    )
    .defines(function () {
        ig.LootboxCardCollection = ig.LootboxGameObject.extend({

            zIndex: 9999,
            onAction: null,
            onBack: null,
            deck: null,
            title: null,
            actionButton: null,
            messageTextField: null,

            init: function (x, y, settings) {
                this.parent(x, y, settings);
                ig.Lootbox.loadData();
                this.forceDraw = true;
                this.onAction = new ig.LootboxSignal();
                this.onBack = new ig.LootboxSignal();

                var collectionData = [];

                for (var i = 0; i < ig.Lootbox.data.cards.length; i++) {
                    var cardData = ig.Lootbox.data.cards[i];
                    var found = false;
                    for (var j = 0; j < collectionData.length; j++) {
                        var collection = collectionData[j];
                        if (collection.id == cardData.id) {
                            if (cardData.level > collection.level) {
                                collection.level = cardData.level;
                            }
                            found = true;
                        }
                    }
                    if (!found) collectionData.push({ id: cardData.id, level: cardData.level });
                }

                var pageW = ig.responsive ? ig.responsive.originalWidth : ig.system.width;
                var pageH = ig.responsive ? ig.responsive.originalHeight : ig.system.height;
                this.deck = new ig.LootboxDeckDisplay(collectionData);
                this.deck.maxSelection = 1;
                this.deck.isSelectable = true;
                this.deck.onCardSelected.add(this.onClickCard, this);

                for (var i = 0; i < this.deck.cards.length; i++) {
                    var card = this.deck.cards[i];
                    if (ig.Lootbox.isUpgradeMode) {
                        card.checkAndSpawnUpgradeButton();
                    } else {
                        if (card.level >= ig.Lootbox.loot.actionableCollectionLevel) {
                            card.isMergeable = true;
                        }
                    }
                }

                this.title = ig.game.spawnEntityBackward(ig.LootboxTextField, pageW / 2, ig.Lootbox.page.titleY, { font: ig.Lootbox.page.titleFont, text: ig.Lootbox.strings.collectionTitle, align: "center", color: "#ffffff", zIndex: 99999 });

                this.actionButton = ig.game.spawnEntityBackward(ig.LootboxSimpleButton, pageW / 2, ig.Lootbox.page.myCollection.actionButtonY, {
                    image: ig.Lootbox.images.simple,
                    font: ig.Lootbox.page.button.font,
                    textColor: ig.Lootbox.page.button.textColor ? ig.Lootbox.page.button.textColor : "#ffffff",
                    offsetY: ig.Lootbox.page.button.offsetY,
                    text: ig.Lootbox.strings.collectionActionButton,
                    zIndex: 99999
                });
                this.actionButton.onClicked.add(this.onClickAction, this)

                this.backButton = ig.game.spawnEntityBackward(ig.LootboxSimpleButton, ig.Lootbox.page.backButtonX, ig.Lootbox.page.backButtonY, { image: ig.Lootbox.images.back, zIndex: 99999 });
                this.backButton.onClicked.addOnce(this.onClickBack, this)

                this.messageTextField = ig.game.spawnEntityBackward(ig.LootboxTextField, pageW / 2, ig.Lootbox.page.myCollection.messageTextY, { font: ig.Lootbox.page.myCollection.messageTextFont, text: ig.Lootbox.strings.collectionButtonMessage, align: "center", color: "#ffffff", zIndex: 99999 });

                if (ig.responsive) {
                    this.backButton.anchorType = "top-left"
                }

                this.actionButton.visible = false;
                ig.game.sortEntitiesDeferred()
            },

            onClickCard: function () {
                ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList[ig.Lootbox.sounds.button]);
                var cards = this.deck.getSelectedCards();
                if (cards.length == 0) {
                    this.actionButton.visible = false;
                } else {
                    if (cards[0].level >= ig.Lootbox.loot.actionableCollectionLevel) {
                        this.actionButton.visible = true;
                    }
                }
            },

            onClickAction: function () {
                var selectedCard = this.deck.getSelectedCards()[0];

                if (!selectedCard) return;
                if (selectedCard.level < ig.Lootbox.loot.actionableCollectionLevel) return;
                this.onAction.dispatch(selectedCard.id);
                this.onAction.clear();
                ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList[ig.Lootbox.sounds.button]);

                this.exitAll();
            },

            onClickBack: function () {
                this.onBack.dispatch();
                this.exitAll();
                ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList[ig.Lootbox.sounds.button]);
            },

            exitAll: function () {

                this.deck.exit();
                this.backButton.exit();
                this.actionButton.exit();
                this.title.exit();
                this.messageTextField.exit();
                this.exit();
            },

            update: function () {
                this.parent();
                if (this.deck) this.deck.update()
            },

            draw: function () {

                var ctx = ig.system.context;
                ctx.save();
                ctx.fillStyle = ig.Lootbox.overlay.color;
                ctx.globalAlpha = ig.Lootbox.overlay.alpha;
                ctx.fillRect(0, 0, ig.system.width, ig.system.height);
                ctx.globalAlpha = 1;
                ctx.restore();

                this.parent();
            },

        });
    });
