/**
 * Created by <PERSON><PERSON>
 * Usage:
 * dl.Entity.extend(MixinNetworkHome);
 */
ig.module(
    'dl.network.mixins.game'
).requires(
    'dl.templates.network-entities.entity-popup-home'
).defines(function () {
    MixinGame = {
        staticInstantiate: function () {
            this.parent();

            return this;
        },

        postInit: function () {
            this.parent();
        },

        resetNetwork: function () {
            ig.game.client.reset();
        },

        checkConnection: function () {
            if (dl.game.networkClient.checkConnection()) return true;

            this.connectToServer();

            return false;
        },

        spawnHomePopup: function (callback) {
            this.spawnPopup(dl.EntityPopupHome, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0.5, y: 0.5 },
                    dockerOffset: { x: 0, y: 0 }
                },
                onPopupClose: callback
            });
        },

        spawnResultPopup: function () {
            // console.log('spawnResultPopup');
            var windowSize={x:1500,y:1000};
            if(this.spectatorResultWindow) this.spectatorResultWindow.kill();
            this.spawnEntity(dl.EntityPopupGameResult, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0.5, y: 0.5 },
                    dockerOffset: { x: 0, y: 0 }
                },
                c_AnimationSheetComponent: {
                    _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                    _size: windowSize,
                },
            });
        },
        spectatorResultWindow:null,
        spawnResultPopupSpectator: function () {
            var windowSize={x:1500,y:1000};
            this.spectatorResultWindow = this.spawnEntity(dl.EntityPopupGameResultSpectator, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0.5, y: 0.5 },
                    dockerOffset: { x: 0, y: 0 }
                },
                c_AnimationSheetComponent: {
                    _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                    _size: windowSize,
                },
            });
        }

    };
});
