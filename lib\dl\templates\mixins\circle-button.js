ig.module(
    'dl.templates.mixins.circle-button'
).requires(
    'dl.game.pointer',
    'dl.game.entity.components.collision.circle-collision'
).defines(function () {
    'use strict';

    dl.MixinCircleButton = {
        staticInstantiate: function () {
            this.parent();

            this._buttonEnable = true;
            this.__pointerOver = false;
            this.__firstClicked = false;

            return this;
        },

        initComponents: function () {
            this.parent();

            this._initCollisionComponent();
        },

        _initCollisionComponent: function () {
            this.cCollisionComponent = this.addComponent(dl.CircleCollision, {
                collisionTag: dl.Pointer.COLLISION_TARGET_TAG,
                over: this.over.bind(this),
                leave: this.leave.bind(this),
                clicked: this.clicked.bind(this),
                clicking: this.clicking.bind(this),
                released: this.released.bind(this)
            });
        },

        setEnable: function (enable) {
            this._buttonEnable = enable;
            this.onButtonStateChanged();
        },

        canFunction: function () {
            return this._buttonEnable;
        },

        onButtonStateChanged: function () {
            this.triggerEvent('buttonStateChanged');
        },

        over: function (pointer) {
            if (ig.ua.mobile && !ig.ua.iPad) return false;
            if (this.__pointerOver) return false;
            if (!this.canFunction()) return false;

            this.__pointerOver = true;
            this.onButtonStateChanged();

            this.onPointerOver(pointer);
        },

        leave: function (pointer) {
            if (ig.ua.mobile && !ig.ua.iPad) return false;
            if (!this.__pointerOver) return false;

            this.__pointerOver = false;
            this.__firstClicked = false;
            this.onButtonStateChanged();

            this.onPointerLeave(pointer);
        },

        clicked: function (pointer) {
            if (this.__firstClicked) return false;
            if (!this.canFunction()) return false;

            this.__firstClicked = true;
            this.onButtonStateChanged();

            this.onPointerFirstClick(pointer);
        },

        clicking: function (pointer) {
            if (!this.__firstClicked) return false;

            this.onPointerClicking(pointer);
        },

        released: function (pointer) {
            if (!this.__firstClicked) return false;

            this.__firstClicked = false;
            this.onButtonStateChanged();

            this.onPointerReleased(pointer);
        },

        onPointerOver: function (pointer) { },
        onPointerLeave: function (pointer) { },
        onPointerFirstClick: function (pointer) { },
        onPointerClicking: function (pointer) { },
        onPointerReleased: function (pointer) { }
    };
});
