ig.module(
    'dl.game.entity'
).requires(
    'dl.game.entity.orientation',
    'dl.utils.mixins.tweens',
    'dl.game.entity.components',
    'dl.game.utilities.entities-helper-mixin',
    'dl.game.entity.cache-canvas'
).defines(function () {
    'use strict';

    dl.Entity = ig.Class.extend({
        staticInstantiate: function () {
            this._entityId = ++dl.Entity.LAST_ENTITY_ID;

            /**
             * State
             */
            this.killed = false;

            /**
             * Sorting
             */
            this._zIndex = 0;
            Object.defineProperty(this, 'zIndex',
                {
                    get: function () { return this._zIndex; },
                    set: function (v) {
                        this._zIndex = v;

                        this.triggerEvent('zIndexChanged');
                    }
                });

            /**
             * Size
             */
            this._size = { x: 16, y: 16 };
            this.size = { x: 16, y: 16 };

            this._useScale = true;
            this._useParentScale = false;
            this._scale = { x: 1, y: 1 };
            this.scale = { x: 1, y: 1 };

            /**
             * Position
             */
            this._pos = { x: 0, y: 0 };
            this.pos = { x: 0, y: 0 };

            this.anchor = { x: 0.5, y: 0.5 };

            this._angle = 0;
            this.angle = 0;
            /**
             * Draw
             */
            this._visible = true;
            this.visible = true;

            this._alpha = 1;
            this.alpha = 1;

            return this;
        },

        init: function (settings) {
            // Merge settings into entity
            this._mergeSettings(settings);

            // Update after setup settings
            this.updateScale();
            this.updateAlpha();
        },

        postInit: function () {
            // do something after init
            if (this.parentInstance && this._useParentScale) {
                this.parentInstance.onEvent('scaleChanged', this.updateScale.bind(this));
            }
        },

        reset: function (settings) {
            this.killed = false;

            // Merge settings into entity
            this._mergeSettings(settings);

            // Update after setup settings
            this.updateScale();
            this.updateAlpha();
        },

        update: function () { },

        lateUpdate: function () { },

        updateOrientation: function () { },

        draw: function (ctx) { },

        kill: function () {
            this.killed = true;

            // Trigger event
            this.triggerEvent('killed');
        },

        _mergeSettings: function (settings) {
            ig.merge(this, settings);
        },

        updateBaseScale: function () {
            this.scale.x = this._scale.x;
            this.scale.y = this._scale.y;
        },

        updateScale: function () {
            this.updateBaseScale();
            if (this.parentInstance && this._useParentScale) {
                if (this._useParentScaleDynamic) {
                    this.scale.x *= this.parentInstance.scale.x;
                    this.scale.y *= this.parentInstance.scale.y;
                } else {
                    this.scale.x = this.parentInstance.scale.x;
                    this.scale.y = this.parentInstance.scale.y;
                }
            }

            // Trigger event
            this.triggerEvent('scaleChanged');

            // Scale change resulting size change
            this.updateSize();
        },

        setSize: function (width, height) {
            this._size.x = width;
            this._size.y = height;

            this.updateSize();
        },

        updateSize: function () {
            this.size.x = this._size.x;
            this.size.y = this._size.y;
            if (this._useScale) {
                this.size.x = this.size.x * this.scale.x;
                this.size.y = this.size.y * this.scale.y;
            }

            // Trigger event
            this.triggerEvent('sizeChanged');

            // Size change resulting position change
            this.updatePos();
        },

        updateBasePos: function () {
            this.pos.x = this._pos.x;
            this.pos.y = this._pos.y;
        },

        updateAnchorPos: function () {
            this.pos.x = this.pos.x +
                this.size.x * (0.5 - this.anchor.x);

            this.pos.y = this.pos.y +
                this.size.y * (0.5 - this.anchor.y);
        },

        updateBaseAngle: function () {
            this.angle = this._angle;
        },

        updateAngle: function () {
            this.updateBaseAngle();

            // Trigger event
            this.triggerEvent('angleChanged');
        },

        updatePos: function () {
            this.updateBasePos();
            this.updateAnchorPos();
            this.updateAngle();

            // Trigger event
            this.triggerEvent('positionChanged');
        },

        setVisible: function (visible) {
            this._visible = visible;
            this.updateVisible();
        },

        updateVisible: function () {
            this.visible = this._visible;

            // Trigger event
            this.triggerEvent('visibleChanged');
        },

        updateAlpha: function () {
            this.alpha = this._alpha;

            // Trigger event
            this.triggerEvent('alphaChanged');
        },

        disableEntityUnder: function () { },
        enableEntityUnder: function () { },
    });

    // entity id counter
    dl.Entity.LAST_ENTITY_ID = 0;
    // mixins
    dl.Entity.inject(dl.EventHandlerMixin);
    dl.Entity.inject(dl.EntityMixinOrientation);
    dl.Entity.inject(dl.TweensMixin);
    dl.Entity.inject(dl.EntityComponentsMixin);
    dl.Entity.inject(dl.EntitiesHelperMixin);
});
