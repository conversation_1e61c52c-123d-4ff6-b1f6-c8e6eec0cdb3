ig.module(
    'dl.game.pointer'
).requires(
    'dl.dl',
    'impact.input'
).defines(function () {
    'use strict';

    dl.Pointer = ig.Class.extend({
        staticInstantiate: function () {
            this.size = { x: 1, y: 1 };
            this.pos = { x: 0, y: 0 };
            this._lastPos = { x: 0, y: 0 };

            // pointer states
            this._isFirstPressed = false;
            this._isPressed = false;
            this._isReleased = false;

            return this;
        },

        update: function () {
            this.updatePos();
            this.handleInput();
        },

        draw: function (ctx) {
            ctx.save();
            ctx.beginPath();
            ctx.ellipse(
                this.pos.x, this.pos.y,
                2, 2,
                0, 0,
                Math.PI * 2);
            ctx.closePath();
            ctx.fillStyle = 'green';
            ctx.fill();
            ctx.restore();
        },

        updatePos: function () {
            var mousePosition = ig.game.io.getClickPos();
            var gamePosition = dl.game.camera.getGamePositionFromScreenPosition(mousePosition.x, mousePosition.y);

            this.pos.x = gamePosition.x;
            this.pos.y = gamePosition.y;

            // prevent trigger when in same position
            if (this.pos.x != this._lastPos.x ||
                this.pos.y != this._lastPos.y) {
                this.triggerEvent('positionChanged');
                this._lastPos.x = this.pos.x;
                this._lastPos.y = this.pos.y;
            }
        },

        handleInput: function () {
            this._isFirstPressed = ig.input.pressed('click');
            this._isPressed = ig.input.state('click');
            this._isReleased = ig.input.released('click');

            // Trigger event
            if (this._isFirstPressed) {
                this.triggerEvent('firstPressed');
            }
            if (this._isReleased) {
                this.triggerEvent('released');
            }
        }
    });

    /**
     * Add mixins
     */
    dl.Pointer.inject(dl.EventHandlerMixin);
});
