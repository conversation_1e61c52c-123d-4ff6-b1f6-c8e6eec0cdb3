'use strict';

ig.module('dl.utils.mixins.tweens.tween-mixin').requires('dl.utils.mixins.tweens.tween.easing', 'dl.utils.mixins.tweens.tween.action', 'dl.utils.mixins.tweens.tween.handler').defines(function () {
  'use strict';
  /**
   * Use on object
   * Example:
  object.createTween()
      .to({
          property1: { x: 0, y: 0 },
          property2: 0.5,
      }, timeInMillisecond, {
          onPropertiesChanged: function () {
              // do somethings when properties change
          }.bind(object),
          onCompleteAction: function () {
            // do somethings when complete action
          }.bind(object)
      })
      .start({
          repeat: true,
          onCompleteTween: function () {
            // do somethings when tween completed
          }.bind(object)
      });
  */

  dl.TweenMixin = {
    updateTweens: function updateTweens () {
      if (!this._tweens) return false;
      var currentTweens = [];

      for (var i = 0; i < this._tweens.length; i++) {
        this._tweens[i].update();

        if (!this._tweens[i].isCompleted()) {
          currentTweens.push(this._tweens[i]);
        }else{
          dl.game.offEvent('pause', this._tweens[i].pauseBound);
          dl.game.offEvent('resume', this._tweens[i].resumeBound);
        }
      }

      this._tweens = currentTweens;
    },
    createTween: function createTween () {
      if (!this._tweens) this._tweens = [];
      var tween = new dl.TweenHandler(this); // bind events

      tween.pauseBound = tween.pause.bind(tween);
      tween.resumeBound = tween.resume.bind(tween);
      dl.game.onEvent('pause', tween.pauseBound);
      dl.game.onEvent('resume', tween.resumeBound);

      this._tweens.push(tween);

      return tween;
    },
    pauseTweens: function pauseTweens () {
      if (!this._tweens) return false;

      for (var i = 0; i < this._tweens.length; i++) {
        this._tweens[i].pause();
      }
    },
    resumeTweens: function resumeTweens () {
      if (!this._tweens) return false;

      for (var i = 0; i < this._tweens.length; i++) {
        this._tweens[i].resume();
      }
    },
    stopTweens: function stopTweens (doCallback) {
      if (!this._tweens) return false;

      for (var i = 0; i < this._tweens.length; i++) {
        this._tweens[i].stop(doCallback);
      }

      this._tweens = [];
    },
    finishTweens: function finishTweens (doCallback) {
      if (!this._tweens) return false;

      for (var i = 0; i < this._tweens.length; i++) {
        this._tweens[i].finish(doCallback);
      }
    }
  };
});
