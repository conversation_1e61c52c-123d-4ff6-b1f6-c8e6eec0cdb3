/**
 * Created by <PERSON><PERSON> Le
 * Utilities functions
 */
if (typeof require !== 'undefined') {
    var getRandomValues = require('get-random-values');
}
var Utilities = {};

Utilities.encrypt = function (key,text) {  
  if(text==='' || text===null || typeof(text)==='undefined') return;

  var code = '';
  var textToChars = function(text){ return text.split("").map( function(c) {return c.charCodeAt(0)})};
  var byteHex = function(n){ return ("0" + Number(n).toString(32)).substr(-2)};
  var applyKeyToChar = function(code) {return textToChars(key).reduce(function(a, b) {return a ^ b}, code)};
  return text.split("").map(textToChars).map(applyKeyToChar).map(byteHex).join("");
};

Utilities.decrypt = function (key,text) {
  if(text==='' || text===null || typeof(text)==='undefined') return ;

  var textToChars = function(text) {return text.split("").map(function(c){return c.charCodeAt(0)})};
  var applyKeyToChar = function(code){return textToChars(key).reduce(function(a, b) {return a ^ b},code)};
  return text.match(/.{1,2}/g).map(function(hex) { return parseInt(hex, 32)}).map(applyKeyToChar).map(function(charCode) { return String.fromCharCode(charCode)}).join("");

};

Utilities.chance = function (chance) {
    return Math.random() < chance;
};

Utilities.randomInt = function (from, to) {
    return from + Math.floor(((to - from + 1) * Math.random()));
};

Utilities.randomFloat = function (from, to) {
    return from + ((to - from) * Math.random());
};

Utilities.randomTime = function (from, to) {
    from = Math.round(from);
    to = Math.round(to);
    if (to <= from) return from;

    return from + Math.floor(((to - from + 1) * Math.random()));
};

Utilities.shuffleArray = function (arr) {
    if (arr != null) {
        for (var i = 0; i < arr.length; i++) {
            var j = Utilities.randomInt(0, arr.length - 1);
            var a = arr[i];
            var b = arr[j];
            arr[i] = b;
            arr[j] = a;
        }
    }
    return arr;
};

Utilities.distanceBetweenTwoPoints = function (targetA, targetB) {
    var xd = targetA.x - targetB.x;
    var yd = targetA.y - targetB.y;

    return Math.sqrt(xd * xd + yd * yd);
};

Utilities.findNewPoint = function (x, y, angle, distance) {
    var result = {};

    result.x = Math.round(Math.cos(angle * Math.PI / 180) * distance + x);
    result.y = Math.round(Math.sin(angle * Math.PI / 180) * distance + y);

    return result;
};

Utilities.calcAngle = function (originX, originY, targetX, targetY, radians) {
    var dx = originX - targetX;
    var dy = originY - targetY;

    // var theta = Math.atan2(dy, dx);  // [0, Ⲡ] then [-Ⲡ, 0]; clockwise; 0° = west
    // theta *= 180 / Math.PI;          // [0, 180] then [-180, 0]; clockwise; 0° = west
    // if (theta < 0) theta += 360;     // [0, 360]; clockwise; 0° = west

    // var theta = Math.atan2(-dy, dx); // [0, Ⲡ] then [-Ⲡ, 0]; anticlockwise; 0° = west
    // theta *= 180 / Math.PI;          // [0, 180] then [-180, 0]; anticlockwise; 0° = west
    // if (theta < 0) theta += 360;     // [0, 360]; anticlockwise; 0° = west

    // var theta = Math.atan2(dy, -dx); // [0, Ⲡ] then [-Ⲡ, 0]; anticlockwise; 0° = east
    // theta *= 180 / Math.PI;          // [0, 180] then [-180, 0]; anticlockwise; 0° = east
    // if (theta < 0) theta += 360;     // [0, 360]; anticlockwise; 0° = east

    var theta = Math.atan2(-dy, -dx); // [0, Ⲡ] then [-Ⲡ, 0]; clockwise; 0° = east
    theta *= 180 / Math.PI; // [0, 180] then [-180, 0]; clockwise; 0° = east
    if (theta < 0) theta += 360; // [0, 360]; clockwise; 0° = east

    if (radians) theta *= Math.PI / 180;

    return theta;
};

Utilities.degToRad = function (degrees) {
	return degrees * Math.PI / 180;
};

Utilities.radToDeg = function (radians) {
	return radians * 180 / Math.PI;
};

Utilities.getDirection = function (angle) {
    var directions = ['east', 'south-east', 'south', 'south-west', 'west', 'north-west', 'north', 'north-east'];
    var index = Math.round(((angle %= 360) < 0 ? angle + 360 : angle) / 45) % 8;
    return directions[index];
};

Utilities.roundAngle = function (angle, nearestAngle) {
    nearestAngle = nearestAngle || 30;
    var newAngle = Math.round(angle / nearestAngle) * nearestAngle;
    return newAngle;
};

Utilities.generateUID = function () {
    var a = new Uint32Array(3);
    if (typeof window !== 'undefined') {
        if (window && window.crypto && window.crypto.getRandomValues) {
            window.crypto.getRandomValues(a);
        }
    } else {
        a = new Uint8Array(3);
        getRandomValues(a);
    }
    if (typeof performance === 'undefined') performance = require('perf_hooks').performance;
    return (performance.now().toString(36) + Array.from(a).map(function (A) {
        return A.toString(36);
    }).join('')).replace(/\./g, '');
};

Utilities.toMMSS = function (seconds) {
    // // don't forget the second param
    // var sec_num = parseInt(this, 10);
    // var minutes = Math.floor(sec_num / 60);
    // var seconds = sec_num - (minutes * 60);

    // if (minutes < 10) { minutes = '0' + minutes; }
    // if (seconds < 10) { seconds = '0' + seconds; }
    // return minutes + ':' + seconds;

    var date = new Date(0);
    date.setSeconds(seconds);
    var timeString = date.toISOString().substring(11, 19);
    timeString = timeString.substring(3, timeString.length);
    return timeString;
};

Utilities.generateOffsetListBasedOnAngle = function (angleInterval, distance, angle, soldierPerRow) {
    angleInterval = typeof angleInterval == 'undefined' ? NETWORK_GAME_CONFIGS.ANGLE_INTERVAL : angleInterval;
    distance = typeof distance == 'undefined' ? NETWORK_GAME_CONFIGS.DISTANCE_FROM_SOURCE : distance;
    // indexList below simply means: [Center, FirstRight, FirstLeft, SecondRight, SecondLeft]
    // visually: [SecondLeft, FirstLeft, Center, FirstRight, SecondRight] = ooooo (but in slightly arced positions)
    if(soldierPerRow==1){
        var indexList = [0];
        var additionalAngle = 0;
    }else if(soldierPerRow==2){
        var indexList = [0,1];
        var additionalAngle = -0.5*angleInterval;
    }else if(soldierPerRow==3){
        var indexList = [0, 1, -1];
        var additionalAngle = 0;
    }else if(soldierPerRow==4){
        var indexList = [0, 1, -1 ,2];
        var additionalAngle = -0.5*angleInterval;
    }else{
        var indexList = [0, 1, -1, 2,-2];
        var additionalAngle = 0;
    }

    var offsetList = [];
    for (var i = 0; i < soldierPerRow; i++) {
        var offset = {};
        offset = Utilities.findNewPoint(0, 0, angle + additionalAngle+ (angleInterval * indexList[i]), distance);
        offsetList.push({
            angle: i,
            offset: offset
        });
    }
    // dl.log(offsetList);
    return offsetList;
};

/**
 * Export module
 */
if (typeof module !== 'undefined') {
    module.exports.Utilities = Utilities;
}
