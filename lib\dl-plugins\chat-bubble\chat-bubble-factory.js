// create namespace
dl.ChatBubble.Factory = {};

ig.module(
    'dl-plugins.chat-bubble.chat-bubble-factory'
).requires(
    'dl-plugins.chat-bubble.factory.text',
    'dl-plugins.chat-bubble.factory.avatar',
    'dl-plugins.chat-bubble.factory.bubble'
).defines(function () {
    'use strict';

    dl.ChatBubble.FactoryManager = {
        deepCopyAndMergerConfigs: function (baseConfigs, targetConfigs) {
            var configs = JSON.parse(JSON.stringify(baseConfigs));

            ig.merge(configs, targetConfigs);
            return configs;
        },

        generateCanvas: function (configs) {
            var canvasData = this.generateText(configs);

            canvasData = this.generateAvatar(canvasData.configs, canvasData.canvas);
            canvasData = this.generateBubble(canvasData.configs, canvasData.canvas);

            return canvasData;
        },

        generateText: function (configs) {
            var textConfigs = this.deepCopyAndMergerConfigs(dl.ChatBubble.Factory.Text.DEFAULT_CONFIGS, configs.textConfigs);

            var canvasData = dl.ChatBubble.Factory.Text.generate(textConfigs);
            configs.textConfigs = textConfigs;
            canvasData.configs = configs;

            return canvasData;
        },

        generateAvatar: function (configs, contentCanvas) {
            var avatarConfigs = this.deepCopyAndMergerConfigs(dl.ChatBubble.Factory.Avatar.DEFAULT_CONFIGS, configs.avatarConfigs);

            var canvasData = dl.ChatBubble.Factory.Avatar.generate(avatarConfigs, contentCanvas);
            configs.avatarConfigs = avatarConfigs;
            canvasData.configs = configs;

            return canvasData;
        },

        generateBubble: function (configs, contentCanvas) {
            var bubbleConfigs = this.deepCopyAndMergerConfigs(dl.ChatBubble.Factory.Bubble.DEFAULT_CONFIGS, configs.bubbleConfigs);

            var canvasData = dl.ChatBubble.Factory.Bubble.generate(bubbleConfigs, contentCanvas);
            configs.bubbleConfigs = bubbleConfigs;
            canvasData.configs = configs;

            return canvasData;
        }
    };
});
