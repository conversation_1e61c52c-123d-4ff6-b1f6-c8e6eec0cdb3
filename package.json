{"name": "multiplayer-impactjs-marketjs-platform-sandbox", "version": "1.0.0", "description": "IMPACTJS MARKETJS PLATFORM\r ==========================\r ### A cleaner, simpler approach to building HTML5 games", "main": "index.js", "directories": {"doc": "docs", "lib": "lib"}, "dependencies": {"badwords-list": "^1.0.0", "express": "^4.17.1", "filipino-badwords-list": "^1.1.2", "form-data": "^4.0.0", "french-badwords-list": "^1.0.5", "get-random-values": "^2.0.0", "italian-badwords-list": "^1.0.3", "nodemon": "^2.0.4", "select-random-file": "^1.2.1", "socket.io": "^2.3.0", "vietnamese-badwords": "^1.0.0", "axios": "^1.2.2"}, "devDependencies": {"eslint": "^6.8.0", "eslint-config-standard": "^14.1.0", "eslint-plugin-import": "^2.20.1", "eslint-plugin-node": "^11.0.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1"}, "scripts": {"start": "nodemon index.js", "debug": "nodemon --inspect index.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/marketjs_sandbox/impactjs-marketjs-platform-sandbox.git"}, "author": "", "license": "ISC", "homepage": "https://bitbucket.org/marketjs_sandbox/impactjs-marketjs-platform-sandbox#readme"}