
/**
 * Created by <PERSON><PERSON> <PERSON>
 * Defining room config, states.
 */

var ROOM_CONFIGS = {};

// default max player the room
ROOM_CONFIGS.MAX_PLAYER = 4;
// default auto start room
ROOM_CONFIGS.AUTO_START = false;
// bots configs
ROOM_CONFIGS.USE_BOT = true;
ROOM_CONFIGS.ADD_BOT_DELAY = 8000;
ROOM_CONFIGS.ADD_BOT_INTERVAL = 500;

// time to start the room
ROOM_CONFIGS.STARTING_TIME = 3000;

// states
ROOM_CONFIGS.ROOM_STATE = {
    INITIALIZING: 0,
    WAITING_FOR_PLAYER: 1,
    WAITING_TO_START: 2,
    STARTING: 3,
    GAME_PLAY: 4,
    ENDED: 5
};

/* ----- Start debug properties ------ */
ROOM_CONFIGS.SKIP_TIME = !true;
if (ROOM_CONFIGS.SKIP_TIME) {
    // default auto start room
    ROOM_CONFIGS.AUTO_START = true;
    ROOM_CONFIGS.ADD_BOT_DELAY = 1000;
    ROOM_CONFIGS.STARTING_TIME = 100;
}
/* ------ End debug properties ------- */

/**
 * Export module
 */
if (typeof module !== 'undefined') {
    module.exports.ROOM_CONFIGS = ROOM_CONFIGS;
}
