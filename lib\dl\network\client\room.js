/**
 * Created by <PERSON><PERSON>
 * <PERSON>le room and store information on client side
 */
ig.module(
	'dl.network.client.room'
).requires(
	'dl.network.client.room-event-handler',
	'dl.network.client.network-game'
).defines(function () {
	ClientRoom = ig.Class.extend({
		init: function (client, data) {
			this.client = client;

			this.roomId = null;
			this.roomPassword = '';
			this.roomMaxPlayer = 2;
			this.autoStartRoomFlag = true;
			this.roomState = null;
			this.roomKey = 123;
			this.encKey = '1f1b1e1a';
			this.playerStatus = '1f1b1e1a';
			this.botStatus = '1f1b1e1a';
			this.playerCode = Utilities.randomInt(10,99);
			this.botCode = Utilities.randomInt(10,99);


			this.playerStatusServer = '1f1b1e1a';
			this.botStatusServer = '1f1b1e1a';

			this.playerList = [];
			this.roomHostPlayerId = null;

			this.networkGame = new NetworkGame(this);
			this.pendingEvents = [];

			// callback handlers
			this.onRoomStartCallback = null;
			this.onRoomStartedCallback = null;
			this.onRoomUpdateCallback = null;

			this.importData(data);
		},

        updateDelay:true,
        update: function () {
            if(!this.updateDelay) return;
			this.handlePendingEvent();
			this.networkGame.update();

			this.encKey = Utilities.encrypt('1!2@3#4$5%6^7&8*',(Utilities.randomInt(10,99)*this.roomKey).toString());
			this.playerStatus = Utilities.encrypt('1!2@3#4$5%6^7&8*',(Utilities.randomInt(10,99)*this.playerCode).toString());
			this.botStatus = Utilities.encrypt('1!2@3#4$5%6^7&8*',(Utilities.randomInt(10,99)*this.botCode).toString());

            this.updateDelay = false;
            setTimeout(function(){
                this.updateDelay = true;                
            }.bind(this),1000);
		},

		importData: function (data) {
			if (!data) return;

			if (typeof data.roomId !== 'undefined') this.roomId = data.roomId;
			if (typeof data.roomPassword !== 'undefined') this.roomPassword = data.roomPassword;
			if (typeof data.roomMaxPlayer !== 'undefined') this.roomMaxPlayer = data.roomMaxPlayer;
			if (typeof data.autoStartRoomFlag !== 'undefined') this.autoStartRoomFlag = data.autoStartRoomFlag;
			if (typeof data.roomState !== 'undefined') this.roomState = data.roomState;
			if (typeof data.roomKey !== 'undefined') {
				this.roomKey = data.roomKey;
			}else{
				this.roomKey = 0;
			}	

			if (data.playerList) {
				this.playerList = [];
				for (var i = 0, il = data.playerList.length; i < il; i++) {
					var playerData = data.playerList[i];
					var player = new RoomPlayer(playerData.playerId);
					player.importData(playerData);

					this.playerList.push(player);
				}
			}
			if (typeof data.roomHostPlayerId !== 'undefined') this.roomHostPlayerId = data.roomHostPlayerId;

			RoomEventHandler.onRoomDataUpdate(this);
		},

		handlePendingEvent: function () {
			var unhandledPendingEvent = [];
			for (var i = 0, iLength = this.pendingEvents.length; i < iLength; i++) {
				var event = this.pendingEvents[i];
				if (!this.handleEvent(event)) {
					unhandledPendingEvent.push(event);
				}
			}

			this.pendingEvents = unhandledPendingEvent;
		},

		handleEvent: function (event) {
			if (!event) return true;

			return RoomEventHandler.handleEvent(this, event);
		},

		/**
		 * Server events
		 */
		handleRoomEvent: function (data) {
			if (!data) return true;

			this.importData(data.roomData);

			this.pendingEvents.push(data.event);
		},

		// ----------------
		// Start client's event functions
		// ----------------
		sendClientEvent: function (eventType, eventInfo) {
			if (typeof eventInfo === 'undefined') eventInfo = {};


			var event = new RoomEvent(eventType, Date.now(), eventInfo);
			this.client.sendRoomEvent(event.packData());

		},

		sendPlayerReady: function () {
			this.sendClientEvent(RoomEvent.EVENT_TYPE.PLAYER_READY);
		},

		sendAutoStartStateChange: function (newState) {
			this.sendClientEvent(RoomEvent.EVENT_TYPE.AUTO_START_STATE_CHANGE, {
				newState: newState
			});
		},

		sendKickPlayer: function (kickPlayerId) {
			// check if is host
			if (this.client.playerId != this.roomHostPlayerId) return;
			this.sendClientEvent(RoomEvent.EVENT_TYPE.KICK_PLAYER, {
				kickPlayerId: kickPlayerId
			});
		},

		replaceMatchingBot:function(){
			this.sendClientEvent(RoomEvent.EVENT_TYPE.REPLACE_MATCHING_BOT);		
		},

		sendEmoji: function (emojiIndex) {
			var player = this.findPlayerById(this.client.playerId);
			if (!player) return;

			this.sendClientEvent(RoomEvent.EVENT_TYPE.SEND_EMOJI, {
				playerId: player.playerId,
				emojiIndex: emojiIndex
			});
		},
		sendBotEmoji: function (emojiIndex,botId) {
			var player = this.findPlayerById(botId);
			if (!player) return;

			this.sendClientEvent(RoomEvent.EVENT_TYPE.SEND_EMOJI, {
				playerId: player.playerId,
				emojiIndex: emojiIndex
			});
		},

		// ----------------
		// End client's event functions
		// ----------------

		// ----------------
		// Start utilities functions
		// ----------------
		isHostPlayerId: function (checkId) {
			if (typeof (checkId) !== 'number') return false;
			if (typeof (this.roomHostPlayerId) !== 'number') return false;

			return this.roomHostPlayerId == checkId;
		},
		findPlayerById: function (playerId) {
			for (var i = 0, il = this.playerList.length; i < il; i++) {
				if (this.playerList[i].playerId == playerId) {
					return this.playerList[i];
				}
			}

			return null;
		},

		getPlayerNumber: function (checkId) {
			if (typeof (checkId) !== 'number') return false;

			for (var i = 0; i < this.playerList.length; i++) {
				if (this.playerList[i].playerId == checkId) {
					return i + 1;
				}
			}
		}
		// ----------------
		// End utilities functions
		// ----------------
	});
});
