ig.module(
    'dl.templates.mixins.button'
).requires(
    'dl.game.pointer',
    'dl.game.entity.components.collision.rectangle-collision'
).defines(function () {
    'use strict';

    dl.MixinButton = {
        staticInstantiate: function () {
            this.parent();

            this._buttonEnable = true;
            this.__pointerOver = false;
            this.__firstClicked = false;

            return this;
        },

        initComponents: function () {
            this.parent();
            this._initCollisionComponent();
        },

        _initCollisionComponent: function () {
            this.cCollisionComponent = this.addComponent(dl.RectangleCollision, {
                collisionTag: dl.Pointer.COLLISION_TARGET_TAG,
                over: this.over.bind(this),
                leave: this.leave.bind(this),
                clicked: this.clicked.bind(this),
                clicking: this.clicking.bind(this),
                released: this.released.bind(this)
            });
        },

        setEnable: function (enable) {
            this._buttonEnable = enable;
            this.onButtonStateChanged();
        },

        canFunction: function () {
            return this._buttonEnable;
        },

        onButtonStateChanged: function () {
            this.triggerEvent('buttonStateChanged');
        },

        over: function (pointer) {
            if (ig.ua.mobile && !ig.ua.iPad) return false;
            if (this.__pointerOver) return false;
            if (!this.canFunction()) return false;

            this.__pointerOver = true;
            this.onButtonStateChanged();

            this.onPointerOver(pointer);
        },

        leave: function (pointer) {
            if (ig.ua.mobile && !ig.ua.iPad) return false;
            if (!this.__pointerOver) return false;

            this.__pointerOver = false;
            this.__firstClicked = false;
            this.onButtonStateChanged();

            this.onPointerLeave(pointer);
        },

        clicked: function (pointer) {
            if (this.__firstClicked) return false;
            if (!this.canFunction()) return false;

            this.__firstClicked = true;
            this.onButtonStateChanged();

            this.onPointerFirstClick(pointer);
        },

        clicking: function (pointer) {
            if (!this.__firstClicked) return false;

            this.onPointerClicking(pointer);
        },

        released: function (pointer) {
            if (!this.__firstClicked) return false;
            ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.button);
            this.__firstClicked = false;
            this.onButtonStateChanged();

            this.onPointerReleased(pointer);
            ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.click2);
        },

        onPointerOver: function (pointer) { },
        onPointerLeave: function (pointer) { },
        onPointerFirstClick: function (pointer) { },
        onPointerClicking: function (pointer) { },
        onPointerReleased: function (pointer) { },

        disableEntityUnder: function () {
            this.parent();
            this.setEnable(false);
        },

        enableEntityUnder: function () {
            this.parent();
            this.setEnable(true);
        },
    };
});
