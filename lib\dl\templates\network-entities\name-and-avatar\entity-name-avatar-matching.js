ig.module(
    'dl.templates.network-entities.name-and-avatar.entity-name-avatar-matching'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'dl.templates.entities.entity-image',
    'dl.templates.mixins.text',
    'dl.templates.entities.buttons.entity-button-image'
).defines(function () {
    'use strict';

    dl.EntityNameAvatarMatching = dl.Entity
        .extend(dl.MixinDocker)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this.playerData = null;
                this.spacing = 25;
                return this;
            },
            postInit: function () {
                this.parent();
                ig.game.matchingAllianceData = [];
                this.playerAvatar = this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    image: dl.preload['name-avatar']['avatar-frame-gray'],
                });

                this.playerAvatarFrame = this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.playerAvatar,
                        dockerPercent: { x: 0.5, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    image: dl.preload['name-avatar']['avatar-frame-gray'],
                });

                this.playerName = this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.playerAvatar,
                        dockerPercent: { x: 0.5, y: 1 },
                        dockerOffset: { x: 0, y: this.spacing }
                    },
                    anchor: { x: 0.5, y: 0 },
                    c_TextComponent: {
                        fontSize: 40,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    text: 'playerName'
                });

                // this.playerAllianceIcon = this.spawnEntity(dl.EntityImage, {
                //     _useParentScale: true,
                //     c_DockerComponent: {
                //         dockerObject: this.playerName,
                //         dockerPercent: { x: 0, y: 1 },
                //         dockerOffset: { x: -60, y: this.spacing+6 }
                //     },
                //     c_AnimationSheetComponent: {
                //         _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                //         _size: {x:110,y:80}
                //     },
                //     anchor: { x: 0, y: 0.2 },
                //     image: dl.preload['emptyAllianceIcon']
                // });

                // this.playerAlliaceName = this.spawnEntity(dl.EntityText, {
                //     _useParentScale: true,
                //     c_DockerComponent: {
                //         dockerObject: this.playerName,
                //         dockerPercent: { x: 0.4, y: 1 },
                //         dockerOffset: { x: 20, y: this.spacing+6 }
                //     },
                //     anchor: { x: 0, y: 0 },
                //     c_TextComponent: {
                //         fontSize: 40,
                //         fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                //         textAlign: 'left', 
                //         textBaseline: 'middle',
                //     },
                //     text: ''
                // });


                this.playerState = this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.playerAvatar,
                        dockerPercent: { x: 0.5, y: 0 },
                        dockerOffset: { x: 0, y: -this.spacing * 0.25 }
                    },
                    anchor: { x: 0.5, y: 1 },
                    c_TextComponent: {
                        fontSize: 40,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    text: 'playerState'
                });

                this.btnKick = this.spawnEntity(dl.EntityButtonImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.playerAvatar,
                        dockerPercent: { x: 1, y: 0 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    anchor: { x: 1, y: 0.1 },
                    image: dl.preload['name-avatar']['button-kick'],
                    onButtonClicked: this.onKick.bind(this)
                });

                this.updateData();
                this.calculateSize();
            },
            getAllianceData:function(allianceId,playerId){
                if(allianceId=="" || allianceId==null || allianceId==0){ 
                    var data={allianceName:'',allianceIcon:null,allianceShortName:''}; 
                    ig.game.matchingAllianceData.push(data);    
                    return;
                }

                var gameId='1651673491261401';
                var url= "https://svas-consumer.marketjs-cloud.com/api/alliance/read";
                var method= "GET";
                var timeout= 5000;
                url = url + ('?game_id=' + gameId) + ('&alliance_id='+allianceId);

                ig.game.client.callXHR(method, url, timeout, null, function(response) {
                    // console.log(response.data);     
                    var data={allianceName:response.data.name,allianceIcon:response.data.icon_id,allianceShortName:response.data.shortcode};     
                    ig.game.matchingAllianceData.push(data);    
    
                    this.playerAlliaceName.updateText(response.data.name);
                    this.playerAllianceIcon.updateImage(dl.preload['allianceIcon'+response.data.icon_id]);
                }.bind(this), function(response) {
                    console.log(response);
                }.bind(this));
            },                  
            // getAllianceStatus:true,
            // updateDataCount:0,
            updateData: function (data) {

                // this.updateDataCount +=1;
                // if(this.updateDataCount % 10 ===0) return
                // if(this.updateDataCount>50) this.updateDataCount=0;

                this.playerData = null;
                this.playerAvatar.updateImage(dl.preload['name-avatar']['avatar-frame-gray']);
                this.playerAvatarFrame.updateImage(dl.preload['name-avatar']['avatar-frame-gray']);
                this.playerName.updateText('');
                this.playerState.updateText('');
                this.showBtnKick(false);

                if (!data) return;
                this.playerData = data;

                // if(this.getAllianceStatus){
                //     this.getAllianceStatus=false;
                //     this.getAllianceData(this.playerData.playerAlliance,data.playerId);
                // }

                // avatar
                this.playerAvatar.updateImage(dl.preload['name-avatar'].avatars[data.playerAvatarId]);
                // name
                this.playerName.updateText(data.playerName);


                // frame
                var isMe = ig.game.client.isMyPlayerId(data.playerId);
                /*
                if (isMe) {
                    this.playerAvatarFrame.updateImage(dl.preload['name-avatar']['avatar-frame-gray']);
                } */
                var playerNumber = ig.game.client.clientGameRoom.getPlayerNumber(data.playerId);
                if (playerNumber === 1) {
                    this.playerName.updateTextColor(dl.configs.getConfig('TEXT_COLOR', 'PLAYER_1'));
                    this.playerAvatarFrame.updateImage(dl.preload['name-avatar']['avatar-frame-red']);
                } else if (playerNumber === 2) {
                    this.playerName.updateTextColor(dl.configs.getConfig('TEXT_COLOR', 'PLAYER_2'));
                    this.playerAvatarFrame.updateImage(dl.preload['name-avatar']['avatar-frame-blue']);
                } else if (playerNumber === 3) {
                    this.playerName.updateTextColor(dl.configs.getConfig('TEXT_COLOR', 'PLAYER_3'));
                    this.playerAvatarFrame.updateImage(dl.preload['name-avatar']['avatar-frame-green']);
                } else if (playerNumber === 4) {
                    this.playerName.updateTextColor(dl.configs.getConfig('TEXT_COLOR', 'PLAYER_4'));
                    this.playerAvatarFrame.updateImage(dl.preload['name-avatar']['avatar-frame-yellow']);
                }

                // state
                var isHost = ig.game.client.clientGameRoom.isHostPlayerId(data.playerId);
                if (isHost) {
                    this.playerState.updateText(_STRINGS.NETWORK.MATCHING_HOST);
                } else {
                    if (data.playerReady) {
                        this.playerState.updateText(_STRINGS.NETWORK.MATCHING_READY);
                    }
                }
                // button kick
                var meIsHost = ig.game.client.clientGameRoom.isHostPlayerId(ig.game.client.playerId);

                if (meIsHost && !isMe) {
                    this.showBtnKick(true);
                }
            },

            disableButton: function () {
                this.btnKick.setEnable(false);
            },

            showBtnKick: function (show) {
                this.btnKick.setVisible(show);
                this.btnKick.setEnable(show);
            },

            onKick: function () {
                if (!this.playerData) return;

                if (!ig.game.client) return;
                if (!ig.game.client.clientGameRoom) return;
                // if (ig.game.client.clientGameRoom.playerList.length != ig.game.client.roomMaxPlayer) return;

                // console.log(this.control.players)
                // console.log(ig.game.matchingAllianceData);
                ig.game.client.clientGameRoom.sendKickPlayer(this.playerData.playerId);
                ig.game.client.clientGameRoom.replaceMatchingBot();
            },

            calculateSize: function () {
                this.setSize(
                    this.playerAvatar.size.x * 1.5,
                    this.playerAvatar.size.y * 4 
                    // this.playerAllianceIcon.size.y + this.playerAvatar.size.y + this.playerName.size.y*4 + this.playerState.size.y + this.spacing * 2
                );
            },

            onEntitySpawn: function (entity) {
                entity._useParentScale = true;

                return entity;
            },

            tweenIn: function (time) {
                dl.TweenTemplate.fadeIn(this, time);
            },

            tweenOut: function (time) {
                dl.TweenTemplate.fadeOut(this, time);
            }
        });

    // Enable cache
    dl.enableCacheCanvas(dl.EntityNameAvatarMatching);
});
