ig.module(
    'dl.templates.network-entities.entity-server-status'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.text'
).defines(function () {
    'use strict';

    dl.EntityServerStatus = dl.Entity
        .extend(dl.MixinDocker)
        .extend(dl.MixinText)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this._useTextAsSize = true;

                this.SHOW_TOTAL_PLAYER = true;
                this.SHOW_PING = true;
                this.SHOW_TIME_DIFFERENT = false;
                this.SHOW_BUILD_VERSION = true;

                return this;
            },

            _initTextComponent: function () {
                this.c_TextComponent.updateProperties({
                    textAlign: 'left',
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                    fontSize: 28
                });
            },
            delay:0,
            update: function () {
                this.parent();
                this.delay +=1;
                if(this.delay>=20){
                    this.delay=0;
                    this.updateNetworkInfo();
                }
            },

            updateNetworkInfo: function () {
                var info = '';

                if (!ig.game.client ||
                    !ig.game.client.connectedToServer) {
                    info = _STRINGS.NETWORK.SERVER_STATUS_OFFLINE;
                } else {
                    // console.log('updateNetworkInfo');
                    if (this.SHOW_PING) {
                        info += this.getPing();
                    }
                    if (this.SHOW_TOTAL_PLAYER) {
                        info += this.getPlayers();
                    }
                    if (this.SHOW_TIME_DIFFERENT) {
                        info += this.getTimeDifferent();
                    }
                    if (this.SHOW_BUILD_VERSION) {
                        info +='<br>'+window.buildversion;
                    }
                }

                console.log(info);
                this.updateText(info);
            },

            getPlayers: function () {
                var serverUserCount = ig.game.client.serverUserCount.toString();
                return _STRINGS.NETWORK.SERVER_STATUS_ONLINE + ': ' + serverUserCount;
            },

            getPing: function () {
                if (!ig.game.client.networkClient) return '';

                return '<br>' + _STRINGS.NETWORK.SERVER_STATUS_PING + ': ' + ig.game.client.networkClient.latencyAverage.toString() + '<br>';
            },

            getTimeDifferent: function () {
                if (!ig.game.client.networkClient) return '';

                return '<br>' + _STRINGS.NETWORK.SERVER_STATUS_TIME_DIFFERENT + ': ' + ig.game.client.networkClient.timeDiffAverage;
            }
        });

    // Enable cache
    dl.enableCacheCanvas(dl.EntityServerStatus);
});
