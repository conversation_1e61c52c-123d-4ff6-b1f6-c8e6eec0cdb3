diff --git a/lib/dl/game/entity.js b/lib/dl/game/entity.js
index cfd8a4f..f10e78d 100755
--- a/lib/dl/game/entity.js
+++ b/lib/dl/game/entity.js
@@ -119,10 +119,10 @@ ig.module(
         updateScale: function () {
             this.updateBaseScale();
             if (this.parentInstance && this._useParentScale) {
-                if (this._useParentScaleDynamic){
+                if (this._useParentScaleDynamic) {
                     this.scale.x *= this.parentInstance.scale.x;
                     this.scale.y *= this.parentInstance.scale.y;
-                }else{
+                } else {
                     this.scale.x = this.parentInstance.scale.x;
                     this.scale.y = this.parentInstance.scale.y;
                 }
@@ -207,7 +207,10 @@ ig.module(
 
             // Trigger event
             this.triggerEvent('alphaChanged');
-        }
+        },
+
+        disableEntityUnder: function () { },
+        enableEntityUnder: function () { },
     });
 
     // entity id counter
diff --git a/lib/dl/game/utilities/entities-disable.js b/lib/dl/game/utilities/entities-disable.js
index 0bed7c3..4921510 100755
--- a/lib/dl/game/utilities/entities-disable.js
+++ b/lib/dl/game/utilities/entities-disable.js
@@ -63,6 +63,45 @@ ig.module(
             }
 
             delete entity.entitiesDisabled;
+        },
+
+        _disableEntitiesRecursive: function (entities) {
+            for (var index = entities.length - 1; index >= 0; index--) {
+                var entity = entities[index];
+
+                if (dl.check.isFunction(entity.disableEntityUnder)) {
+                    dl.game.entitiesDisabled.push(entity);
+                    entity.disableEntityUnder();
+                }
+
+                if (entity._entities) {
+                    this._disableEntitiesRecursive(entity._entities);
+                }
+            }
+        },
+
+        disableEntities: function () {
+            if (dl.check.isArray(dl.game.entitiesDisabled)) {
+                this.enableEntities();
+            }
+            dl.game.entitiesDisabled = [];
+
+            this._disableEntitiesRecursive( this.entities);
+        },
+
+        enableEntities: function () {
+            if (dl.check.isArray(dl.game.entitiesDisabled)) {
+                var currentEntity = dl.game.entitiesDisabled.pop();
+                while (currentEntity) {
+                    if (dl.check.isFunction(currentEntity.enableEntityUnder)) {
+                        currentEntity.enableEntityUnder();
+                    }
+
+                    currentEntity = dl.game.entitiesDisabled.pop();
+                }
+            }
+
+            delete dl.game.entitiesDisabled;
         }
     });
 });
diff --git a/lib/dl/my-game-extends.js b/lib/dl/my-game-extends.js
index 6e50864..2c554f2 100755
--- a/lib/dl/my-game-extends.js
+++ b/lib/dl/my-game-extends.js
@@ -35,6 +35,7 @@ ig.module(
      * Extends game
      */
     dl.MyGameExtends = {
+        entitiesBackward: [],
         /**
          * overriding MyGame
          */
@@ -221,6 +222,8 @@ ig.module(
                 this.pointer.draw(ctx);
 
                 ctx.restore();
+                this.updateBackward();
+                this.drawBackward();
             } else {
                 this.parent();
             }
@@ -253,6 +256,134 @@ ig.module(
         /**
          * Overloading MyGame
          */
-        resumeGame: function () { }
+        resumeGame: function () { },
+
+
+        update: function () {
+            // load new level?
+            if (this._levelToLoad) {
+                this.loadLevel(this._levelToLoad);
+                this._levelToLoad = null;
+            }
+
+            // update entities
+            this.updateEntities();
+            this.checkEntities();
+
+            // remove all killed entities
+            for (var i = 0; i < this._deferredKill.length; i++) {
+                this._deferredKill[i].erase();
+                this.entities.erase(this._deferredKill[i]);
+            }
+            this._deferredKill = [];
+
+            // sort entities?
+            if (this._doSortEntities || this.autoSort) {
+                this.sortEntities();
+                this._doSortEntities = false;
+            }
+
+            // update background animations
+            for (var tileset in this.backgroundAnims) {
+                var anims = this.backgroundAnims[tileset];
+                for (var a in anims) {
+                    anims[a].update();
+                }
+            }
+        },
+
+        spawnEntityBackward: function (type, x, y, settings) {
+            var entityClass = typeof (type) === 'string'
+                ? ig.global[type]
+                : type;
+
+            if (!entityClass) {
+                throw ("Can't spawn entity of type " + type);
+            }
+            var ent = new (entityClass)(x, y, settings || {});
+            this.entitiesBackward.push(ent);
+            return ent;
+        },
+
+        updateBackward: function () {
+            // Insert all entities into a spatial hash and check them against any
+            // other entity that already resides in the same cell. Entities that are
+            // bigger than a single cell, are inserted into each one they intersect
+            // with.
+
+            // A list of entities, which the current one was already checked with,
+            // is maintained for each entity.
+
+            var hash = {};
+            for (var e = 0; e < this.entitiesBackward.length; e++) {
+                var entity = this.entitiesBackward[e];
+
+                // Skip entities that don't check, don't get checked and don't collide
+                if (
+                    entity.type == ig.Entity.TYPE.NONE &&
+                    entity.checkAgainst == ig.Entity.TYPE.NONE &&
+                    entity.collides == ig.Entity.COLLIDES.NEVER
+                ) {
+                    continue;
+                }
+
+                var checked = {},
+                    xmin = Math.floor(entity.pos.x / this.cellSize),
+                    ymin = Math.floor(entity.pos.y / this.cellSize),
+                    xmax = Math.floor((entity.pos.x + entity.size.x) / this.cellSize) + 1,
+                    ymax = Math.floor((entity.pos.y + entity.size.y) / this.cellSize) + 1;
+
+                for (var x = xmin; x < xmax; x++) {
+                    for (var y = ymin; y < ymax; y++) {
+
+                        // Current cell is empty - create it and insert!
+                        if (!hash[x]) {
+                            hash[x] = {};
+                            hash[x][y] = [entity];
+                        }
+                        else if (!hash[x][y]) {
+                            hash[x][y] = [entity];
+                        }
+
+                        // Check against each entity in this cell, then insert
+                        else {
+                            var cell = hash[x][y];
+                            for (var c = 0; c < cell.length; c++) {
+
+                                // Intersects and wasn't already checkd?
+                                if (entity.touches(cell[c]) && !checked[cell[c].id]) {
+                                    checked[cell[c].id] = true;
+                                    ig.Entity.checkPair(entity, cell[c]);
+                                }
+                            }
+                            cell.push(entity);
+                        }
+                    } // end for y size
+                } // end for x size
+            } // end for entities
+
+            for (var i = 0; i < this.entitiesBackward.length; i++) {
+                var ent = this.entitiesBackward[i];
+                if (!ent._killed) {
+                    ent.update();
+                }
+            }
+
+            this.entitiesBackward = this.entitiesBackward.filter(function (entity) {
+                return !entity._killed;
+            });
+        },
+
+        sortEntities: function () {
+            this.parent();
+
+            this.entitiesBackward.sort(this.sortBy);
+        },
+
+        drawBackward: function () {
+            for (var i = 0; i < this.entitiesBackward.length; i++) {
+                this.entitiesBackward[i].draw();
+            }
+        },
     };
 });
diff --git a/lib/dl/templates/mixins/button.js b/lib/dl/templates/mixins/button.js
index 954436c..c9b12e6 100755
--- a/lib/dl/templates/mixins/button.js
+++ b/lib/dl/templates/mixins/button.js
@@ -98,6 +98,16 @@ ig.module(
         onPointerLeave: function (pointer) { },
         onPointerFirstClick: function (pointer) { },
         onPointerClicking: function (pointer) { },
-        onPointerReleased: function (pointer) { }
+        onPointerReleased: function (pointer) { },
+
+        disableEntityUnder: function () {
+            this.parent();
+            this.setEnable(false);
+        },
+
+        enableEntityUnder: function () {
+            this.parent();
+            this.setEnable(true);
+        },
     };
 });
diff --git a/lib/dl/templates/mixins/div-layer.js b/lib/dl/templates/mixins/div-layer.js
index b4bfae2..e3c5153 100755
--- a/lib/dl/templates/mixins/div-layer.js
+++ b/lib/dl/templates/mixins/div-layer.js
@@ -20,10 +20,12 @@ ig.module(
         },
 
         disableEntityUnder: function () {
+            this.parent();
             this.c_DivLayerComponent.hideDiv();
         },
 
         enableEntityUnder: function () {
+            this.parent();
             this.c_DivLayerComponent.showDiv();
         }
     };
diff --git a/lib/dl/templates/mixins/text-input-layer.js b/lib/dl/templates/mixins/text-input-layer.js
index 1c8de58..91a1f5d 100755
--- a/lib/dl/templates/mixins/text-input-layer.js
+++ b/lib/dl/templates/mixins/text-input-layer.js
@@ -44,12 +44,16 @@ ig.module(
         },
 
         disableEntityUnder: function () {
+            this.parent();
+
             if (this.killed) return;
 
             this.hide();
         },
 
         enableEntityUnder: function () {
+            this.parent();
+            
             if (this.killed) return;
 
             this.show();
diff --git a/lib/game/main.js b/lib/game/main.js
index 171fc26..5acfee5 100755
--- a/lib/game/main.js
+++ b/lib/game/main.js
@@ -8,6 +8,8 @@ ig.module(
 	'dl.network.mixins.main',
 	'plugins.packer.packer-plugin',
 	'impact.debug.debug',
+	'plugins.lootbox.lootbox-plugin',
+	'plugins.secure-ls',
 	// 'plugins.box2d.joncomDebug',
 
     // Patches
@@ -39,7 +41,9 @@ ig.module(
 	'plugins.helpers.utilsmath',
 	'plugins.helpers.utilsvector2',
 	'plugins.io.simple-keyboard',
+	'plugins.responsive.responsive-plugin',
 	'plugins.responsive.responsive-plugin-v2',
+	
 	// NETWORKING
 
     // Data types
diff --git a/lib/plugins/responsive/responsive-plugin-v2.js b/lib/plugins/responsive/responsive-plugin-v2.js
index 04d762c..d5a9f5b 100755
--- a/lib/plugins/responsive/responsive-plugin-v2.js
+++ b/lib/plugins/responsive/responsive-plugin-v2.js
@@ -7,7 +7,7 @@ ig.module(
 ).requires(
     'impact.system',
     'impact.entity',
-    'plugins.handlers.size-handler'
+    'plugins.handlers.size-handler',
 ).defines(function () {
     ig.SizeHandler.inject({
         resize: function () {
@@ -55,10 +55,14 @@ ig.module(
                 // if need to fill vertically
                 newWidth = this.originalResolution.x;
                 newHeight = newWidth / newAspectRatio;
+                ig.responsive.scaleX = 1;
+                ig.responsive.scaleY = newHeight / this.originalResolution.y;
             } else {
                 // if need to fill horizontally
                 newHeight = this.originalResolution.y;
                 newWidth = newHeight * newAspectRatio;
+                ig.responsive.scaleX = newWidth / this.originalResolution.x;
+                ig.responsive.scaleY = 1;
             }
 
             this.scaleRatioMultiplier = new Vector2(innerWidth / newWidth, innerHeight / newHeight);
@@ -66,6 +70,14 @@ ig.module(
             this.mobile.actualResolution = new Vector2(newWidth, newHeight);
             this.desktop.actualSize = new Vector2(innerWidth, innerHeight);
             this.mobile.actualSize = new Vector2(innerWidth, innerHeight);
+
+            ig.responsive.originalWidth = this.originalResolution.x;
+            ig.responsive.originalHeight = this.originalResolution.y;
+            ig.responsive.width = newWidth;
+            ig.responsive.height = newHeight;
+            ig.responsive.halfWidth = newWidth / 2;
+            ig.responsive.halfHeight = newHeight / 2;
+            ig.responsive.fillScale = Math.max(ig.responsive.scaleX, ig.responsive.scaleY);
         },
 
         resizeLayers: function (width, height) {
diff --git a/lib/plugins/responsive/responsive-plugin.js b/lib/plugins/responsive/responsive-plugin.js
index 8a6b621..3c739f9 100755
--- a/lib/plugins/responsive/responsive-plugin.js
+++ b/lib/plugins/responsive/responsive-plugin.js
@@ -12,193 +12,193 @@ ig.module('plugins.responsive.responsive-plugin')
         'plugins.handlers.size-handler',
         'plugins.responsive.responsive-utils'
     ).defines(function () {
-        BABYLON = false;
-        ig.SizeHandler.inject({
-
-            resize: function () {
-                this.parent();
-            },
-
-            sizeCalcs: function () {
-                if (!this.originalResolution) {
-                    this.originalResolution = new Vector2(this.desktop.actualResolution.x, this.desktop.actualResolution.y);
-                    ig.responsive.originalWidth = this.desktop.actualResolution.x;
-                    ig.responsive.originalHeight = this.desktop.actualResolution.y;
-                }
-                var innerWidth = window.innerWidth;
-                var innerHeight = window.innerHeight;
-
-                var newAspectRatio = innerWidth / innerHeight;
-                var originalAspectRatio = this.originalResolution.x / this.originalResolution.y;
-                var newWidth = 0;
-                var newHeight = 0;
-                this.windowSize = new Vector2(innerWidth, innerHeight);
-                if (newAspectRatio < originalAspectRatio) { // if need to fill vertically
-                    newWidth = this.originalResolution.x;
-                    newHeight = newWidth / newAspectRatio;
-                    ig.responsive.scaleX = 1;
-                    ig.responsive.scaleY = newHeight / this.originalResolution.y;
-                } else {
-                    newHeight = this.originalResolution.y;
-                    newWidth = newHeight * newAspectRatio;
-                    ig.responsive.scaleX = newWidth / this.originalResolution.x;
-                    ig.responsive.scaleY = 1;
-                }
-
-                this.scaleRatioMultiplier = new Vector2(innerWidth / newWidth, innerHeight / newHeight);
-                this.desktop.actualResolution = new Vector2(newWidth, newHeight);
-                this.mobile.actualResolution = new Vector2(newWidth, newHeight);
-                this.desktop.actualSize = new Vector2(innerWidth, innerHeight);
-                this.mobile.actualSize = new Vector2(innerWidth, innerHeight);
-                ig.responsive.width = newWidth;
-                ig.responsive.height = newHeight;
-                ig.responsive.halfWidth = newWidth / 2;
-                ig.responsive.halfHeight = newHeight / 2;
-                ig.responsive.fillScale = Math.max(ig.responsive.scaleX, ig.responsive.scaleY);
-            },
-
-            resizeLayers: function (width, height) {
-                ig.system.resize(ig.sizeHandler.desktop.actualResolution.x, ig.sizeHandler.desktop.actualResolution.y);
-                for (var index = 0; index < this.coreDivsToResize.length; index++) {
-                    var elem = ig.domHandler.getElementById(this.coreDivsToResize[index]);
-
-                    var l = Math.floor(((ig.sizeHandler.windowSize.x / 2) - (ig.sizeHandler.desktop.actualSize.x / 2)));
-                    var t = Math.floor(((ig.sizeHandler.windowSize.y / 2) - (ig.sizeHandler.desktop.actualSize.y / 2)));
-                    if (l < 0) l = 0;
-                    if (t < 0) t = 0;
-                    ig.domHandler.resizeOffset(elem, Math.floor(ig.sizeHandler.desktop.actualSize.x), Math.floor(ig.sizeHandler.desktop.actualSize.y), l, t);
-                }
-
-                for (var key in this.adsToResize) {
-                    var keyDiv = ig.domHandler.getElementById('#' + key);
-                    var keyBox = ig.domHandler.getElementById('#' + key + '-Box');
-
-                    var divLeft = (window.innerWidth - this.adsToResize[key]['box-width']) / 2 + 'px';
-                    var divTop = (window.innerHeight - this.adsToResize[key]['box-height']) / 2 + 'px';
-
-                    if (keyDiv) {
-                        ig.domHandler.css(keyDiv, { width: window.innerWidth, height: window.innerHeight });
-                    }
-                    if (keyBox) {
-                        ig.domHandler.css(keyBox, { left: divLeft, top: divTop });
-                    }
-                }
-
-                var canvas = ig.domHandler.getElementById('#canvas');
-                var offsets = ig.domHandler.getOffsets(canvas);
-                var offsetLeft = offsets.left;
-                var offsetTop = offsets.top;
-                var aspectRatioMin = Math.min(ig.sizeHandler.scaleRatioMultiplier.x, ig.sizeHandler.scaleRatioMultiplier.y);
-
-                for (var key in this.dynamicClickableEntityDivs) {
-                    var div = ig.domHandler.getElementById('#' + key);
-
-                    var posX = this.dynamicClickableEntityDivs[key].entity_pos_x;
-                    var posY = this.dynamicClickableEntityDivs[key].entity_pos_y;
-                    var sizeX = this.dynamicClickableEntityDivs[key].width;
-                    var sizeY = this.dynamicClickableEntityDivs[key].height;
-
-                    var divleft = Math.floor(offsetLeft + posX * this.scaleRatioMultiplier.x) + 'px';
-                    var divtop = Math.floor(offsetTop + posY * this.scaleRatioMultiplier.y) + 'px';
-                    var divwidth = Math.floor(sizeX * this.scaleRatioMultiplier.x) + 'px';
-                    var divheight = Math.floor(sizeY * this.scaleRatioMultiplier.y) + 'px';
-
-                    ig.domHandler.css(div
-                        , {
-                            float: 'left',
-                             position: 'absolute',
-                             left: divleft,
-                             top: divtop,
-                             width: divwidth,
-                             height: divheight,
-                             'z-index': 3
-                        }
-                    );
-
-                    if (this.dynamicClickableEntityDivs[key]['font-size']) {
-                        var fontSize = this.dynamicClickableEntityDivs[key]['font-size'];
-                        ig.domHandler.css(div, { 'font-size': (fontSize * aspectRatioMin) + 'px' });
-                    }
-                }
-
-                $('#ajaxbar').width(this.windowSize.x);
-                $('#ajaxbar').height(this.windowSize.y);
-            },
-
-            reorient: function () {
-                if (!ig.ua.mobile) {
-                    this.resize();
-                }
-                else {
-                    this.resize();
-                    this.resizeAds();
-                }
-
-                if (BABYLON) this.resizeBabylon();
-                if (ig.game) {
-                    ig.game.update();
-                    ig.game.draw();
-                }
-            },
-
-            resizeBabylon: function () {
-                var w = window.innerWidth;
-                var h = window.innerHeight;
-                var ratio = w / h;
-
-                // var oriW = this.desktop.actualResolution.x
-                // var oriH = this.desktop.actualResolution.y
-                var oriW = ig.responsive.originalWidth;
-                var oriH = ig.responsive.originalHeight;
-                var oriRatio = oriW / oriH;
-
-                var maxSize = Math.max(oriW, oriH);
-                if (ig.ua.mobile) maxSize = 640;
-
-                minSize = Math.min(oriW, oriH);
-
-                if (ratio > oriRatio) {
-                    if (h > oriH) h = oriH;
-                    w = Math.floor((window.innerWidth / window.innerHeight) * h);
-
-                    if (w > maxSize) w = maxSize;
-                    h = Math.floor((window.innerHeight / window.innerWidth) * w);
-                } else {
-                    if (w > oriW) w = oriW;
-                    h = Math.floor((window.innerHeight / window.innerWidth) * w);
-
-                    if (h > maxSize) h = maxSize;
-                    w = Math.floor((window.innerWidth / window.innerHeight) * h);
-                }
-
-                var renderRatioW = 1;
-                var renderRatioH = 1;
-
-                if (window.innerWidth > maxSize) {
-                    renderRatioW = window.innerWidth / maxSize;
-                }
-                if (window.innerHeight > maxSize) {
-                    renderRatioH = window.innerHeight / maxSize;
-                }
-                wgl.system.engine.setSize(w, h);
-                wgl.system.engine.resize();
-                wgl.system.engine.setHardwareScalingLevel(Math.max(renderRatioW, renderRatioH));
-
-                ig.gameScene.zoomFactor = 1;
-                if (ig.ua.mobile && minSize < maxSize) {
-                    ig.gameScene.zoomFactor = maxSize / minSize;
-                }
-                ig.wglW = w;
-                ig.wglH = h;
-                ig.wglInnerW = window.innerWidth;
-                ig.wglInnerH = window.innerHeight;
-
-                console.log(
-                    'babylon renderSize : ', wgl.system.engine.getRenderWidth(), wgl.system.engine.getRenderHeight(),
-                    'hwScalingLevel : ', wgl.system.engine.getHardwareScalingLevel());
-            }
-
-        });
+        // BABYLON = false;
+        // ig.SizeHandler.inject({
+
+        //     resize: function () {
+        //         this.parent();
+        //     },
+
+        //     sizeCalcs: function () {
+        //         if (!this.originalResolution) {
+        //             this.originalResolution = new Vector2(this.desktop.actualResolution.x, this.desktop.actualResolution.y);
+        //             ig.responsive.originalWidth = this.desktop.actualResolution.x;
+        //             ig.responsive.originalHeight = this.desktop.actualResolution.y;
+        //         }
+        //         var innerWidth = window.innerWidth;
+        //         var innerHeight = window.innerHeight;
+
+        //         var newAspectRatio = innerWidth / innerHeight;
+        //         var originalAspectRatio = this.originalResolution.x / this.originalResolution.y;
+        //         var newWidth = 0;
+        //         var newHeight = 0;
+        //         this.windowSize = new Vector2(innerWidth, innerHeight);
+        //         if (newAspectRatio < originalAspectRatio) { // if need to fill vertically
+        //             newWidth = this.originalResolution.x;
+        //             newHeight = newWidth / newAspectRatio;
+        //             ig.responsive.scaleX = 1;
+        //             ig.responsive.scaleY = newHeight / this.originalResolution.y;
+        //         } else {
+        //             newHeight = this.originalResolution.y;
+        //             newWidth = newHeight * newAspectRatio;
+        //             ig.responsive.scaleX = newWidth / this.originalResolution.x;
+        //             ig.responsive.scaleY = 1;
+        //         }
+
+        //         this.scaleRatioMultiplier = new Vector2(innerWidth / newWidth, innerHeight / newHeight);
+        //         this.desktop.actualResolution = new Vector2(newWidth, newHeight);
+        //         this.mobile.actualResolution = new Vector2(newWidth, newHeight);
+        //         this.desktop.actualSize = new Vector2(innerWidth, innerHeight);
+        //         this.mobile.actualSize = new Vector2(innerWidth, innerHeight);
+        //         ig.responsive.width = newWidth;
+        //         ig.responsive.height = newHeight;
+        //         ig.responsive.halfWidth = newWidth / 2;
+        //         ig.responsive.halfHeight = newHeight / 2;
+        //         ig.responsive.fillScale = Math.max(ig.responsive.scaleX, ig.responsive.scaleY);
+        //     },
+
+        //     resizeLayers: function (width, height) {
+        //         ig.system.resize(ig.sizeHandler.desktop.actualResolution.x, ig.sizeHandler.desktop.actualResolution.y);
+        //         for (var index = 0; index < this.coreDivsToResize.length; index++) {
+        //             var elem = ig.domHandler.getElementById(this.coreDivsToResize[index]);
+
+        //             var l = Math.floor(((ig.sizeHandler.windowSize.x / 2) - (ig.sizeHandler.desktop.actualSize.x / 2)));
+        //             var t = Math.floor(((ig.sizeHandler.windowSize.y / 2) - (ig.sizeHandler.desktop.actualSize.y / 2)));
+        //             if (l < 0) l = 0;
+        //             if (t < 0) t = 0;
+        //             ig.domHandler.resizeOffset(elem, Math.floor(ig.sizeHandler.desktop.actualSize.x), Math.floor(ig.sizeHandler.desktop.actualSize.y), l, t);
+        //         }
+
+        //         for (var key in this.adsToResize) {
+        //             var keyDiv = ig.domHandler.getElementById('#' + key);
+        //             var keyBox = ig.domHandler.getElementById('#' + key + '-Box');
+
+        //             var divLeft = (window.innerWidth - this.adsToResize[key]['box-width']) / 2 + 'px';
+        //             var divTop = (window.innerHeight - this.adsToResize[key]['box-height']) / 2 + 'px';
+
+        //             if (keyDiv) {
+        //                 ig.domHandler.css(keyDiv, { width: window.innerWidth, height: window.innerHeight });
+        //             }
+        //             if (keyBox) {
+        //                 ig.domHandler.css(keyBox, { left: divLeft, top: divTop });
+        //             }
+        //         }
+
+        //         var canvas = ig.domHandler.getElementById('#canvas');
+        //         var offsets = ig.domHandler.getOffsets(canvas);
+        //         var offsetLeft = offsets.left;
+        //         var offsetTop = offsets.top;
+        //         var aspectRatioMin = Math.min(ig.sizeHandler.scaleRatioMultiplier.x, ig.sizeHandler.scaleRatioMultiplier.y);
+
+        //         for (var key in this.dynamicClickableEntityDivs) {
+        //             var div = ig.domHandler.getElementById('#' + key);
+
+        //             var posX = this.dynamicClickableEntityDivs[key].entity_pos_x;
+        //             var posY = this.dynamicClickableEntityDivs[key].entity_pos_y;
+        //             var sizeX = this.dynamicClickableEntityDivs[key].width;
+        //             var sizeY = this.dynamicClickableEntityDivs[key].height;
+
+        //             var divleft = Math.floor(offsetLeft + posX * this.scaleRatioMultiplier.x) + 'px';
+        //             var divtop = Math.floor(offsetTop + posY * this.scaleRatioMultiplier.y) + 'px';
+        //             var divwidth = Math.floor(sizeX * this.scaleRatioMultiplier.x) + 'px';
+        //             var divheight = Math.floor(sizeY * this.scaleRatioMultiplier.y) + 'px';
+
+        //             ig.domHandler.css(div
+        //                 , {
+        //                     float: 'left',
+        //                      position: 'absolute',
+        //                      left: divleft,
+        //                      top: divtop,
+        //                      width: divwidth,
+        //                      height: divheight,
+        //                      'z-index': 3
+        //                 }
+        //             );
+
+        //             if (this.dynamicClickableEntityDivs[key]['font-size']) {
+        //                 var fontSize = this.dynamicClickableEntityDivs[key]['font-size'];
+        //                 ig.domHandler.css(div, { 'font-size': (fontSize * aspectRatioMin) + 'px' });
+        //             }
+        //         }
+
+        //         $('#ajaxbar').width(this.windowSize.x);
+        //         $('#ajaxbar').height(this.windowSize.y);
+        //     },
+
+        //     reorient: function () {
+        //         if (!ig.ua.mobile) {
+        //             this.resize();
+        //         }
+        //         else {
+        //             this.resize();
+        //             this.resizeAds();
+        //         }
+
+        //         if (BABYLON) this.resizeBabylon();
+        //         if (ig.game) {
+        //             ig.game.update();
+        //             ig.game.draw();
+        //         }
+        //     },
+
+        //     resizeBabylon: function () {
+        //         var w = window.innerWidth;
+        //         var h = window.innerHeight;
+        //         var ratio = w / h;
+
+        //         // var oriW = this.desktop.actualResolution.x
+        //         // var oriH = this.desktop.actualResolution.y
+        //         var oriW = ig.responsive.originalWidth;
+        //         var oriH = ig.responsive.originalHeight;
+        //         var oriRatio = oriW / oriH;
+
+        //         var maxSize = Math.max(oriW, oriH);
+        //         if (ig.ua.mobile) maxSize = 640;
+
+        //         minSize = Math.min(oriW, oriH);
+
+        //         if (ratio > oriRatio) {
+        //             if (h > oriH) h = oriH;
+        //             w = Math.floor((window.innerWidth / window.innerHeight) * h);
+
+        //             if (w > maxSize) w = maxSize;
+        //             h = Math.floor((window.innerHeight / window.innerWidth) * w);
+        //         } else {
+        //             if (w > oriW) w = oriW;
+        //             h = Math.floor((window.innerHeight / window.innerWidth) * w);
+
+        //             if (h > maxSize) h = maxSize;
+        //             w = Math.floor((window.innerWidth / window.innerHeight) * h);
+        //         }
+
+        //         var renderRatioW = 1;
+        //         var renderRatioH = 1;
+
+        //         if (window.innerWidth > maxSize) {
+        //             renderRatioW = window.innerWidth / maxSize;
+        //         }
+        //         if (window.innerHeight > maxSize) {
+        //             renderRatioH = window.innerHeight / maxSize;
+        //         }
+        //         wgl.system.engine.setSize(w, h);
+        //         wgl.system.engine.resize();
+        //         wgl.system.engine.setHardwareScalingLevel(Math.max(renderRatioW, renderRatioH));
+
+        //         ig.gameScene.zoomFactor = 1;
+        //         if (ig.ua.mobile && minSize < maxSize) {
+        //             ig.gameScene.zoomFactor = maxSize / minSize;
+        //         }
+        //         ig.wglW = w;
+        //         ig.wglH = h;
+        //         ig.wglInnerW = window.innerWidth;
+        //         ig.wglInnerH = window.innerHeight;
+
+        //         console.log(
+        //             'babylon renderSize : ', wgl.system.engine.getRenderWidth(), wgl.system.engine.getRenderHeight(),
+        //             'hwScalingLevel : ', wgl.system.engine.getHardwareScalingLevel());
+        //     }
+
+        // });
 
         ig.Entity.inject({
             anchoredOffset: { x: 0, y: 0 },
