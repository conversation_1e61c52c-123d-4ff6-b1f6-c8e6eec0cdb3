/**
 * Docker Plugin Usage Documentation
 * 
 * This plugin provides Docker positioning functionality for entities,
 * allowing them to be positioned relative to other objects with percentage
 * and offset-based positioning.
 * 
 * Created by: Augment Agent (consolidating existing Docker functionality)
 * Version: 1.0.0
 */

/**
 * BASIC SETUP
 * 
 * 1. Include the plugin in your module requirements:
 */
ig.module('your.module.name')
.requires(
    'dl-plugins.docker.docker-plugin'
    // ... other requirements
)
.defines(function() {
    // Your module code
});

/**
 * METHOD 1: Using the Mixin (Recommended)
 * 
 * The easiest way to add Docker functionality to an entity is to extend
 * the DockerMixin. This automatically adds the Docker component and
 * provides convenient helper methods.
 */

// Example entity using the mixin
YourEntity = ig.Entity.extend(ig.DockerMixin).extend({
    init: function(x, y, settings) {
        this.parent(x, y, settings);
        
        // Docker component is automatically available as this.dockerComponent
        // Default behavior: docks to camera at center position
    },
    
    postInit: function() {
        this.parent();
        
        // Dock to camera at top-left corner with offset
        this.dockToCamera({
            percent: { x: 0, y: 0 },
            offset: { x: 10, y: 10 }
        });
    }
});

/**
 * METHOD 2: Manual Component Addition
 * 
 * You can manually add the Docker component if you need more control
 * or don't want to use the mixin.
 */

YourEntity = ig.Entity.extend({
    initComponents: function() {
        this.parent();
        
        // Manually add Docker component
        this.dockerComponent = this.addComponent(ig.DockerComponent, {
            dockerObject: ig.game.camera,
            dockerPercent: { x: 0.5, y: 0.5 },
            dockerOffset: { x: 0, y: 0 }
        });
    }
});

/**
 * CONFIGURATION OPTIONS
 * 
 * Docker positioning is controlled by three main properties:
 */

// dockerObject: The object to dock to (required)
// dockerPercent: Position as percentage of target object's size
//   { x: 0, y: 0 }     = top-left corner
//   { x: 0.5, y: 0.5 } = center (default)
//   { x: 1, y: 1 }     = bottom-right corner
// dockerOffset: Additional pixel offset from calculated position
//   { x: 10, y: -20 }  = 10 pixels right, 20 pixels up

/**
 * COMMON USAGE PATTERNS
 */

// 1. Dock to camera (UI elements)
entity.dockToCamera({
    percent: { x: 0.5, y: 0.1 },  // Top center
    offset: { x: 0, y: 20 }       // 20 pixels down
});

// 2. Dock to another entity
entity.dockTo(parentEntity, {
    percent: { x: 1, y: 0 },      // Top-right of parent
    offset: { x: 5, y: 0 }        // 5 pixels to the right
});

// 3. Using convenience methods (when using mixin)
entity.dockToTopLeft(parentEntity, { x: 10, y: 10 });
entity.dockToTopRight(parentEntity, { x: -10, y: 10 });
entity.dockToBottomLeft(parentEntity, { x: 10, y: -10 });
entity.dockToBottomRight(parentEntity, { x: -10, y: -10 });
entity.dockToCenter(parentEntity);

/**
 * DYNAMIC CONFIGURATION
 */

// Update docker configuration at runtime
entity.updateDockerConfig({
    dockerObject: newTarget,
    dockerPercent: { x: 0.8, y: 0.2 },
    dockerOffset: { x: 0, y: 0 }
});

// Enable/disable docker positioning
entity.setDockerEnabled(false);  // Use normal positioning
entity.setDockerEnabled(true);   // Resume docker positioning

// Get current configuration
var config = entity.getDockerConfig();
console.log('Docked to:', config.dockerObject);
console.log('Position:', config.dockerPercent);
console.log('Offset:', config.dockerOffset);
console.log('Enabled:', config.enabled);

/**
 * ADVANCED USAGE
 */

// Custom docker object (any object with pos and size properties)
var customTarget = {
    pos: { x: 100, y: 100 },
    size: { x: 200, y: 150 }
};

entity.dockTo(customTarget, {
    percent: { x: 0.5, y: 1 },    // Bottom center
    offset: { x: 0, y: 10 }       // 10 pixels below
});

// Responsive positioning for mobile
if (ig.ua.mobile) {
    entity.dockToCamera({
        percent: { x: 0.5, y: 0.23 },
        offset: { x: 0, y: 0 }
    });
} else {
    entity.dockToCamera({
        percent: { x: 0.5, y: 0.3 },
        offset: { x: 0, y: 0 }
    });
}

/**
 * MIGRATION FROM EXISTING DOCKER USAGE
 * 
 * The plugin maintains backward compatibility with existing Docker usage.
 * Existing code using dl.DockerComponent and dl.MixinDocker will continue
 * to work without changes.
 */

// Old usage (still works):
// this.c_DockerComponent = this.addComponent(dl.DockerComponent);
// .extend(dl.MixinDocker)

// New usage (recommended):
// this.dockerComponent = this.addComponent(ig.DockerComponent);
// .extend(ig.DockerMixin)

/**
 * EVENT HANDLING
 * 
 * The Docker component automatically listens for 'positionChanged' events
 * from the docker object and updates positioning accordingly. This ensures
 * that docked entities stay properly positioned when their target moves.
 */

// If your docker object supports events, position updates are automatic
// If not, you can manually trigger updates:
entity.dockerComponent.updatePos();

/**
 * PERFORMANCE CONSIDERATIONS
 * 
 * - Docker positioning is only calculated when the docker object moves
 * - Components can be disabled to temporarily use normal positioning
 * - Event listeners are automatically cleaned up when components are destroyed
 */
