ig.module(
    'game.dl-entities.game-map-continent'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker'
).defines(function () {
    dl.EntityGameMapContinent = dl.Entity
        .extend(dl.MixinDocker)
        .extend({
            postInit: function () {
                this.parent();
            },

            update: function () {
                this.parent();
            },

            draw: function (ctx) {
                this.parent(ctx);
                ctx.save();
                var tmpNation = {
                    x: -this.mapData.mapDimension.width * 0.5 + this.mapData.continentDetails.objects[0].x,
                    y: -this.mapData.mapDimension.height * 0.5 + this.mapData.continentDetails.objects[0].y
                };
                ctx.beginPath();
                ctx.moveTo(tmpNation.x, tmpNation.y);
                for (var i = 0; i < this.mapData.continentDetails.objects[0].polygon.length; i++) {
                    ctx.lineTo(tmpNation.x + this.mapData.continentDetails.objects[0].polygon[i].x, tmpNation.y + this.mapData.continentDetails.objects[0].polygon[i].y);
                }
                ctx.closePath();
                // change to white later
                ctx.fillStyle = '#ffffff';
                ctx.fill();
                ctx.restore();
            }
        });

    // Enable cache
    // dl.enableCacheCanvas(dl.EntityGameMapContinent);
});
