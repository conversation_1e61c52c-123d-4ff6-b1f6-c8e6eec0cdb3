ig.module("plugins.svas")
.requires(
    'plugins.handlers.dom-handler',
    'impact.timer',
    'plugins.crypto-aes'
)
.defines(function() {
    ig.Svas = ig.Class.extend({
        NAME: "SVAS",
        VERSION: "1.2.5",
        parameters: {
            "verbose": null
        },
        settings: {
            localStorageKey: 'mjs-svas-user-info-v1',
            gameId: '1651673491261401',
            rememberNickname: true,
            autoShowUserRegistration: true,
            alwaysShowRegistration: false,
            autoShowLeaderboard: false,
            showLoginConfirmation: false,
            leaderboardMaxEntries: 100,
            customStyle: null,
            callbackFunctions: {
                onLogin: null,
                onRegister: null,
                onUserInfoLoaded: null,
                onUserInfoUpdate: null,
                onLogout: null
            },
            copyIcon:"data:image/png;base64,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",
            strings: {
                reCaptchaBranding: "Protected by reCAPTCHA",
                registerHeader: "ENTER A NICKNAME",
                registerNicknamePlaceholder: "Nickname",
                registerMessage: "Register a nickname to show off your best score. ",
                registerButton: "Register",
                registerFooterMessage: "Already have an account?",
                registerFooterLink: "Log in",
                loginHeader: "LOG IN",
                loginNicknamePlaceholder: "Nickname",
                loginPincodePlaceholder: "6 Digit PIN",
                loginPincodeToggleLabel: "Show Pincode",
                loginButton: "Submit",
                loginFooterMessage: "Don't have an account?",
                loginFooterLink: "Register Now",
                successfulRegistrationHeader: "Successful registration!",
                successfulRegistrationMessage: "Your PIN code is",
                successfulRegistrationTip: "Tip: write this down somewhere safe",
                successfulRegistrationButton: "Ok",
                successfulLoginHeader: "Successful login!",
                successfulLoginMessage: "Your PIN code is",
                successfulLoginTip: "Tip: write this down somewhere safe",
                successfulLoginButton: "Ok",
                loginNotificationInvalid: "Invalid login! Please check your nickname and pincode.",
                nicknameNotificationInvalid: "Invalid nickname, it contains non-alphanumeric characters.",
                nicknameNotificationMinLength: "Invalid nickname, it must contains at least {minLength} letters.",
                nicknameNotificationMaxLength: "Invalid nickname, it must contains at most {maxLength} letters.",
                nicknameNotificationEmpty: "Invalid nickname, it cannot be empty.",
                nicknameNotificationAnother: "Please choose another nickname.",
                pincodeNotificationInvalid: "Invalid PIN code, it contains non-numeric characters.",
                pincodeNotificationFixedLength: "Invalid PIN code, it must contains exactly {fixedLength} digits.",
                pincodeNotificationEmpty: "Invalid PIN code, it cannot be empty.",
                leaderboardHeader: {
                    "score": "LEADERBOARD",
                    "stats_wins": "MOST WINS",
                    "stats_losses": "MOST LOSSES",
                    "stats_win_rate": "BEST WIN RATE",
                    "virtual_currency1": "THE RICH LIST",
                    "virtual_currency2": "THE RICH LIST",
                    "virtual_currency3": "THE RICH LIST",
                    "virtual_currency4": "THE RICH LIST",
                    "virtual_currency5": "THE RICH LIST"
                },
                leaderboardSubheaderRank: {
                    "score": "RANK",
                    "stats_wins": "RANK",
                    "stats_losses": "RANK",
                    "stats_win_rate": "RANK",
                    "virtual_currency1": "RANK",
                    "virtual_currency2": "RANK",
                    "virtual_currency3": "RANK",
                    "virtual_currency4": "RANK",
                    "virtual_currency5": "RANK",
                },
                leaderboardSubheaderName: {
                    "score": "NICKNAME",
                    "stats_wins": "NICKNAME",
                    "stats_losses": "NICKNAME",
                    "stats_win_rate": "NICKNAME",
                    "virtual_currency1": "NICKNAME",
                    "virtual_currency2": "NICKNAME",
                    "virtual_currency3": "NICKNAME",
                    "virtual_currency4": "NICKNAME",
                    "virtual_currency5": "NICKNAME",
                },
                leaderboardSubheaderValue: {
                    "score": "SCORE",
                    "stats_wins": "WINS",
                    "stats_losses": "LOSSES",
                    "stats_win_rate": "WIN RATE",
                    "virtual_currency1": "COINS",
                    "virtual_currency2": "COINS",
                    "virtual_currency3": "COINS",
                    "virtual_currency4": "COINS",
                    "virtual_currency5": "COINS"
                },
                leaderboardContentLoading: "Loading, please wait...",
                leaderboardFooterMessage: "Want to compete in the leaderboards? ",
                leaderboardFooterLinkRegister: "Register Now",
                leaderboardFooterLinkLogout: "Log Out",
                leaderboardErrorMessage: "An error occurred while loading the leaderboards, please try again later.",
            },            
            popupContainterId: "ajaxbar",
            userNickname: {
                minLength: 5, 
                maxLength: 12
            },
            userPincode: {
                fixedLength: 6
            },
            userRegistrationAPI: {
                url: "https://svas-consumer.marketjs-cloud.com/api/register",
                method: "POST",
                timeout: 5000
            },
            userLoginAPI: {
                url: "https://svas-consumer.marketjs-cloud.com/api/login",
                method: "POST",
                timeout: 5000
            },
            postScoreAPI: {
                url: "https://svas-consumer.marketjs-cloud.com/api/score", 
                method: "POST",
                timeout: 5000
            },
            getLeaderboardAPI: {
                score: {
                    url: "https://svas-consumer.marketjs-cloud.com/api/getLeaderboard",
                    method: "GET",
                    timeout: 5000
                },
                stats: {
                    url: "https://svas-consumer.marketjs-cloud.com/api/user/stats/getLeaderboard", 
                    method: "GET",
                    timeout: 5000
                },
                virtual_currency: {
                    url: "https://svas-consumer.marketjs-cloud.com/api/user/virtual_currency/getLeaderboard",
                    method: "GET",
                    timeout: 5000
                }
            },
        },

        isUserAuthenticated: false,
        userInfo: {
            id: null,
            nickname: null,
            pincode: null,
        },        

        scoreData: {
            currentGameScore: 0,
            currentGameTime: 0,
            pendingScoreSubmission: false,
            lastPostedScore: 0,
            lastPostedGameTime: 0,
            internalGameTimer: null,
        },

        leaderboardData: {
            score: [],
            stats_wins: [],
            stats_losses: [],
            stats_win_rate: [],
            virtual_currency1: [],
            virtual_currency2: [],
            virtual_currency3: [],
            virtual_currency4: [],
            virtual_currency5: []
        },

        divElements: {
            loader: null,
            userRegistration: null,
            successfulRegistration: null,
            userLogin: null,
            successfulLogin: null,
            scoreLeaderboard: null,
            stats_winsLeaderboard: null,
            stats_lossesLeaderboard: null,
            stats_win_rateLeaderboard: null,
            virtual_currency1Leaderboard: null,
            virtual_currency2Leaderboard: null,
            virtual_currency3Leaderboard: null,
            virtual_currency4Leaderboard: null,
            virtual_currency5Leaderboard: null,
            leaderboardFooter: null
        },

        rankTypes: {
            score: "score",
            stats_wins: "stats_wins",
            stats_losses: "stats_losses",
            stats_win_rate: "stats_win_rate",
            virtual_currency1: "virtual_currency1",
            virtual_currency2: "virtual_currency2",
            virtual_currency3: "virtual_currency3",
            virtual_currency4: "virtual_currency4",
            virtual_currency5: "virtual_currency5"
        },

        reCaptcha: {
            loaded: null,
            url: "https://www.google.com/recaptcha/api.js",
            siteKey: "6LeTWtweAAAAAOeZKAcG7GumhqIqMEsZ2IFZp-3k"
        },

        init: function(settings){
            ig.merge(this.settings, settings);

            // this.settings.localStorageKey=ig.game.localStorageKey;
            // alias
            this.setAlias();

            // get URL parameters
            this.getUrlParameters();

            this.injectCustomCss();

            this.initReCaptcha();

            this.updateGameId(this.settings.gameId);

            // this.ready();

            return this;
        },

        ready: function() { 
            console.log("Initialized Svas");

            this.loadUserInfo();
            if (this.settings.autoShowUserRegistration) {
                this.showUserRegistration();
            }
        },

        setAlias: function() {
            ig.svas = this;
            ig.global.svas = window.svas = this;
        },

        getQueryVariable: function(variable) {
            var hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');

            for (var i = 0; i < hashes.length; i++) {
                var match = hashes[i].match(/([^=]+?)=(.+)/);

                if (match) {
                    var key = decodeURIComponent(match[1]),
                        value = decodeURIComponent(match[2]);

                    if (variable === key) {
                        return value;
                    }
                }
            }
        },

        getUrlParameters: function() {
            for (var key in this.parameters) {
                if (this.parameters.hasOwnProperty(key)) {
                    this.parameters[key] = this.getQueryVariable(key);
                }
            }
        },

        jsonToCss: function(json) {
            var css = "";
            for (var className in json) {
                if (json.hasOwnProperty(className)) {
                    for (var property in json[className]) {
                        if (json[className].hasOwnProperty(property)) {
                            css += className + " {" + property + ":" + json[className][property] + "} ";
                        }
                    }
                }
            }
            return css;
        },

        injectCustomCss: function() {
            if (this.settings.customStyle !== null && typeof(this.settings.customStyle) !== "undefined") {
                var style = ig.domHandler.create("style");
                
                ig.domHandler.attr(style, "type", "text/css");

                if (typeof(this.settings.customStyle) === "string") {
                    ig.domHandler.html(style, this.settings.customStyle);
                }
                else if (typeof(this.settings.customStyle) === "object") {
                    ig.domHandler.html(style, this.jsonToCss(this.settings.customStyle));
                }

                ig.domHandler.appendToHead(style);
            }
        },

        initReCaptcha: function(){
            var script = document.createElement("script");
            script.src = this.reCaptcha.url + "?render=" + this.reCaptcha.siteKey;
            script.onload = function(){
                this.log("Protected by reCAPTCHA");
                this.reCaptcha.loaded = true;
            }.bind(this);
            document.head.appendChild(script);
        },

        loadUserInfo: function() {
            if (this.settings.rememberNickname !== true) {
                return false;
            }
            var storage = ig.game.io.storage;
            var storedUserInfo = storage.get(this.settings.localStorageKey);

            if ( storedUserInfo !== null && typeof(storedUserInfo) !== "undefined" ) {
                this.userInfo.id = storedUserInfo.id;
                this.userInfo.nickname = storedUserInfo.nickname;
                this.userInfo.pincode = storedUserInfo.pincode;
                this.userInfo.alliance = storedUserInfo.alliance || ig.game.svasData.alliance;
            }

            if (this.validateUserId(this.userInfo.id) && this.validateUserNickname(this.userInfo.nickname) === true && this.validateUserPincode(this.userInfo.pincode) === true) {
                this.isUserAuthenticated = true;
            }

            try {
                if (this.settings.callbackFunctions.onUserInfoLoaded !== null && typeof (this.settings.callbackFunctions.onUserInfoLoaded) === "function") {
                    this.settings.callbackFunctions.onUserInfoLoaded(this.userInfo);
                }
            } catch (error) {
                this.log("Error in callback function onUserInfoLoaded: " + error);
            }
        },

        saveUserInfo: function() {
            // if (this.settings.rememberNickname !== true) {
            //     return false;
            // }
            var storage = ig.game.io.storage;
            var userInfoString = this.userInfo;
            storage.set(this.settings.localStorageKey, userInfoString);

            try {
                if (this.settings.callbackFunctions.onUserInfoUpdated !== null && typeof (this.settings.callbackFunctions.onUserInfoUpdated) === "function") {
                    this.settings.callbackFunctions.onUserInfoUpdated(this.userInfo);
                }
            } catch (error) {
                this.log("Error in callback function onUserInfoUpdated: " + error);
            }
        },

        deleteUserInfo: function() {
            this.userInfo.id = 0;
            this.userInfo.nickname = null;
            this.userInfo.pincode = null;

            this.saveUserInfo();
        },

        createPopupDiv: function(divId, divElementName, divContent, callback) {
            var existingDivElement = ig.domHandler.getElementById("#" + divId);
            if (existingDivElement !== null && typeof(existingDivElement) !== "undefined") {
                return existingDivElement; 
            }
            if ( this.divElements[divElementName] !== null && typeof (this.divElements[divElementName]) !== "undefined" ) {
                return this.divElements[divElementName];
            }

            var parentContainerElement = ig.domHandler.getElementById("#" + this.settings.popupContainterId);
            var newDiv = ig.domHandler.create('div');
            ig.domHandler.attr(newDiv, "id", divId);
            ig.domHandler.attr(newDiv, "class", "svas-overlay-container");
            ig.domHandler.appendChild(parentContainerElement, newDiv);

            ig.domHandler.html(newDiv, divContent);
            this.divElements[divElementName] = newDiv;
            if (typeof(callback) === "function") {
                callback(newDiv);
            }
            return newDiv;
        },

        createUserRegistrationDiv: function() {
            var divId = "svas-user-registration-container";
            var divElementContent = '\
                <div id="box-register" class="svas-box svas-box-center">\
                    <a>\
                        <div id="box-register-close" class="svas-close-button"></div>\
                    </a>\
                    <div class="svas-box-header">' + this.settings.strings.registerHeader + '</div>\
                    <div class="svas-box-contents" id="register-form" name="register-form">\
                        <div class="svas-box-nickname">\
                            <input autofocus type="text" id="register-nickname" name="register-nickname" class="svas-box-form-input" placeholder="' + this.settings.strings.registerNicknamePlaceholder + '" autocomplete="off" autocomplete="off" minlength="' + this.settings.userNickname.minLength + '" maxlength="' + this.settings.userNickname.maxLength + '">\
                        </\
                        <p style="font-size: 2em">\
                            ' + this.settings.strings.registerMessage + '\
                        </p>\
                        <div class="register-submit">\
                            <div id="register-notification" class="svas-notification"></div>\
                            <div id="register-submit-button" type="submit" class="svas-submit-button">' + this.settings.strings.registerButton + '</div>\
                        </div>\
                        <div id="box-register-footer" class="svas-box-footer">\
                            ' + this.settings.strings.registerFooterMessage + '\
                            <a style="text-decoration: underline; cursor: pointer; color: #48c107" id="register-footer-link">' + this.settings.strings.registerFooterLink + '</a>\
                        </div>\
                        <span class="svas-recaptcha-branding">' + this.settings.strings.reCaptchaBranding + '</span>\
                    </div>\
                </div>\
            ';
            this.createPopupDiv(divId, "userRegistration", divElementContent);

            // elements
            var closeButtonElement = ig.domHandler.getElementById("#" + "box-register-close"),
                nicknameInputElement = ig.domHandler.getElementById("#" + "register-nickname"),
                registerNotificationElement = ig.domHandler.getElementById("#" + "register-notification"),
                registerButtonElement = ig.domHandler.getElementById("#" + "register-submit-button"),
                registerFooterLinkElement = ig.domHandler.getElementById("#" + "register-footer-link");

            // event - close button
            if (closeButtonElement !== null && typeof(closeButtonElement) !== "undefined") {
                ig.domHandler.addEvent(closeButtonElement, "click", function(event) {
                    if(ig.game.disableBtStatus) return;
                    ig.game.playButton();
                    this.hideUserRegistration();
                }.bind(this), false);
            }

            // event - nickname input
            if (nicknameInputElement !== null && typeof(nicknameInputElement) !== "undefined") {
                if (this.userInfo.nickname !== null && typeof (this.userInfo.nickname) !== "undefined") {
                    ig.domHandler.val( nicknameInputElement, this.userInfo.nickname );
                }
                ig.domHandler.addEvent(nicknameInputElement, "input", function(event) {
                    this.validateUserNickname(ig.domHandler.getVal(nicknameInputElement), registerNotificationElement);
                }.bind(this), false);
                ig.domHandler.addEvent(nicknameInputElement, "keyup", function(event) {
                    if (event.key === "Enter") {
                        var nickname = ig.domHandler.getVal(nicknameInputElement);
                        Number.prototype.map = undefined;                    
    
                        if (this.validateUserNickname(nickname, registerNotificationElement) === true) {
                            if (grecaptcha !== null && typeof(grecaptcha) !== "undefined" && this.reCaptcha.loaded === true) {
                                try {
                                    grecaptcha.ready(function() {
                                        try{
                                            grecaptcha.execute(this.reCaptcha.siteKey, {action: 'submit'}).then(function(response) {
                                                this.registerNickname(nickname, response, registerNotificationElement);
                                            }.bind(this));
                                        }catch(e){
                                            console.log(e);
                                        }
                                    }.bind(this));
                                } catch(error) {}
                            }
                        }
                    }
                }.bind(this), false);
            }

            // event - register button
            if (registerButtonElement !== null && typeof(registerButtonElement) !== "undefined") {
                ig.domHandler.addEvent(registerButtonElement, "click", function(event) {
                    ig.game.playButton();
                    Number.prototype.map = undefined;                    
                    var nickname = ig.domHandler.getVal(nicknameInputElement);
    
                    if (this.validateUserNickname(nickname, registerNotificationElement) === true) {
                        if (grecaptcha !== null && typeof(grecaptcha) !== "undefined" && this.reCaptcha.loaded === true) {
                            try {
                                grecaptcha.ready(function() {
                                    try{
                                        grecaptcha.execute(this.reCaptcha.siteKey, {action: 'submit'}).then(function(response) {
                                            this.registerNickname(nickname, response, registerNotificationElement);
                                        }.bind(this));
                                    }catch(e){
                                        console.log(e)
                                    }
                                }.bind(this));
                            } catch(error) {}
                        }
                    }
                }.bind(this), false);
            }

            // event - register footer link
            if (registerFooterLinkElement !== null && typeof(registerFooterLinkElement) !== "undefined") {
                ig.domHandler.addEvent(registerFooterLinkElement, "click", function() {
                    this.hideUserRegistration();
                    this.showUserLogin();
                }.bind(this), false);
            }
        },

        createSuccessfulRegistrationDiv: function() {
            var divId = "svas-successful-registration-container";
            var divElementContent = '\
                <div id="box-successful-register" class="svas-box svas-box-center">\
                    <a>\
                        <div id="box-successful-register-close" class="svas-close-button"></div>\
                    </a>\
                    <div class="svas-box-header">' + this.settings.strings.successfulRegistrationHeader + '</div>\
                    <div class="svas-box-contents" style="text-align: center;" id="successful-registration-form" name="successful-registration-form">\
                        <p style="font-size: 2em;">\
                            ' + this.settings.strings.successfulRegistrationMessage + '\
                            <p id="svas-successful-register-pincode-reminder" class="svas-box-contents-highlight" style="font-size: 2.8em;">\
                                ' + this.userInfo.pincode + '\
                            </p>\
                        </p>\
                        <div id="successful-registration-notification" class="svas-notification-tip">' + this.settings.strings.successfulRegistrationTip + '</div>\
                        <div class="register-submit">\
                            <div ><img class="button-copy" src="'+this.settings.copyIcon+'" style="max-width:60px;max-height:60px;margin: 10px;" onclick="ig.svas.copyToClipboard()"></div>\
                            <div id="successful-registration-submit-button" type="submit" class="svas-submit-button">' + this.settings.strings.successfulRegistrationButton + '</div>\
                        </div>\
                    </div>\
                </div>\
            ';
            this.createPopupDiv(divId, "successfulRegistration", divElementContent);

            // elements
            var closeButtonElement = ig.domHandler.getElementById("#" + "box-successful-register-close"),
                successfulRegistrationSubmitButtonElement = ig.domHandler.getElementById("#" + "successful-registration-submit-button");

            // event - close button
            if (closeButtonElement !== null && typeof(closeButtonElement) !== "undefined") {
                ig.domHandler.addEvent(closeButtonElement, "click", function(event) {
                    ig.game.playButton();
                    this.hideSuccessfulRegistration();
                }.bind(this), false);
            }

            // event - successful registration button
            if (successfulRegistrationSubmitButtonElement !== null && typeof(successfulRegistrationSubmitButtonElement) !== "undefined") {
                ig.domHandler.addEvent(successfulRegistrationSubmitButtonElement, "click", function(event) {
                    ig.game.playButton();
                    this.hideSuccessfulRegistration();
                }.bind(this), false);
            }
        },
        copyToClipboard:function(){
            navigator.clipboard.writeText(this.userInfo.pincode);
            document.getElementById("successful-registration-notification").innerHTML="Pincode copied into clipboard";
        },
        createUserLoginDiv: function() {
            var divId = "svas-login-container";
            var divElementContent = '\
                <div id="box-login" class="svas-box svas-box-center">\
                    <a>\
                        <div id="box-login-close" class="svas-close-button"></div>\
                    </a>\
                    <div class="svas-box-header">' + this.settings.strings.loginHeader + '</div>\
                    <div class="svas-box-contents" id="login-form" name="login-form">\
                        <div class="svas-box-nickname">\
                            <input autofocus type="text" id="login-nickname" name="login-nickname" class="svas-box-form-input" placeholder="' + this.settings.strings.loginNicknamePlaceholder + '" autocomplete="off" autocomplete="off" minlength="' + this.settings.userNickname.minLength + '" maxlength="' + this.settings.userNickname.maxLength + '">\
                        </div>\
                        <div class="svas-box-pincode">\
                            <input autofocus type="password" id="login-pincode" name="login-pincode" class="svas-box-form-input" placeholder="' + this.settings.strings.loginPincodePlaceholder + '" autocomplete="off" autocomplete="off" minlength="' + this.settings.userPincode.fixedLength + '" maxlength="' + this.settings.userPincode.fixedLength + '">\
                            <input autofocus type="checkbox" id="login-pincode-toggle"><label for="login-pincode-toggle">' + this.settings.strings.loginPincodeToggleLabel + '</label>\
                        </div>\
                        <div>\
                            <div id="login-notification" class="svas-notification"></div>\
                            <div id="login-submit-button" type="submit" class="svas-submit-button">' + this.settings.strings.loginButton + '</div>\
                        </div>\
                        <div id="box-login-footer" class="svas-box-footer">\
                            ' + this.settings.strings.loginFooterMessage + '\
                            <a class="svas-a-link" id="login-footer-link">' + this.settings.strings.loginFooterLink + '</a>\
                        </div>\
                        <span class="svas-recaptcha-branding">' + this.settings.strings.reCaptchaBranding + '</span>\
                    </div>\
                </div>\
            ';
            this.createPopupDiv(divId, "userLogin", divElementContent);

            // elements
            var closeButtonElement = ig.domHandler.getElementById("#" + "box-login-close"),
                nicknameInputElement = ig.domHandler.getElementById("#" + "login-nickname"),
                pincodeInputElement = ig.domHandler.getElementById("#" + "login-pincode"),
                pincodeToggleElement = ig.domHandler.getElementById("#" + "login-pincode-toggle"),
                loginNotificationElement = ig.domHandler.getElementById("#" + "login-notification"),
                loginSubmitButtonElement = ig.domHandler.getElementById("#" + "login-submit-button"),
                loginFooterLinkElement = ig.domHandler.getElementById("#" + "login-footer-link");
            
            // event - close button
            if (closeButtonElement !== null && typeof(closeButtonElement) !== "undefined") {
                ig.domHandler.addEvent(closeButtonElement, "click", function(){
                    if(ig.game.disableBtStatus) return;
                    ig.game.playButton();
                    this.hideUserLogin();
                }.bind(this), false);
            }

            // event - nickname input
            if (nicknameInputElement !== null && typeof(nicknameInputElement) !== "undefined") {
                if (this.userInfo.nickname !== null && typeof (this.userInfo.nickname) !== "undefined") {
                    if(this.userInfo.nickname=='*****'){
                        var nickname='';
                    }else{
                        var nickname=this.userInfo.nickname;
                    }
                    ig.domHandler.val(nicknameInputElement, nickname);
                }
                ig.domHandler.addEvent(nicknameInputElement, "input", function(event) {
                    this.validateUserNickname(ig.domHandler.getVal(nicknameInputElement), loginNotificationElement);
                }.bind(this), false);
                ig.domHandler.addEvent(nicknameInputElement, "keyup", function(event) {
                    this.validateUserNickname(ig.domHandler.getVal(nicknameInputElement), loginNotificationElement);
                    if (event.key === "Enter") {
                        pincodeInputElement.focus();
                    }
                }.bind(this), false);
            }

            // event - pincode input
            if (pincodeInputElement !== null && typeof(pincodeInputElement) !== "undefined") {
                if (this.userInfo.pincode !== null && typeof (this.userInfo.pincode) !== "undefined") {
                    ig.domHandler.val(pincodeInputElement, this.userInfo.pincode);
                }
                ig.domHandler.addEvent(pincodeInputElement, "input", function(event) {
                    this.validateUserPincode(ig.domHandler.getVal(pincodeInputElement));
                }.bind(this), false);
                ig.domHandler.addEvent(pincodeInputElement, "keyup", function(event) {
                    Number.prototype.map = undefined;                    
                    var nickname = ig.domHandler.getVal(nicknameInputElement), 
                        pincode = ig.domHandler.getVal(pincodeInputElement);
                    this.validateUserPincode(pincode, loginNotificationElement);
                    if (event.key === "Enter") {
                        if (grecaptcha !== null && typeof(grecaptcha) !== "undefined" && this.reCaptcha.loaded === true) {
                            try {
                                grecaptcha.ready(function() {
                                    grecaptcha.execute(this.reCaptcha.siteKey, {action: 'submit'}).then(function(response) {
                                        this.login(nickname.trim(), pincode.trim(), response, loginNotificationElement);
                                    }.bind(this));
                                }.bind(this));
                            } catch(error) {}
                        }
                    }
                }.bind(this), false);
            }

            // event - pincode toggle
            if (pincodeToggleElement !== null && typeof(pincodeToggleElement) !== "undefined") {
                ig.domHandler.addEvent(pincodeToggleElement, "click", function(event) {
                    var togglerankType = ig.domHandler.getAttr(pincodeInputElement, "type") === "password" ? "text" : "password";
                    ig.domHandler.attr(pincodeInputElement, "type", togglerankType);
                }, false);  
            }

            // event - submit button
            if (loginSubmitButtonElement !== null && typeof(loginSubmitButtonElement) !== "undefined") {
                ig.domHandler.addEvent(loginSubmitButtonElement, "click", function(event) {
                    ig.game.playButton();
                    Number.prototype.map = undefined;                    
                    
                    var nickname = ig.domHandler.getVal(nicknameInputElement), 
                        pincode = ig.domHandler.getVal(pincodeInputElement);

                    if (grecaptcha !== null && typeof(grecaptcha) !== "undefined" && this.reCaptcha.loaded === true) {
                        try {
                            grecaptcha.ready(function() {
                                try{
                                    grecaptcha.execute(this.reCaptcha.siteKey, {action: 'submit'}).then(function(response) {
                                        this.login(nickname, pincode, response, loginNotificationElement);
                                    }.bind(this));
                                }catch(e){
                                    console.log(e);
                                }   
                            }.bind(this));
                        } catch(error) {
                            console.log(error)
                        }
                    }
                }.bind(this), false);
            }

            // event - login footer link
            if (loginFooterLinkElement !== null && typeof(loginFooterLinkElement) !== "undefined") {
                ig.domHandler.addEvent(loginFooterLinkElement, "click", function(event) {
                    this.hideUserLogin();
                    this.showUserRegistration();
                }.bind(this), false);
            }
        },

        createSuccessfulLoginDiv: function() {
            var divId = "svas-successful-login-container";
            var divElementContent = '\
                <div id="box-successful-login" class="svas-box svas-box-center">\
                    <a>\
                        <div id="box-successful-login-close" class="svas-close-button"></div>\
                    </a>\
                    <div class="svas-box-header">' + this.settings.strings.successfulLoginHeader + '</div>\
                    <div class="svas-box-contents" style="text-align: center;" id="successful-login-form" name="successful-login-form">\
                        <p style="font-size: 1.5em;">\
                            ' + this.settings.strings.successfulLoginMessage + '\
                            <p id="svas-successful-login-pincode-reminder" class="svas-box-contents-highlight" style="font-size: 2.8em;">\
                                ' + this.userInfo.pincode + '\
                            </p>\
                        </p>\
                        <div id="successful-login-notification" class="svas-notification-tip">' + this.settings.strings.successfulLoginTip + '</div>\
                        <div>\
                            <div id="successful-login-submit-button" type="submit" class="svas-submit-button">' + this.settings.strings.successfulLoginButton + '</div>\
                        </div>\
                    </div>\
                </div>\
            ';
            this.createPopupDiv(divId, "successfulLogin", divElementContent);

            // elements
            var closeButtonElement = ig.domHandler.getElementById("#" + "box-successful-login-close"),
                successfulLoginSubmitButtonElement = ig.domHandler.getElementById("#" + "successful-login-submit-button");

            // event - close button
            if (closeButtonElement !== null && typeof(closeButtonElement) !== "undefined") {
                ig.domHandler.addEvent(closeButtonElement, "click", function(event) {
                    ig.game.playButton();
                    this.hideSuccessfulLogin();
                }.bind(this), false);
            }

            // event - successful login button
            if (successfulLoginSubmitButtonElement !== null && typeof(successfulLoginSubmitButtonElement) !== "undefined") {
                ig.domHandler.addEvent(successfulLoginSubmitButtonElement, "click", function(event) {
                    ig.game.playButton();
                    this.hideSuccessfulLogin();
                }.bind(this), false);
            }
        },

        createLeaderboardFooterDiv: function() {
            var divId = "svas-leaderboard-footer";
            var existingDivElement = ig.domHandler.getElementById("#" + divId);
            if (existingDivElement !== null && typeof(existingDivElement) !== "undefined") {
                return existingDivElement; 
            }
            if ( this.divElements.leaderboardFooter !== null && typeof (this.divElements.leaderboardFooter) !== "undefined" ) {
                return this.divElements.leaderboardFooter;
            }

            var newElement = ig.domHandler.create("div");
            ig.domHandler.attr(newElement, "id", divId);
            ig.domHandler.attr(newElement, "class", "svas-box-footer svas-box-footer-leaderboard");
            ig.domHandler.html(newElement, this.settings.strings.leaderboardFooterMessage);
            this.divElements.leaderboardFooter = newElement;
            return newElement;
        },

        createLeaderboardDiv: function(rankType) {
            if (rankType === null || typeof (rankType) === "undefined") {
                rankType = "score";
            }
            if (rankType === "virtual_currency") {
                rankType = "virtual_currency1";
            }

            var containerDivId = "svas-" + rankType + "-leaderboard-container", 
                boxDivId = "box" + rankType + "-leaderboard",
                closeDivId = "svas-" + rankType + "-leaderboard-close",
                contentDivId = "svas-" + rankType + "-leaderboard-contents";
            var divElementContent = '\
                <div id="' + boxDivId + '" class="svas-box svas-box-leaderboard svas-box-center">\
                    <div class="svas-horizontal-separator-small"></div>\
                    <a>\
                        <div id="' + closeDivId + '" class="svas-close-button"></div>\
                    </a>\
                    <div class="svas-box-header">' + this.settings.strings.leaderboardHeader[rankType] + '</div>\
                    <div class="svas-box-subheader">\
                        <div class="group">\
                            <div class="svas-column svas-span-2-of-10">\
                                <div class="svas-box-col-header">' + this.settings.strings.leaderboardSubheaderRank[rankType] + '</div>\
                            </div>\
                            <div class="svas-column svas-span-5-of-10">\
                                <div class="svas-box-col-header">' + this.settings.strings.leaderboardSubheaderName[rankType] + '</div>\
                            </div>\
                            <div class="svas-column svas-span-3-of-10">\
                                <div class="svas-box-col-header" style="text-align: right;">' + this.settings.strings.leaderboardSubheaderValue[rankType] + '</div>\
                            </div>\
                        </div>\
                    </div>\
                    <div class="svas-box-contents svas-box-contents-leaderboard" id="' + contentDivId + '" name="svas-leaderboard-contents">' + this.settings.strings.leaderboardContentLoading + '</div>\
                </div>\
            ';
            this.createPopupDiv(containerDivId, (rankType + "Leaderboard"), divElementContent);
            // console.log('createLeaderboardDiv');

            // elements
            var closeButtonElement = ig.domHandler.getElementById("#" + closeDivId);

            // event - close button
            if (closeButtonElement !== null && typeof(closeButtonElement) !== "undefined") {
                ig.domHandler.addEvent(closeButtonElement, "click", function(event){
                    ig.game.playButton();
                    this.hideLeaderboard(rankType);
                }.bind(this), false);
            }

            this.updateLeaderboard(rankType);
        },

        createLoaderDiv: function() {
            var parentContainerElement = ig.domHandler.getElementById("#" + this.settings.popupContainterId);
            
            var divId = "svas-loader-container";
            var divElementContent = '\
                <div class="svas-loader-spinner">\
                    <div></div>\
                    <div></div>\
                    <div></div>\
                    <div></div>\
                    <div></div>\
                    <div></div>\
                    <div></div>\
                    <div></div>\
                    <div></div>\
                    <div></div>\
                    <div></div>\
                    <div></div>\
                </div>\
            ';

            this.createPopupDiv(divId, "loader", divElementContent);
            ig.domHandler.attr(this.divElements.loader, "class", "svas-loader-container");
            ig.domHandler.appendChild(parentContainerElement, this.divElements.loader);
        },

        showLoader: function() {
            if (this.divElements.loader === null || typeof(this.divElements.loader) === "undefined" ) {
                this.createLoaderDiv();
            }
            ig.domHandler.show(this.divElements.loader);
        },

        hideLoader: function() {
            if (this.divElements.loader !== null && typeof(this.divElements.loader) !== "undefined" ) {
                ig.domHandler.hide(this.divElements.loader);
            }
        },

        showUserRegistration: function() {

            if (this.settings.alwaysShowRegistration === false) {
                if (this.userInfo.id !== null && this.userInfo.id !== 0 && typeof (this.userInfo.nickname) !== "undefined") {
                    return this.hideUserRegistration();
                }
            }

            if (this.divElements.userRegistration === null || typeof(this.divElements.userRegistration) === "undefined" ) {
                this.createUserRegistrationDiv();
            }
            ig.game.playButton();
            this.clearNotifications();
            ig.domHandler.show(this.divElements.userRegistration);
            setTimeout(function(){
                document.getElementById('register-nickname').value='';
            }.bind(this),1000);
        },

        hideUserRegistration: function() {
            if (this.divElements.userRegistration !== null && typeof(this.divElements.userRegistration) !== "undefined" ) {
                ig.domHandler.hide(this.divElements.userRegistration);
            }
        },

        showSuccessfulRegistration: function() {
            if (this.divElements.successfulRegistration === null || typeof(this.divElements.successfulRegistration) === "undefined" ) {
                this.createSuccessfulRegistrationDiv();
            }
            
            if (this.userInfo.pincode !== null && typeof (this.userInfo.pincode) !== "undefined") {
                var pincodeReminderElement = ig.domHandler.getElementById("#" + "svas-successful-register-pincode-reminder");
                if (pincodeReminderElement !== null && typeof(pincodeReminderElement) !== "undefined") {
                    ig.domHandler.html(pincodeReminderElement, this.userInfo.pincode);
                }
            }

            ig.domHandler.show(this.divElements.successfulRegistration);
        },

        hideSuccessfulRegistration: function() {
            if (this.divElements.successfulRegistration !== null && typeof(this.divElements.successfulRegistration) !== "undefined" ) {
                ig.domHandler.hide(this.divElements.successfulRegistration);
            }
        },

        showUserLogin: function() {
            if (this.divElements.userLogin === null || typeof(this.divElements.userLogin) === "undefined" ) {
                this.createUserLoginDiv();
            }
            ig.game.playButton();
            this.clearNotifications();
            ig.domHandler.show(this.divElements.userLogin);
            setTimeout(function(){
                document.getElementById('login-nickname').value='';
            }.bind(this),500);
        },

        hideUserLogin: function() {
            if (this.divElements.userLogin !== null && typeof(this.divElements.userLogin) !== "undefined" ) {
                ig.domHandler.hide(this.divElements.userLogin);
            }
        },

        showSuccessfulLogin: function() {
            if (this.divElements.successfulLogin === null || typeof(this.divElements.successfulLogin) === "undefined" ) {
                this.createSuccessfulLoginDiv();
            }

            if (this.userInfo.pincode !== null && typeof (this.userInfo.pincode) !== "undefined") {
                var pincodeReminderElement = ig.domHandler.getElementById("#" + "svas-login-register-pincode-reminder");
                if (pincodeReminderElement !== null && typeof(pincodeReminderElement) !== "undefined") {
                    ig.domHandler.html(pincodeReminderElement, this.userInfo.pincode);
                }
            }

            ig.domHandler.show(this.divElements.successfulLogin);
        },

        hideSuccessfulLogin: function() {
            if (this.divElements.successfulLogin !== null && typeof(this.divElements.successfulLogin) !== "undefined" ) {
                ig.domHandler.hide(this.divElements.successfulLogin);
            }
        },

        showLeaderboard: function(rankType) {
            this.loadUserInfo();
            if (rankType === null || typeof (rankType) === "undefined") {
                rankType = "virtual_currency";
            }
            if (rankType === "virtual_currency") {
                rankType = "virtual_currency1";
            }

            if (this.divElements[(rankType + "Leaderboard")] === null || typeof(this.divElements[(rankType + "Leaderboard")]) === "undefined" ) {
                this.createLeaderboardDiv(rankType);                 
                ig.domHandler.show(this.divElements[(rankType + "Leaderboard")]);
            }
            else {
                ig.domHandler.show(this.divElements[(rankType + "Leaderboard")]);
                this.updateLeaderboard(rankType);
            }

            this.showLeaderboardFooter(rankType);
        },

        hideLeaderboard: function(rankType) {
            if (rankType === null || typeof (rankType) === "undefined") {
                rankType = "score";
            }
            if (rankType === "virtual_currency") {
                rankType = "virtual_currency1";
            }

            if (this.divElements[(rankType + "Leaderboard")] !== null && typeof(this.divElements[(rankType + "Leaderboard")]) !== "undefined" ) {
                ig.domHandler.hide(this.divElements[(rankType + "Leaderboard")]);
            }
        },

        hideAllLeaderboards: function() {
            for (var rankType in this.rankTypes) {
                this.hideLeaderboard(rankType);
            }
        },

        showLeaderboardFooter: function(rankType) {
            if (rankType === null || typeof (rankType) === "undefined") {
                rankType = "score";
            }
            if (rankType === "virtual_currency") {
                rankType = "virtual_currency1";
            }

            if (this.divElements.leaderboardFooter === null || typeof(this.divElements.leaderboardFooter) === "undefined" ) {
                this.divElements.leaderboardFooter = this.createLeaderboardFooterDiv();
            }
            var leaderboardBoxDivId = "box" + rankType + "-leaderboard";
            ig.domHandler.appendChild(ig.domHandler.getElementById("#" + leaderboardBoxDivId), this.divElements.leaderboardFooter);
            ig.domHandler.show(this.divElements.leaderboardFooter);

            this.updateLeaderboardFooter();
        },

        hideLeaderboardFooter: function() {
            if (this.divElements.leaderboardFooter !== null && typeof(this.divElements.leaderboardFooter) !== "undefined" ) {
                ig.domHandler.hide(this.divElements.leaderboardFooter);
            }
        },

        clearNotifications: function(notificationElement) {
            if (notificationElement !== null && typeof (notificationElement) !== "undefined") {
                ig.domHandler.html(notificationElement, "");
                return true;
            }
            else {
                var loginNotificationElement = ig.domHandler.getElementById("#" + "login-notification"), 
                    registerNotificationElement = ig.domHandler.getElementById("#" + "register-notification");
            
                if (loginNotificationElement !== null && typeof (loginNotificationElement) !== "undefined") {
                    ig.domHandler.html(loginNotificationElement, "");
                }
                if (registerNotificationElement !== null && typeof (registerNotificationElement) !== "undefined") {
                    ig.domHandler.html(registerNotificationElement, "");
                }
                return true;
            }
        },

        updateGameId: function(gameId) {
            if ( gameId === null || typeof (gameId) === "undefined" ) {
                return false;
            }
            this.settings.gameId = gameId.toString();
        },

        updateUserInfo: function(id, nickname, pincode) {
            if ( this.updateUserId(id) && this.updateUserNickname(nickname) && this.updateUserPincode(pincode)) {
                return this.saveUserInfo();
            }
        },

        updateUserId: function(id) {
            id = id.toString();
            if ( id === null || typeof (id) === "undefined" || id === "") {
                this.log("Invalid id");
                return false;
            }

            this.userInfo.id = id;
            return true;
        },

        updateUserNickname: function(nickname) {
            nickname = nickname.toString();
            if (this.validateUserNickname(nickname) === true) {
                this.userInfo.nickname = nickname;
                this.saveUserInfo();
                this.hideUserRegistration();
                return true;
            }
            else {
                this.log("Invalid nickname");
                return false;
            }
        },

        updateUserPincode: function(pincode) {
            pincode = pincode.toString();
            if (this.validateUserPincode(pincode) === true) {
                this.userInfo.pincode = pincode;
                this.saveUserInfo();
                this.hideUserRegistration();
                return true;
            }
            else {
                this.log("Invalid pincode");
                return false;
            }
        },

        updateScoreTime: function(score, time) {
            score = parseInt(score) || 0;
            time = parseInt(time) || 0;

            if ( this.scoreData.currentGameScore !== score || this.scoreData.currentGameTime !== time ) {
                this.scoreData.pendingScoreSubmission = true;
            }

            this.scoreData.currentGameScore = score;
            this.scoreData.currentGameTime = time;
        },

        updateLeaderboard: function(rankType) {
            if (rankType === null || typeof (rankType) === "undefined") {
                rankType = "score";
            }
            if (rankType === "virtual_currency") {
                rankType = "virtual_currency1";
            }

            var leaderboardContentElement = ig.domHandler.getElementById("#" + "svas-" + rankType + "-leaderboard-contents");
            
            this.updateLeaderboardFooter(rankType);

            if (leaderboardContentElement) {
                ig.domHandler.html(leaderboardContentElement, this.settings.strings.leaderboardContentLoading);
            }
            this.getLeaderboardDataFromServer(rankType, function(response) {
                this.log(" leaderboard data received");
                this.leaderboardData[rankType] = response.message;

                var leaderboardHtml = "";
                leaderboardHtml += '<div id="svas-leaderboard-entry" class="svas-box-entry">';
                if (this.leaderboardData[rankType] !== null && typeof(this.leaderboardData[rankType]) !== "undefined" && this.leaderboardData[rankType].length > 0) {
                    var maximumEntry = Math.min(this.settings.leaderboardMaxEntries, this.leaderboardData[rankType].length);
                    for (var i = 0; i < maximumEntry; i++) {
                        var leaderboardEntry = this.leaderboardData[rankType][i];
                        var leaderboardValue = null;
                        switch(rankType) {
                            case "virtual_currency1":
                            case "virtual_currency2":
                            case "virtual_currency3":
                            case "virtual_currency4":
                            case "virtual_currency5":
                                leaderboardValue = parseInt(leaderboardEntry[rankType]).toLocaleString();
                                break;

                            case "stats_wins":
                                leaderboardValue = parseFloat(leaderboardEntry.stats_wins).toLocaleString();
                                break;

                            case "stats_losses":
                                leaderboardValue = parseFloat(leaderboardEntry.stats_losses).toLocaleString();
                                break;

                            case "stats_win_rate":
                                leaderboardValue = parseFloat(leaderboardEntry.stats_win_rate).toFixed(2) + '%';
                                break;

                            default:
                            case "score":
                                leaderboardValue = parseInt(leaderboardEntry.score).toLocaleString();
                                break;
                        }

                        var xx=window.innerWidth;
                        var yy=window.innerHeight;
                        if(ig.ua.mobile){
                            if(xx<yy){
                                var name=leaderboardEntry.nickname.toString();
                                if(name.length>7){
                                    var name=leaderboardEntry.nickname.toString().substr(0,7)+'...';
                                }
                            }else{
                                var name=leaderboardEntry.nickname.toString();
                            }
                        }else{
                                var name=leaderboardEntry.nickname.toString();
                        }

                        leaderboardHtml += '\
                            <div class="group' + (leaderboardEntry.uid.toString() === this.userInfo.id?' svas-box-contents-highlight':'') + '" style="font-size:20px;">\
                                <div class="svas-column svas-span-2-of-10">\
                                    <div class="svas-box-score">' + parseInt( 1 + i )  + '</div>\
                                </div>\
                                <div class="svas-column svas-span-5-of-10">\
                                    <div class="svas-box-username">' + name + '</div>\
                                </div>\
                                <div class="svas-column svas-span-3-of-10">\
                                    <div class="svas-box-score">' + leaderboardValue + '</div>\
                                </div>\
                            </div>\
                        ';
                    }
                }
                leaderboardHtml += '</div>';

                var leaderboardContentElement = ig.domHandler.getElementById("#" + "svas-" + rankType + "-leaderboard-contents");
                if (leaderboardContentElement !== null && typeof (leaderboardContentElement) !== "undefined") {
                    ig.domHandler.html(leaderboardContentElement, leaderboardHtml);
                }
            }.bind(this), function(response) {
                this.log("Error getting leaderboard data");

                if (leaderboardContentElement) {
                    ig.domHandler.html(leaderboardContentElement, this.settings.strings.leaderboardErrorMessage);
                }
            }.bind(this));
        },

        updateLeaderboardFooter: function() {
            var leaderboardFooterElement = ig.domHandler.getElementById("#" + "svas-leaderboard-footer");

            if (leaderboardFooterElement === null || typeof (leaderboardFooterElement) === "undefined") {
                leaderboardFooterElement = this.createLeaderboardFooterDiv();
            }
            
            if (leaderboardFooterElement !== null && typeof (leaderboardFooterElement) !== "undefined") {
                var hasUserInfo = (this.userInfo !== null && typeof(this.userInfo) !== "undefined") && 
                                (this.userInfo.id !== null && typeof(this.userInfo.id) !== "undefined") &&
                                (this.userInfo.nickname !== null && typeof(this.userInfo.nickname) !== "undefined") &&
                                (this.userInfo.pincode !== null && typeof(this.userInfo.pincode) !== "undefined"),
                    leaderboardFooterHtml = "",
                    leaderboardFooterLinkId = "svas-leaderboard-footer-link";

                if (hasUserInfo !== true || this.isUserAuthenticated !== true) {
                    leaderboardFooterHtml = this.settings.strings.leaderboardFooterMessage + '\
                        <a class="svas-a-link" id="' + leaderboardFooterLinkId + '">' + this.settings.strings.leaderboardFooterLinkRegister + '</a>\
                    ';
                }
                else {
                    leaderboardFooterHtml = '\
                        <span style="float: left; padding-bottom: 0.25em;">' + this.userInfo.nickname + ' (PIN: ' + this.userInfo.pincode + ')</span>\
                        <a style="float: right;" class="svas-a-link" id="' + leaderboardFooterLinkId + '">' + this.settings.strings.leaderboardFooterLinkLogout + '</a>\
                    ';
                }
                ig.domHandler.html(leaderboardFooterElement, leaderboardFooterHtml);

                var leaderboardFooterLink = ig.domHandler.getElementById("#" + leaderboardFooterLinkId);
                if (leaderboardFooterLink !== null && typeof (leaderboardFooterLink) !== "undefined") {
                    if (hasUserInfo !== true || this.isUserAuthenticated !== true) {
                        ig.domHandler.addEvent(leaderboardFooterLink, "click", function(event) {
                            this.hideAllLeaderboards();
                            this.showUserRegistration();
                        }.bind(this), false);
                    }
                    else {
                        ig.domHandler.addEvent(leaderboardFooterLink, "click", function(event) {
                            this.logout();
                        }.bind(this), false);
                    }
                }
            }
        },

        trackGameRestarted: function() {
            this.log("Game restarted");

            this.resetScoreData();
            
            this.scoreData.internalGameTimer = new ig.Timer();
        },

        trackGameStarted: function(reset) {
            this.log("Game started");

            if ( reset === true ) {
                this.resetScoreData();
            }
            
            if ( this.scoreData.internalGameTimer === null || typeof (this.scoreData.internalGameTimer) === "undefined" ) {
                this.scoreData.internalGameTimer = new ig.Timer();
            }
            else {
                this.scoreData.internalGameTimer.unpause();
            }
        },

        trackGamePaused: function() {
            this.log("Game paused");

            if ( this.scoreData.internalGameTimer !== null && typeof (this.scoreData.internalGameTimer) !== "undefined" ) {
                this.scoreData.internalGameTimer.pause();
            }
        },

        trackGameResumed: function() {
            this.log("Game resumed");

            if ( this.scoreData.internalGameTimer !== null && typeof (this.scoreData.internalGameTimer) !== "undefined" ) {
                this.scoreData.internalGameTimer.unpause();
            }
        },

        trackGameInterrupted: function() {
            this.log("Game interrupted");

            this.resetScoreData();
        },

        trackGameEnded: function(score, gameTime) {
            this.log("Game ended");

            if ( this.scoreData.internalGameTimer !== null && typeof (this.scoreData.internalGameTimer) !== "undefined" ) {
                this.scoreData.internalGameTimer.pause();
            }

            if (score === null || typeof (score) === "undefined") {
                score = 0;
            }
            if (gameTime === null || typeof (gameTime) === "undefined") {
                gameTime = this.getInternalGameTime();
            }
            
            score = parseInt(score);
            gameTime = parseInt(gameTime);
            
            this.updateScoreTime(score, gameTime);
            this.postScore(score, gameTime);

            if (this.settings.autoShowLeaderboard === true) {
                this.showLeaderboard("score");
            }
        },

        getInternalGameTime: function() {
            var gameTime = 0;
            
            if ( this.scoreData.internalGameTimer !== null && typeof (this.scoreData.internalGameTimer) !== "undefined" ) {
                gameTime = this.scoreData.internalGameTimer.delta();
                gameTime = parseInt(gameTime);
            }

            return gameTime;
        },

        resetScoreData: function() {
            this.scoreData.internalGameTimer = null;
            this.scoreData.currentGameScore = 0;
            this.scoreData.currentGameTime = 0;
            this.scoreData.pendingScoreSubmission = false;
        },

        postScore: function(score, gameTime) {
            if (this.userInfo.nickname === null || typeof (this.userInfo.nickname) === "undefined"|| this.settings.gameId === null || typeof (this.settings.gameId) === "undefined") {
                return false;
            }
            if (this.scoreData.pendingScoreSubmission === false) {
                return false;
            }

            if (score === null || typeof (score) === "undefined") {
                score = this.scoreData.currentGameScore || 0;
            }
            if (gameTime === null || typeof (gameTime) === "undefined") {
                gameTime = this.getInternalGameTime() || 0;
            }

            this.postScoreToServer(score, gameTime);
        },

        postScoreToServer: function(score, gameTime, successfulCallback, errorCallback) {
            if (this.userInfo.nickname === null || typeof (this.userInfo.nickname) === "undefined"|| this.settings.gameId === null || typeof (this.settings.gameId) === "undefined") {
                return false;
            }
            if (score === null || typeof (score) === "undefined" || gameTime === null || typeof (gameTime) === "undefined") {
                return false;
            }

            if (CryptoJS === null || typeof (CryptoJS) === "undefined") {
                return false;
            }

            var e = function (msgString) {
                var iv = CryptoJS.lib.WordArray.random(16);
                return iv.concat(CryptoJS.AES.encrypt(CryptoJS.enc.Utf8.parse(msgString), CryptoJS.enc.Utf8.parse(window.atob(this.settings.sk)), {
                    iv: iv
                }).ciphertext).toString(CryptoJS.enc.Base64);
            }.bind(this);

            var formData = new FormData();
            formData.append('game_id', this.settings.gameId);
            formData.append('uid', this.userInfo.id);
            formData.append('score', e( score.toString() ) );
            formData.append('game_time', e( gameTime.toString() ) );

            this.callXHR(this.settings.postScoreAPI.method, this.settings.postScoreAPI.url, this.settings.postScoreAPI.timeout, formData, function(response) {
                this.log("Score posted successfully");
                this.scoreData.pendingScoreSubmission = false;
                this.scoreData.lastPostedScore = score;
                this.scoreData.lastPostedGameTime = gameTime;
                this.updateLeaderboard("score");

                if (successfulCallback !== null && typeof (successfulCallback) === "function") {
                    successfulCallback(response);
                }
            }.bind(this), function(response) {
                this.log("Score posting failed");

                if (errorCallback !== null && typeof (errorCallback) === "function") {
                    errorCallback(response);
                }
            }.bind(this));
        },

        getLeaderboardDataFromServer: function(rankType, successfulCallback, errorCallback) {
            if (rankType === null || typeof (rankType) === "undefined") {
                rankType = "score";
            }
            if (rankType === "virtual_currency") {
                rankType = "virtual_currency1";
            }

            if ( this.settings.gameId !== null && typeof (this.settings.gameId) !== "undefined" ) {
                var url, method, timeout;
                switch (rankType) {
                    case "virtual_currency1":
                    case "virtual_currency2":
                    case "virtual_currency3":
                    case "virtual_currency4":
                    case "virtual_currency5":
                        url = this.settings.getLeaderboardAPI.virtual_currency.url + ('?game_id=' + this.settings.gameId) + ('&type=' + rankType);
                        method = this.settings.getLeaderboardAPI.virtual_currency.method;
                        timeout = this.settings.getLeaderboardAPI.virtual_currency.timeout;
                        break;

                    case "stats_wins":
                        url = this.settings.getLeaderboardAPI.stats.url + ('?game_id=' + this.settings.gameId) + ('&type=' + 'stats_wins');
                        method = this.settings.getLeaderboardAPI.stats.method;
                        timeout = this.settings.getLeaderboardAPI.stats.timeout;
                        break;
                    
                    case "stats_losses":
                        url = this.settings.getLeaderboardAPI.stats.url + ('?game_id=' + this.settings.gameId) + ('&type=' + 'stats_losses');
                        method = this.settings.getLeaderboardAPI.stats.method;
                        timeout = this.settings.getLeaderboardAPI.stats.timeout;
                        break;
                    
                    case "stats_win_rate":
                        url = this.settings.getLeaderboardAPI.stats.url + ('?game_id=' + this.settings.gameId) + ('&type=' + 'stats_win_rate');
                        method = this.settings.getLeaderboardAPI.stats.method;
                        timeout = this.settings.getLeaderboardAPI.stats.timeout;
                        break;
                    
                    case "score":
                    default: 
                        url = this.settings.getLeaderboardAPI.score.url + ('?game_id=' + this.settings.gameId);
                        method = this.settings.getLeaderboardAPI.score.method;
                        timeout = this.settings.getLeaderboardAPI.score.timeout;
                        break;
                }

                this.callXHR(method, url, timeout, null, function(response) {
                    if (successfulCallback !== null && typeof (successfulCallback) === "function") {
                        successfulCallback(response);
                    }
                }.bind(this), function(response) {
                    if (errorCallback !== null && typeof (errorCallback) === "function") {
                        errorCallback(response);
                    }
                }.bind(this));
            }
        },

        validateUserId: function(id) {
            return (id !== null && typeof (id) !== "undefined" && id.length > 0);
        },

        validateUserNickname: function(nickname, notificationElement) {
            var isValidNickname = true;
            var notificationElement = notificationElement;
            var notificationMessage = "";

            var alphanumericRegex = new RegExp("^[a-zA-Z0-9]+$");
            if (!alphanumericRegex.test(nickname)) {
                isValidNickname = false;

                notificationMessage = this.settings.strings.nicknameNotificationInvalid;
            }

            if (nickname.length < this.settings.userNickname.minLength) {
                isValidNickname = false;
                notificationMessage = this.settings.strings.nicknameNotificationMinLength.replace("{minLength}", this.settings.userNickname.minLength);
            }

            if (nickname.length > this.settings.userNickname.maxLength) {
                isValidNickname = false;

                notificationMessage = this.settings.strings.nicknameNotificationMaxLength.replace("{maxLength}", this.settings.userNickname.maxLength);
            }

            if (nickname === null || typeof (nickname) !== "string" || nickname === "") {
                isValidNickname = false;
                notificationMessage = this.settings.strings.nicknameNotificationEmpty;
            }

            if ( notificationElement !== null && typeof (notificationElement) !== "undefined" ) {    
                if (isValidNickname) {
                    ig.domHandler.html(notificationElement, '<label for="register-nickname" generated="true" class="svas-notification-valid">' + notificationMessage + '</label>');
                }
                else {
                    ig.domHandler.html(notificationElement, '<label for="register-nickname" generated="true" class="svas-notification-error">' + notificationMessage + '</label>');
                }
            }

            return isValidNickname;
        },

        validateUserPincode: function(pincode, notificationElement) {
            var isValidPincode = true;
            var notificationElement = notificationElement;
            var notificationMessage = "";

            if (pincode.length !== this.settings.userPincode.fixedLength) {
                isValidPincode = false;

                notificationMessage = this.settings.strings.pincodeNotificationFixedLength.replace("{fixedLength}", this.settings.userPincode.fixedLength);
            }

            var numericRegex = new RegExp("^[0-9]+$");
            if (!numericRegex.test(pincode)) {
                isValidPincode = false;

                notificationMessage = this.settings.strings.pincodeNotificationInvalid;
            }

            if (pincode === null || typeof (pincode) === "undefined" || pincode === "") {
                isValidPincode = false;

                notificationMessage = this.settings.strings.pincodeNotificationEmpty;
            }

            if ( notificationElement !== null && typeof (notificationElement) !== "undefined" ) {    
                if (isValidPincode) {
                    ig.domHandler.html(notificationElement, '<label for="login-pincode" generated="true" class="svas-notification-valid">' + notificationMessage + '</label>');
                }
                else {
                    ig.domHandler.html(notificationElement, '<label for="login-pincode" generated="true" class="svas-notification-error">' + notificationMessage + '</label>');
                }
            }

            return isValidPincode;
        },

        registerNickname: function(nickname, reCaptchaResponse, notificationElement) {
            var notificationElement = notificationElement || ig.domHandler.getElementById("#" + "register-notification");
            var notificationMessage = "Valid nickname";

            if (nickname === null || typeof (nickname) === "undefined" || nickname === "") {
                notificationMessage = this.settings.strings.nicknameNotificationEmpty;

                if ( notificationElement !== null && typeof (notificationElement) !== "undefined" ) {
                    ig.domHandler.html(notificationElement, '<label for="register-nickname" generated="true" class="svas-notification-error">' + notificationMessage + '</label>');
                }
                return false;
            }

            var formData = new FormData();
                formData.append('nickname', nickname);
                formData.append('g-recaptcha-response', reCaptchaResponse);

            this.callXHR(this.settings.userRegistrationAPI.method, this.settings.userRegistrationAPI.url, this.settings.userRegistrationAPI.timeout, formData, function(response) {
                this.isUserAuthenticated = true;
                notificationMessage = this.settings.strings.successfulRegistrationHeader + " " + this.settings.strings.successfulRegistrationMessage + " " + response.pin_code + ". " + this.settings.strings.successfulRegistrationTip;

                if (response.uid !== null && typeof (response.uid) !== "undefined" && response.pin_code !== null && typeof (response.pin_code) !== "undefined") {
                    this.updateUserInfo(response.uid, nickname, response.pin_code);
                    // ig.game.newUserStatus = true;
                    // console.log(ig.game.newUserStatus);
                    ig.game.resetCoinsGems();
                    ig.game.client.registrationSuccess(response.uid);
                    ig.game.title.passStatus = true;
                }

                if ( notificationElement !== null && typeof (notificationElement) !== "undefined" ) {    
                    ig.domHandler.html(notificationElement, '<label for="register-nickname" generated="true" class="svas-notification-valid">' + notificationMessage + '</label>');
                }

                // submit score if user in case user is registered from leaderboard page
                if (this.scoreData.pendingScoreSubmission === true) {
                    this.postScore();
                    this.showLeaderboard("score");
                }

                if(!ig.game.registerWaitStatus){
                    this.showSuccessfulRegistration();
                }


                try {
                    if (this.settings.callbackFunctions.onRegister !== null && typeof (this.settings.callbackFunctions.onRegister) === "function") {
                        this.settings.callbackFunctions.onRegister(this.userInfo);
                    }
                } catch (error) {
                    this.log("Error in callback function onRegister: " + error);
                }
            }.bind(this), function(response) {
                notificationMessage = this.settings.strings.nicknameNotificationAnother;

                if ( notificationElement !== null && typeof (notificationElement) !== "undefined" ) {
                    ig.domHandler.html(notificationElement, '<label for="register-nickname" generated="true" class="svas-notification-error">' + notificationMessage + '</label>');
                }
            }.bind(this));
        },

        login: function(nickname, pincode, reCaptchaResponse, notificationElement) {

            var notificationElement = notificationElement || ig.domHandler.getElementById("#" + "login-notification");
            var notificationMessage = this.settings.strings.successfulLoginHeader;

            if (this.validateUserNickname(nickname, notificationElement) !== true) {
                return false;
            }

            if (this.validateUserPincode(pincode, notificationElement) !== true) {
                return false;
            }

            var formData = new FormData();
                formData.append('nickname', nickname);
                formData.append('pin_code', pincode);
                formData.append('g-recaptcha-response', reCaptchaResponse);

            this.callXHR(this.settings.userLoginAPI.method, this.settings.userLoginAPI.url, this.settings.userLoginAPI.timeout, formData, function(response) {
                this.isUserAuthenticated = true;
                notificationMessage = this.settings.strings.successfulLoginHeader;
                
                if (notificationElement !== null && typeof (notificationElement) !== "undefined") {
                    ig.domHandler.html(notificationElement, '<label for="register-nickname" generated="true" class="svas-notification-valid">' + notificationMessage + '</label>');
                }
                if (response.uid !== null && typeof (response.uid) !== "undefined" && response.uid !== "") {
                    this.updateUserInfo(response.uid, nickname, pincode);
                    ig.game.resetCoinsGems();
                    ig.game.client.requestSvasData(response.uid);
                    ig.game.title.passStatus = true;
                }

                this.hideUserLogin();
                this.hideUserRegistration();

                // submit score if user in case user is registered from leaderboard page
                if (this.scoreData.pendingScoreSubmission === true) {
                    this.postScore();
                    this.showLeaderboard("score");
                } else {
                    if (this.settings.showLoginConfirmation === true) {
                        this.showSuccessfulLogin();
                    }
                }

                try {
                    if (this.settings.callbackFunctions.onLogin !== null && typeof (this.settings.callbackFunctions.onLogin) === "function") {
                        this.settings.callbackFunctions.onLogin(this.userInfo);
                    }
                } catch (error) {
                    this.log("Error in callback function onLogin: " + error);
                }
            }.bind(this), function(response) {
                this.showUserLogin();
                notificationElement = notificationElement || ig.domHandler.getElementById("#" + "login-notification");
                notificationMessage = this.settings.strings.loginNotificationInvalid;
                if (notificationElement !== null && typeof (notificationElement) !== "undefined") {
                    ig.domHandler.html(notificationElement, '<label for="register-nickname" generated="true" class="svas-notification-error">' + notificationMessage + '</label>');
                }
                this.logout();
            }.bind(this));
        },

        logout: function() {
            ig.game.playButton();
            this.isUserAuthenticated = false;
            this.deleteUserInfo();
            this.updateLeaderboardFooter();
            // try {
            //     if (this.settings.callbackFunctions.onLogout !== null && typeof (this.settings.callbackFunctions.onLogout) === "function") {
            //         this.settings.callbackFunctions.onLogout(this.userInfo);
            //     }
            // } catch (error) {
            //     console.log("Error in callback function onLogout: " + error);
            // }
            ig.game.resetStatus = true;
            ig.alliance.deleteUserInfo();
            ig.game.client.resetSvasData();
            window.playerNameInput.changeText();
            ig.game.resetCoinsGems();
        },

        callXHR: function(method, url, timeout, data, successfulCallback, errorCallback) {
            this.showLoader();

            var xhr = new XMLHttpRequest();
            xhr.open(method, url, true);
            xhr.timeout = timeout || 5000;
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    var response = "";
                    try {
                        response = JSON.parse(xhr.responseText);
                        if (xhr.status === 200) {
                            if (successfulCallback !== null && typeof (successfulCallback) === "function") {
                                successfulCallback(response);
                            }
                        }
                        else {
                            if (errorCallback !== null && typeof (errorCallback) === "function") {
                                errorCallback(response);
                            }
                        }
                    } catch(error) {
                        if (errorCallback !== null && typeof (errorCallback) === "function") {
                            errorCallback(response);
                        }
                    }
                    this.hideLoader();
                }
            }.bind(this);

            if (data !== null && typeof (data) !== "undefined") {
                xhr.send(data);
            } else {
                xhr.send();
            }
        },

        log: function(message) {
            if (this.parameters.verbose) {
                switch (this.parameters.verbose) {
                    case "false":
                    case "off":
                    case "0":
                    case "":
                    case null:
                    case undefined:
                    break;

                    default:
                    case "true":
                    case "on":
                    case "1":
                        if (window.console && typeof(window.console.log) === "function") {
                            console.log("%c [" + this.NAME + " v" + this.VERSION + "] " + message + " ", "color: #F35E36; background: #0B31A1;");
                        }
                    break;

                    case "trace": 
                    case "2":
                        if (window.console && typeof(window.console.trace) === "function") {
                            console.trace("%c [" + this.NAME + " v" + this.VERSION + "] " + message + " ", "color: #F35E36; background: #0B31A1;");
                        }
                    break;
                }
            }
        }
    });
});