ig.module(
    'dl.game.entity.components.draw.draw'
).requires(
    'dl.game.entity.components.component'
).defines(function () {
    'use strict';

    dl.EntityDrawComponent = dl.EntityComponent.extend({
        staticInstantiate: function (entity) {
            this.parent(entity);

            // Position and size
            this._size = { x: 16, y: 16 };
            this._docker = { x: 0.5, y: 0.5 };
            this._anchor = { x: 0.5, y: 0.5 };
            this._offset = { x: 0, y: 0 };

            this.size = { x: 16, y: 16 };
            this.offset = { x: 0, y: 0 };
            this.pos = { x: 0, y: 0 };

            this._lastSize = { x: null, y: null };
            this._lastPos = { x: null, y: null };

            // Flags
            this.repositionFlag = false;

            // bind events
            this.entity.onEvent('positionChanged', function () {
                this.repositionFlag = true;
            }.bind(this));
            this.entity.onEvent('scaleChanged', function () {
                this.repositionFlag = true;
            }.bind(this));

            return this;
        },

        lateUpdate: function () {
            this.parent();

            if (this.repositionFlag) {
                this.reposition();
            }
        },

        triggerDrawChanged: function () {
            this.entity.triggerEvent('componentDrawChanged');
        },

        requestReposition: function () {
            this.repositionFlag = true;
        },

        reposition: function () {
            this.repositionFlag = false;

            this.updateSize();
            this.updatePos();
        },

        updateSize: function () {
            if (this._lastSize.x != this.size.x || this._lastSize.y != this.size.y) {
                this.triggerEvent('sizeChanged', this.size.x, this.size.y);

                this._lastSize.x = this.size.x;
                this._lastSize.y = this.size.y;

                this.triggerDrawChanged();
            }
        },

        updateOffset: function () {
            this.offset.x = this._offset.x * this.entity.scale.x;
            this.offset.y = this._offset.y * this.entity.scale.y;
        },

        updateDocker: function () {
            this.pos.x += this.entity.size.x * (this._docker.x - 0.5);
            this.pos.y += this.entity.size.y * (this._docker.y - 0.5);
        },

        updateAnchor: function () {
            this.pos.x += this.size.x * (0.5 - this._anchor.x);
            this.pos.y += this.size.y * (0.5 - this._anchor.y);
        },

        updatePos: function () {
            this.updateOffset();

            this.pos.x = 0 + this.offset.x;
            this.pos.y = 0 + this.offset.y;

            this.updateDocker();
            this.updateAnchor();

            if (this._lastPos.x != this.pos.x ||
                this._lastPos.y != this.pos.y) {
                this._lastPos.x = this.pos.x;
                this._lastPos.y = this.pos.y;

                this.triggerDrawChanged();
            }
        }
    });
});
