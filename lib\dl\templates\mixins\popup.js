ig.module(
    'dl.templates.mixins.popup'
).requires(
    'dl.game.entity.components.collision.rectangle-collision'
).defines(function () {
    'use strict';

    dl.MixinPopup = {
        staticInstantiate: function () {
            this.parent();

            // bind events
            this.setSize(dl.system.width, dl.system.height);
            dl.system.onEvent('sizeChanged', this.setSize.bind(this));

            return this;
        },

        postInit: function () {
            this.parent();

            dl.game.disableEntityUnder(this);
        },

        initComponents: function () {
            this.parent();

            this._initCollisionComponent();
        },

        _initCollisionComponent: function () {
            this.cCollisionComponent = this.addComponent(dl.RectangleCollision, {
                collisionTag: dl.CollisionComponent.GetCollisionTag('BLOCK_INPUT')
            });
        },

        draw: function (ctx) {
            // overlay background
            ctx.save();
            ctx.fillStyle = 'rgba(0,0,0,0.5)';
            ctx.fillRect(
                this.pos.x - this.size.x * 0.5,
                this.pos.y - this.size.y * 0.5,
                this.size.x,
                this.size.y);
            ctx.restore();

            this.parent(ctx);
        },

        kill: function () {
            dl.game.enableEntityUnder(this);
            this.parent();
            if (typeof this.onPopupClose === 'function') {
                this.onPopupClose();
            }
        }
    };

    dl.MixinPopupContent = {
        onEntitySpawn: function (entity) {
            entity._useParentScale = true;

            return entity;
        },

        tweenIn: function (callback) {
            var time = dl.configs.getConfig('POPUP_TWEEN_TIME', 'IN');

            // this._alpha = 0;
            // this.updateAlpha();

            // this._scale = { x: 0.25, y: 0.25 };
            // this.updateAlpha();

            // this.createTween()
            //     .to({
            //         _alpha: 1,
            //         _scale: { x: 1, y: 1 }
            //     }, time, {
            //         easing: dl.TweenEasing.Back.EaseOut,
            //         onPropertiesChanged: function () {
            //             this.updateAlpha();
            //             this.updateScale();
            //         }.bind(this)
            //     })
            //     .start({
            //         onCompleteTween: function () {
            //             if (dl.check.isFunction(callback)) callback();
            //         }.bind(this)
            //     });
            dl.TweenTemplate.moveInTop(this, time, callback);
        },

        tweenOut: function (callback) {
            var time = dl.configs.getConfig('POPUP_TWEEN_TIME', 'OUT');
            // this.createTween()
            //     .to({
            //         _alpha: 0,
            //         _scale: { x: 0.25, y: 0.25 }
            //     }, time, {
            //         easing: dl.TweenEasing.Back.EaseIn,
            //         onPropertiesChanged: function () {
            //             this.updateAlpha();
            //             this.updateScale();
            //         }.bind(this)
            //     })
            //     .start({
            //         onCompleteTween: function () {
            //             if (dl.check.isFunction(callback)) callback();
            //         }.bind(this)
            //     });
            dl.TweenTemplate.moveOutTop(this, time, callback);
        }
    };
});
