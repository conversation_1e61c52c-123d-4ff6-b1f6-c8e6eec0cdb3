/**
 * Created by <PERSON><PERSON> <PERSON>
 * Store room player information
 */

var RoomPlayer = function (socket, room, playerId, playerRequestInfo) {
	var self = {};

	self.init = function (socket, room, playerId, playerRequestInfo) {
		self.socket = socket;
		self.room = room;

		// info
		self.playerId = playerId;
		self.playerName = "playerName";
		self.playerAvatarId = 0;
		self.playerSvasId = 0;
		self.playerAlliance = '';

		// state
		self.playerReady = false;
		self.botFlag = false;

		self.importData(playerRequestInfo);
	};

	self.packData = function () {
		var package = {};

		// info
		package.playerId = self.playerId;
		package.playerName = self.playerName;
		package.playerAvatarId = self.playerAvatarId;
		package.playerSvasId = self.playerSvasId;
		package.playerAlliance = self.playerAlliance;

		//state
		package.playerReady = self.playerReady;
		package.botFlag = self.botFlag;

		return package;
	};

	self.importData = function (data) {
		if (!data) return;

		// info
		if (typeof data.playerId !== "undefined") self.playerId = data.playerId;
		if (typeof data.playerName !== "undefined") self.playerName = data.playerName;
		if (typeof data.playerAvatarId !== "undefined") self.playerAvatarId = data.playerAvatarId;
		if (typeof data.playerSvasId !== "undefined") self.playerSvasId = data.playerSvasId;
		if (typeof data.playerAlliance !== "undefined") self.playerAlliance = data.playerAlliance;

		// state
		if (typeof data.playerReady !== "undefined") self.playerReady = !!data.playerReady;
		if (typeof data.botFlag !== "undefined") self.botFlag = !!data.botFlag;
	};

	self.init(socket, room, playerId, playerRequestInfo);
	return self;
};


/**
 * Export module
 */
if (typeof module !== "undefined") {
	module.exports.RoomPlayer = RoomPlayer;
}