ig.module(
    'dl.game.camera'
).requires(
    'dl.utils.mixins.event-handler',
    'dl.utils.mixins.tweens',
    'dl.game.utilities.camera-follow'
).defines(function () {
    'use strict';

    dl.Camera = ig.Class.extend({
        staticInstantiate: function () {
            /**
             * Size stuffs
             */
            this._cameraZoom = { x: 1, y: 1 };
            this._size = { x: 0, y: 0 };
            this.size = { x: 0, y: 0 };

            /**
             * Position stuffs
             */
            this._nextPos = null;
            this._pos = { x: 0, y: 0 };
            this.pos = { x: 0, y: 0 };

            this.init();

            return this;
        },

        init: function () {
            // bind events
            dl.system.onEvent('sizeChanged', this._setSize.bind(this), "CAMERA");

            this._nextPos = null;
            this._cameraZoom = { x: 1, y: 1 };

            this._setSize(dl.system.width, dl.system.height);
            this._setPos(0, 0);
        },

        reset: function () {
            this._nextPos = null;
            this._cameraZoom = { x: 1, y: 1 };

            this._setSize(dl.system.width, dl.system.height);
            this._setPos(0, 0);
        },

        update: function () {
            if (dl.check.isDefined(this._nextPos)) {
                this._setPos(this._nextPos.x, this._nextPos.y);
                this._nextPos = null;
            }
        },

        _setSize: function (width, height) {
            this._size.x = width;
            this._size.y = height;

            this.updateSize();
            this._setPos(this.pos.x, this.pos.y);
        },

        updateSize: function () {
            this.size.x = this._size.x;
            this.size.y = this._size.y;

            this.triggerEvent('sizeChanged');
        },

        /**
         * Game size to screen
         */
        getScreenSizeFromGameSize: function (x, y) {
            return {
                x: x * this._cameraZoom.x,
                y: y * this._cameraZoom.y
            };
        },

        /**
         * Screen size to game size
         */
        getGameSizeFromScreenSize: function (x, y) {
            return {
                x: x / this._cameraZoom.x,
                y: y / this._cameraZoom.y
            };
        },

        /**
         * Set new position of camera
         */
        setPos: function (x, y) {
            this._nextPos = {
                x: x,
                y: y
            };
        },

        _setPos: function (x, y) {
            this._pos.x = x;
            this._pos.y = y;
            this.updatePos();
        },

        updatePos: function () {
            this.pos.x = this._pos.x;
            this.pos.y = this._pos.y;

            this.triggerEvent('positionChanged');
        },

        /**
         * Get Screen position
         */
        getScreenPositionFromGamePosition: function (x, y) {
            return {
                x: (x - ((this.pos.x - this.size.x * 0.5) - (1 - this._cameraZoom.x) * this.pos.x) / this._cameraZoom.x) * this._cameraZoom.x,
                y: (y - ((this.pos.y - this.size.y * 0.5) - (1 - this._cameraZoom.y) * this.pos.y) / this._cameraZoom.y) * this._cameraZoom.y
            };
        },

        /**
         * Get Game position
         */
        getGamePositionFromScreenPosition: function (x, y) {
            return {
                x: x / this._cameraZoom.x + ((this.pos.x - this.size.x * 0.5) - (1 - this._cameraZoom.x) * this.pos.x) / this._cameraZoom.x,
                y: y / this._cameraZoom.y + ((this.pos.y - this.size.y * 0.5) - (1 - this._cameraZoom.y) * this.pos.y) / this._cameraZoom.y
            };
        },

        /**
         * Translate context to camera
         * The game center Axis is in middle
         */
        translateContext: function (ctx) {
            ctx.translate(
                -(this.pos.x - this.size.x * 0.5),
                -(this.pos.y - this.size.y * 0.5)
            );

            // ctx.translate(this.pos.x, this.pos.y);
            // ctx.scale(this._cameraZoom.x, this._cameraZoom.y);
            // ctx.translate(-this.pos.x, -this.pos.y);

            return ctx;
        }
    });

    // Add mixins
    dl.Camera.inject(dl.EventHandlerMixin);
    dl.Camera.inject(dl.CameraFollow);
    dl.Camera.inject(dl.TweensMixin);
});
