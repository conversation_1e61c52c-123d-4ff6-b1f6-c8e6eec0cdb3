ig.module(
    'plugins.io.simple-keyboard'
)
.requires()
.defines(function () {
    ig.SimpleKeyboard = ig.Class.extend({
        maxlength: 0,
        font: null,
        listeningTo: null,

        init: function (settings) {
            this.maxlength = settings.maxlength;
            this.font = settings.font;
            this.listeningTo = settings.listeningTo;
            this.spawnKeyboardDiv();
            this.spawnKeyboard();
        },

        spawnKeyboardDiv: function () {
            var kb = ig.domHandler.getElementByClass('simple-keyboard');
            var kbc = ig.domHandler.getElementById('#simple-keyboard-container');
            var canvas = ig.domHandler.getElementById('#canvas');
            var offsets = ig.domHandler.getOffsets(canvas);

            // var widthRatio = ig.sizeHandler.sizeRatio.x
            // var heightRatio = ig.sizeHandler.sizeRatio.y

            var canvasWidth = ig.responsive.width;
            var canvasHeight = ig.responsive.height;

            var aspectRatioMin = Math.min(ig.sizeHandler.scaleRatioMultiplier.x, ig.sizeHandler.scaleRatioMultiplier.y);
            var offsetLeft = offsets.left;
            var offsetTop = offsets.top;

            // var kbleft = Math.floor(offsetLeft * aspectRatioMin) + "px";
            // var kbtop = Math.floor(offsetTop * aspectRatioMin) + "px";
            var kbwidth = Math.floor(canvasWidth * aspectRatioMin) + 'px';
            // var kbheight = Math.floor(canvasHeight * heightRatio) + "px";

            var kbcleft = Math.floor(offsetLeft * aspectRatioMin) + 'px';
            var kbctop = Math.floor(offsetTop * aspectRatioMin) + 'px';
            var kbcwidth = Math.floor(canvasWidth * aspectRatioMin) + 'px';
            var kbcheight = Math.floor(canvasHeight * aspectRatioMin) + 'px';

            ig.domHandler.css(kbc, {
                float: 'left',
                position: 'absolute',
                left: kbcleft,
                top: kbctop,
                // bottom: 0,
                width: kbcwidth,
                height: kbcheight,
                // "z-index": 0,
                display: 'flex',
                'flex-direction': 'column',
                'justify-content': 'flex-end'
            });

            ig.domHandler.css(kb, {
                // float: "left",
                // position: "absolute",
                // left: kbleft,
                // top: kbtop,
                bottom: 0,
                width: kbwidth,
                // height: kbheight,
                'z-index': 5,
                order: 2
            });

            ig.sizeHandler.coreDivsToResize.push('#simple-keyboard-container');
        },

        spawnKeyboard: function () {
            var SimpleKeyboard = window.SimpleKeyboard.default;

            this.myKeyboard = new SimpleKeyboard({
                theme: 'hg-theme-default',
                onChange: function (input) { this.onChange(input); }.bind(this),
                onKeyPress: function (button) { this.onKeyPress(button); }.bind(this),
                maxLength: this.maxlength,
                mergeDisplay: true,
                layoutName: 'default',
                layout: {
                    default: [
                    'q w e r t y u i o p',
                    'a s d f g h j k l',
                    '{shift} z x c v b n m {backspace}',
                    '{numbers} {space} {ent}'
                    ],
                    shift: [
                    'Q W E R T Y U I O P',
                    'A S D F G H J K L',
                    '{shift} Z X C V B N M {backspace}',
                    '{numbers} {space} {ent}'
                    ],
                    numbers: ['1 2 3', '4 5 6', '7 8 9', '{abc} 0 {backspace}']
                },
                display: {
                    '{numbers}': '123',
                    '{ent}': 'return',
                    '{escape}': 'esc ⎋',
                    '{tab}': 'tab ⇥',
                    '{backspace}': '⌫',
                    '{capslock}': 'caps lock ⇪',
                    '{shift}': '⇧',
                    '{controlleft}': 'ctrl ⌃',
                    '{controlright}': 'ctrl ⌃',
                    '{altleft}': 'alt ⌥',
                    '{altright}': 'alt ⌥',
                    '{metaleft}': 'cmd ⌘',
                    '{metaright}': 'cmd ⌘',
                    '{abc}': 'ABC'
                }
            });

            this.setupListeners();
        },

        setupListeners: function () {
            this.myKeyboard.setInput('');
            var input = document.querySelector('#' + this.listeningTo);
            if (!input) return;

            if (input.value || input.value.trim > 0) {
                this.myKeyboard.setInput(input.value);
            }

            input.addEventListener('input', function (event) {
                this.myKeyboard.setInput(event.target.value);
            }.bind(this));

            input.addEventListener('focus', function (event) {
                this.showKeyboard();
                input.blur();
            }.bind(this));

            this.createOverlay();

            this.hideKeyboard();

            document.querySelector('#canvas').addEventListener('click', function (event) {
                /**
                 * Hide the keyboard when you're not clicking in the input
                 */
                if (!event.target.id.includes(this.listeningTo) && !event.target.className.includes('simple-keyboard')) {
                    this.hideKeyboard();
                    event.preventDefault();
                }
            }.bind(this));
            document.querySelector('#canvas').addEventListener('touchstart', function (event) {
                /**
                 * Hide the keyboard when you're not clicking in the input
                 */
                if (!event.target.id.includes(this.listeningTo) && !event.target.className.includes('simple-keyboard')) {
                    this.hideKeyboard();
                    event.preventDefault();
                }
            }.bind(this));

            this.setKeyboardInputValue();
        },

        resizeKeyboard: function () {
            var aspectRatioMin = Math.min(ig.sizeHandler.scaleRatioMultiplier.x, ig.sizeHandler.scaleRatioMultiplier.y);
            var kb = ig.domHandler.getElementByClass('simple-keyboard');

            var canvasWidth = ig.responsive.width;

            var kbwidth = Math.floor(canvasWidth * aspectRatioMin) + 'px';

            ig.domHandler.css(kb, {
                width: kbwidth,
                'z-index': 5,
                order: 2
            });

            var input = ig.domHandler.getElementById('#keyboard_input');

            if (input) {
                var canvasWidth = ig.responsive.width;
                var canvasHeight = ig.responsive.height;

                var inputwidth = Math.floor(canvasWidth * aspectRatioMin) + 'px';
                var inputheight = Math.floor((canvasHeight * aspectRatioMin) * 0.05) + 'px';

                ig.domHandler.css(input, {
                    width: inputwidth,
                    height: inputheight,
                    'z-index': 5,
                    'box-sizing': 'border-box',
                    order: 1,
                    'text-align': 'center'
                });
            }
        },

        onChange: function (input) {
            document.querySelector('#' + this.listeningTo).value = input;
            document.querySelector('#keyboard_input').value = input;
            // console.log("Input changed", input);
        },

        onKeyPress: function (button) {
            // console.log("Button pressed", button);
            if (button == '{ent}') {
                document.querySelector('#' + this.listeningTo).blur();
                this.hideKeyboard();
            }
            /**
             * If you want to handle the shift and caps lock buttons
             */
            if (button === '{shift}' || button === '{lock}') this.handleShift();
            if (button === '{numbers}' || button === '{abc}') this.handleNumbers();
        },

        handleShift: function () {
            var currentLayout = this.myKeyboard.options.layoutName;
            var shiftToggle = currentLayout === 'default' ? 'shift' : 'default';

            this.myKeyboard.setOptions({
                layoutName: shiftToggle
            });
        },

        handleNumbers: function () {
            var currentLayout = this.myKeyboard.options.layoutName;
            var numbersToggle = currentLayout !== 'numbers' ? 'numbers' : 'default';

            this.myKeyboard.setOptions({
                layoutName: numbersToggle
            });
        },

        showKeyboard: function () {
            var div = ig.domHandler.getElementById('#simple-keyboard-container');
            ig.domHandler.show(div);

            var overlay = ig.domHandler.getElementById('#canvas_overlay');
            ig.domHandler.show(overlay);

            var inputOverlay = ig.domHandler.getElementById('#keyboard_input');

            if (!inputOverlay) {
                this.createTextBox();
            }
        },

        hideKeyboard: function () {
            var playerName = document.querySelector('#player_name');
            if (playerName) {
                playerName.blur();
            }
            var div = ig.domHandler.getElementById('#simple-keyboard-container');
            ig.domHandler.hide(div);

            var overlay = ig.domHandler.getElementById('#canvas_overlay');
            ig.domHandler.hide(overlay);
        },

        createOverlay: function () {
            var div = ig.domHandler.create('div');
            var kbc = ig.domHandler.getElementById('#simple-keyboard-container');
            ig.domHandler.attr(div, 'id', 'canvas_overlay');

            var canvas = ig.domHandler.getElementById('#canvas');
            var offsets = ig.domHandler.getOffsets(canvas);

            // var widthRatio = ig.sizeHandler.sizeRatio.x
            // var heightRatio = ig.sizeHandler.sizeRatio.y

            var canvasWidth = ig.responsive.width;
            var canvasHeight = ig.responsive.height;

            var aspectRatioMin = Math.min(ig.sizeHandler.scaleRatioMultiplier.x, ig.sizeHandler.scaleRatioMultiplier.y);
            // var offsetLeft = offsets.left;
            var offsetTop = offsets.top;

            // var divleft = Math.floor(offsetLeft * aspectRatioMin) + "px";
            var divtop = Math.floor(offsetTop * aspectRatioMin) + 'px';
            var divwidth = Math.floor(canvasWidth * aspectRatioMin) + 'px';
            var divheight = Math.floor(canvasHeight * aspectRatioMin) + 'px';

            ig.domHandler.css(div, {
                float: 'left',
                position: 'absolute',
                // left: divleft,
                top: divtop,
                width: divwidth,
                height: divheight,
                'z-index': 4,
                background: 'rgba(0,0,0,0.6)',
                display: 'none'
            });

            ig.domHandler.appendChild(kbc, div);

            document.querySelector('#canvas_overlay').addEventListener('click', function (event) {
                this.hideKeyboard();
                event.preventDefault();
            }.bind(this));
            document.querySelector('#canvas_overlay').addEventListener('touchstart', function (event) {
                this.hideKeyboard();
                event.preventDefault();
            }.bind(this));

            ig.sizeHandler.coreDivsToResize.push('#canvas_overlay');
        },

        createTextBox: function () {
            var input = ig.domHandler.create('input');
            var kbc = ig.domHandler.getElementById('#simple-keyboard-container');

            ig.domHandler.attr(input, 'id', 'keyboard_input');

            // var canvas = ig.domHandler.getElementById("#canvas");
            // var offsets = ig.domHandler.getOffsets(canvas);

            // var widthRatio = ig.sizeHandler.sizeRatio.x
            // var heightRatio = ig.sizeHandler.sizeRatio.y

            var canvasWidth = ig.responsive.width;
            var canvasHeight = ig.responsive.height;

            var aspectRatioMin = Math.min(ig.sizeHandler.scaleRatioMultiplier.x, ig.sizeHandler.scaleRatioMultiplier.y);
            // var offsetLeft = offsets.left;
            // var offsetTop = offsets.top;

            // var inputleft = Math.floor(offsetLeft * aspectRatioMin) + "px";
            // var inputtop = Math.floor(offsetTop * aspectRatioMin) + "px";
            var inputwidth = Math.floor(canvasWidth * aspectRatioMin) + 'px';
            var inputheight = Math.floor((canvasHeight * aspectRatioMin) * 0.05) + 'px';

            ig.domHandler.css(input, {
                // float: "left",
                // position: "absolute",
                // left: inputleft,
                // top: inputtop,
                // bottom: 0,
                width: inputwidth,
                height: inputheight,
                'z-index': 5,
                'box-sizing': 'border-box',
                order: 1,
                'text-align': 'center'
            });

            ig.domHandler.attr(input, 'readonly');
            ig.domHandler.attr(input, 'maxlength', this.maxlength);
            ig.domHandler.appendChild(kbc, input);

            var keyboardInput = document.querySelector('#keyboard_input');

            if (!keyboardInput) return;

            keyboardInput.addEventListener('focus', function (event) {
                keyboardInput.blur();
            }.bind(this));

            this.setKeyboardInputValue();
        },

        setKeyboardInputValue: function () {
            var keyboardInput = document.querySelector('#keyboard_input');
            if (!keyboardInput) return;
            keyboardInput.value = document.querySelector('#' + this.listeningTo).value;
        }
    });
});
