ig.module(
    'dl.templates.entities.buttons.entity-button-more-games'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.div-layer',
    'dl.templates.mixins.button',
    'dl.templates.mixins.animation-sheet',
    'dl.templates.mixins.button-effect'
).defines(function () {
    'use strict';

    dl.EntityButtonMoreGames = dl.Entity
        .extend(dl.MixinDocker)
        .extend(dl.MixinDivLayer)
        .extend(dl.MixinButton)
        .extend(dl.MixinAnimationSheet)
        .extend(dl.MixinButtonScaleEffect)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this._useAnimationSheetAsSize = true;
                this.image = dl.preload['button-more-games'];

                return this;
            },

            _initDivLayerComponent: function () {
                this.c_DivLayerComponent = this.addComponent(dl.DivLayerComponent, {
                    link: _SETTINGS.MoreGames.Link,
                    newWindow: _SETTINGS.MoreGames.NewWindow,
                    divLayerName: 'more-games'
                });
            }
        });

    // Enable cache
    dl.enableCacheCanvas(dl.EntityButtonMoreGames);
});
