ig.module(
    'dl.templates.network-entities.entity-popup-shop'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.popup',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.animation-sheet',
    'dl.templates.mixins.text',
    'dl.templates.entities.buttons.entity-button-image-text',
    'dl.templates.entities.buttons.entity-button-image',
	'plugins.secure-ls'
).defines(function () {
    'use strict';

    dl.EntityPopupShop = dl.Entity
        .extend(dl.MixinPopup)
        .extend(dl.MixinDocker)
        .extend({
            postInit: function () {
                this.parent();

                var width=1300;
                var height=1100;

                this.content = this.spawnEntity(dl.EntityPopupShop_Content, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:width,y:height}
                    },
                });
            }
        });

    dl.EntityPopupShop_Content = dl.Entity
        .extend(dl.MixinPopupContent)
        .extend(dl.MixinDocker)
        .extend(dl.MixinAnimationSheet)
        .extend(dl.MixinText)
        .extend({
            buildingIndex:0,
            buyIndex:0,
            skinUsed:[0,0],
            btSkin:[],
            btSkinBg:[],
            //framework part
            staticInstantiate: function () {
                this.parent();
                this.image = dl.preload['popup-large-wide-long'];

                this._useAnimationSheetAsSize = true;

                this.text = 'PREMIUM SKIN';


                if(this.buildingIndex==0){
                    this.buyIndex=ig.game.skinFortUsed;                    
                }else if(this.buildingIndex==1){
                    this.buyIndex=ig.game.skinMissileUsed;                                        
                }else if(this.buildingIndex==2){
                    this.buyIndex=ig.game.skinTowerUsed;                                        
                }else if(this.buildingIndex==3){
                    this.buyIndex=ig.game.skinDomeUsed;                                        
                }


                return this;
            },

            postInit: function () {
                this.parent();
                this.initButtons();
                this.tweenIn();
                window.shopWindow=this;

                setTimeout(function(){
                    this.btnClose.enableEntityUnder();
                    this.changeImage();
                }.bind(this),200);
            },
            currentIdx:0,


            _initTextComponent: function () {

                this.c_TextComponent.updateProperties({
                    fillStyle: '#404040',
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                    textAlign: 'center', // [center|end|left|right|start];
                    textBaseline: 'middle', // [alphabetic|top|hanging|middle|ideographic|bottom];

                    fontSize: 48,

                    _docker: { x: 0.5, y: 0 },
                    _anchor: { x: 0.5, y: 0.5 },
                    _offset: { x: 0, y: 160 }
                });
            },
            update:function(){
                this.parent();
            },
            initButtons: function () {

                this.btnClose = this.spawnEntity(dl.EntityButtonImage, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 1, y: 0 },
                        dockerOffset: { x: -10, y: 10 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:100,y:100}
                    },
                    anchor: { x: 1, y: 0 },
                    image: dl.preload['popup-button-close'],
                    onButtonClicked: function () {
                        this.tweenOut(function () {
                            this.kill();
                        }.bind(this.parentInstance));
                    }.bind(this)
                });


                this.spacing = 90;
                this.btLeft = this.spawnEntity(dl.EntityButtonImage, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0, y: 0.5 },
                        dockerOffset: { x: this.spacing, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:70,y:100}
                    },
                    anchor: { x: 1, y: 0.5 },
                    image: dl.preload['name-avatar']['button-left'],
                    onButtonClicked: function () {
                        if((this.buildingIndex-1)<0){
                            this.buildingIndex = ig.game.shopData.length-1;
                        }else{
                            this.buildingIndex -=1;
                        }


                        if(this.buildingIndex==0){
                            this.buyIndex=ig.game.skinFortUsed;                    
                        }else if(this.buildingIndex==1){
                            this.buyIndex=ig.game.skinMissileUsed;                                        
                        }else if(this.buildingIndex==2){
                            this.buyIndex=ig.game.skinTowerUsed;                                        
                        }else if(this.buildingIndex==3){
                            this.buyIndex=ig.game.skinDomeUsed;                                        
                        }


                        this.changeImage();
                    }.bind(this)
                });

                this.btRight = this.spawnEntity(dl.EntityButtonImage, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 1, y: 0.5 },
                        dockerOffset: { x: -this.spacing, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:70,y:100}
                    },
                    anchor: { x: 0, y: 0.5 },
                    image: dl.preload['name-avatar']['button-right'],
                    onButtonClicked: function () {
                        if((this.buildingIndex+1)>(ig.game.shopData.length-1)){
                            this.buildingIndex = 0;
                        }else{
                            this.buildingIndex +=1;
                        }

                        if(this.buildingIndex==0){
                            this.buyIndex=ig.game.skinFortUsed;                    
                        }else if(this.buildingIndex==1){
                            this.buyIndex=ig.game.skinMissileUsed;                                        
                        }else if(this.buildingIndex==2){
                            this.buyIndex=ig.game.skinTowerUsed;                                        
                        }else if(this.buildingIndex==3){
                            this.buyIndex=ig.game.skinDomeUsed;                                        
                        }


                        this.changeImage();
                    }.bind(this)
                });

                this.btBuy = this.spawnEntity(dl.EntityButtonImageText, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 1 },
                        dockerOffset: { x: 0, y: -100 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:400,y:120}
                    },
                    c_TextComponent: {
                        fillStyle: '#ffffff',
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                        textAlign: 'center', 
                        textBaseline: 'middle',
                        fontSize: 40,
                    },
                    text:'',
                    anchor: { x: 0.5, y: 0.5 },
                    image: dl.preload['button-green'],
                    onButtonClicked: function () {
                        if(ig.game.shopData[this.buildingIndex].data[this.buyIndex].buyStatus==0){
                            this.buySkin();                            
                        }else{
                            this.useSkin();                                                        
                        }                        
                    }.bind(this)
                });

                this.changeImage();


            },
            buySkin:function(){
                ig.game.client.buySkin(this.buildingIndex,this.buyIndex,ig.game.shopData[this.buildingIndex].data[this.buyIndex].buyPrice);
                ig.game.shopData[this.buildingIndex].data[this.buyIndex].buyStatus=1;
                this.useSkin();
            },
            useSkin:function(){

                if(this.buildingIndex==0){
                    ig.game.skinFortUsed=this.buyIndex;                    
                }else if(this.buildingIndex==1){
                    ig.game.skinMissileUsed=this.buyIndex;                                        
                }else if(this.buildingIndex==2){
                    ig.game.skinTowerUsed=this.buyIndex;                                        
                }else if(this.buildingIndex==3){
                    ig.game.skinDomeUsed=this.buyIndex;                                        
                }

                this.changeImage();
            },
            changeImage:function(){


                if(this.buildingIndex==0){
                    this.skinUsed = [0,ig.game.skinFortUsed];
                }else if(this.buildingIndex==1){
                    this.skinUsed = [1,ig.game.skinMissileUsed];
                }else if(this.buildingIndex==2){
                    this.skinUsed = [2,ig.game.skinTowerUsed];
                }else if(this.buildingIndex==3){
                    this.skinUsed = [3,ig.game.skinDomeUsed];
                }

                if(this.skinImage){
                    this.skinImage.kill();
                    this.skinName.kill();

                    for(var j=0;j<this.btSkin.length;j++){
                        if(this.btSkin[j]) this.btSkin[j].kill();
                        if(this.btSkinBg[j]) this.btSkinBg[j].kill();
                    }

                    this.btSkin=[];
                    this.btSkinBg=[];
                }

                if(this.btBuyIcon){
                    this.btBuyIcon.kill();
                }

                if(ig.game.shopData[this.buildingIndex].data[this.buyIndex].buyStatus==0){

                    this.btBuyIcon=this.spawnEntity(dl.EntityImage, {
                        _useParentScale: true,
                        c_DockerComponent: {
                            dockerObject: this.btBuy,
                            dockerPercent: { x: 0.2, y: 0.5},
                            dockerOffset: { x: 0, y: 0 }
                        },
                        c_AnimationSheetComponent: {
                            _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                            _size: {x:55,y:70}
                        },
                        image: dl.preload['gems3'],
                        anchor: { x: 0, y: 0.5 },
                    });
    
                    if(this.btBuy) this.btBuy.updateText('   '+ig.game.shopData[this.buildingIndex].data[this.buyIndex].buyPrice);

                }else{
                    if(this.skinUsed[0]==this.buildingIndex && this.skinUsed[1]==this.buyIndex){
                        if(this.btBuy) this.btBuy.updateText('USED');
                    }else{
                        if(this.btBuy) this.btBuy.updateText('USE SKIN');                        
                    }
                }

                this.skinImageBg=this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.5},
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:1100,y:640}
                    },
                    image: dl.preload['grey-square-lite'],
                    anchor: { x: 0.5, y: 0.5 },
                });
                this.skinImage=this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.skinImageBg,
                        dockerPercent: { x: 1, y: 0.48},
                        dockerOffset: { x: -60, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:310,y:560}
                    },
                    image: dl.preload[ig.game.shopData[this.buildingIndex].data[this.buyIndex].buyImg],
                    anchor: { x: 1, y: 0.5},
                });

                this.skinName=this.spawnEntity(dl.EntityText, {
                    c_DockerComponent: {
                        dockerObject: this.skinImage,
                        dockerPercent: {x:0.5,y:1},
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_TextComponent: {
                        fillStyle: '#404040',
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                        textAlign: 'center', 
                        textBaseline: 'middle',
                        fontSize: 40,
                    },
                    anchor: { x: 0.5, y: 0.5 },
                    text:ig.game.shopData[this.buildingIndex].data[this.buyIndex].buyTitle,
                });


                this.shopTitle=this.spawnEntity(dl.EntityText, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: {x:0.5,y:0},
                        dockerOffset: { x: 0, y: 80 }
                    },
                    c_TextComponent: {
                        fillStyle: '#404040',
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                        textAlign: 'center', 
                        textBaseline: 'middle',
                        fontSize: 90,
                    },
                    anchor: { x: 0.5, y: 0.5 },
                    text:'SHOP',
                });



                var control = this;
                var posY=30;
                for(var i=0;i<=5;i++){
                    var offsetX=200;
                    var posX=30+i*offsetX;
                    if(i>2){
                        var posY=310;
                        var posX=30+(i-3)*offsetX;
                    }

                    this.btSkinBg.push(this.spawnEntity(dl.EntityImage, {
                        _useParentScale: true,
                        c_DockerComponent: {
                            dockerObject: this.skinImageBg,
                            dockerPercent: { x:0 , y:0 },
                            dockerOffset: { x: posX, y: posY }
                        },
                        c_AnimationSheetComponent: {
                            _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                            _size: {x:200,y:280}
                        },
                        image: dl.preload['white-square-border'],
                        anchor: { x: 0, y: 0 },
                    }));


                    this.btSkin.push(this.spawnEntity(dl.EntityButtonImageText, {
                        c_DockerComponent: {
                            dockerObject: this.btSkinBg[i],
                            dockerPercent: { x: 0.5, y: 0.5 },
                            dockerOffset: { x: 0, y: 0}
                        },
                        c_AnimationSheetComponent: {
                            _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                            _size: {x:120,y:220}
                        },
                        c_TextComponent: {
                            fillStyle: '#ffffff',
                            fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                            textAlign: 'center', 
                            textBaseline: 'bottom',
                            fontSize: 20,
                        },
                        buyIndex:i,
                        anchor: { x: 0.5, y: 0.5 },
                        image: dl.preload[ig.game.shopData[this.buildingIndex].data[i].buyImg],
                        onButtonClicked: function () {
                            control.buyIndex=this.buyIndex;
                            control.unlockAll();
                            control.changeImage();
                            setTimeout(function(){
                                control.btSkin[this.buyIndex].disableEntityUnder();                                
                                control.btSkinBg[this.buyIndex].updateImage(dl.preload['green-square-border']);
                            }.bind(this),100);
                        }
                    }));
                }


                if(ig.game.gemsbalance<ig.game.shopData[this.buildingIndex].data[this.buyIndex].buyPrice && ig.game.shopData[this.buildingIndex].data[this.buyIndex].buyStatus==0){
                    this.btBuy.disableEntityUnder();
                    this.btSkin[this.buyIndex].disableEntityUnder();
                }else{
                    if(this.skinUsed[0]==this.buildingIndex && this.skinUsed[1]==this.buyIndex){
                        this.btSkinBg[this.buyIndex].updateImage(dl.preload['green-square-border']);
                        this.btBuy.disableEntityUnder();                    
                        this.btSkin[this.buyIndex].disableEntityUnder();
                    }else{
                        this.btBuy.enableEntityUnder();                    
                        this.btSkin[this.buyIndex].enableEntityUnder();
                    }
                }


                this.updateText(ig.game.shopData[this.buildingIndex].name);

            },
            unlockAll:function(){
                for(var j=0;j<this.btSkin.length;j++){
                    if(this.btSkin[j]) {
                        this.btSkinBg[j].updateImage(dl.preload['white-square-border']);
                        this.btSkin[j].enableEntityUnder();
                    }
                }                
            },
            tweenIn: function (callback) {
                this.parent(callback);
            },

            tweenOut: function (callback) {
                this.parent(callback);
            }
        });
});
