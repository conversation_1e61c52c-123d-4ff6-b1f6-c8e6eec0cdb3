var _STRINGS = {
	Ad: {
		Mobile: {
			Preroll: {
				ReadyIn: 'The game is ready in ',
				Loading: 'Your game is loading...',
				Close: 'Close'
			},
			Header: {
				ReadyIn: 'The game is ready in ',
				Loading: 'Your game is loading...',
				Close: 'Close'
			},
			End: {
				ReadyIn: 'Advertisement ends in ',
				Loading: 'Please wait ...',
				Close: 'Close'
			}
		}
	},

	Splash: {
		Loading: 'Loading ...',
		LogoLine1: 'Some text here',
		LogoLine2: 'powered by MarketJS',
		LogoLine3: 'none',
        TapToStart: 'TAP TO START'
	},

	Game: {
		ResultHeader: ['Game Over!', 'Congratulations!'],
		Win: 'You have won the war.',
		Los<PERSON>: 'You have lost the war.',
		ResultRanking: ['1', '2', '3', '4'],
		Score: 'Score: ',
		Points: ' PTS.',
		You: '(You)',
		Host: '(Host)',
		Bot: '(<PERSON>t)',
		Ready: '(Ready)',
		Settings: 'Settings',
		Connecting: 'CONNECTING TO SERVER. PLEASE WAIT', // Ben edited
		Disconnected: 'YOU HAVE BEEN DISCONNECTED. ATTEMPTING TO RECONNECT', // Ben edited
		EnemyLeft: 'OPPONENT HAS LEFT!',
		EnemyLeft2: ' (disconnected)',
		GameOver: 'GAME OVER!',
		RoomCode: 'Room Code',
		Share: 'Share Link',
		Copy: 'Text copied to clipboard!',
		Private: 'PRIVATE',
		Code: 'ENTER CODE',
		QuitGame: 'Leave the game?',
		Waiting: 'WAITING FOR AN OPPONENT',
		LobbyHeader: ['Waiting for Opponent', 'Starting Match...', 'Press Start When You\'re Ready...'],
		LobbyAutoStart: 'Game will automatically start in ',
		PlayerDc: 'DISCONNECTED',
		RANDOM_MATCH: 'Random Match',
		PRIVATE_MATCH: 'Private Match',
		ENTER_PRIVATE_ROOM: 'Enter Waiting Area',
		NEW_MATCH: 'New Match',
		EXIT: 'Exit',
		CAPTURE_NEUTRAL: '<replace> captured a neutral nation',
		CAPTURE_ENEMY: '<replace1> captured a nation from <replace2>',
		HAVE_PROTECTION: 'Target is resting! Attack later!'
	},

	Lobby: {
		PlayerNumberSelect: 'Select Number of Players'
	},

	Tutorial: {
		Header: 'Instructions',
		Instructions: ['Wait for the right angle...', 'CLICK!'],
		Food: ['<-- ', ' Food! <br> Eat to <br> score!']
	},

	Notification: {
		EmptyRoomCode: 'Room Code must not be empty!',
		GameStart: ['First to score ', ' points wins!'],
		Bump: [' kicked ', ' into the water!', ' outside!'],
		Room404: 'Joining Private Room Failed! <br> Room does not exist!',
		PlayerQuit: ' has left the game!',
		PlayerDc: 'A player has disconnected!',
		Kicked: 'You have been kicked from the room!',
		InvalidName: 'Player Name Not Allowed!',
		InvalidCode: 'Code Not Allowed!',
		KickedCooldown: ['You cannot kick any more players. <br> Wait ', ' second', ' seconds'],
		JoinRoom: ' has joined'
	},

	Button: {
		Play: 'ARENA',
		Leaderboard: 'LEADERBOARD',
		// Not used > is an asset
		BackTitle: 'BACK TO TITLE',
		// Not used > is an asset
		Continue: 'NEXT GAME',
		Private: 'PRIVATE MATCH',
		// Not used > is an asset
		PrivateConnect: 'CONNECT',
		PrivateBack: 'BACK',
		// Not used > is an asset
		PlayWithBot: 'PLAY WITH BOT',
		LeaveRoom: 'LEAVE ROOM',
		Enter: 'ENTER LOBBY',
		Copy: 'COPY',
		Share: 'SHARE',
		PopupMenuOk: 'OK',
		PopupMenuCancel: 'CANCEL',
		Ready: 'Ready',
		CancelReady: 'Cancel Ready',
		// Not used > is an asset
		Exit: 'EXIT ROOM',
		Start: 'START',
		// Not used > is an asset
		SFX: ['sfx on', 'sfx off'],
		// Not used > is an asset
		BGM: ['bgm on', 'bgm off'],
		Lobby: {
			PlayerCount: ['2 PLAYERS', '4 PLAYERS']
		}
	},

	NETWORK: {
		ROOM_CODE: 'You are in private room<br>Invite Code: <replace1><br>Server: <replace2>',
		MATCHING_TITLE_WAIT: 'Waiting for players...',
		MATCHING_TITLE_START: 'Game will start soon...',
		MATCHING_HOST: 'HOST',
		MATCHING_READY: 'READY',
		MATCHING_BUTTON_START: 'Start',
		MATCHING_BUTTON_EXIT: 'Exit',
		MATCHING_BUTTON_READY: 'Ready',
		MATCHING_BUTTON_NOT_READY: 'Not ready',
		MATCHING_BUTTON_SHARE: 'Share',
		MATCHING_AUTO_START: 'Auto-start once all players ready',

		SERVER_STATUS_ONLINE: 'Players Online',
		SERVER_STATUS_OFFLINE: 'OFFLINE',
		SERVER_STATUS_PING: 'Ping',
		SERVER_STATUS_TIME_DIFFERENT: 'TIME DIFFERENT',

		CONNECTING_POPUP_CHECKING: 'Checking connection',
		CONNECTING_POPUP_CONNECTING: 'Connecting',
		CONNECTING_POPUP_ERROR: 'Connect error ERROR',
		CONNECTING_POPUP_TIME_OUT: 'Connect time out',
		CONNECTING_POPUP_CONNECTED: 'Connected',

		MATCHING_POPUP_NO_CONNECTION: 'NO CONNECTION TO SERVER',
		MATCHING_POPUP_MATCHING: 'FINDING ROOM',
		MATCHING_POPUP_MATCHING_SUCCESS: 'FOUND ROOM',

		MATCHING_POPUP_REJECT_REASON_UNKNOWN: 'UNKNOWN ISSUE',
		MATCHING_POPUP_REJECT_REASON_SERVER_ERROR: 'SERVER ERROR',
		MATCHING_POPUP_REJECT_REASON_INVALID_REQUEST: 'INVALID REQUEST',
		MATCHING_POPUP_REJECT_REASON_VERSION_MISMATCH: 'VERSION MISMATCH',
		MATCHING_POPUP_REJECT_REASON_CANNOT_FIND_ROOM: 'CAN NOT FIND ROOM',

		NOTIFICATION_MATCHING_PLAYER_JOINED: '<replace> has joined',
		NOTIFICATION_MATCHING_PLAYER_LEFT: '<replace> has left',

		CHECK_NAME_NOTIFICATION_INVALID: 'Invalid Name',
		CHECK_NAME_NOTIFICATION_EMPTY: 'Name is empty',
		CHECK_NAME_NOTIFICATION_TOO_MUCH_LENGTH: 'Name too long',
		CHECK_NAME_NOTIFICATION_CONTAIN_INVALID_CHARACTER: 'Name contains invalid characters',
		CHECK_NAME_NOTIFICATION_BAD_WORD: 'Name is bad word',

		POPUP_SELECT_MAX_PLAYER_TITLE: 'SELECT MAX PLAYER',
		POPUP_SELECT_MAX_PLAYER_BUTTON_2: '2 PLAYERS',
		POPUP_SELECT_MAX_PLAYER_BUTTON_3: '3 PLAYERS',
		POPUP_SELECT_MAX_PLAYER_BUTTON_4: '4 PLAYERS',

		POPUP_SELECT_PASSWORD_TITLE: 'Enter a password<br>or create one',
		POPUP_SELECT_PASSWORD_BUTTON_CONNECT: 'Connect',

		POPUP_SHARE_TITLE: 'INVITE CODE',
		POPUP_SHARE_BUTTON_COPY: 'Copy to Clipboard',
		POPUP_SHARE_NOTIFICATION: 'Copied',

		POPUP_HOME_TITLE: 'Leave the game?',

		POPUP_RESULT_TITLE_HEADER_WIN: 'CONGRATULATIONS',
		POPUP_RESULT_TITLE_SUBTITLE_WIN: 'You have won the war!',
		POPUP_RESULT_TITLE_HEADER_LOSE: 'GAME OVER',
		POPUP_RESULT_TITLE_SUBTITLE_LOSE: 'You have been eliminated',

		CHECK_PASSWORD_NOTIFICATION_INVALID: 'Invalid password',
		CHECK_PASSWORD_NOTIFICATION_EMPTY: 'Password is empty',
		CHECK_PASSWORD_NOTIFICATION_TOO_MUCH_LENGTH: 'Password too long',
		CHECK_PASSWORD_NOTIFICATION_NOT_ENOUGH_LENGTH: 'Password too short',
		CHECK_PASSWORD_NOTIFICATION_CONTAIN_INVALID_CHARACTER: 'Password contains invalid characters',

		INSTRUCTION_LEVEL_TITLE: 'How to Play',
		INSTRUCTION_LEVEL_STEP1: 'Step 1: Drag and drop to send troops',
		INSTRUCTION_LEVEL_STEP2: 'Step 2: Attack and conquer other nations',
		INSTRUCTION_LEVEL_STEP3: 'Step 3: Be the last person standing',
		INSTRUCTION_LEVEL_STEP4: 'IMPORTANT!: Do not change tabs or minimize<br>during gameplay',
		INSTRUCTION_COUNTDOWN: 'Game starting in <replace> ...',

		COUNT_DOWN_TIMER_START: 'Fight',
		GAME_OVER_LEVEL_TITLE: 'GAME OVER',
		GAME_OVER_RANKS: ['1st', '2nd', '3rd', '4th'],

		WARNING_HIGH_PING: 'High ping detected',
		SELECT_SERVER: 'CHANGE SERVER',

		POPUP_SELECT_SERVER: {
			TITLE: 'Choose a server',
			BUTTON_0: 'Asia',
			BUTTON_1: 'Europe',
			BUTTON_2: 'North America'
		},

		CAPTURED_BUILDING: {
			FORT: ['<replace> has captured the Fort!','Fort start training soldiers twice faster'],
			WD: ['<replace> has captured the Weather Dome!', 'The Weather Dome is causing thunderstorms on the battlefield!'],
			PT: ['<replace> has captured the Propaganda Tower!', 'Enemy Propaganda is demoralizing our troops! Training Rate -50%'],
			MS: ['<replace> has captured the Missile Silo!', 'A missile has been launched!']
		}
	}
};


