ig.module(
    'dl.templates.entities.entity-image'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.animation-sheet'
).defines(function () {
    'use strict';

    dl.EntityImage = dl.Entity
        .extend(dl.MixinDocker)
        .extend(dl.MixinAnimationSheet)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this._useAnimationSheetAsSize = true;

                return this;
            }
        });

    // Enable cache
    dl.enableCacheCanvas(dl.EntityImage);
});
