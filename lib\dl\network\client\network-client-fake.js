/**
 * Created by <PERSON><PERSON>
 * Handle user, messages and ping on client side
 */
ig.module(
    'dl.network.client.network-client-fake'
).defines(function () {
    NetworkClientFake = ig.Class.extend({
        init: function (settings) {
            this.server_ip = settings.server_ip;
            this.http_port = settings.http_port;
            this.https_port = settings.https_port;
            this.onUpdate = settings.onUpdate;

            this.fullServerIP = '';
            this.socket = null;

            // pings
            this.latencyAverage = 0;
            this.latencyLog = [];
            this.latencyLogSize = 10;
            this.startConnect();
            this.refresh();

        },

        startConnect: function () {
            this.connect(
                function () {
                    // console.log('Success ping to: ', this.fullServerIP);
                }.bind(this),
                function () {
                    // console.log('Fail ping to: ', this.fullServerIP);
                }.bind(this)
            );
        },

        kill: function () {
            this.disconnect(
                function () {

                }.bind(this),
                function () {

                }.bind(this)
            );
        },
        /**
         * Connection's functions
         */
        checkConnection: function () {
            if (!this.socket) return false;
            if (this.socket.disconnected) return false;

            return true;
        },

        connectSocket: function () {
            var port = this.http_port;
            if (document.location.protocol &&
                typeof (document.location.protocol.includes) == 'function' &&
                document.location.protocol.includes('https')) {
                port = this.https_port;
            } else {
                port = this.http_port;
            }

            this.fullServerIP = this.server_ip + ':' + port;

            // dl.log('Pinging ' + this.fullServerIP);
            this.socket = io(this.fullServerIP, { reconnection: false });

            return !!this.socket;
        },

        connect: function (successCallback, failCallback) {
            this.resetPing();

            var connecting = false;
            if (!this.socket) {
                connecting = this.connectSocket();
            } else {
                if (this.socket.disconnected) {
                    this.socket.connect();
                    connecting = true;
                }
            }

            if (connecting) {
                this.socket.off();
                this.socket.on('connect', function () {
                    this.onServerConnect();

                    this.socket.off('disconnect');
                    this.socket.on('disconnect', function () {
                        this.onServerDisconnect();
                    }.bind(this));

                    this.socket.off(SERVER_CONFIGS.TAGS.PING_REPLY);
                    this.socket.on(SERVER_CONFIGS.TAGS.PING_REPLY, function (data) {
                        this.onPingServerReply(data);
                    }.bind(this));
                }.bind(this));

                if (typeof (successCallback) == 'function') successCallback();
                return true;
            }

            if (typeof (failCallback) == 'function') failCallback();
            return false;
        },

        disconnect: function (successCallback, failCallback) {
            if (this.socket) {
                if (!this.socket.disconnected) {
                    this.socket.once('disconnect', function () {
                        if (typeof (successCallback) == 'function') successCallback();
                    }.bind(this));

                    this.socket.disconnect();
                    return true;
                }
            }

            if (typeof (failCallback) == 'function') failCallback();
            return false;
        },

        onServerConnect: function () {
            for (var i = 0; i < this.latencyLogSize; i++) {
                this.pingServer();
            }
            if (typeof this.onUpdate === 'function') this.onUpdate();
        },

        onServerDisconnect: function () {
            if (typeof this.onUpdate === 'function') this.onUpdate();
        },

        /**
         * Pings functions
         */
        resetPing: function () {
            this.latencyAverage = 0;
            this.latencyLog = [];
            this.latencyLogSize = 10;
        },

        pingServer: function () {
            if (!this.socket) return;

            // ping
            this.socket.emit(SERVER_CONFIGS.TAGS.PING, {
                pingTime: Date.now()
            });
        },

        onPingServerReply: function (data) {
            if (!data) return;
            if (isNaN(data.pingTime) || data.pingTime === null) return;
            if (isNaN(data.pongTime) || data.pongTime === null) return;

            var roundTripTime = Date.now() - data.pingTime;

            var latency = Math.floor(roundTripTime / 2);
            this.calculateAverage(latency, data.pongTime);

            if (typeof this.onUpdate === 'function') this.onUpdate();
        },

        calculateAverage: function (latency, pongTime) {
            if (isNaN(latency) || latency === null) return;
            if (isNaN(pongTime) || pongTime === null) return;

            if (this.latencyLog.length >= this.latencyLogSize) {
                this.latencyLog.shift();
            }

            this.latencyLog.push(latency);

            var sum = 0;
            for (var i = 0, iLength = this.latencyLog.length; i < iLength; i++) {
                sum += this.latencyLog[i];
            }

            this.latencyAverage = Math.round(sum / this.latencyLog.length);
        },

        getInfo: function () {
            if (!this.checkConnection()) return 'Offline';
            
            if(this.latencyAverage<=0 || this.latencyAverage>=1000) {
                return '999+ ms';
            }else{
                return this.latencyAverage.toString() + ' ms';                
            }
        },
        refresh: function () {
            if(!ig.game.popupStatus) return;

            setTimeout(function(){
                this.refresh();
            }.bind(this),1000);

            for (var i = 0; i < this.latencyLogSize; i++) {
                this.pingServer();
            }
        }
    });
});
