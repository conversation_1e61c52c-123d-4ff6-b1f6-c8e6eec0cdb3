/**
 * Created by <PERSON><PERSON>
 * Handle NetworkGame events on client side
 */
ig.module(
    'dl.network.client.network-game-event-handler'
).defines(function () {
    NetworkGameEventHandler = {};

    NetworkGameEventHandler.handleEvent = function (networkGame, event) {
        if (!event) return;

        switch (event.type) {
            // ----------------
            // Start Instruction's functions
            // ----------------
            case NetworkGameEvent.EVENT_TYPE.START_INSTRUCTION:
                {
                    return NetworkGameEventHandler.onStartInstruction(networkGame, event);
                } break;
            case NetworkGameEvent.EVENT_TYPE.END_INSTRUCTION:
                {
                    return NetworkGameEventHandler.onEndInstruction(networkGame, event);
                } break;
            // ----------------
            // End Instruction's functions
            // ----------------

            // ----------------
            // Start count down functions
            // ----------------
            case NetworkGameEvent.EVENT_TYPE.START_COUNT_DOWN:
                {
                    return NetworkGameEventHandler.onStartCountDown(networkGame, event);
                } break;
            case NetworkGameEvent.EVENT_TYPE.END_COUNT_DOWN:
                {
                    return NetworkGameEventHandler.onEndCountDown(networkGame, event);
                } break;
            // ----------------
            // End count down functions
            // ----------------

            // ----------------
            // Start Game's functions
            // ----------------
            case NetworkGameEvent.EVENT_TYPE.START_GAME:
                {
                    return NetworkGameEventHandler.onStartGame(networkGame, event);
                } break;
            case NetworkGameEvent.EVENT_TYPE.GAME_UPDATE:
                {
                    return NetworkGameEventHandler.onGameUpdate(networkGame, event);
                } break;
            case NetworkGameEvent.EVENT_TYPE.END_GAME:
                {
                    return NetworkGameEventHandler.onEndGame(networkGame, event);
                } break;
            // ----------------
            // Start Game's functions
            // ----------------

            // ----------------
            // Start custom functions
            // ----------------
            case NetworkGameEvent.EVENT_TYPE.CAPITAL_ATTACK:
                return NetworkGameEventHandler.onCapitalAttack(networkGame, event);

            case NetworkGameEvent.EVENT_TYPE.SPAWN_SOLDIER:
                return NetworkGameEventHandler.onSpawnSoldier(networkGame, event);
            case NetworkGameEvent.EVENT_TYPE.CHANGE_OWNER:
                return NetworkGameEventHandler.onChangeOwner(networkGame, event);
            case NetworkGameEvent.EVENT_TYPE.CLEAR_SPAWN_TIMERS:
                return NetworkGameEventHandler.onClearSpawnTimers(networkGame, event);
            case NetworkGameEvent.EVENT_TYPE.CAPTURE_BLDG:
                return NetworkGameEventHandler.onCaptureBldg(networkGame, event);
            // ----------------
            // End custom functions
            // ----------------
        };
    };

    // ----------------
    // Start Utilities functions
    // ----------------
    NetworkGameEventHandler.checkCurrentLevel = function (checkLevel, levelInstance) {
        if (ig.game.director.levels[ig.game.director.currentLevel] != checkLevel) return false;
        if (!levelInstance) return false;

        return true;
    };

    NetworkGameEventHandler.onNetworkGameDataUpdated = function (networkGame) {
        switch (ig.game.director.levels[ig.game.director.currentLevel]) {
            case LevelGame:
                ig.game.game.updateData(networkGame.current_NetworkGameData);
                break;
            case LevelInstruction:
                if (!ig.game.client) return;
                if (!ig.game.client.clientGameRoom) return;
                ig.game.client.clientGameRoom.mapData = networkGame.current_NetworkGameData.mapData;
                break;
        }
    };
    // ----------------
    // End Utilities functions
    // ----------------

    // ----------------
    // Start Instruction's functions
    // ----------------
    NetworkGameEventHandler.onStartInstruction = function (networkGame, event) {
        // console.log("onStartInstruction");
        if (!NetworkGameEventHandler.checkCurrentLevel(LevelInstruction, ig.game.instructionLevel)) return false;

        var startTime = ig.game.networkClient.getServerTimeInClient(event.info.startTime);
        ig.game.instructionLevel.startTimer(startTime, event.info.duration);

        return true;
    };

    NetworkGameEventHandler.onEndInstruction = function (networkGame, event) {
        // console.log("onEndInstruction");
        if (!NetworkGameEventHandler.checkCurrentLevel(LevelInstruction, ig.game.instructionLevel)) return false;

        // ig.game.director.jumpTo(LevelGame);
        
        ig.game.instructionLevel.tweenOut(function () {
            // ig.game.director.jumpTo(LevelGame);
            ig.game.director.loadLevel(7);
        }.bind(ig.game.instructionLevel));

        return true;
    };
    // ----------------
    // End Instruction's functions
    // ----------------

    // ----------------
    // Start count down functions
    // ----------------
    NetworkGameEventHandler.onStartCountDown = function (networkGame, event) {
        // console.log("onStartCountDown");
        if (!NetworkGameEventHandler.checkCurrentLevel(LevelGame, ig.game.game)) return false;

        // var startTime = ig.game.networkClient.getServerTimeInClient(event.info.startTime);
        // ig.game.game.countDownTimer.startTimer(startTime, event.info.duration);

        return true;
    };

    NetworkGameEventHandler.onEndCountDown = function (networkGame, event) {
        // console.log("onEndCountDown");
        // switch (ig.game.director.levels[ig.game.director.currentLevel]) {
        //     case LevelGame:
        //         ig.game.managers.game.initCapitalTimer();
        //         ig.game.managers.game.enableControls = true;
        //         break;
        //     case LevelInstruction:
        //         break;
        // }
        ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.goodluck);
        return true;
    };
    // ----------------
    // End count down functions
    // ----------------

    // ----------------
    // Start Game's functions
    // ----------------
    NetworkGameEventHandler.onStartGame = function (networkGame, event) {
        
        if (!networkGame.clientStartTime) {
            networkGame.clientStartTime = ig.game.networkClient.getServerTimeInClient(networkGame.networkGameData.serverStartTime);
        }

        // console.log("onStartGame");
        if (!NetworkGameEventHandler.checkCurrentLevel(LevelGame, ig.game.game)) return false;
        networkGame.gameStarted = true;
        // var startTime = ig.game.networkClient.getServerTimeInClient(event.info.startTime);
        // ig.game.game.gameTimer.startTimer(startTime, event.info.duration);

        return true;
    };

    NetworkGameEventHandler.onGameUpdate = function (networkGame, event) {
        return true;
    };

    NetworkGameEventHandler.onEndGame = function (networkGame, event) {
        // console.log("onEndGame");
        if (!NetworkGameEventHandler.checkCurrentLevel(LevelGame, ig.game.game)) return false;

        ig.game.game.createTween()
            .to({}, NETWORK_GAME_CONFIGS.LEVEL_TRANSITION_TIME, {})
            .start({
                onCompleteTween: function () {
                    this.tweenOut(function () {
                        // ig.game.director.loadLevel(6);
                        ig.game.director.jumpTo(LevelGameOver);
                    }.bind(this));
                }.bind(ig.game.game)
            });

        // ig.game.game.tweenOut(function () {
        //     ig.game.director.jumpTo(LevelGameOver);
        // }.bind(ig.game.game));

        return true;
    };
    // ----------------
    // End Game's functions
    // ----------------

    // ----------------
    // Start custom functions
    // ----------------
    NetworkGameEventHandler.onCapitalAttack = function (networkGame, event) {
        switch (ig.game.director.levels[ig.game.director.currentLevel]) {
            case LevelGame:
                if (ig.game.managers && ig.game.managers.game) {
                    ig.game.managers.game.onCapitalAttack(event.info);
                }
                break;
        }
        return true;
    };

    NetworkGameEventHandler.onSpawnSoldier = function (networkGame, event) {
        switch (ig.game.director.levels[ig.game.director.currentLevel]) {
            case LevelGame:
                if (ig.game.managers && ig.game.managers.game) {
                    ig.game.managers.game.onNetworkSpawnSoldier(event.info.soldier, event.info.player);
                }
                break;
        }
        return true;
    };

    NetworkGameEventHandler.onChangeOwner = function (networkGame, event) {

        switch (ig.game.director.levels[ig.game.director.currentLevel]) {
            case LevelGame:
                if (ig.game.managers && ig.game.managers.game) {
                    ig.game.managers.game.onChangeOwner(event.info);
                }
                break;
        }
        return true;
    };

    NetworkGameEventHandler.onClearSpawnTimers = function (networkGame, event) {
        switch (ig.game.director.levels[ig.game.director.currentLevel]) {
            case LevelGame:
                if (ig.game.managers && ig.game.managers.game) {
                    ig.game.managers.game.onClearSpawnTimers(event.info);
                }
                break;
        }
        return true;
    };

    NetworkGameEventHandler.onCaptureBldg = function (networkGame, event) {
        switch (ig.game.director.levels[ig.game.director.currentLevel]) {
            case LevelGame:
                if (ig.game.managers && ig.game.managers.game) {
                    ig.game.managers.game.onCaptureBldg(event.info);
                }
                break;
        }
        return true;
    };
    // ----------------
    // End custom functions
    // ----------------
});
