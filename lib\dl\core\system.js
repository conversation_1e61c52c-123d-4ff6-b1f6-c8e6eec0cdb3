ig.module(
    'dl.core.system'
).requires(
    'impact.system',
    'dl.utils.mixins.event-handler',
    'dl.core.system.timer'
).defines(function () {
    /**
     * Add mixins
     */
    ig.System.inject(dl.EventHandlerMixin);

    /**
     * Inject
     */
    ig.System.inject({

        /**
         * Overriding
         */
        init: function () {
            this.parent.apply(this, arguments);
            this.timer = new dl.SystemTimer();
            this.dl_tick = 0;
            dl.system = this;
        },

        /**
         * Overriding
         */
        run: function () {
            dl.SystemTimer.Step();
            this.dl_tick = this.timer.tick();

            this.parent.apply(this, arguments);
        },

        /**
         * Overriding
         */
        resize: function () {
            this.parent.apply(this, arguments);

            if (dl.check.isFunction(this.triggerEvent)) {
                this.triggerEvent('sizeChanged', this.width, this.height);
            }
        }
    });
});
