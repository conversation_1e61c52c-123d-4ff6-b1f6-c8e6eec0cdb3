ig.module(
    'game.dl-entities.game-player-info'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.animation-sheet'
).defines(function () {
    dl.EntityGamePlayerInfo = dl.Entity
        .extend(dl.MixinDocker)
        .extend(dl.MixinAnimationSheet)
        .extend({
            staticInstantiate: function () {
                this.parent();
                if (!window.playerList) {
                    window.playerList = [];
                }
                window.playerList.push(this);

                if(ig.ua.mobile && !ig.ua.iPad){
                    this.size = {x: 500,y: 300};
                }else{
                    this.size = {x: 500,y: 250};
                }

                this.youText = null;
                this.playerData = null;
                this.playerNumber = null;
                this.setSize(this.size.x, this.size.y);
                return this;
            },

            postInit: function () {
                this.parent();

                if(ig.ua.mobile && !ig.ua.iPad){

                    this.playerAvatar = this.spawnEntity(dl.EntityImage, {
                        c_DockerComponent: {
                            dockerObject: this,
                            dockerPercent: { x: 0, y: 0.5 },
                            dockerOffset: { x: 0, y: 0 }
                        },
                        anchor: { x: 0, y: 0.5 },
                        c_AnimationSheetComponent: {
                            _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                            _size: { x: 150, y: 150 }
                        },
                        image: dl.preload['name-avatar']['avatar-empty']
                    });

                    this.playerBorder = this.spawnEntity(dl.EntityImage, {
                        c_DockerComponent: {
                            dockerObject: this.playerAvatar,
                            dockerPercent: { x: 0, y: 0.5 },
                            dockerOffset: { x: 0, y: 0 }
                        },
                        anchor: { x: 0, y: 0.5 },
                        c_AnimationSheetComponent: {
                            _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                            _size: { x: 500, y: 150 }
                        },
                        image: dl.preload['game-player-info'].backgrounds[0]
                    });

                    this.playerName = this.spawnEntity(dl.EntityText, {
                        c_DockerComponent: {
                            dockerObject: this.playerBorder,
                            dockerPercent: { x: 0.66, y: 0.5 },
                            dockerOffset: { x: 0, y: 0 }
                        },
                        anchor: { x: 0.5, y: 0.5 },
                        c_TextComponent: {
                            fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                            fontSize: 50,
                            fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                            maxWidth: this.size.x - 300
                        },
                        text: 'playerName'
                    });

                    // this.allianceIcon = this.spawnEntity(dl.EntityImage, {
                    //     c_DockerComponent: {
                    //         dockerObject: this.playerBorder,
                    //         dockerPercent: { x: 0.5, y: 0.9 },
                    //         dockerOffset: { x: 0, y: 20 }
                    //     },
                    //     anchor: { x: 0.5, y: 0 },
                    //     c_AnimationSheetComponent: {
                    //         _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                    //         _size: { x: 100, y: 70 }
                    //     },
                    //     image:null
                    // });


                    // this.allianceName = this.spawnEntity(dl.EntityText, {
                    //     c_DockerComponent: {
                    //         dockerObject: this.playerBorder,
                    //         dockerPercent: { x: 0.7, y: 0.8 },
                    //         dockerOffset: { x: 0, y: 36 }
                    //     },
                    //     anchor: { x: 0, y: 0 },
                    //     c_TextComponent: {
                    //         fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                    //         fontSize: 36,
                    //         fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                    //         maxWidth: this.size.x - 190,
                    //         textAlign:'left',
                    //     },
                    //     text: 'Alliance'
                    // });


                    this.eliminated = this.spawnEntity(dl.EntityImage, {
                        c_DockerComponent: {
                            dockerObject: this,
                            dockerPercent: { x: 0.5, y: 0.5 },
                            dockerOffset: { x: 0, y: 0 }
                        },
                        image: null
                    });


                }else{

                    this.playerAvatar = this.spawnEntity(dl.EntityImage, {
                        c_DockerComponent: {
                            dockerObject: this,
                            dockerPercent: { x: 0, y: 0.5 },
                            dockerOffset: { x: 0, y: 0 }
                        },
                        anchor: { x: 0, y: 0.5 },
                        c_AnimationSheetComponent: {
                            _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                            _size: { x: 116, y: 116 }
                        },
                        image: dl.preload['name-avatar']['avatar-empty']
                    });

                    this.playerBorder = this.spawnEntity(dl.EntityImage, {
                        c_DockerComponent: {
                            dockerObject: this.playerAvatar,
                            dockerPercent: { x: 0, y: 0.5 },
                            dockerOffset: { x: 0, y: 0 }
                        },
                        anchor: { x: 0, y: 0.5 },
                        image: dl.preload['game-player-info'].backgrounds[0]
                    });

                    this.playerName = this.spawnEntity(dl.EntityText, {
                        c_DockerComponent: {
                            dockerObject: this.playerBorder,
                            dockerPercent: { x: 0.66, y: 0.5 },
                            dockerOffset: { x: 0, y: 0 }
                        },
                        anchor: { x: 0.5, y: 0.5 },
                        c_TextComponent: {
                            fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                            fontSize: 30,
                            fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                            maxWidth: this.size.x - 300
                        },
                        text: 'playerName'
                    });


                    // this.allianceIcon = this.spawnEntity(dl.EntityImage, {
                    //     c_DockerComponent: {
                    //         dockerObject: this.playerBorder,
                    //         dockerPercent: { x: 0.5, y: 0.9 },
                    //         dockerOffset: { x: 0, y: 20 }
                    //     },
                    //     anchor: { x: 0.5, y: 0 },
                    //     c_AnimationSheetComponent: {
                    //         _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                    //         _size: { x: 100, y: 70 }
                    //     },
                    //     image:null
                    // });


                    // this.allianceName = this.spawnEntity(dl.EntityText, {
                    //     c_DockerComponent: {
                    //         dockerObject: this.playerBorder,
                    //         dockerPercent: { x: 0.7, y: 0.8 },
                    //         dockerOffset: { x: 0, y: 36 }
                    //     },
                    //     anchor: { x: 0, y: 0 },
                    //     c_TextComponent: {
                    //         fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                    //         fontSize: 36,
                    //         fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                    //         maxWidth: this.size.x - 190,
                    //         textAlign:'left',
                    //     },
                    //     text: 'Alliance'
                    // });


                    this.eliminated = this.spawnEntity(dl.EntityImage, {
                        c_DockerComponent: {
                            dockerObject: this,
                            dockerPercent: { x: 0.5, y: 0.5 },
                            dockerOffset: { x: 0, y: 0 }
                        },
                        image: null
                    });
                }


                this.updateData();
            },

            updateData: function (data) {
                this.playerData = null;

                if (!data) return;
                this.playerData = data;

                // has left
                var hasLeft = this.playerData.isLeaved || this.playerData.eliminated;
                if (hasLeft) {
                    this.eliminated.updateImage(dl.preload['game-player-info'].eliminated);
                } else {
                    var playerNumber = ig.game.client.clientGameRoom.getPlayerNumber(data.playerId);
                    if (!this.playerNumber) this.playerNumber = playerNumber;

                    if (this.playerNumber === 1) {
                        this.playerBorder.updateImage(dl.preload['game-player-info'].backgrounds[0]);
                    } else if (this.playerNumber === 2) {
                        this.playerBorder.updateImage(dl.preload['game-player-info'].backgrounds[1]);
                    } else if (this.playerNumber === 3) {
                        this.playerBorder.updateImage(dl.preload['game-player-info'].backgrounds[2]);
                    } else if (this.playerNumber === 4) {
                        this.playerBorder.updateImage(dl.preload['game-player-info'].backgrounds[3]);
                    }
                }

                // avatar
                this.playerAvatar.updateImage(dl.preload['name-avatar'].avatars[data.playerAvatarId]);
                // name
                if(data.playerName != null) this.playerName.updateText(data.playerName);
                // if(data.playerId !=null && ig.game.matchingAllianceData[data.playerId] && ig.game.matchingAllianceData[data.playerId].allianceName!=null)  this.allianceName.updateText(ig.game.matchingAllianceData[data.playerId].allianceName);
                // if(data.playerId !=null && ig.game.matchingAllianceData[data.playerId] && ig.game.matchingAllianceData[data.playerId].allianceIcon!=null)  this.allianceIcon.updateImage(dl.preload['allianceIcon'+ig.game.matchingAllianceData[data.playerId].allianceIcon]);


                // frame
                var isMe = ig.game.client.isMyPlayerId(data.playerId);
                if (isMe && !this.youText) {
                    this.playerName.c_DockerComponent.dockerPercent.y = 0.38;
                    this.playerName.c_TextComponent.fontSize = 34;
                    this.youText = this.spawnEntity(dl.EntityText, {
                        c_DockerComponent: {
                            dockerObject: this.playerName,
                            dockerPercent: { x: 0.5, y: 1 },
                            dockerOffset: { x: 0, y: 7 }
                        },
                        anchor: { x: 0.5, y: 0.5 },
                        c_TextComponent: {
                            fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                            fontSize: 20,
                            fontFamily: dl.configs.getConfig('FONT', 'bold').name
                        },
                        text: _STRINGS.Game.You
                    });
                }
            },

            tweenIn: function (time) {
                dl.TweenTemplate.scaleIn(this, time);
                dl.TweenTemplate.scaleIn(this.playerAvatar, time);
                dl.TweenTemplate.scaleIn(this.playerName, time);
            },

            tweenOut: function (time) {
                dl.TweenTemplate.scaleOut(this, time);
                dl.TweenTemplate.scaleOut(this.playerAvatar, time);
                dl.TweenTemplate.scaleOut(this.playerName, time);
            }
        });

    // Enable cache
    dl.enableCacheCanvas(dl.EntityGamePlayerInfo);
});
