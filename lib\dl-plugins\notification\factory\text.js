ig.module(
    'dl-plugins.notification.factory.text'
).defines(function () {
    "use strict";

    dl.Notification.Factory.Text = {
        generate: function (configs, contentCanvas) {
            var canvas = document.createElement('canvas');
            var context = canvas.getContext('2d');

            var measureSize = this.measure(configs, context);
            configs.measureSize = measureSize;
            // update canvas size
            canvas.width = measureSize.x;
            canvas.height = measureSize.y;

            // update canvas size with content
            if (contentCanvas) {
                canvas.width += contentCanvas.width + configs.padding;

                if (contentCanvas.height > canvas.height) {
                    canvas.height = contentCanvas.height;
                }
            }

            this.draw(configs, context, contentCanvas);
            this.drawContentCanvas(configs, context, contentCanvas);

            return {
                configs: configs,
                canvas: canvas
            };
        },

        measure: function (configs, ctx) {
            var text = configs.text.toString();


            ctx.save();
            ctx.font = configs.fontSize + "px" + " " + configs.fontFamily;
            var metrics = ctx.measureText(text);
            ctx.restore();

            configs.textData = {
                text: text,
                textWidth: metrics.width,
                fontHeight: metrics.fontBoundingBoxAscent + metrics.fontBoundingBoxDescent
            };

            // some browser doesn't support
            if (isNaN(configs.textData.fontHeight) || configs.textData.fontHeight == null) {
                configs.textData.fontHeight = configs.fontSize;
            }

            return {
                x: configs.textData.textWidth,
                y: configs.textData.fontHeight
            };
        },

        draw: function (configs, ctx, contentCanvas) {
            ctx.save();

            ctx.fillStyle = configs.fillStyle;
            ctx.textAlign = "center";
            ctx.textBaseline = "middle";
            ctx.font = configs.fontSize + "px" + " " + configs.fontFamily;

            var drawPos = {
                x: configs.measureSize.x * 0.5,
                y: ctx.canvas.height * 0.5
            };
            if (contentCanvas) {
                drawPos.x += contentCanvas.width + configs.padding
            }

            ctx.fillText(configs.textData.text,
                drawPos.x,
                drawPos.y);

            ctx.restore();
        },

        drawContentCanvas: function (configs, ctx, contentCanvas) {
            if (!contentCanvas) return ctx;

            var x = 0;
            var y = ctx.canvas.height * 0.5 - contentCanvas.height * 0.5;

            ctx.drawImage(contentCanvas, x, y);
        }
    };

    dl.Notification.Factory.Text.DEFAULT_CONFIGS = {
        text: "",

        fillStyle: "black",
        fontSize: 24,
        fontFamily: "Arial",

        padding: 10
    };
});