/**
 * Created by <PERSON><PERSON>
 * Defining room password configs
 * Check password
 */

var RoomPassword = {};

RoomPassword.MIN_LENGTH = 5;
RoomPassword.MAX_LENGTH = 12;

RoomPassword.RETURN_CODES = {
    INVALID: 0,
    EMPTY: 1,
    TOO_MUCH_LENGTH: 2,
    NOT_ENOUGH_LENGTH: 3,
    CONTAIN_INVALID_CHARACTER: 4,
    VALID: 5
};

RoomPassword.checkPassword = function (input) {
    if (input == null) return RoomPassword.RETURN_CODES.INVALID;
    if (typeof input === 'undefined') return RoomPassword.RETURN_CODES.INVALID;
    if (typeof input !== 'string') return RoomPassword.RETURN_CODES.INVALID;
    if (input == '') return RoomPassword.RETURN_CODES.EMPTY;

    // Check length
    if (input.length < RoomPassword.MIN_LENGTH) return RoomPassword.RETURN_CODES.NOT_ENOUGH_LENGTH;
    if (input.length > RoomPassword.MAX_LENGTH) return RoomPassword.RETURN_CODES.TOO_MUCH_LENGTH;

    /**
     * Check with regular expression.
     * 0 to 9,
     * Uppercase character A-Z
     * Non-uppercase character a-z
     * Special Characters: #?!@$%^&*-
     */
    var regex = new RegExp('^[A-Za-z0-9#?!@$%^&*-]*$');
    var testResult = regex.test(input);
    if (!testResult) {
        return RoomPassword.RETURN_CODES.CONTAIN_INVALID_CHARACTER;
    }

    return RoomPassword.RETURN_CODES.VALID;
};

/**
 * Export module
 */
if (typeof module !== 'undefined') {
    module.exports.RoomPassword = RoomPassword;
}
