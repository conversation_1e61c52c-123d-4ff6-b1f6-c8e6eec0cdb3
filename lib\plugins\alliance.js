ig.module("plugins.alliance")
.requires(
    'plugins.handlers.dom-handler',
    'impact.timer',
    'plugins.crypto-aes'
)
.defines(function() {
    ig.Alliance = ig.Class.extend({
        NAME: "MJS-ALLIANCE",
        VERSION: "1.0.0",
        parameters: {
            "verbose": null
        },
        iconIndex:0,
        settings: {
            localStorageKey: 'mjs-svas-user-info-v1',
            gameId: '1651673491261401',
            rememberNickname: true,
            listingMaxEntries:100,
            notificationDelay:1600,
            notificationYpos:10, // percent of screen height
            iconAlliance:[],
            countryCodeList : [
                { countryName: "Andorra", alpha2: "AD", alpha3: "AND", numeric: "020" },
                {
                    countryName: "United Arab Emirates",
                    alpha2: "AE",
                    alpha3: "ARE",
                    numeric: "784",
                },
                {
                    countryName: "Antigua and Barbuda",
                    alpha2: "AG",
                    alpha3: "ATG",
                    numeric: "028",
                },
                { countryName: "Anguil<PERSON>", alpha2: "AI", alpha3: "AIA", numeric: "660" },
                { countryName: "Albania", alpha2: "AL", alpha3: "ALB", numeric: "008" },
                { countryName: "Armenia", alpha2: "AM", alpha3: "ARM", numeric: "051" },
                { countryName: "Angola", alpha2: "AO", alpha3: "AGO", numeric: "024" },
                { countryName: "Antarctica", alpha2: "AQ", alpha3: "ATA", numeric: "010" },
                { countryName: "Argentina", alpha2: "AR", alpha3: "ARG", numeric: "032" },
                {
                    countryName: "American Samoa",
                    alpha2: "AS",
                    alpha3: "ASM",
                    numeric: "016",
                },
                { countryName: "Austria", alpha2: "AT", alpha3: "AUT", numeric: "040" },
                { countryName: "Australia", alpha2: "AU", alpha3: "AUS", numeric: "016" },
                { countryName: "Aruba", alpha2: "AW", alpha3: "ABW", numeric: "533" },
                { countryName: "Åland Islands", alpha2: "AX", alpha3: "ALA", numeric: "248" },
                { countryName: "Azerbaijan", alpha2: "AZ", alpha3: "AZE", numeric: "031" },
                {
                    countryName: "Bosnia and Herzegovina",
                    alpha2: "BA",
                    alpha3: "BIH",
                    numeric: "070",
                },
                { countryName: "Barbados", alpha2: "BB", alpha3: "BRB", numeric: "052" },
                { countryName: "Bangladesh", alpha2: "BD", alpha3: "BGD", numeric: "050" },
                { countryName: "Belgium", alpha2: "BE", alpha3: "BEL", numeric: "056" },
                { countryName: "Burkina Faso", alpha2: "BF", alpha3: "BFA", numeric: "854" },
                { countryName: "Bulgaria", alpha2: "BG", alpha3: "BGR", numeric: "100" },
                { countryName: "Bahrain", alpha2: "BH", alpha3: "BHR", numeric: "048" },
                { countryName: "Burundi", alpha2: "BI", alpha3: "BDI", numeric: "108" },
                { countryName: "Benin", alpha2: "BJ", alpha3: "BEN", numeric: "204" },
                {
                    countryName: "Saint Barthélemy",
                    alpha2: "BL",
                    alpha3: "BLM",
                    numeric: "652",
                },
                { countryName: "Bermuda", alpha2: "BM", alpha3: "BMU", numeric: "060" },
                { countryName: "Brunei", alpha2: "BN", alpha3: "BRN", numeric: "096" },
                { countryName: "Bolivia", alpha2: "BO", alpha3: "BOL", numeric: "068" },
                { countryName: "Bonaire", alpha2: "BQ-BO" },
                { countryName: "Saba", alpha2: "BQ-SA" },
                { countryName: "Sint Eustatius", alpha2: "BQ-SE" },
                { countryName: "Brazil", alpha2: "BR", alpha3: "BRA", numeric: "076" },
                { countryName: "Bahamas", alpha2: "BS", alpha3: "BHS", numeric: "044" },
                { countryName: "Bhutan", alpha2: "BT", alpha3: "BTN", numeric: "064" },
                { countryName: "Botswana", alpha2: "BW", alpha3: "BWA", numeric: "072" },
                { countryName: "Belarus", alpha2: "BY", alpha3: "BLR", numeric: "112" },
                { countryName: "Belize", alpha2: "BZ", alpha3: "BLZ", numeric: "084" },
                { countryName: "Canada", alpha2: "CA", alpha3: "CAN", numeric: "124" },
                { countryName: "Cocos Islands", alpha2: "CC", alpha3: "CCK", numeric: "166" },
                {
                    countryName: "Democratis Republic of Congo",
                    alpha2: "CD",
                    alpha3: "COD",
                    numeric: "180",
                },
                {
                    countryName: "Central African Republic",
                    alpha2: "CF",
                    alpha3: "CAF",
                    numeric: "140",
                },
                { countryName: "Congo", alpha2: "CG", alpha3: "COG", numeric: "178" },
                { countryName: "Switzerland", alpha2: "CH", alpha3: "CHE", numeric: "756" },
                { countryName: "Côte d'Ivoire", alpha2: "CI", alpha3: "CIV", numeric: "384" },
                { countryName: "Cook Island", alpha2: "CK", alpha3: "COK", numeric: "184" },
                { countryName: "Chile", alpha2: "CL", alpha3: "CHL", numeric: "152" },
                { countryName: "Cameroon", alpha2: "CM", alpha3: "CMR", numeric: "120" },
                { countryName: "China", alpha2: "CN", alpha3: "CHN", numeric: "156" },
                { countryName: "Colombia", alpha2: "CO", alpha3: "COL", numeric: "170" },
                { countryName: "Costa Rica", alpha2: "CR", alpha3: "CRI", numeric: "188" },
                { countryName: "Cuba", alpha2: "CU", alpha3: "CUB", numeric: "192" },
                { countryName: "Cape Verde", alpha2: "CV", alpha3: "CPV", numeric: "132" },
                { countryName: "Curaçao", alpha2: "CW", alpha3: "CUW", numeric: "531" },
                {
                    countryName: "Christmas Island",
                    alpha2: "CX",
                    alpha3: "CXR",
                    numeric: "162",
                },
                { countryName: "Cyprus", alpha2: "CY", alpha3: "CYP", numeric: "196" },
                {
                    countryName: "Czech Republic",
                    alpha2: "CZ",
                    alpha3: "CZE",
                    numeric: "203",
                },
                { countryName: "Germany", alpha2: "DE", alpha3: "DEU", numeric: "276" },
                { countryName: "Djibouti", alpha2: "DJ", alpha3: "DJI", numeric: "262" },
                { countryName: "Denmark", alpha2: "DK", alpha3: "DNK", numeric: "208" },
                { countryName: "Dominica", alpha2: "DM", alpha3: "DMA", numeric: "212" },
                {
                    countryName: "Dominican Republic",
                    alpha2: "DO",
                    alpha3: "DOM",
                    numeric: "214",
                },
                { countryName: "Ecuador", alpha2: "EC", alpha3: "ECU", numeric: "218" },
                { countryName: "Estonia", alpha2: "EE", alpha3: "EST", numeric: "233" },
                { countryName: "Egypt", alpha2: "EG", alpha3: "EGY", numeric: "818" },
                {
                    countryName: "Western Sahara",
                    alpha2: "EH",
                    alpha3: "ESH",
                    numeric: "732",
                },
                { countryName: "Eritrea", alpha2: "ER", alpha3: "ERI", numeric: "232" },
                { countryName: "Spain", alpha2: "ES", alpha3: "ESP", numeric: "724" },
                { countryName: "Ethiopia", alpha2: "ET", alpha3: "ETH", numeric: "231" },
                { countryName: "Finland", alpha2: "FI", alpha3: "FIN", numeric: "246" },
                { countryName: "Fiji", alpha2: "FJ", alpha3: "FJI", numeric: "242" },
                {
                    countryName: "Falkland Islands",
                    alpha2: "FK",
                    alpha3: "FLK",
                    numeric: "238",
                },
                {
                    countryName: "Micronesia (Federated States of)",
                    alpha2: "FM",
                    alpha3: "FSM",
                    numeric: "583",
                },
                { countryName: "Faroe Island", alpha2: "FO", alpha3: "FRO", numeric: "234" },
                { countryName: "France", alpha2: "FR", alpha3: "FRA", numeric: "250" },
                { countryName: "Gabon", alpha2: "GA", alpha3: "GAB", numeric: "266" },
                { countryName: "England", alpha2: "GB-ENG" },
                { countryName: "Scotland", alpha2: "GB-SCT" },
                {
                    countryName: "United Kingdom",
                    alpha2: "GB-UKM",
                    alpha3: "GBR",
                    numeric: "836",
                },
                { countryName: "Wales", alpha2: "GB-WLS" },
                { countryName: "Northern Ireland", alpha2: "GB-NIR" },
                { countryName: "Grenada", alpha2: "GD", alpha3: "GRD", numeric: "308" },
                { countryName: "Georgia", alpha2: "GE", alpha3: "GEO", numeric: "268" },
                { countryName: "French Guiana", alpha2: "GF", alpha3: "GUF", numeric: "254" },
                { countryName: "Guernsey", alpha2: "GG", alpha3: "GGY", numeric: "831" },
                { countryName: "Ghana", alpha2: "GH", alpha3: "GHA", numeric: "288" },
                { countryName: "Gibraltar", alpha2: "GI", alpha3: "GIB", numeric: "292" },
                { countryName: "Greenland", alpha2: "GL", alpha3: "GRL", numeric: "304" },
                { countryName: "Gambia", alpha2: "GM", alpha3: "GMB", numeric: "270" },
                { countryName: "Guinea", alpha2: "GN", alpha3: "GIN", numeric: "324" },
                { countryName: "Guadeloupe", alpha2: "GP", alpha3: "GLP", numeric: "312" },
                {
                    countryName: "Equatorial Guinea",
                    alpha2: "GQ",
                    alpha3: "GNQ",
                    numeric: "226",
                },
                { countryName: "Greece", alpha2: "GR", alpha3: "GRC", numeric: "300" },
                {
                    countryName: "South Gerogia and the South Sandwich Islands",
                    alpha2: "GS",
                    alpha3: "SGS",
                    numeric: "239",
                },
                { countryName: "Guatemala", alpha2: "GT", alpha3: "GTM", numeric: "320" },
                { countryName: "Guam", alpha2: "GU", alpha3: "GUM", numeric: "316" },
                { countryName: "Guinea-Bissau", alpha2: "GW", alpha3: "GNB", numeric: "624" },
                { countryName: "Guyana", alpha2: "GY", alpha3: "GUY", numeric: "328" },
                { countryName: "Hong Kong", alpha2: "HK", alpha3: "HKG", numeric: "344" },
                {
                    countryName: "Heard Island and McDonald Islands",
                    alpha2: "HM",
                    alpha3: "HMD",
                    numeric: "334",
                },
                { countryName: "Honduras", alpha2: "HN", alpha3: "HND", numeric: "340" },
                { countryName: "Croatia", alpha2: "HR", alpha3: "HRV", numeric: "191" },
                { countryName: "Haiti", alpha2: "HT", alpha3: "HTI", numeric: "332" },
                { countryName: "Hungary", alpha2: "HU", alpha3: "HUN", numeric: "348" },
                { countryName: "Indonesia", alpha2: "ID", alpha3: "IDN", numeric: "360" },
                { countryName: "Ireland", alpha2: "IE", alpha3: "IRL", numeric: "372" },
                { countryName: "Isle of Man", alpha2: "IM", alpha3: "IMN", numeric: "833" },
                { countryName: "India", alpha2: "IN", alpha3: "IND", numeric: "356" },
                {
                    countryName: "British Indian Ocean Territory",
                    alpha2: "IO",
                    alpha3: "IOT",
                    numeric: "086",
                },
                { countryName: "Iraq", alpha2: "IQ", alpha3: "IRQ", numeric: "368" },
                { countryName: "Iran", alpha2: "IR", alpha3: "IRN", numeric: "364" },
                { countryName: "Iceland", alpha2: "IS", alpha3: "ISL", numeric: "352" },
                { countryName: "Italy", alpha2: "IT", alpha3: "ITA", numeric: "380" },
                { countryName: "Jersey", alpha2: "JE", alpha3: "JEY", numeric: "832" },
                { countryName: "Jamaica", alpha2: "JM", alpha3: "JAM", numeric: "388" },
                { countryName: "Jordan", alpha2: "JO", alpha3: "JOR", numeric: "400" },
                { countryName: "Japan", alpha2: "JP", alpha3: "JPN", numeric: "392" },
                { countryName: "Kenya", alpha2: "KE", alpha3: "KEN", numeric: "404" },
                { countryName: "Kyrgyzstan", alpha2: "KG", alpha3: "KGZ", numeric: "417" },
                { countryName: "Cambodia", alpha2: "KH", alpha3: "KHM", numeric: "116" },
                { countryName: "Kiribati", alpha2: "KI", alpha3: "KIR", numeric: "296" },
                { countryName: "Comoros", alpha2: "KM", alpha3: "COM", numeric: "174" },
                {
                    countryName: "Korea (the Democratic People's Republic of)",
                    alpha2: "KP",
                    alpha3: "PRK",
                    numeric: "408",
                },
                {
                    countryName: "Korea (the Republic of)",
                    alpha2: "KR",
                    alpha3: "KOR",
                    numeric: "410",
                },
                { countryName: "Kuwait", alpha2: "KW", alpha3: "KWT", numeric: "414" },
                {
                    countryName: "Cayman Islands",
                    alpha2: "KY",
                    alpha3: "CYM",
                    numeric: "136",
                },
                { countryName: "Kazakhstan", alpha2: "KZ", alpha3: "KAZ", numeric: "398" },
                {
                    countryName: "Lao People's Democratic Republic (the)",
                    alpha2: "LA",
                    alpha3: "LAO",
                    numeric: "418",
                },
                { countryName: "Lebanon", alpha2: "LB", alpha3: "LBN", numeric: "422" },
                { countryName: "Saint Lucia", alpha2: "LC", alpha3: "LCA", numeric: "662" },
                { countryName: "Liechtenstein", alpha2: "LI", alpha3: "LIE", numeric: "438" },
                { countryName: "Sri Lanka", alpha2: "LK", alpha3: "LKA", numeric: "144" },
                { countryName: "Liberia", alpha2: "LR", alpha3: "LBR", numeric: "430" },
                { countryName: "Lesotho", alpha2: "LS", alpha3: "LSO", numeric: "426" },
                { countryName: "Lithuania", alpha2: "LT", alpha3: "LTU", numeric: "440" },
                { countryName: "Luxembourg", alpha2: "LU", alpha3: "LUX", numeric: "442" },
                { countryName: "Latvia", alpha2: "LV", alpha3: "LVA", numeric: "428" },
                { countryName: "Libya", alpha2: "LY", alpha3: "LBY", numeric: "434" },
                { countryName: "Morocco", alpha2: "MA", alpha3: "MAR", numeric: "504" },
                { countryName: "Monaco", alpha2: "MC", alpha3: "MCO ", numeric: "492" },
                {
                    countryName: "Moldova (the Republic of)",
                    alpha2: "MD",
                    alpha3: "MDA",
                    numeric: "498",
                },
                { countryName: "Montenegro", alpha2: "ME", alpha3: "MNE", numeric: "499" },
                { countryName: "Saint Martin", alpha2: "MF", alpha3: "MAF", numeric: "663" },
                { countryName: "Madagascar", alpha2: "MG", alpha3: "MDG", numeric: "450" },
                {
                    countryName: "Marshall Islands (the)",
                    alpha2: "MH",
                    alpha3: "MHL",
                    numeric: "584",
                },
                {
                    countryName: "North Macedonia",
                    alpha2: "MK",
                    alpha3: "MKD",
                    numeric: "807",
                },
                { countryName: "Mali", alpha2: "ML", alpha3: "MLI", numeric: "466" },
                { countryName: "Myanmar", alpha2: "MM", alpha3: "MMR", numeric: "104" },
                { countryName: "Mongolia", alpha2: "MN", alpha3: "MNG", numeric: "496" },
                { countryName: "Macao", alpha2: "MO", alpha3: "MAC", numeric: "446" },
                {
                    countryName: "Northern Mariana Islands (the)",
                    alpha2: "MP",
                    alpha3: "MNP",
                    numeric: "580",
                },
                { countryName: "Martinique", alpha2: "MQ", alpha3: "MTQ", numeric: "474" },
                { countryName: "Mauritania", alpha2: "MR", alpha3: "MRT", numeric: "478" },
                { countryName: "Montserrat", alpha2: "MS", alpha3: "MSR", numeric: "500" },
                { countryName: "Malta", alpha2: "MT", alpha3: "MLT", numeric: "470" },
                { countryName: "Mauritius", alpha2: "MU", alpha3: "MUS", numeric: "480" },
                { countryName: "Maldives", alpha2: "MV", alpha3: "MDV", numeric: "462" },
                { countryName: "Malawi", alpha2: "MW", alpha3: "MWI", numeric: "454" },
                { countryName: "Mexico", alpha2: "MX", alpha3: "MEX", numeric: "484" },
                { countryName: "Malaysia", alpha2: "MY", alpha3: "MYS", numeric: "458" },
                { countryName: "Mozambique", alpha2: "MZ", alpha3: "MOZ", numeric: "508" },
                { countryName: "Namibia", alpha2: "NA", alpha3: "NAM", numeric: "516" },
                { countryName: "New Caledonia", alpha2: "NC", alpha3: "NCL", numeric: "540" },
                { countryName: "Niger", alpha2: "NE", alpha3: "NER", numeric: "562" },
                {
                    countryName: "Norfolk Island",
                    alpha2: "NF",
                    alpha3: "NFK",
                    numeric: "574",
                },
                { countryName: "Nigeria", alpha2: "NG", alpha3: "NGA", numeric: "566" },
                { countryName: "Nicaragua", alpha2: "NI", alpha3: "NIC", numeric: "558" },
                { countryName: "Netherlands", alpha2: "NL", alpha3: "NLD", numeric: "528" },
                { countryName: "Norway", alpha2: "NO", alpha3: "NOR", numeric: "578" },
                { countryName: "Nepal", alpha2: "NP", alpha3: "NPL", numeric: "524" },
                { countryName: "Nauru", alpha2: "NR", alpha3: "NRU", numeric: "520" },
                { countryName: "Niue", alpha2: "NU", alpha3: "NIU", numeric: "570" },
                { countryName: "New Zealand", alpha2: "NZ", alpha3: "NZL", numeric: "554" },
                { countryName: "Oman", alpha2: "OM", alpha3: "OMN", numeric: "512" },
                { countryName: "Panama", alpha2: "PA", alpha3: "PAN", numeric: "591" },
                { countryName: "Peru", alpha2: "PE", alpha3: "PER", numeric: "604" },
                {
                    countryName: "French Polyesia",
                    alpha2: "PF",
                    alpha3: "PYF",
                    numeric: "258",
                },
                {
                    countryName: "Papua New Guinea",
                    alpha2: "PG",
                    alpha3: "PNG",
                    numeric: "598",
                },
                {
                    countryName: "Phillippines (the)",
                    alpha2: "PH",
                    alpha3: "PHL",
                    numeric: "608",
                },
                { countryName: "Pakistan", alpha2: "PK", alpha3: "PAK", numeric: "586" },
                { countryName: "Poland", alpha2: "PL", alpha3: "POL", numeric: "616" },
                {
                    countryName: "Saint Pierre and Miquelon",
                    alpha2: "PM",
                    alpha3: "SPM",
                    numeric: "666",
                },
                { countryName: "Pitcairn", alpha2: "PN", alpha3: "PCN", numeric: "612" },
                { countryName: "Puerto Rico", alpha2: "PR", alpha3: "PRI", numeric: "630" },
                {
                    countryName: "Palestine, State of",
                    alpha2: "PS",
                    alpha3: "PSE",
                    numeric: "275",
                },
                { countryName: "Portugal", alpha2: "PT", alpha3: "PRT", numeric: "620" },
                { countryName: "Palau", alpha2: "PW", alpha3: "PLW", numeric: "585" },
                { countryName: "Paraguay", alpha2: "PY", alpha3: "PRY", numeric: "600" },
                { countryName: "Qatar", alpha2: "QA", alpha3: "QAT", numeric: "634" },
                { countryName: "Réunion", alpha2: "RE", alpha3: "REU", numeric: "638" },
                { countryName: "Romania", alpha2: "RO", alpha3: "ROU", numeric: "642" },
                { countryName: "Serbia", alpha2: "RS", alpha3: "SRB", numeric: "688" },
                {
                    countryName: "Russian Federation (the)",
                    alpha2: "RU",
                    alpha3: "RUS",
                    numeric: "643",
                },
                { countryName: "Rwanda", alpha2: "RW", alpha3: "RWA", numeric: "646" },
                { countryName: "Saudi Arabia", alpha2: "SA", alpha3: "SAU", numeric: "682" },
                {
                    countryName: "Solomon Islands",
                    alpha2: "SB",
                    alpha3: "SLB",
                    numeric: "090",
                },
                { countryName: "Seychelles", alpha2: "SC", alpha3: "SYC", numeric: "690" },
                { countryName: "Sudan (the)", alpha2: "SD", alpha3: "SDN", numeric: "729" },
                { countryName: "Sweden", alpha2: "SE", alpha3: "SWE", numeric: "752" },
                { countryName: "Singapore", alpha2: "SG", alpha3: "SGP", numeric: "702" },
                {
                    countryName: "Saint Helena, Ascension Island, Traistan da Cunha",
                    alpha2: "SH",
                    alpha3: "SHN",
                    numeric: "654",
                },
                { countryName: "Slovenia", alpha2: "SI", alpha3: "SVN", numeric: "705" },
                {
                    countryName: "Svalbard, Jan Mayen",
                    alpha2: "SJ",
                    alpha3: "SJM",
                    numeric: "744",
                },
                { countryName: "Slovakia", alpha2: "SK", alpha3: "SVK", numeric: "703" },
                { countryName: "Sierra Leone", alpha2: "SL", alpha3: "SLE", numeric: "694" },
                { countryName: "San Marino", alpha2: "SM", alpha3: "SMR", numeric: "674" },
                { countryName: "Senegal", alpha2: "SN", alpha3: "SEN", numeric: "686" },
                { countryName: "Somalia", alpha2: "SO", alpha3: "SOM", numeric: "706" },
                { countryName: "Suriname", alpha2: "SR", alpha3: "SUR", numeric: "740" },
                { countryName: "South Sudan", alpha2: "SS", alpha3: "SSD", numeric: "728" },
                {
                    countryName: "Sao Tome and Principe",
                    alpha2: "ST",
                    alpha3: "STP",
                    numeric: "678",
                },
                { countryName: "El Salvador", alpha2: "SV", alpha3: "SLV", numeric: "222" },
                { countryName: "Sint Maarten", alpha2: "SX", alpha3: "SXM", numeric: "534" },
                {
                    countryName: "Syrian Arab Republic (the)",
                    alpha2: "SY",
                    alpha3: "SYR",
                    numeric: "760",
                },
                { countryName: "Eswatini", alpha2: "SZ", alpha3: "SWZ", numeric: "748" },
                {
                    countryName: "Turks and Caicos Islands (the)",
                    alpha2: "TC",
                    alpha3: "TCA",
                    numeric: "796",
                },
                { countryName: "Chad", alpha2: "TD", alpha3: "TCD", numeric: "148" },
                {
                    countryName: "French Southern Territories",
                    alpha2: "TF",
                    alpha3: "ATF",
                    numeric: "260",
                },
                { countryName: "Togo", alpha2: "TG", alpha3: "TGO", numeric: "768" },
                { countryName: "Thailand", alpha2: "TH", alpha3: "THA", numeric: "764" },
                { countryName: "Tajikistan", alpha2: "TJ", alpha3: "TJK", numeric: "762" },
                { countryName: "Tokelau", alpha2: "TK", alpha3: "TKL", numeric: "772" },
                { countryName: "Timor-Leste", alpha2: "TL", alpha3: "TLS", numeric: "626" },
                { countryName: "Turkmenistan", alpha2: "TM", alpha3: "TKM", numeric: "795" },
                { countryName: "Tunisia", alpha2: "TN", alpha3: "TUN", numeric: "788" },
                { countryName: "Tonga", alpha2: "TO", alpha3: "TON", numeric: "776" },
                { countryName: "Turkey", alpha2: "TR", alpha3: "TUR", numeric: "792" },
                {
                    countryName: "Trinidad and Tobago",
                    alpha2: "TT",
                    alpha3: "TTO",
                    numeric: "780",
                },
                { countryName: "Tuvalu", alpha2: "TV", alpha3: "TUV", numeric: "798" },
                { countryName: "Taiwan", alpha2: "TW", alpha3: "TWN", numeric: "158" },
                {
                    countryName: "Tanzania, the United Republic of",
                    alpha2: "TZ",
                    alpha3: "TZA",
                    numeric: "834",
                },
                { countryName: "Ukraine", alpha2: "UA", alpha3: "UKR", numeric: "804" },
                { countryName: "Uganda", alpha2: "UG", alpha3: "UGA", numeric: "800" },
                {
                    countryName: "United States Minor Outlying Islands (the)",
                    alpha2: "UM",
                    alpha3: "UMI",
                    numeric: "581",
                },
                {
                    countryName: "United States of America",
                    alpha2: "US",
                    alpha3: "USA",
                    numeric: "840",
                },
                { countryName: "Uruguay", alpha2: "UY", alpha3: "URY", numeric: "858" },
                { countryName: "Uzbekistan", alpha2: "UZ", alpha3: "UZB", numeric: "860" },
                { countryName: "Holy See", alpha2: "VA", alpha3: "VAT", numeric: "336" },
                {
                    countryName: "Saint Vincent and the Grenadines",
                    alpha2: "VC",
                    alpha3: "VCT",
                    numeric: "670",
                },
                {
                    countryName: "Venezuela (Bolivarian Republic of)",
                    alpha2: "VE",
                    alpha3: "VEN",
                    numeric: "862",
                },
                {
                    countryName: "Virgin Islands (British)",
                    alpha2: "VG",
                    alpha3: "VGB",
                    numeric: "092",
                },
                {
                    countryName: "Virgin Islands (U.S.)",
                    alpha2: "VI",
                    alpha3: "VIR",
                    numeric: "850",
                },
                { countryName: "Vietnam", alpha2: "VN", alpha3: "VNM", numeric: "704" },
                { countryName: "Vanuatu", alpha2: "VU", alpha3: "VUT", numeric: "548" },
                {
                    countryName: "Wallis and Futuna",
                    alpha2: "WF",
                    alpha3: "WLF",
                    numeric: "876",
                },
                { countryName: "Samoa", alpha2: "WS", alpha3: "WSM", numeric: "882" },
                { countryName: "Yemen", alpha2: "YE", alpha3: "YEM", numeric: "887" },
                { countryName: "South Africa", alpha2: "ZA", alpha3: "ZAF", numeric: "710" },
                { countryName: "Zambia", alpha2: "ZM", alpha3: "ZMB", numeric: "894" },
                { countryName: "Zimbabwe", alpha2: "ZW", alpha3: "ZWE", numeric: "716" },
                { countryName: "Afghanistan", alpha2: "AF", alpha3: "AFG", numeric: "004" },
                { countryName: "Bouvet Island", alpha2: "BV", alpha3: "BVT", numeric: "074" },
            ],
            iconGift:"data:image/png;base64,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",
            iconView:"data:image/png;base64,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",
            iconLogout:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAABuwAAAbsBOuzj4gAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAO/SURBVHic7ZtNqFVVGIaf93iLaBJlmUkpZYaIIWINsyTQSQMdCEIQd9DvFUVDURQMBUXRUK74MxC80CBQEByaYunASYRFUVGZ+IMo/c0SFHkdrHPldNnr3HPOPfus1r73hT351nfe9a3nrP3D3mvJNo2SNBVYCrwCLACeoTwZOAcM2r5QYj9NKrAfHMAK4K96Yb08bgOTG2vp1VEbBiHpKPA58ERptON6BHg7Qb8BgKR+oD9FAQ36PkWnAp6rd/5YigLq+sL2khQd9wHvUDz428Ax4NsS+zdwznaZfTRVH+FKP1L/AC/a/rvH9fRcNYoBDI2HwUMAML0gfr7XhaRSbfSUamsCQOoCUmsCQOoC2pGkSZKWSfpY0pRueGYFAPgaOAF8ClyXNDBWw2wASFoIzG8IPQQckLRqLL7ZAAAejcQHJa3p1DQnAKeAS5G2vZLWdWKaDQCHNzbLCS9sirRb0oZ2fbMBAGD7IvAm8GckZaekTe14ZgUAwPZ3wCLgj0jKdklbWvXLDgCA7R+AN4BbkZStkra24pUlAADbPxIg3IykbJG0fTSfbAEA2P4ZeB24EUnZJGlnM4+sAQDY/oUwE65HUjZI2h37ffYAAGz/SoBwLZKyTtLeooZKAACwfYlwOlyJpKyRNDgyWBkAALYvEyBcjqSsknRAkoYDlQIAYPsKAULssXkAODQMoa+sQiQ9DbwHzC2rj1F0A5gZafsAmCTp/VIASHoB+Al4uAz/Luld4EJZp8CH/L8HP6yByl0D2lVZAA4Dd0ry7qYOlnINsP27pOmkvQhOA15r0n4EGILiFRtLU6zW6NZB+OT/W2RsJsxQ/WeFSFUkaQZh3VHsFngQ+Kj+hqm854AUkvQ88CUwI5Ky3/bqxkBlZoCkmYR/Pjb4fSMHDxUBIGkW8BXh3C/SHttrixqyByDpJcLgn42k7LK9Pvb7rAFImk2Y9tMiKTtsb2zmkS0ASXMI//zUSMo225tH88nyLiBpLnAWeCqS8ontba14ZQdA0jzgDPBkJGWz7R2t+mUFQNJ84DQwOZKy0faudjyzAVB/g3Oc+ODX297Trm82AIAlxB9v19re14lpTgD+jcRX297fqWk2t0Hb54GLDaG7wMqxDB7ymgEArwJvAbOAz2zHPo62rKwA2L4HnOymZzanQFmaAJC6gNSaAABcLYgv7HUhqVQDvimI90tKsX2u5+ojAFg2Iv44YS1u5TdNjfttczXb14CO19p2SYslLUrRcQ3A9hD1z0QJ9XKSXsf75ml5nG+fvw/04I/d/LylIAAAAABJRU5ErkJggg==",
            navButton:{ 
                        prev:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAE8AAABLCAYAAAAxpdqQAAAACXBIWXMAAAsSAAALEgHS3X78AAACcElEQVR4nO2c0U3DMBBAj8I/bFCYADagE5y6AWWDjgBf/DJCWcET0AUq2KBsABMUufJVoTRtknPiO/uelJ/KqqwnK3mxkpxtNhswjoOIkzBg7Zxb02CTVwMiXgHADADmADCujFoCwNQ5923y9gjS5uG4rBn2CQATkxdAxGsAePKr6oi0Ko8XQ05QIhVpDy2nNy1WXrgIeGn3Hf/iqjh5iDgN57Ou0nYUIw8RZ2GljRsMb0TW8o7kRhSylNcwN9hkJa9DbrDIQh4jN1iolhchN1iolBczNzioktdHbnAQL6/v3OAgVt5QucFBnLyhc4ODGHmpcoNDcnmpc4NDMnlScoPD4PKk5QaHQeRJzg0OvcrTkBscepGnKTc4RJWnMTc4RJGnOTc4sOTlkBscOsnLKTc4NJaXa25wOCkv99zgUCuvlNzg8E9eabnBYSev1NzgsJWHiAtbae05X61W/pz2om3iAvgahauo0YGRSeuOyWPg5b2rnX1ivLzX8HS30ZKRf5/APxYPAG8mrx1/XiUIdxczu49txPLgexi2GdCIw/Kq2N5dLaflEUGiX4m3qWYrjObyCNtA2NFeHhEkzgreUOgujyh4/48vjygwc+LJIwrKnPjyqmSeOf3KIzLNnGHkEZllzrDyiEwyJ408QnnmpJVHKM0cGfIIZZkjS14VBZkjVx4hOHPkyyMEZo4eeYSgzNEnjxCQOXrlEQkzR788IkHm5COvykCZk6c8oufMyVse0VPmlCGPiJw5ZckjImVOmfIIZuaULY/omDnPJm+PFplzY/JqOJI5P/5359zC5J0gXKH9cQcAHwCw2H6lGwB+AfVFI3y45+R+AAAAAElFTkSuQmCC",
                        next:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAE8AAABLCAYAAAAxpdqQAAAACXBIWXMAAAsSAAALEgHS3X78AAAClElEQVR4nO2c4W3bMBBGPwf+72yQbtBu0CxgIhskI3SDeoNmBHmCBlwg7gbNBHE3aCZwwYZXyLKtSjpR4h3vAf4nG8LDyXoSQC4OhwMI59wHAOED7/0ORit/5TnnrgE8AfhcO/gXgEcAlff+t2k8ZbFer4O4MGUfLxzzFiU+msRjrgDctYgLrAB8BbB3zlXx0i4e1OR1IUi8B/BqEt8J8q4HfI8k7pxztylOTAJXzHMMN5jnKLHrBKuBK68u8btzLvwvPpQiL9xtd41EGYMiMmesyWtyA+BbvENvYkeqI5U8QnXmpJZHqMycqeTVUZM5c8gjxGfOnPIIsZmTKlU4iMmcHCaviZjMyVEekX3m5CyPyDZzJMirk1XmSJNHZJE5UuURs2ZOjqnCYdLMkT55TSbNHG3yiEkyR6s8ImnmaJdXZ/TMKUkeMVrmlCiPYGeOtlTh0DtzSp68Jr0zx+Sd0jlzTN5l/ps5Jq8bZzPH5PWDMqeCyRvMfWhEkzecLyaPgcljYPKGszN5w3gJj3Imrz9bALfh+Xcp7cxn4q320mBPp2Dy2mldwGPyzhNeT22891XbQSbvmJc4Za3SCJP3zo84ab1WepYubxtvAoOWx5Yqbxsnbd/h2IuUJO9sbnAoQV6y9cKa5XXKDQ4a5fXKDQ6a5A3KDQ4a5LFyg4NkeaPkBgdp8kbPDQ5S5GW5PUnu8pLnBodc5U2WGxxykzd5bnDIRd5sucFhbnmz5waHOeRllRscppSnbje0KeRlnRscUsoTkRscUsgTlRscxpQnMjc4jCFPdG5wGCpPTW5wWMbNVruuALLNV2sEeVVctNGG2tzgQPskP8SJWjV+S31ucPi3Q3dc4RIkfgLwM1zOtkt3CwD+ABEeSEA5KkhQAAAAAElFTkSuQmCC",    
                      },
            bannedImg:"data:image/png;base64,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",
            strings: {
                reCaptchaBranding: "Protected by reCAPTCHA",                
                contentLoading:"Please wait...",
                contentError: "An error occurred while loading the data, please try again later.",

                allianceNameNotificationInvalid: "Invalid alliance name. Please use letters or numbers only.", // Ben edited
                allianceNameNotificationMinLength: "Invalid alliance name, please use at least {minLength} letters.", // Ben edited
                allianceNameNotificationMaxLength: "Invalid alliance name, please use maximum {maxLength} letters.", // Ben edited
                allianceNameNotificationEmpty: "Invalid alliance name, it cannot be empty.",
                allianceNameNotificationAnother: "Please choose another alliance name.",

                shortcodeNotificationInvalid: "Invalid short code. Please use letters or numbers only.", // Ben edited
                shortcodeNotificationFixedLength: "Invalid short code length. Please use exactly {fixedLength} characters.", // Ben edited
                shortcodeNotificationEmpty: "Invalid short code, it cannot be empty.", // Ben edited

                leaveMessage:"You left the alliance", // Ben edited
                noCurrentGifts:"You do not have gifts to claim.", // Ben edited
                noMember:"You need to find another member to transfer ownership rights of this alliance.", // Ben edited

                passwordNotAllowed: "Invalid password. Please use at least 4 characters.",

                listingText: {
                    title:"Alliance Listing",
                    code:"Code",
                    name:"Name",
                    members:"Members",
                    experience:"Experience",
                    buttons:{
                        mostExp:'Most Experience',
                        leastExp:'Least Experience',
                        oldest:'Oldest Alliances',
                        fresh:'Latest Alliances',
                    }
                },

                listingButton:{
                    search:"Search Alliance",
                    newalliance:"Create New Alliance",        
                },

                searchingText: {
                    title:"Search",
                    code:"Code",
                    name:"Name",
                    members:"Members",
                    experience:"Experience",
                    placeholder:'Type alliance shortcode or name to search',
                    searchButton:'Search',
                },
                newAlliance: {
                    title:"Create Your Alliance",
                    name:"Alliance Name",
                    code:"Shortcode",
                    icon:"Icon",
                    description:"Description",
                    password:"Password",
                    createButton:"Create",
                },

                readAlliance: {
                    title:"Alliance",
                    name:"Name",
                    code:"Shortcode",
                    icon:"Icon",
                    description:"Description",
                    password:"Password",
                    kickMessage: "You kicked the player out of the alliance", // Ben edited
                    listingMember: {
                        members:"Members",
                        name:"Name",
                        experience:"Experience",
                        kick:"Kick",
                    },
                    buttons:{
                        gift:"Gifts", // Ben edited
                        join:"Join",
                        leave:"Leave",
                        update:"Update",
                        transfer:"Transfer",
                        list:"List",
                    },
                },

                joinAlliance: {
                    title:"Join Alliance",
                    placeholder:"Enter Password",
                    submitbutton:'Join',
                },
                giftText: {
                    title:"Alliance Gifts",
                    buttonClaim:"Claim",
                },

            },       

            popupContainterId: "ajaxbar",

            allianceName: {
                minLength: 5, 
                maxLength: 12
            },

            allianceShortCode: {
                fixedLength:4
            },

            listAlliance: {
                url: "https://svas-consumer.marketjs-cloud.com/api/alliance/list",
                method: "GET",
                timeout: 5000
            },

            searchAlliance: {
                url: "https://svas-consumer.marketjs-cloud.com/api/alliance/list",
                method: "GET",
                timeout: 5000
            },

            createNewAlliance: {
                url: "https://svas-consumer.marketjs-cloud.com/api/alliance/create",
                method: "POST",
                timeout: 5000
            },

            readAlliance: {
                url: "https://svas-consumer.marketjs-cloud.com/api/alliance/read",
                method: "GET",
                timeout: 5000
            },

            leaveAlliance: {
                url: "https://svas-consumer.marketjs-cloud.com/api/alliance/leave",
                method: "POST",
                timeout: 5000
            },

            joinAlliance: {
                url: "https://svas-consumer.marketjs-cloud.com/api/alliance/join",
                method: "POST",
                timeout: 5000
            },

            updateAlliance: {
                url: "https://svas-consumer.marketjs-cloud.com/api/alliance/update",
                method: "POST",
                timeout: 5000
            },

            giftListAlliance: {
                url: "https://svas-consumer.marketjs-cloud.com/api/alliance_gifts/list",
                method: "GET",
                timeout: 5000
            },

            giftClaimAlliance: {
                url: "https://svas-consumer.marketjs-cloud.com/api/user/alliance_gifts/claim",
                method: "POST",
                timeout: 5000
            },

            transferAlliance: {
                url: "https://svas-consumer.marketjs-cloud.com/api/alliance/transfer",
                method: "POST",
                timeout: 5000
            },

        },

        isUserAuthenticated: false,
        userInfo: {
            id: null,
            nickname: null,
            pincode: null,
        },        

        reCaptcha: {
            loaded: null,
            url: "https://www.google.com/recaptcha/api.js",
            siteKey: "6LeTWtweAAAAAOeZKAcG7GumhqIqMEsZ2IFZp-3k"
        },
        divElements:[],
        init: function(settings){
            ig.merge(this.settings, settings);

            // this.settings.localStorageKey=ig.game.localStorageKey;
            // alias
            this.setAlias();

            // get URL parameters
            this.getUrlParameters();

            this.initReCaptcha();

            this.updateGameId(this.settings.gameId);
            this.initAllianceIcon();

            this.ready();

            return this;
        },
        initAllianceIcon:function(){
            for(var i=0;i<this.settings.countryCodeList.length;i++){
                this.settings.iconAlliance.push('media/graphics/sprites/allianceIcon/'+this.settings.countryCodeList[i].alpha2+'.svg');
            }
        },
        ready: function() { 
            console.log("Initialized Alliance");
            this.loadUserInfo();
            this.notifPosTop = this.settings.notificationYpos;

            setTimeout(function(){
                this.updateOrientation();
            }.bind(this),1000);
        },
        setAlias: function() {
            ig.alliance = this;
            ig.global.alliance = window.alliance = this;
        },

        getQueryVariable: function(variable) {
            var hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');

            for (var i = 0; i < hashes.length; i++) {
                var match = hashes[i].match(/([^=]+?)=(.+)/);

                if (match) {
                    var key = decodeURIComponent(match[1]),
                        value = decodeURIComponent(match[2]);

                    if (variable === key) {
                        return value;
                    }
                }
            }
        },

        getUrlParameters: function() {
            for (var key in this.parameters) {
                if (this.parameters.hasOwnProperty(key)) {
                    this.parameters[key] = this.getQueryVariable(key);
                }
            }
        },

        showMainWindow:function(){
            this.loadUserInfo();
            console.log(this.userInfo);
            if(!this.notificationStarted){
                this.notificationStarted = true;
                this.loopNotification();
            }

            if(this.userInfo.alliance!="" && this.userInfo.alliance!=null){
                this.viewAlliance(this.userInfo.alliance,'mainWindow');
            }else{
                this.showAllianceList();
            }            
        },
        initReCaptcha: function(){
            var script = document.createElement("script");
            script.src = this.reCaptcha.url + "?render=" + this.reCaptcha.siteKey;
            script.onload = function(){
                this.log("Protected by reCAPTCHA");
                this.reCaptcha.loaded = true;
            }.bind(this);
            document.head.appendChild(script);
        },

        loadUserInfo: function() {
            if (this.settings.rememberNickname !== true) {
                return false;
            }
            var storage = ig.game.io.storage;
            var storedUserInfo = storage.get(this.settings.localStorageKey);

            if ( storedUserInfo !== null && typeof(storedUserInfo) !== "undefined" ) {
                this.userInfo.id = storedUserInfo.id;
                this.userInfo.nickname = storedUserInfo.nickname;
                this.userInfo.pincode = storedUserInfo.pincode;
                this.userInfo.alliance = storedUserInfo.alliance || ig.game.svasData.alliance;
            }

            if(typeof(this.userInfo.alliance)=='undefined') this.userInfo.alliance="";

            // try {
            //     if (this.settings.callbackFunctions.onUserInfoLoaded !== null && typeof (this.settings.callbackFunctions.onUserInfoLoaded) === "function") {
            //         this.settings.callbackFunctions.onUserInfoLoaded(this.userInfo);
            //     }
            // } catch (error) {
            //     console.log("Error in callback function onUserInfoLoaded: " + error);
            // }
        },

        saveUserInfo: function() {

            if (this.settings.rememberNickname !== true) {
                return false;
            }

            var storage = ig.game.io.storage;
            var userInfoString = this.userInfo;
            storage.set(this.settings.localStorageKey, userInfoString);

            // try {
            //     if (this.settings.callbackFunctions.onUserInfoUpdated !== null && typeof (this.settings.callbackFunctions.onUserInfoUpdated) === "function") {
            //         this.settings.callbackFunctions.onUserInfoUpdated(this.userInfo);
            //     }
            // } catch (error) {
            //     this.log("Error in callback function onUserInfoUpdated: " + error);
            // }

            ig.game.client.saveGameData();
        },

        deleteUserInfo: function() {
            this.userInfo.id = 0;
            this.userInfo.nickname = null;
            this.userInfo.pincode = null;
            this.userInfo.alliance = null;

            ig.game.svasData.coins = 0; 
            ig.game.svasData.gems = 0; 
            ig.game.svasData.game_id = 0; 
            ig.game.svasData.uid = 0; 
            ig.game.svasData.nickname = ''; 

            ig.game.svasData.alliance = ''; 
            ig.game.svasData.allianceName = ''; 
            ig.game.svasData.allianceIcon = null; 
            ig.game.svasData.allianceShortName = ''; 
            setTimeout(function(){
                this.updateOrientation();
            }.bind(this),1000);

            this.saveUserInfo();
        },
        update:function(){

        },
        notificationQueue:[],
        notificationShowStatus:false,
        notificationStarted:false,
        loopNotification:function(){

            if(!this.notificationStarted) return;

            this.update();
            this.fadeOutNotification();
            for(var i=0;i<this.notificationQueue.length;i++){
                if(this.notificationQueue[i]!=='' && !this.notificationShowStatus){
                    this.notificationDiv(this.notificationQueue[i]);
                    this.notificationQueue[i]='';
                    break;
                }
            }

            var temp=[];
            for(var i=0;i<this.notificationQueue.length;i++){
                if(this.notificationQueue[i]!==''){
                    temp.push(this.notificationQueue[i]);
                }
            }

            setTimeout(function(){
                this.loopNotification();
            }.bind(this),50);
        },
        notificationDiv:function(message){
            this.notificationShowStatus = true;
            this.fadeOutStatus = false;
            var containerDivId = "alliance-notification-container"; 
            var divElementContent = '\
                <div id="box-notification" class="alliance-box-notification" style="top:'+this.notifPosTop+'%">\
                    <div class="lable" id="notification-text" name="notification-text">'+message+'</div>\
                </div>\
            ';

            this.createPopupDivNotification(containerDivId, "allianceNotification", divElementContent);
            ig.game.playNotification();
            setTimeout(function(){
                this.notifAlpha = 1;
                this.fadeOutStatus=true;
            }.bind(this),this.settings.notificationDelay);            

        },
        fadeOutStatus:false,
        notifAlpha:1,
        notifDiff :0.05,
        fadeOutNotification:function(){
            if(!this.fadeOutStatus) return;
            this.notifAlpha -=this.notifDiff;
            this.notifDiff +=0.03;
            this.notifPosTop -=0.2;
            var notifContainer= document.getElementById("alliance-notification-container");
            var notifBox= document.getElementById("box-notification");

            if(notifContainer) notifContainer.style="opacity:"+this.notifAlpha;
            if(notifBox) notifBox.style="top:"+this.notifPosTop+"%";

            if(this.notifAlpha<=0){
                if(notifContainer) document.getElementById("alliance-notification-container").remove();
                if(notifContainer) this.divElements["allianceNotification"]=null;
                if(notifContainer) this.notificationShowStatus = false;
                this.fadeOutStatus = false;
                this.notifAlpha =1;
                this.notifDiff =0.05;
                this.notifPosTop = this.settings.notificationYpos;
            }
        },
        showNotification:function(message){
            this.notificationQueue.push(message);
        },
        createPopupDivNotification: function(divId, divElementName, divContent) {
            var existingDivElement = ig.domHandler.getElementById("#" + divId);
            if (existingDivElement !== null && typeof(existingDivElement) !== "undefined") {
                return existingDivElement; 
            }
            if ( this.divElements[divElementName] !== null && typeof (this.divElements[divElementName]) !== "undefined" ) {
                return this.divElements[divElementName];
            }

            var parentContainerElement = ig.domHandler.getElementById("#" + this.settings.popupContainterId);
            var newDiv = ig.domHandler.create('div');
            ig.domHandler.attr(newDiv, "id", divId);
            ig.domHandler.attr(newDiv, "class", "alliance-notification-container");
            ig.domHandler.appendChild(parentContainerElement, newDiv);

            ig.domHandler.html(newDiv, divContent);
            this.divElements[divElementName] = newDiv;
            return newDiv;
        },



        showAllianceList: function() {

            if (this.divElements["allianceListing"] === null || typeof(this.divElements["allianceListing"]) === "undefined" ) {
                this.createAllianceListDiv();                 
                ig.domHandler.show(this.divElements["allianceListing"]);
            }
            else {
                ig.domHandler.show(this.divElements["allianceListing"]);
                this.updateAllianceList(0);
            }

        },
        hideAllianceListing: function() {
            if (this.divElements["allianceListing"] !== null && typeof(this.divElements["allianceListing"]) !== "undefined" ) {
                ig.domHandler.hide(this.divElements["allianceListing"]);
            }
            this.notificationStarted = false;
        },


        showAllianceSearch: function() {
            this.hideAllianceListing();

            setTimeout(function(){
                if(!this.notificationStarted){
                    this.notificationStarted = true;
                    this.loopNotification();
                }
            }.bind(this),1000);

            if (this.divElements["allianceSearch"] === null || typeof(this.divElements["allianceSearch"]) === "undefined" ) {
                this.createAllianceSearchDiv();                 
                ig.domHandler.show(this.divElements["allianceSearch"]);
            }
            else {
                ig.domHandler.show(this.divElements["allianceSearch"]);
            }
            ig.game.playButton();

        },
        hideAllianceSearch: function() {

            if (this.divElements["allianceSearch"] !== null && typeof(this.divElements["allianceSearch"]) !== "undefined" ) {
                this.showAllianceList();
                ig.domHandler.hide(this.divElements["allianceSearch"]);
            }
        },


        showAllianceCreate: function() {
            ig.game.playButton();
            this.hideAllianceListing();
            setTimeout(function(){
                if(!this.notificationStarted){
                    this.notificationStarted = true;
                    this.loopNotification();
                }
            }.bind(this),1000);

            if (this.divElements["allianceCreate"] === null || typeof(this.divElements["allianceCreate"]) === "undefined" ) {
                this.createAllianceNewDiv();                 
                ig.domHandler.show(this.divElements["allianceCreate"]);
            }else {
                ig.domHandler.show(this.divElements["allianceCreate"]);
            }

            this.iconIndex=0;

            setTimeout(function(){               
                document.getElementById('alliance-name-new').value='';
                document.getElementById('alliance-shortcode-new').value='';
                document.getElementById('alliance-description-new').value='';
                document.getElementById('alliance-password-new').value='';
                document.getElementById('alliance-use-password-new').checked=true;
            }.bind(this),1000);


        },
        hideAllianceCreate: function() {

            if (this.divElements["allianceCreate"] !== null && typeof(this.divElements["allianceCreate"]) !== "undefined" ) {
                this.showAllianceList();
                ig.domHandler.hide(this.divElements["allianceCreate"]);
            }
        },


        createPopupDiv: function(divId, divElementName, divContent, callback) {
            var existingDivElement = ig.domHandler.getElementById("#" + divId);
            if (existingDivElement !== null && typeof(existingDivElement) !== "undefined") {
                return existingDivElement; 
            }
            if ( this.divElements[divElementName] !== null && typeof (this.divElements[divElementName]) !== "undefined" ) {
                return this.divElements[divElementName];
            }

            var parentContainerElement = ig.domHandler.getElementById("#" + this.settings.popupContainterId);
            var newDiv = ig.domHandler.create('div');
            ig.domHandler.attr(newDiv, "id", divId);
            ig.domHandler.attr(newDiv, "class", "alliance-overlay-container");
            ig.domHandler.appendChild(parentContainerElement, newDiv);

            ig.domHandler.html(newDiv, divContent);
            this.divElements[divElementName] = newDiv;
            if (typeof(callback) === "function") {
                callback(newDiv);
            }
            return newDiv;
        },
        btTransferProp:{style:'',disabled:null,class:null},
        updateOrientation:function(){
            // console.log('alliance updateOrientation');

            var xx=window.innerWidth;
            var yy=window.innerHeight;
            if(ig.ua.mobile){
                if(xx<yy){
                    var expPadding="text-align:right;padding-left:12px;";
                    var buttonMarginB="margin-top:10px;margin-bottom:18px";
                    var contentHeight='overflow-y: scroll;scrollbar-width: auto;width:100%;height:400px;max-height:420px;';                    
                    var windowSize="min-height:420px;";
                    var windowReadSize="min-height: 420px;overflow-y: scroll;scrollbar-width: auto;max-width: 800px;";
                    $(".alliance-name-long").css("display", "none");
                    $(".alliance-name-short").css("display", "block");

                    if(document.getElementById("box-listing")) document.getElementById("box-listing").style = "top:10%";                                    

                    if(document.getElementById("bt-new-div")) document.getElementById("bt-new-div").style = "";                                    
                    if(document.getElementById("bt-search-div")) document.getElementById("bt-search-div").style = "";                                    
                }else{
                    var expPadding="text-align:right;";
                    var buttonMarginB="margin-top:0px;margin-bottom:0px";
                    var contentHeight='overflow-y: scroll;scrollbar-width: auto;scrollbar-width: auto;width:100%;height:180px;max-height:220px;';                    
                    var windowSize="min-height:230px;overflow-y: scroll;scrollbar-width: auto;";
                    var windowReadSize="min-height: 2400px;overflow-y: scroll;scrollbar-width: auto;max-width: 600px;";
                    $(".alliance-name-short").css("display", "none");
                    $(".alliance-name-long").css("display", "block");

                    if(document.getElementById("box-listing")) document.getElementById("box-listing").style = "top:44%";                                    

                    if(document.getElementById("bt-new-div")) document.getElementById("bt-new-div").style = "margin-top:-10px";                                    
                    if(document.getElementById("bt-search-div")) document.getElementById("bt-search-div").style = "margin-top:-10px";                                    

                }

                if(document.getElementById("button-transfer")) document.getElementById("button-transfer").style = "margin-top:-6px;font-size:12px;min-height:24px;";                                    
                if(document.getElementById("button-transfer")) document.getElementById("button-transfer").disabled = this.btTransferProp.disabled;                                    
                if(document.getElementById('box-read')) document.getElementById('box-read').style=windowReadSize;

            }else{
                var expPadding="text-align:right;";
                var buttonMarginB="margin-top:0px;margin-bottom:0px";
                var contentHeight='overflow-y: scroll;width:100%;height:230px;max-height:360px;';
                var windowSize="min-height:420px;";

                if(document.getElementById("button-transfer")) document.getElementById("button-transfer").style = "";                                    
                if(document.getElementById("button-transfer")) document.getElementById("button-transfer").disabled = this.btTransferProp.disabled;                                    

                $(".alliance-name-short").css("display", "none");
                $(".alliance-name-long").css("display", "block");

                if(document.getElementById("bt-new-div")) document.getElementById("bt-new-div").style = "";                                    
                if(document.getElementById("bt-search-div")) document.getElementById("bt-search-div").style = "";                                    
            }

            if(ig.game.svasData.uid!=0){
                if(document.getElementById("button-transfer") && !this.btTransferProp.disabled) document.getElementById("button-transfer").className = "alliance-readform-button";                                    

                if(document.getElementById("register-text-link")) document.getElementById("register-text-link").style = "display:none;";                                    
                if(document.getElementById("register-text-link-parent")) document.getElementById("register-text-link-parent").style = "display:none;";                                    

                if(document.getElementById("create-new-alliance")){
                    document.getElementById("create-new-alliance").disabled = false;                
                    document.getElementById("create-new-alliance").style = "margin-left: 26%;";                                                    
                    document.getElementById("create-new-alliance").className = "alliance-submit-button";                                
                }
            }else{  
                if(ig.ua.mobile){
                    if(xx<yy){
                        if(document.getElementById("register-text-link")) document.getElementById("register-text-link").style = "display: block;text-align: center;background-color: white;padding: 10px;margin-top: 20px;border-radius: 10px;margin-right: 12px;";                                    
                        if(document.getElementById("register-text-link-parent")) document.getElementById("register-text-link-parent").style = "display:block;text-align:center";                                    
                    }else{
                        if(document.getElementById("register-text-link")) document.getElementById("register-text-link").style = "display: block;text-align: center;background-color: white;padding: 10px;margin-top: -13px;border-radius: 10px;margin-right: 12px;";                                    
                        if(document.getElementById("register-text-link-parent")) document.getElementById("register-text-link-parent").style = "display:nonde;";                                    
                    }
                }else{
                    if(document.getElementById("register-text-link")) document.getElementById("register-text-link").style = "display:block;text-align:center";                                    
                    if(document.getElementById("register-text-link-parent")) document.getElementById("register-text-link-parent").style = "display:block;text-align:center";                                    
                }

                if(document.getElementById("create-new-alliance")){
                    document.getElementById("create-new-alliance").disabled = true;                
                    document.getElementById("create-new-alliance").style = "pointer-events: none;background: #aaaaaa;border-style: solid;border-color: #808080;border-width: 2px;margin-left: 26%;";                                    
                }

            }

            // if(this.userInfo.id==0){
            //     if(document.getElementById('InputTextLayer_PlayerName')) document.getElementById('InputTextLayer_PlayerName').disabled=true;                    
            // }else{
            //     if(document.getElementById('InputTextLayer_PlayerName')) document.getElementById('InputTextLayer_PlayerName').disabled=false;                    
            // }


            if(document.getElementById('buttonSort')) document.getElementById('buttonSort').style=buttonMarginB;
            if(document.getElementById('expTitle')) document.getElementById('expTitle').style=expPadding;
            if(document.getElementById('expTitle2')) document.getElementById('expTitle2').style=expPadding;
            if(document.getElementById('alliance-listing-contents')) document.getElementById('alliance-listing-contents').style=contentHeight;
            if(document.getElementById('box-create')) document.getElementById('box-create').style=windowSize;

        },
        createAllianceListDiv: function(closecallback) {

            var xx=window.innerWidth;
            var yy=window.innerHeight;
            if(ig.ua.mobile){
                if(xx<yy){
                    var expPadding="text-align:right;padding-left:12px;";
                    var buttonMarginB="margin-top:10px;margin-bottom:18px";
                    var contentHeight='overflow-y: scroll;width:100%;height:400px;max-height:420px;';                    
                    var btfontsize='font-size:12px';
                }else{
                    var expPadding="text-align:right;";
                    var buttonMarginB="margin-top:0px;margin-bottom:0px";
                    var contentHeight='overflow-y: scroll;width:100%;height:180px;max-height:220px;';                    
                    var btfontsize='font-size:12px';
                }
            }else{
                var expPadding="text-align:right;";
                var buttonMarginB="margin-top:0px;margin-bottom:0px";
                var contentHeight='overflow-y: scroll;width:100%;height:260px;max-height:360px;';
                var btfontsize='';
            }

            var containerDivId = "alliance-listing-container", 
                boxDivId = "box-listing",
                closeDivId = "alliance-listing-close",
                contentDivId = "alliance-listing-contents";
            var divElementContent = '\
                <div id="' + boxDivId + '" class="alliance-box alliance-box-leaderboard alliance-box-center">\
                    <a>\
                        <div id="' + closeDivId + '" class="alliance-close-button"></div>\
                    </a>\
                    <div class="section group" style="margin-top: 4px;">\
                        <div class="col span_12_of_12 alliance-box-header">' + this.settings.strings.listingText.title + '</div>\
                    </div>\
                    <div class="section group">\
                    <div class="col span_12_of_12" style="'+buttonMarginB+'" id="buttonSort">\
                        <div class="alliance-column" style="width:25%">\
                            <div id="button-most" class="alliance-listing-order-button" type="button" onclick="ig.alliance.updateAllianceList(1)" ><div style="padding-top:5px;padding-bottom:5px;'+btfontsize+'">' + this.settings.strings.listingText.buttons.mostExp + '</div></div>\
                        </div>\
                        <div class="alliance-column" style="width:25%">\
                            <div id="button-least" class="alliance-listing-order-button" type="button" onclick="ig.alliance.updateAllianceList(2)" ><div style="padding-top:5px;padding-bottom:5px;'+btfontsize+'">' + this.settings.strings.listingText.buttons.leastExp + '</div></div>\
                        </div>\
                        <div class="alliance-column" style="width:25%">\
                            <div id="button-oldest" class="alliance-listing-order-button" type="button" onclick="ig.alliance.updateAllianceList(3)" ><div style="padding-top:5px;padding-bottom:5px;'+btfontsize+'">' + this.settings.strings.listingText.buttons.oldest + '</div></div>\
                        </div>\
                        <div class="alliance-column" style="width:25%">\
                            <div id="button-fresh" class="alliance-listing-order-button" type="button" onclick="ig.alliance.updateAllianceList(4)" ><div style="padding-top:5px;padding-bottom:5px;'+btfontsize+'">' + this.settings.strings.listingText.buttons.fresh + '</div></div>\
                        </div>\
                    </div>\
                    </div>\
                    <div class="section group" style="margin-right: 0px;">\
                            <div class="col span_1_of_12 header-color" >&nbsp</div>\
                            <div class="col span_1_of_12 header-color" >&nbsp;</div>\
                            <div class="col span_2_of_12 header-color"><span style="margin-left:10px;">' + this.settings.strings.listingText.code + '</span></div>\
                            <div class="col span_3_of_12 header-color">' + this.settings.strings.listingText.name + '</div>\
                            <div class="col span_2_of_12 header-color" style="text-align:center">' + this.settings.strings.listingText.members + '</div>\
                            <div class="col span_2_of_12 header-color" style="'+expPadding+'" id="expTitle">' + this.settings.strings.listingText.experience + '</div>\
                            <div class="col span_2_of_12 header-color" style="text-align:center">&nbsp;</div>\
                    </div>\
                    <div id="' + contentDivId + '" style="'+contentHeight+'">' + this.settings.strings.contentLoading + '</div>\
                    <div class="register-submit" style="margin-top:10px;">\
                            <div class="alliance-column alliance-span-5-of-10" id="bt-new-div">\
                                <div id="create-new-alliance" onclick="ig.alliance.showAllianceCreate()" type="button" class="alliance-submit-button" style="margin-left: 26%;"><div style="padding:5px;">' + this.settings.strings.listingButton.newalliance + '</div></div>\
                            </div>\
                            <div class="alliance-column alliance-span-5-of-10" id="bt-search-div">\
                                <div id="search-alliance" type="button" onclick="ig.alliance.showAllianceSearch()" class="alliance-submit-button" style="margin-left: 18%;"><div style="padding:5px;">' + this.settings.strings.listingButton.search + '</div></div>\
                            </div>\
                    </div>';
            if(ig.game.svasData.uid==0){
                 divElementContent +='<div id="register-text-link-parent">&nbsp;</div><div id="register-text-link" style="text-align: center;">Want to compete in the leaderboards? <div class="svas-a-link divlink" onclick="ig.alliance.registerSvasWindow()" style="display:inline;">Register Now</div></div>';
            }

            divElementContent +='<div>&nbsp;</div>\
                </div>\
            ';

            if(ig.game.svasData.uid!=0){
                if(document.getElementById("button-transfer")) document.getElementById("button-transfer").className = "alliance-readform-button";                                    
            }


            this.createPopupDiv(containerDivId, "allianceListing", divElementContent);
            // console.log('createAllianceListDiv');

            // elements
            var closeButtonElement = ig.domHandler.getElementById("#" + closeDivId);

            // event - close button
            if (closeButtonElement !== null && typeof(closeButtonElement) !== "undefined") {
                ig.domHandler.addEvent(closeButtonElement, "click", function(event){
                    if(typeof(closecallback)=='function'){
                        closecallback();
                    }
                    ig.game.playButton();
                    this.hideAllianceListing();
                }.bind(this), false);
            }

            this.updateAllianceList(0);
            this.updateOrientation();
        },
        registerSvasWindow:function(){
            ig.game.playButton();
            this.hideAllianceListing();
            try {
                ig.svas.showUserRegistration();
            }catch(e){}
        },
        createAllianceSearchDiv: function() {
            var xx=window.innerWidth;
            var yy=window.innerHeight;
            if(ig.ua.mobile && xx<yy){
                var expPadding="text-align:right;padding-left:12px;";
            }else{
                var expPadding="text-align:right;";
            }

            var containerDivId = "alliance-searching-container", 
                boxDivId = "box-searching",
                closeDivId = "alliance-searching-close",
                contentDivId = "alliance-searching-contents";
            var divElementContent = '\
                <div id="' + boxDivId + '" class="col alliance-box alliance-box-leaderboard alliance-box-center">\
                    <a>\
                        <div id="' + closeDivId + '" class="alliance-close-button"></div>\
                    </a>\
                    <div class="alliance-box-header">' + this.settings.strings.searchingText.title + '</div>\
                    <div class="register-submit">\
                        <div class="alliance-span-10-of-10" style="margin-left:6px;">\
                            <input autofocus type="text" style="max-width:60%" id="search-keyword" name="search-keyword" class="alliance-box-form-input" placeholder="' + this.settings.strings.searchingText.placeholder + '" autocomplete="off" autocomplete="off" >\
                            <div id="search-button" type="button" class="alliance-search-button" onclick="ig.alliance.doSearching()" >' + this.settings.strings.searchingText.searchButton + '</div>\
                        </div>\
                    </div>\
                    <div class="section group" style="margin-right: 18px;">\
                            <div class="col span_1_of_12 header-color" >&nbsp</div>\
                            <div class="col span_1_of_12 header-color" >&nbsp;</div>\
                            <div class="col span_2_of_12 header-color"><span style="margin-left:10px;">' + this.settings.strings.searchingText.code + '</span></div>\
                            <div class="col span_3_of_12 header-color">' + this.settings.strings.searchingText.name + '</div>\
                            <div class="col span_2_of_12 header-color" style="text-align:center">' + this.settings.strings.searchingText.members + '</div>\
                            <div class="col span_2_of_12 header-color" style="'+expPadding+'" id="expTitle2">' + this.settings.strings.searchingText.experience + '</div>\
                            <div class="col span_2_of_12 header-color" style="text-align:center">&nbsp;</div>\
                    </div>\
                    <div id="' + contentDivId + '" style="overflow-y: scroll;height:390px;max-height:420px;width:100%"></div>\
                </div>\
            ';
            this.createPopupDiv(containerDivId, "allianceSearch", divElementContent);
            // console.log('createAllianceListDiv');

            // elements
            var closeButtonElement = ig.domHandler.getElementById("#" + closeDivId);

            // event - close button
            if (closeButtonElement !== null && typeof(closeButtonElement) !== "undefined") {
                ig.domHandler.addEvent(closeButtonElement, "click", function(event){
                    ig.game.playButton();
                    this.hideAllianceSearch();
                }.bind(this), false);
            }
        },


        createAllianceNewDiv: function() {

            var xx=window.innerWidth;
            var yy=window.innerHeight;

            if(ig.ua.mobile){
                if(xx<yy){
                    var windowSize="min-height:420px;";
                }else{
                    var windowSize="min-height:230px;overflow-y: scroll;";
                }
            }else{
                    var windowSize="min-height:420px;";
            }


            var containerDivId = "alliance-create-container", 
                boxDivId = "box-create",
                closeDivId = "alliance-create-close";
            var divElementContent = '\
                <div id="' + boxDivId + '" class="alliance-box alliance-box-create-new alliance-box-center" style="'+windowSize+'">\
                    <a>\
                        <div id="' + closeDivId + '" class="alliance-close-button"></div>\
                    </a>\
                    <div class="alliance-box-header">' + this.settings.strings.newAlliance.title + '</div>\
                    <div class="alliance-box-subheader">\
                        <div class="alliance-column alliance-span-1-of-10 top-space">&nbsp;</div>\
                        <div class="alliance-column alliance-span-3-of-10 top-space">\
                            <div class="alliance-box-username">'+this.settings.strings.newAlliance.name +'</div>\
                        </div>\
                        <div class="alliance-column alliance-span-5-of-10 top-space">\
                            <input autofocus type="text" id="alliance-name-new" name="alliance-name-new" class="alliance-box-form-input" placeholder="" autocomplete="off" autocomplete="off" maxlength="'+this.settings.allianceName.maxLength+'">\
                        </div>\
                        <div class="alliance-column alliance-span-1-of-10 top-space">&nbsp;</div>\
                    </div>\
                    <br>\
                    <div class="alliance-box-subheader">\
                        <div class="alliance-column alliance-span-1-of-10 top-space">&nbsp;</div>\
                        <div class="alliance-column alliance-span-3-of-10 top-space">\
                            <div class="alliance-box-username">'+this.settings.strings.newAlliance.code +'</div>\
                        </div>\
                        <div class="alliance-column alliance-span-3-of-10 top-space">\
                            <input autofocus type="text" id="alliance-shortcode-new" name="alliance-shortcode-new" class="alliance-box-form-input" placeholder="" autocomplete="off" autocomplete="off" maxlength="'+this.settings.allianceShortCode.fixedLength+'">\
                        </div>\
                        <div class="alliance-column alliance-span-3-of-10 top-space">&nbsp;</div>\
                    </div>\
                    <br>\
                    <div class="alliance-box-subheader">\
                        <div class="alliance-column alliance-span-1-of-10 top-space">&nbsp;</div>\
                        <div class="alliance-column alliance-span-3-of-10 top-space">\
                            <div class="alliance-box-username">'+this.settings.strings.newAlliance.icon +'</div>\
                        </div>\
                        <div class="alliance-column alliance-span-five-percent" style="text-align:right;padding-right:12px;margin-top:24px;">\
                            <div onclick="ig.alliance.prevIcon()" style="width:20px;height:40px;">\
                                <image class="alliance-nav-icon-button" src="'+this.settings.navButton.prev+'">\
                            </div>\
                        </div>\
                        <div class="alliance-column alliance-span-2-of-10 top-space">\
                            <image id="iconImageNew" name="iconImageNew" src="'+this.settings.iconAlliance[this.iconIndex]+'" style="width:90px;height:60px;margin-top:6px;">\
                        </div>\
                        <div class="alliance-column alliance-span-five-percent" style="margin-top: 24px;margin-left: 14px;">\
                            <div onclick="ig.alliance.nextIcon()" style="width:20px;height:40px;margin-left: -31px;">\
                                <image class="alliance-nav-icon-button alliance-nav-icon-button-right" src="'+this.settings.navButton.next+'" >\
                            </div>\
                        </div>\
                        <div class="alliance-column alliance-span-2-of-10 top-space" style="width:-webkit-fill-available;">&nbsp;</div>\
                    </div>\
                    <br>\
                    <div class="alliance-box-subheader">\
                        <div class="alliance-column alliance-span-1-of-10 top-space">&nbsp;</div>\
                        <div class="alliance-column alliance-span-3-of-10 top-space">\
                            <div class="alliance-box-username">'+this.settings.strings.newAlliance.description +'</div>\
                        </div>\
                        <div class="alliance-column alliance-span-5-of-10 top-space">\
                            <textarea id="alliance-description-new" style="min-height:60px;resize: none;" name="alliance-description-new" class="alliance-box-form-input" placeholder="" autocomplete="off" autocomplete="off" ></textarea>\
                        </div>\
                        <div class="alliance-column alliance-span-1-of-10 top-space">&nbsp;</div>\
                    </div>\
                    <br>\
                    <div class="alliance-box-subheader">\
                        <div class="alliance-column alliance-span-1-of-10 top-space-password">&nbsp;</div>\
                        <div class="alliance-column alliance-span-3-of-10 top-space-password">\
                            <div class="alliance-box-username">'+this.settings.strings.newAlliance.password +'</div>\
                        </div>\
                        <div class="alliance-column alliance-span-6-of-10 top-space-password">\
                            <input type="checkbox" onchange="ig.alliance.uncheckPassword(this)" id="alliance-use-password-new" name="alliance-use-password-new" checked style="margin-top:14px"><label for="alliance-use-password"><div style="margin-top: -18px;margin-left: 30px;">Use Password?</div></label>\
                        </div>\
                        <div class="alliance-column alliance-span-4-of-10 top-space-password">&nbsp;</div>\
                        <div class="alliance-column alliance-span-2-of-10 top-space">\
                            <input autofocus type="password" id="alliance-password-new" name="alliance-password-new" class="alliance-box-form-input" placeholder="" autocomplete="off" autocomplete="off" >\
                        </div>\
                        <div class="alliance-column alliance-span-4-of-10 top-space" style="padding-top:10px;">\
                            <input autofocus type="checkbox" id="password-show-text-new" value="0"><label for="password-show-text" style="font-size:11px;"> Show Password</label>\
                        </div>\
                   </div>\
                    <br>\
                    <div class="alliance-box-subheader">\
                        <div class="alliance-column alliance-span-3-of-10 top-space">\
                            <div id="submit-new-alliance" type="button" onclick="ig.alliance.createNewAlliance()" class="alliance-create-button">' + this.settings.strings.newAlliance.createButton + '</div>\
                        </div>\
                        <div class="alliance-column alliance-span-3-of-10 top-space">&nbsp;</div>\
                    </div>\
                    <div>&nbsp;</div>\
                </div>\
            ';
            this.createPopupDiv(containerDivId, "allianceCreate", divElementContent);

          
            var passwordtogleEle=ig.domHandler.getElementById('#password-show-text-new');
            var passwordInputElement=ig.domHandler.getElementById('#alliance-password-new');
            if (passwordtogleEle !== null && typeof(passwordtogleEle) !== "undefined") {
                ig.domHandler.addEvent(passwordtogleEle, "click", function(event) {
                    var togglerankType = ig.domHandler.getAttr(passwordInputElement, "type") === "password" ? "text" : "password";
                    ig.domHandler.attr(passwordInputElement, "type", togglerankType);
                }.bind(this), false);  
            }


            var closeButtonElement = ig.domHandler.getElementById("#" + closeDivId);
            if (closeButtonElement !== null && typeof(closeButtonElement) !== "undefined") {
                ig.domHandler.addEvent(closeButtonElement, "click", function(event){
                    ig.game.playButton();
                    this.hideAllianceCreate();
                }.bind(this), false);
            }


        },
        validateAlliancePassword:function(pass){
            if(document.getElementById('alliance-use-password-new').checked){
                if(pass.trim().length<=3){
                    this.showNotification(this.settings.strings.passwordNotAllowed);                    
                    return false;
                }else{
                    return true;
                }
            }else{
                return true;
            }
        },
        createNewAlliance:function(){
            ig.game.playButton();

            var postData={
                    name : document.getElementById('alliance-name-new').value,
                    code : document.getElementById('alliance-shortcode-new').value,
                    icon : this.iconIndex,
                    desc : document.getElementById('alliance-description-new').value,
                    pass : document.getElementById('alliance-password-new').value,
                    reqpass:document.getElementById('alliance-use-password-new').checked,
                    leader : this.userInfo.id
                }

            if(this.validateAllianceName(document.getElementById('alliance-name-new').value) && this.validateShortCode(document.getElementById('alliance-shortcode-new').value) && this.validateAlliancePassword(document.getElementById('alliance-password-new').value)){
                Number.prototype.map = undefined;                    

                if (grecaptcha !== null && typeof(grecaptcha) !== "undefined" && this.reCaptcha.loaded === true) {
                    try {
                        grecaptcha.ready(function() {
                            grecaptcha.execute(this.reCaptcha.siteKey, {action: 'submit'}).then(function(response) {

                                this.postNewDataToServer(postData,response,
                                    function(response) {
                                        this.showNotification(response.message);
                                        // console.log(response);
                                        this.userInfo.alliance = response.data.alliance_id;
                                        ig.game.svasData.alliance =response.data.alliance_id;
                                        ig.game.client.saveGameData();
                                        this.hideAllianceCreate();
                                        this.hideAllianceListing();
                                        this.viewAlliance(response.data.alliance_id,'mainWindow');
                                    }.bind(this), 
                                    function(response) {
                                        this.showNotification(response.message);
                                    }.bind(this)
                                );

                            }.bind(this));
                        }.bind(this));
                    } catch(error) {}
                }
                
            }else{
                console.log('param failed');                
            }

        },
        postNewDataToServer:function(postData,reCaptchaValue,successfulCallback,errorCallback){


            if(this.settings.gameId !== null && typeof (this.settings.gameId) !== "undefined"){
                var url, method, timeout;


                url = this.settings.createNewAlliance.url;
                method = this.settings.createNewAlliance.method;
                timeout = this.settings.createNewAlliance.timeout;

                var sendData = new FormData();
                sendData.append('game_id', this.settings.gameId);
                sendData.append('leader_uid', postData.leader);
                sendData.append('name', postData.name);
                sendData.append('shortcode', postData.code);
                sendData.append('description', postData.desc);
                sendData.append('require_password', postData.reqpass);
                sendData.append('password', postData.pass);
                sendData.append('icon_id', postData.icon);
                sendData.append('g-recaptcha-response', reCaptchaValue);


                this.callXHR(method, url, timeout, sendData, function(response) {
                    if (successfulCallback !== null && typeof (successfulCallback) === "function") {
                        successfulCallback(response);
                    }
                }.bind(this), function(response) {
                    if (errorCallback !== null && typeof (errorCallback) === "function") {
                        errorCallback(response);
                    }
                }.bind(this));
            }


        },
        postLeaveDataToServer:function(postData,reCaptchaValue,successfulCallback,errorCallback){


            if(this.settings.gameId !== null && typeof (this.settings.gameId) !== "undefined"){
                var url, method, timeout;

                url = this.settings.leaveAlliance.url;
                method = this.settings.leaveAlliance.method;
                timeout = this.settings.leaveAlliance.timeout;

                var sendData = new FormData();
                sendData.append('game_id', postData.game_id);
                sendData.append('uid', postData.uid);
                sendData.append('alliance_id', postData.alliance_id);
                sendData.append('g-recaptcha-response', reCaptchaValue);

                this.callXHR(method, url, timeout, sendData, function(response) {
                    if (successfulCallback !== null && typeof (successfulCallback) === "function") {
                        successfulCallback(response);
                    }
                }.bind(this), function(response) {
                    if (errorCallback !== null && typeof (errorCallback) === "function") {
                        errorCallback(response);
                    }
                }.bind(this));
            }


        },        
        uncheckPassword:function(obj){
            if(!obj.checked){
                document.getElementById('alliance-password-new').value="";
                document.getElementById('alliance-password-new').disabled=true;
                document.getElementById('password-show-text-new').disabled=true;
                document.getElementById('password-show-text-new').checked=false;
            }else{
                document.getElementById('alliance-password-new').disabled=false;                
                document.getElementById('password-show-text-new').disabled=false;
            }
        },
        uncheckPassword2:function(obj){
            if(!obj.checked){
                document.getElementById('alliance-password').value="";
                document.getElementById('alliance-password').disabled=true;
                document.getElementById('password-show-text').disabled=true;
                document.getElementById('password-show-text').checked=false;
            }else{
                document.getElementById('alliance-password').disabled=false;                
                document.getElementById('password-show-text').disabled=false;
            }
        },
        prevIcon:function(){
            ig.game.playButton();
            if((this.iconIndex-1)<0){
                this.iconIndex = this.settings.iconAlliance.length-1;
            }else{
                this.iconIndex -=1;
            }
            if(document.getElementById("iconImageNew")) document.getElementById("iconImageNew").src=this.settings.iconAlliance[this.iconIndex];
            if(document.getElementById("iconImageRead")) document.getElementById("iconImageRead").src=this.settings.iconAlliance[this.iconIndex];
        },
        nextIcon:function(){
            ig.game.playButton();
            if((this.iconIndex+1)>=this.settings.iconAlliance.length){
                this.iconIndex = 0;
            }else{
                this.iconIndex +=1;
            }
            if(document.getElementById("iconImageNew")) document.getElementById("iconImageNew").src=this.settings.iconAlliance[this.iconIndex];
            if(document.getElementById("iconImageRead")) document.getElementById("iconImageRead").src=this.settings.iconAlliance[this.iconIndex];
        },
        updateAllianceReadDiv:function(data,origin){
            var owner=this.checkAllianceLeader(data.members);
            this.updateMembersData(data,owner);
            if(data.is_banned===true){
                var descFilterBanned='**************\n\n'+'This alliance is under review by the moderators.';
            }else{
                var descFilterBanned=data.description;
            }

            if(owner){

                document.getElementById("alliance-description").disabled=false;
                document.getElementById("alliance-name").disabled=false;
                document.getElementById("alliance-shortcode").disabled=false;
                document.getElementById("alliance-use-password").disabled = false;
                document.getElementById("alliance-password").disabled = false;

                document.getElementById("alliance-password-label").style = "";
                document.getElementById("alliance-use-password").style = "margin-top:14px;";
                document.getElementById("alliance-password").style = "width: 58%;";

                document.getElementById("password-show-text").style = "";
                document.getElementById("password-show-text-label").style = "font-size:11px;";

                document.getElementById("btleft").style = "";
                document.getElementById("btright").style = "margin-left: 31px;";
                document.getElementById("kick-title").style = "";
                document.getElementById("button-list-read2").style = "display:block";


                document.getElementById("button-gift").className = "alliance-readform-button";
                document.getElementById("button-join").className = "alliance-readform-button-disabled";
                // document.getElementById("button-leave").className = "alliance-readform-button-disabled";
                document.getElementById("button-leave").className = "alliance-readform-button";
                document.getElementById("button-update").className = "alliance-readform-button";


                if(ig.ua.mobile){
                    document.getElementById("button-gift").style = "margin-top:-6px;font-size:12px;min-height:24px;";
                    document.getElementById("button-join").style = "pointer-events: none;margin-top:-6px;font-size:12px;min-height:24px;";
                    document.getElementById("button-leave").style = "margin-top:-6px;font-size:12px;min-height:24px;";
                    document.getElementById("button-update").style = "margin-top:-6px;font-size:12px;min-height:24px;";
                    document.getElementById("button-list-read2").style = "display:block;min-height:20px;padding-top:5px;padding-bottom:5px;";
                }else{
                    document.getElementById("button-gift").style = "";
                    document.getElementById("button-join").style = "pointer-events: none;";
                    document.getElementById("button-update").style = "";                    
                    document.getElementById("button-list-read2").style = "display:block;";
                }

            }else{

                document.getElementById("alliance-description").disabled=true;
                document.getElementById("alliance-name").disabled=true;
                document.getElementById("alliance-shortcode").disabled=true;
                document.getElementById("alliance-use-password").disabled = true;
                document.getElementById("alliance-password").disabled = true;

                document.getElementById("alliance-password-label").style = "display:none;";
                document.getElementById("alliance-use-password").style = "margin-top:14px;display:none;";
                document.getElementById("alliance-password").style = "width: 58%;display:none;";

                document.getElementById("password-show-text").style = "display:none;";
                document.getElementById("password-show-text-label").style = "font-size:11px;display:none;";

                document.getElementById("btleft").style = "pointer-events: none;";
                document.getElementById("btright").style = "pointer-events: none;margin-left: 31px;";
                document.getElementById("kick-title").style = "display:none;";

                document.getElementById("button-gift").className = "alliance-readform-button";
                document.getElementById("button-join").className = "alliance-readform-button";
                document.getElementById("button-leave").className = "alliance-readform-button";
                document.getElementById("button-update").className = "alliance-readform-button-disabled";

                if(ig.ua.mobile){
                    document.getElementById("button-gift").style = "margin-top:-6px;font-size:12px;min-height:24px;";
                    document.getElementById("button-join").style = "margin-top:-6px;font-size:12px;min-height:24px;";
                    document.getElementById("button-leave").style = "margin-top:-6px;font-size:12px;min-height:24px;";
                    document.getElementById("button-update").style = "pointer-events: none;margin-top:-6px;font-size:12px;min-height:24px;";
                }else{
                    document.getElementById("button-gift").style = "";
                    document.getElementById("button-join").style = "";
                    document.getElementById("button-leave").style = "";
                    document.getElementById("button-update").style = "pointer-events: none;";
                }


                if(this.userInfo.alliance!=''){
                    if(ig.ua.mobile){
                        document.getElementById("button-join").style = "pointer-events: none;margin-top:-6px;font-size:12px;min-height:24px;";
                        document.getElementById("button-leave").style = "margin-top:-6px;font-size:12px;min-height:24px;";                        
                        document.getElementById("button-list-read2").style = "display:block;min-height:20px;padding-top:5px;padding-bottom:5px;";
                    }else{
                        document.getElementById("button-join").style = "pointer-events: none;";
                        document.getElementById("button-leave").style = "";                        
                        document.getElementById("button-list-read2").style = "display:block";
                    }

                    document.getElementById("button-join").className = "alliance-readform-button-disabled";                
                    document.getElementById("button-leave").className = "alliance-readform-button";                                
                }else{
                    if(ig.ua.mobile){
                        document.getElementById("button-join").style = "margin-top:-6px;font-size:12px;min-height:24px;";
                        document.getElementById("button-leave").style = "pointer-events: none;margin-top:-6px;font-size:12px;min-height:24px;";                        
                    }else{
                        document.getElementById("button-join").style = "";
                        document.getElementById("button-leave").style = "pointer-events: none;";                        
                    }

                    document.getElementById("button-join").className = "alliance-readform-button";                                
                    document.getElementById("button-leave").className = "alliance-readform-button-disabled";                
                    document.getElementById("button-leave").style = "pointer-events: none;";

                    document.getElementById("button-list-read2").style = "display:none";
                }
            }


            //value            
            document.getElementById("div-origin").value=origin;
            document.getElementById("div-alliance-id").value=data.alliance_id;
            this.iconIndex = data.icon_id;
            document.getElementById("iconImageRead").src=this.settings.iconAlliance[this.iconIndex];
            document.getElementById("alliance-description").innerHTML=descFilterBanned;
            document.getElementById("alliance-name").value=data.name;
            document.getElementById("alliance-shortcode").value=data.shortcode;
            if(data.require_password){
                document.getElementById("alliance-use-password").checked = true;
                document.getElementById("alliance-password-label").disabled = false;
                document.getElementById("alliance-password").disabled = false;
                document.getElementById("div-alliance-reqpass").value = true;
            }else{
                document.getElementById("alliance-use-password").checked = false;
                document.getElementById("alliance-password-label").disabled = true;
                document.getElementById("alliance-password").disabled = true;
                document.getElementById("div-alliance-reqpass").value = false;
            }

            if(!this.isMemberAlliance(data.members)){
                if(ig.ua.mobile){
                    document.getElementById("button-gift").style = "pointer-events: none;margin-top:-6px;font-size:12px;min-height:24px;";                                    
                }else{
                    document.getElementById("button-gift").style = "pointer-events: none;";                                                        
                }
                document.getElementById("button-gift").disabled = true;
                document.getElementById("button-gift").className = "alliance-readform-button-disabled";                
                document.getElementById("button-gift").style = "pointer-events: none;";

                document.getElementById("button-leave").disabled = true;
                document.getElementById("button-leave").className = "alliance-readform-button-disabled";                
                document.getElementById("button-leave").style = "pointer-events: none;";

            }

            if(ig.game.svasData.uid==0){
                document.getElementById("button-join").disabled = true;                
                document.getElementById("button-join").style = "pointer-events: none;";                                    
                document.getElementById("button-join").className = "alliance-readform-button-disabled";                                
            }


        },
        checkAllianceLeader:function(members){
            for(var i=0;i<members.length;i++){
                if(members[i].leader==true){
                    if(members[i].uid==this.userInfo.id){
                        return true;
                        break;
                    }
                }
            }
            return false;
        },
        createAllianceReadDiv: function(data,origin) {
            // console.log(data);
            var owner=this.checkAllianceLeader(data.members);
            if(owner){
                var disable_el="";
                var disable_pass="";
                var disable_bt1="";
                var disable_col1="";
                var disable_col12="margin-top:14px;";
                var disable_col13="";
                var disable_col14="width: 58%;";


                var disable_bt2="";
                var disable_bt3="pointer-events: none;";
                // var disable_bt4="pointer-events: none;";
                var disable_bt4="";
                var disable_bt5="";
                var disable_bt6="display:block;";

                var disable_bt2_class="alliance-readform-button";
                var disable_bt3_class="alliance-readform-button-disabled";
                var disable_bt4_class="alliance-readform-button";
                // var disable_bt4_class="alliance-readform-button-disabled";
                var disable_bt5_class="alliance-readform-button";
                var disable_bt6_class="alliance-readform-button-list";

                if(data.require_password){
                    var checkpass = 'checked';
                    var disable_pass="";
                }else{
                    var checkpass = '';
                    var disable_pass="disabled";
                }
            }else{
                var disable_el="disabled";
                var disable_pass="disabled";
                var disable_bt1="pointer-events: none;";
                var disable_col1="display:none;";
                var disable_col12="display:none;";
                var disable_col13="display:none;";
                var disable_col14="display:none;";

                var disable_bt2="";
                var disable_bt3="";
                var disable_bt4="";
                var disable_bt5="pointer-events: none;";
                var disable_bt6="display:block;";

                var disable_bt2_class="alliance-readform-button";
                var disable_bt3_class="alliance-readform-button";
                var disable_bt4_class="alliance-readform-button";
                var disable_bt5_class="alliance-readform-button-disabled";

                if(this.userInfo.alliance!=''){
                    var disable_bt3="pointer-events: none;";
                    var disable_bt3_class="alliance-readform-button-disabled";
                    var disable_bt4="";
                    var disable_bt4_class="alliance-readform-button";                
                    var disable_bt6_class="alliance-readform-button-list";
                    var disable_bt6="display:block;";
                }else{
                    var disable_bt3="";
                    var disable_bt3_class="alliance-readform-button";                
                    var disable_bt4="pointer-events: none;";
                    var disable_bt4_class="alliance-readform-button-disabled";
                    var disable_bt6_class="alliance-readform-button-list";
                    var disable_bt6="display:none;";
                }

            }

            if(data.is_banned===true){
                var descFilterBanned='**************\n\n'+'This alliance is under review by the moderators.';
            }else{
                var descFilterBanned=data.description;
            }

            this.iconIndex = data.icon_id;

            var containerDivId = "alliance-read-container", 
                boxDivId = "box-read",
                closeDivId = "alliance-read-close";

            var xx=window.innerWidth;
            var yy=window.innerHeight;

            if(ig.ua.mobile){

                if(xx<yy){
                    var windowReadSize="min-height: 420px;overflow-y: scroll;max-width: 800px;";
                }else{
                    var windowReadSize="min-height: 230px;overflow-y: scroll;max-width: 600px;";
                }


                var divElementContent = '\
                    <input type="hidden" id="div-origin" name="div-origin" value="'+origin+'">\
                    <input type="hidden" id="div-alliance-id" name="div-alliance-id" value="'+data.alliance_id+'">\
                    <input type="hidden" id="div-alliance-reqpass" name="div-alliance-reqpass" value="'+data.require_password+'">\
                    <input type="hidden" id="div-alliance-next-owner" name="div-alliance-next-owner" value="">\
                    <div id="' + boxDivId + '" class="alliance-box alliance-box-read alliance-box-center" style="'+windowReadSize+'">\
                        <a>\
                            <div id="' + closeDivId + '" class="alliance-close-button"></div>\
                        </a>\
                        <div class="alliance-box-header">' + this.settings.strings.readAlliance.title + '</div>\
                        <div class="alliance-box-subheader">\
                            <div class="alliance-column alliance-span-3-of-10 top-space">\
                                <div class="alliance-box-username">'+this.settings.strings.readAlliance.name +'</div>\
                            </div>\
                            <div class="alliance-column alliance-span-6-of-10 top-space">\
                                <input autofocus type="text" '+disable_el+' id="alliance-name" name="alliance-name" class="alliance-box-form-input" placeholder="" autocomplete="off" autocomplete="off" maxlength="'+this.settings.allianceName.maxLength+'" value="'+data.name+'">\
                            </div>\
                            <div class="alliance-column alliance-span-1-of-10 top-space">&nbsp;</div>\
                        </div>\
                        <br>\
                        <div class="alliance-box-subheader">\
                            <div class="alliance-column alliance-span-3-of-10 top-space">\
                                <div class="alliance-box-username">'+this.settings.strings.readAlliance.code +'</div>\
                            </div>\
                            <div class="alliance-column alliance-span-4-of-10 top-space">\
                                <input type="text" '+disable_el+' id="alliance-shortcode" name="alliance-shortcode" class="alliance-box-form-input" placeholder="" autocomplete="off" autocomplete="off" maxlength="'+this.settings.allianceShortCode.fixedLength+'" value="'+data.shortcode+'">\
                            </div>\
                            <div class="alliance-column alliance-span-3-of-10 top-space">&nbsp;</div>\
                        </div>\
                        <br>\
                        <div class="alliance-box-subheader">\
                            <div class="alliance-column alliance-span-3-of-10 top-space">\
                                <div class="alliance-box-username" id="alliance-password-label" style="'+disable_col1+'">'+this.settings.strings.readAlliance.password +'</div>\
                            </div>\
                            <div class="alliance-column alliance-span-five-percent top-space">\
                                <input type="checkbox" '+disable_el+' onchange="ig.alliance.uncheckPassword2(this)" id="alliance-use-password" name="alliance-use-password" '+checkpass+' style="margin-top:14px;'+disable_col12+'">\
                            </div>\
                            <div class="alliance-column alliance-span-6-of-10 top-space">\
                                <input autofocus type="password" '+disable_pass+' id="alliance-password" name="alliance-password" class="alliance-box-form-input" placeholder="" autocomplete="off" autocomplete="off" style="width: 58%;margin-left:3px;'+disable_col13+'" value="">\
                            </div>\
                            <div class="alliance-column alliance-span-five-percent top-space" style="margin-left: -73px;width: 57px;">\
                                <input autofocus type="checkbox" id="password-show-text" value="0" '+disable_pass+' style="'+disable_col1+'"><label for="password-show-text" style="font-size:11px;'+disable_col14+'" id="password-show-text-label"> Show Password</label>\
                            </div>\
                        </div>&nbsp;<br>\
                        <div class="alliance-box-subheader">\
                            <div class="alliance-column alliance-span-3-of-10 top-space">\
                                <div class="alliance-box-username">'+this.settings.strings.readAlliance.icon +'</div>\
                            </div>\
                            <div class="alliance-column alliance-span-1-of-10" style="text-align:left;padding-right:12px;margin-top:24px;">\
                                <div onclick="ig.alliance.prevIcon()" '+disable_el+' style="width:20px;height:40px;'+disable_bt1+'" id="btleft" name="btleft">\
                                    <image class="alliance-nav-icon-button" src="'+this.settings.navButton.prev+'">\
                                </div>\
                            </div>\
                            <div class="alliance-column alliance-span-2-of-10 top-space" style="min-width:60px;">\
                                <image id="iconImageRead" name="iconImageRead" src="'+this.settings.iconAlliance[data.icon_id]+'" style="width:90px;height:60px;margin-top:6px;">\
                            </div>\
                            <div class="alliance-column alliance-span-2-of-10" style="margin-top: 24px;";>\
                                <div onclick="ig.alliance.nextIcon()" '+disable_el+' style="margin-left: 31px;width:20px;height:40px;'+disable_bt1+'" id="btright" name="btright">\
                                    <image class="alliance-nav-icon-button" src="'+this.settings.navButton.next+'" style="margin-left: 24px;">\
                                </div>\
                            </div>\
                            <div class="alliance-column alliance-span-1-of-10" >&nbsp;</div>\
                        </div>\
                        <div class="alliance-box-subheader" style="min-height:225px;">\
                            <div class="alliance-column alliance-span-10-of-10 top-space">\
                                <div class="alliance-box-username">'+this.settings.strings.readAlliance.description +'</div>\
                            </div>\
                            <div class="alliance-column alliance-span-10-of-10 top-space">\
                                <textarea id="alliance-description" '+disable_el+' style="min-height:50px;max-width:94%;resize:none" name="alliance-description" class="alliance-box-form-input" placeholder="" autocomplete="off" autocomplete="off" >'+descFilterBanned+'</textarea>\
                            </div>\
                            <div class="alliance-column alliance-span-10-of-10 top-space" style="padding-top:5px;padding-bottom:10px;margin-left:-10px;">\
                                <div id="button-list-read2" type="button" onclick="ig.alliance.showAllianceListReadForm()"  style="'+disable_bt6+'min-height:20px;padding-top:5px;padding-bottom:5px;" class="'+disable_bt6_class+'">' + this.settings.strings.readAlliance.buttons.list + '</div>\
                            </div>\
                        </div>\
                        <div class="alliance-box-subheader" style="padding-right: 16px;">\
                           <div class="alliance-span-10-of-10">\
                                <div class="alliance-column alliance-span-2-of-10 header-color">\
                                    <div class="alliance-box-col-header">' + this.settings.strings.readAlliance.listingMember.members + '</div>\
                                </div>\
                                <div class="alliance-column alliance-span-4-of-10 header-color">\
                                    <div class="alliance-box-col-header">' + this.settings.strings.readAlliance.listingMember.name + '</div>\
                                </div>\
                                <div class="alliance-column alliance-span-2-of-10 header-color" style="text-align:right">\
                                    <div class="alliance-box-col-header">' + this.settings.strings.readAlliance.listingMember.experience + '</div>\
                                </div>\
                                <div class="alliance-column alliance-span-2-of-10 header-color" style="text-align:center;">\
                                    <div class="alliance-box-col-header" style="'+disable_col1+'" id="kick-title" name="kick-title">' + this.settings.strings.readAlliance.listingMember.kick + '</div>\
                                </div>\
                            </div>\
                        </div>&nbsp;<br>\
                        <div class="alliance-box-subheader" style="margin-top:20px;padding-right:18px;">\
                            <div id="membersDiv" name="membersDiv" class="alliance-span-10-of-10" style="overflow-y: scroll;height:160px;">\
                            </div>\
                        </div>\
                        <div class="alliance-box-subheader">\
                            <div class="alliance-column" style="width:20%">\
                                <div id="button-transfer" type="button" onclick="ig.alliance.transferAlliance()" class="'+disable_bt2_class+'" style="'+disable_bt2+';margin-top: -16px; font-size:12px;min-height:24px;">' + this.settings.strings.readAlliance.buttons.transfer + '</div>\
                            </div>\
                            <div class="alliance-column" style="width:20%">\
                                <div id="button-gift" type="button" onclick="ig.alliance.showGiftAlliance()" class="'+disable_bt2_class+'" style="'+disable_bt2+';position: absolute;margin-top: -6px;font-size:12px;min-height:24px;">' + this.settings.strings.readAlliance.buttons.gift + '</div>\
                            </div>\
                            <div class="alliance-column" style="width:20%">\
                                <div id="button-join" type="button" onclick="ig.alliance.joinAlliance()" class="'+disable_bt3_class+'" style="'+disable_bt3+';position: absolute;margin-top: -6px;font-size:12px;min-height:24px;">' + this.settings.strings.readAlliance.buttons.join + '</div>\
                            </div>\
                            <div class="alliance-column" style="width:20%">\
                                <div id="button-leave" type="button" onclick="ig.alliance.leaveAllianceAction()" class="'+disable_bt4_class+'" style="'+disable_bt4+';position: absolute;margin-top: -6px;font-size:12px;min-height:24px;">' + this.settings.strings.readAlliance.buttons.leave + '</div>\
                            </div>\
                            <div class="alliance-column" style="width:20%">\
                                <div id="button-update" type="button" onclick="ig.alliance.updateAllianceData()" class="'+disable_bt5_class+'" style="'+disable_bt5+';position: absolute;margin-top: -6px;font-size:12px;min-height:24px;">' + this.settings.strings.readAlliance.buttons.update + '</div>\
                            </div>\
                        </div>\
                        <br>&nbsp;<br>\
                    </div>\
                ';
            }else{                
                var divElementContent = '\
                    <input type="hidden" id="div-origin" name="div-origin" value="'+origin+'">\
                    <input type="hidden" id="div-alliance-id" name="div-alliance-id" value="'+data.alliance_id+'">\
                    <input type="hidden" id="div-alliance-reqpass" name="div-alliance-reqpass" value="'+data.require_password+'">\
                    <input type="hidden" id="div-alliance-next-owner" name="div-alliance-next-owner" value="">\
                    <div id="' + boxDivId + '" class="alliance-box alliance-box-read alliance-box-center" style="min-height:420px;">\
                        <a>\
                            <div id="' + closeDivId + '" class="alliance-close-button"></div>\
                        </a>\
                        <div class="alliance-box-header">' + this.settings.strings.readAlliance.title + '</div>\
                        <table style="width:100%;border:0px;">\
                            <tr>\
                                <td style="width:30%;vertical-align: -webkit-baseline-middle;">\
                                    <div class="alliance-box-subheader">\
                                        <div class="alliance-column alliance-span-3-of-10 top-space">\
                                            <div class="alliance-box-username">'+this.settings.strings.readAlliance.icon +'</div>\
                                        </div>\
                                        <div class="alliance-column alliance-span-7-of-10 top-space">&nbsp;</div>\
                                        <div class="alliance-column alliance-span-2-of-10" style="text-align:right;padding-right:12px;margin-top:24px;">\
                                            <div onclick="ig.alliance.prevIcon()" '+disable_el+' style="width:20px;height:40px;'+disable_bt1+'" id="btleft" name="btleft">\
                                                <image class="alliance-nav-icon-button" src="'+this.settings.navButton.prev+'">\
                                            </div>\
                                        </div>\
                                        <div class="alliance-column alliance-span-2-of-10 top-space" style="min-width:60px;">\
                                            <image id="iconImageRead" name="iconImageRead" src="'+this.settings.iconAlliance[data.icon_id]+'" style="width:90px;height:60px;margin-top:6px;">\
                                        </div>\
                                        <div class="alliance-column alliance-span-2-of-10" style="margin-top: 24px;";>\
                                            <div onclick="ig.alliance.nextIcon()" '+disable_el+' style="margin-left: 31px;width:20px;height:40px;'+disable_bt1+'" id="btright" name="btright">\
                                                <image class="alliance-nav-icon-button" src="'+this.settings.navButton.next+'" style="margin-left: 11px;">\
                                            </div>\
                                        </div>\
                                    </div>\
                                    <br>&nbsp;\
                                    <br>&nbsp;\
                                    <br>&nbsp;\
                                    <br>&nbsp;\
                                    <br>&nbsp;\
                                    <div class="alliance-box-subheader" style="min-height:215px;">\
                                        <div class="alliance-column alliance-span-10-of-10 top-space">\
                                            <div class="alliance-box-username">'+this.settings.strings.readAlliance.description +'</div>\
                                        </div>\
                                        <div class="alliance-column alliance-span-10-of-10 top-space">\
                                            <textarea id="alliance-description" '+disable_el+' style="min-height:160px;resize:none" name="alliance-description" class="alliance-box-form-input" placeholder="" autocomplete="off" autocomplete="off" >'+descFilterBanned+'</textarea>\
                                        </div>\
                                    </div>\
                                    <div class="alliance-box-subheader">\
                                        <div class="alliance-column" style="width:50%">\
                                            <div id="button-transfer" type="button" onclick="ig.alliance.transferAlliance()" class="'+disable_bt2_class+'" style="'+disable_bt2+'">' + this.settings.strings.readAlliance.buttons.transfer + '</div>\
                                        </div>\
                                        <div class="alliance-column" style="width:30%">\
                                            <div id="button-list-read2" type="button" onclick="ig.alliance.showAllianceListReadForm()" class="'+disable_bt6_class+'" style="'+disable_bt6+'">' + this.settings.strings.readAlliance.buttons.list + '</div>\
                                        </div>\
                                    </div>\
                                </td>\
                                <td style="vertical-align: -webkit-baseline-middle;padding-left:10px;">\
                                    <div class="alliance-box-subheader">\
                                        <div class="alliance-column alliance-span-3-of-10 top-space">\
                                            <div class="alliance-box-username">'+this.settings.strings.readAlliance.name +'</div>\
                                        </div>\
                                        <div class="alliance-column alliance-span-6-of-10 top-space">\
                                            <input autofocus type="text" '+disable_el+' id="alliance-name" name="alliance-name" class="alliance-box-form-input" placeholder="" autocomplete="off" autocomplete="off" maxlength="'+this.settings.allianceName.maxLength+'" value="'+data.name+'">\
                                        </div>\
                                        <div class="alliance-column alliance-span-1-of-10 top-space">&nbsp;</div>\
                                    </div>\
                                    <br>\
                                    <div class="alliance-box-subheader">\
                                        <div class="alliance-column alliance-span-3-of-10 top-space">\
                                            <div class="alliance-box-username">'+this.settings.strings.readAlliance.code +'</div>\
                                        </div>\
                                        <div class="alliance-column alliance-span-4-of-10 top-space">\
                                            <input type="text" '+disable_el+' id="alliance-shortcode" name="alliance-shortcode" class="alliance-box-form-input" placeholder="" autocomplete="off" autocomplete="off" maxlength="'+this.settings.allianceShortCode.fixedLength+'" value="'+data.shortcode+'">\
                                        </div>\
                                        <div class="alliance-column alliance-span-3-of-10 top-space">&nbsp;</div>\
                                    </div>\
                                    <br>\
                                    <div class="alliance-box-subheader">\
                                        <div class="alliance-column alliance-span-3-of-10 top-space">\
                                            <div class="alliance-box-username" id="alliance-password-label" style="'+disable_col1+'">'+this.settings.strings.readAlliance.password +'</div>\
                                        </div>\
                                        <div class="alliance-column alliance-span-five-percent top-space">\
                                            <input type="checkbox" '+disable_el+' onchange="ig.alliance.uncheckPassword2(this)" id="alliance-use-password" name="alliance-use-password" '+checkpass+' style="margin-top:14px;'+disable_col12+'">\
                                        </div>\
                                        <div class="alliance-column alliance-span-6-of-10 top-space">\
                                            <input autofocus type="password" '+disable_pass+' id="alliance-password" name="alliance-password" class="alliance-box-form-input" placeholder="" autocomplete="off" autocomplete="off" style="width: 58%;'+disable_col13+'" value="">\
                                            <input autofocus type="checkbox" id="password-show-text" value="0" '+disable_pass+' style="'+disable_col1+'"><label for="password-show-text" style="font-size:11px;'+disable_col14+'" id="password-show-text-label"> Show Password</label>\
                                        </div>\
                                        <div class="alliance-column alliance-span-five-percent top-space"></div>\
                                   </div>&nbsp;<br>\
                                    <div class="alliance-box-subheader" style="padding-right: 16px;">\
                                       <div class="alliance-span-10-of-10">\
                                            <div class="alliance-column alliance-span-2-of-10 header-color">\
                                                <div class="alliance-box-col-header">' + this.settings.strings.readAlliance.listingMember.members + '</div>\
                                            </div>\
                                            <div class="alliance-column alliance-span-4-of-10 header-color">\
                                                <div class="alliance-box-col-header">' + this.settings.strings.readAlliance.listingMember.name + '</div>\
                                            </div>\
                                            <div class="alliance-column alliance-span-2-of-10 header-color" style="text-align:right">\
                                                <div class="alliance-box-col-header">' + this.settings.strings.readAlliance.listingMember.experience + '</div>\
                                            </div>\
                                            <div class="alliance-column alliance-span-2-of-10 header-color" style="text-align:center;">\
                                                <div class="alliance-box-col-header" style="'+disable_col1+'" id="kick-title" name="kick-title">' + this.settings.strings.readAlliance.listingMember.kick + '</div>\
                                            </div>\
                                        </div>\
                                    </div>&nbsp;<br>\
                                    <div class="alliance-box-subheader" style="margin-top:20px;padding-right:18px;">\
                                        <div id="membersDiv" name="membersDiv" class="alliance-span-10-of-10" style="overflow-y: scroll;height:160px;">\
                                        </div>\
                                    </div>\
                                    <div class="alliance-box-subheader">\
                                        <div class="alliance-column" style="width:25%">\
                                            <div id="button-gift" type="button" onclick="ig.alliance.showGiftAlliance()" class="'+disable_bt2_class+'" style="'+disable_bt2+'">' + this.settings.strings.readAlliance.buttons.gift + '</div>\
                                        </div>\
                                        <div class="alliance-column" style="width:25%">\
                                            <div id="button-join" type="button" onclick="ig.alliance.joinAlliance()" class="'+disable_bt3_class+'" style="'+disable_bt3+'">' + this.settings.strings.readAlliance.buttons.join + '</div>\
                                        </div>\
                                        <div class="alliance-column" style="width:25%">\
                                            <div id="button-leave" type="button" onclick="ig.alliance.leaveAllianceAction()" class="'+disable_bt4_class+'" style="'+disable_bt4+'">' + this.settings.strings.readAlliance.buttons.leave + '</div>\
                                        </div>\
                                        <div class="alliance-column" style="width:25%">\
                                            <div id="button-update" type="button" onclick="ig.alliance.updateAllianceData()" class="'+disable_bt5_class+'" style="'+disable_bt5+'">' + this.settings.strings.readAlliance.buttons.update + '</div>\
                                        </div>\
                                    </div>\
                                </td>\
                            </tr>\
                        </table>\
                        <br>&nbsp;<br>\
                    </div>\
                ';
            }

            this.createPopupDiv(containerDivId, "allianceRead", divElementContent);

            var passwordtogleEle=ig.domHandler.getElementById('#password-show-text');
            var passwordInputElement=ig.domHandler.getElementById('#alliance-password');
            if (passwordtogleEle !== null && typeof(passwordtogleEle) !== "undefined") {
                ig.domHandler.addEvent(passwordtogleEle, "click", function(event) {
                    var togglerankType = ig.domHandler.getAttr(passwordInputElement, "type") === "password" ? "text" : "password";
                    ig.domHandler.attr(passwordInputElement, "type", togglerankType);
                }.bind(this), false);  
            }


            var closeButtonElement = ig.domHandler.getElementById("#" + closeDivId);
            if (closeButtonElement !== null && typeof(closeButtonElement) !== "undefined") {
                ig.domHandler.addEvent(closeButtonElement, "click", function(event){
                    ig.game.playButton();
                    this.hideAllianceRead();
                }.bind(this), false);
            }


            this.updateMembersData(data,owner,data.is_banned);

        },
        showAllianceListReadForm:function(){
            ig.game.playButton();

            if (this.divElements["allianceListing"] === null || typeof(this.divElements["allianceListing"]) === "undefined" ) {
                this.createAllianceListDiv(function(){
                    this.showMainWindow();
                }.bind(this));                 
                ig.domHandler.show(this.divElements["allianceListing"]);
            }else {
                ig.domHandler.show(this.divElements["allianceListing"]);
                this.updateAllianceList(0);
            }

            this.hideAllianceRead();

        },
        transferAlliance:function(){
            ig.game.playButton();
            var target=document.getElementById('div-alliance-next-owner').value;
            // console.log('transfer alliance to '+target);

            if(target=='' || target==null && document.getElementById("button-transfer").disabled==false){
                this.showNotification(this.settings.strings.noMember);
                return;
            }

            url = this.settings.transferAlliance.url;
            method = this.settings.transferAlliance.method;
            timeout = this.settings.transferAlliance.timeout;


            if (grecaptcha !== null && typeof(grecaptcha) !== "undefined" && this.reCaptcha.loaded === true) {
                Number.prototype.map = undefined;                    
                try {
                    grecaptcha.ready(function() {
                        grecaptcha.execute(this.reCaptcha.siteKey, {action: 'submit'}).then(function(reChaptchaValue) {

                        var postData = new FormData();
                        postData.append('game_id', this.settings.gameId);
                        postData.append('alliance_id', document.getElementById('div-alliance-id').value);
                        postData.append('uid', this.userInfo.id);
                        postData.append('target_uid', target);
                        postData.append('g-recaptcha-response', reChaptchaValue);

                        this.callXHR(method, url, timeout, postData, function(response) {
                            // console.log(response);
                            this.showNotification(response.message);
                            ig.game.client.saveGameData();
                            this.showMainWindow();
                        }.bind(this), function(response) {
                            // console.log(response);
                            this.showNotification(response.message);
                        }.bind(this));

                        }.bind(this));
                    }.bind(this));
                } catch(error) {}
            }


        },
        showGiftAlliance:function(){

            ig.game.playButton();
            ig.domHandler.hide(this.divElements["allianceRead"]);
            if (this.divElements["giftAllianceDiv"] === null || typeof(this.divElements["giftAllianceDiv"]) === "undefined" ) {
                this.createGiftAllianceDiv();                 
                ig.domHandler.show(this.divElements["giftAllianceDiv"]);
            }else{
                ig.domHandler.show(this.divElements["giftAllianceDiv"]);
                this.updateGiftAllianceDiv();                 
            }
        },
        hideGiftAlliance: function() {
            if (this.divElements["giftAllianceDiv"] !== null && typeof(this.divElements["giftAllianceDiv"]) !== "undefined" ) {
                ig.domHandler.hide(this.divElements["giftAllianceDiv"]);
                ig.domHandler.show(this.divElements["allianceRead"]);
            }
        },
        createGiftAllianceDiv:function(){

            var containerDivId = "alliance-gift-container", 
                boxDivId = "box-gift",
                closeDivId = "alliance-gift-close",
                contentDivId = "alliance-gift-contents";
            var divElementContent = '\
                <div id="' + boxDivId + '" class="alliance-box alliance-box-gift alliance-box-center">\
                    <a>\
                        <div id="' + closeDivId + '" class="alliance-close-button"></div>\
                    </a>\
                    <div class="alliance-box-header" style="border-bottom: black;border-style: solid;padding-bottom: 12px;margin-right: 17px;">' + this.settings.strings.giftText.title + '</div>\
                    <div id="' + contentDivId + '" style="overflow-y: scroll;height:390px;max-height:420px;width:100%;"></div>\
                    <div>&nbsp;</div>\
                </div>\
            ';
            this.createPopupDiv(containerDivId, "giftAllianceDiv", divElementContent);
            // console.log('createAllianceListDiv');

            // elements
            var closeButtonElement = ig.domHandler.getElementById("#" + closeDivId);

            // event - close button
            if (closeButtonElement !== null && typeof(closeButtonElement) !== "undefined") {
                ig.domHandler.addEvent(closeButtonElement, "click", function(event){
                    ig.game.playButton();
                    this.hideGiftAlliance();
                }.bind(this), false);
            }

            this.updateGiftAllianceDiv();
        },
        updateGiftAllianceDiv:function(){
            var allianceId = document.getElementById('div-alliance-id').value;
            var receiver_id = this.userInfo.id;            
            ig.game.client.getGiftList(allianceId,receiver_id);
        },
        listGiftAllianceDiv:function(data){
            var contentDivId = "alliance-gift-contents";
            document.getElementById(contentDivId).innerHTML = ""; 

            this.listingData = data;
            var giftDataHtml = "";
            if (this.listingData && this.listingData.length > 0) {
                var maximumEntry = Math.min(this.settings.listingMaxEntries, this.listingData.length);
                for (var i = 0; i < maximumEntry; i++) {
                    var listingEntry = this.listingData[i];
                     giftDataHtml += '\
                        <div class="section group">\
                            <div class="col span_2_of_12 top-space-text"><img src="'+this.settings.iconGift+'" style="max-height:50px;max-width:50px;margin-top:-10px;margin-left:10px;"></div>\
                            <div class="col span_7_of_12 top-space-text" style="margin-top:4px;padding-left: 15px;">'+listingEntry.message+'</div>\
                            <div class="col span_2_of_12" >\
                                <div id="button-claim" type="button" onclick="ig.alliance.claimGift(\''+listingEntry.gift_id.toString()+'\')" class="alliance-claim-button">' + this.settings.strings.giftText.buttonClaim + '</div>\
                            </div>\
                        </div>\
                    ';
                }
            }else{
                     giftDataHtml += '\
                        <div class="section group">\
                            <div class="col span_11_of_12 top-space-text" style="margin-left:14px;">'+this.settings.strings.noCurrentGifts+'</div>\
                        </div>\
                    ';                    
            }
            document.getElementById(contentDivId).innerHTML = giftDataHtml; 

        },
        claimGift:function(giftid){
            ig.game.playButton();
            var allianceId = document.getElementById('div-alliance-id').value;
            var receiverId = this.userInfo.id;            
            ig.game.client.claimGift(allianceId,receiverId,giftid);

        },
        updateAllianceData:function(){
            ig.game.playButton();

            var postData={
                    game_id:this.settings.gameId,
                    leader_uid : this.userInfo.id,
                    alliance_id : document.getElementById('div-alliance-id').value,
                    name : document.getElementById('alliance-name').value,
                    shortcode : document.getElementById('alliance-shortcode').value,
                    icon_id : this.iconIndex,
                    description : document.getElementById('alliance-description').value,
                    password : document.getElementById('alliance-password').value,
                    require_password:document.getElementById('alliance-use-password').checked,
                }

            if (grecaptcha !== null && typeof(grecaptcha) !== "undefined" && this.reCaptcha.loaded === true) {
                Number.prototype.map = undefined;                    
                try {
                    grecaptcha.ready(function() {
                        grecaptcha.execute(this.reCaptcha.siteKey, {action: 'submit'}).then(function(response) {

                            this.postUpdateAllianceToServer(postData,response,
                                function(response) {
                                    this.showNotification(response.message);
                                    // console.log(response);    
                                    var origin=document.getElementById("div-origin").value;
                                    var allianceId=document.getElementById("div-alliance-id").value;
                                    this.viewAlliance(allianceId,origin);
                                }.bind(this), 
                                function(response) {
                                    this.showNotification(response.message);
                                    // console.log(response);    
                                    var origin=document.getElementById("div-origin").value;
                                    var allianceId=document.getElementById("div-alliance-id").value;
                                    this.viewAlliance(allianceId,origin);
                                }.bind(this)
                            );

                        }.bind(this));
                    }.bind(this));
                } catch(error) {}
            }

        },
        postUpdateAllianceToServer:function(postData,reCaptchaValue,successfulCallback,errorCallback){
            if(this.settings.gameId !== null && typeof (this.settings.gameId) !== "undefined"){
                var url, method, timeout;

                url = this.settings.updateAlliance.url;
                method = this.settings.updateAlliance.method;
                timeout = this.settings.updateAlliance.timeout;

                var sendData = new FormData();
                sendData.append('game_id', postData.game_id);
                sendData.append('leader_uid', postData.leader_uid);
                sendData.append('alliance_id', postData.alliance_id);
                sendData.append('shortcode', postData.shortcode);
                sendData.append('name', postData.name);
                sendData.append('icon_id', postData.icon_id);
                sendData.append('description', postData.description);
                sendData.append('password', postData.password);
                sendData.append('require_password', postData.require_password);
                sendData.append('g-recaptcha-response', reCaptchaValue);

                this.callXHR(method, url, timeout, sendData, function(response) {
                    if (successfulCallback !== null && typeof (successfulCallback) === "function") {
                        successfulCallback(response);
                    }
                }.bind(this), function(response) {
                    if (errorCallback !== null && typeof (errorCallback) === "function") {
                        errorCallback(response);
                    }
                }.bind(this));
            }            
        },
        joinAlliance:function(){
            ig.game.playButton();
            var reqpass = document.getElementById('div-alliance-reqpass').value;
            // console.log(reqpass);            
            if(reqpass=='true'){
                if (this.divElements["joinAllianceDiv"] === null || typeof(this.divElements["joinAllianceDiv"]) === "undefined" ) {
                    this.createJoinAllianceDiv();                 
                    ig.domHandler.show(this.divElements["joinAllianceDiv"]);
                }else{
                    document.getElementById('alliance-join-password').value='';;
                    ig.domHandler.show(this.divElements["joinAllianceDiv"]);
                }
            }else{
                this.goJoinAlliance();
            }
        },
        hideJoinAlliance: function() {
            if (this.divElements["joinAllianceDiv"] !== null && typeof(this.divElements["joinAllianceDiv"]) !== "undefined" ) {
                ig.domHandler.hide(this.divElements["joinAllianceDiv"]);
            }
        },
        createJoinAllianceDiv: function() {

            if(ig.ua.mobile){
                var btSize="min-height:26px;";
            }else{
                var btSize="min-height:18px;";
            }

            var containerDivId = "alliance-join-container"; 
                boxDivId = "box-join";
                closeDivId = "alliance-join-close";
            var divElementContent = '\
                <div id="' + boxDivId + '" class="alliance-box alliance-box-join-alliance alliance-box-center">\
                    <a>\
                        <div id="' + closeDivId + '" class="alliance-close-button"></div>\
                    </a>\
                    <div class="alliance-box-header">' + this.settings.strings.joinAlliance.title + '</div>\
                    <div class="register-submit">\
                        <div class="alliance-span-10-of-10">\
                            <input autofocus type="text" style="max-width:60%" id="alliance-join-password" name="alliance-join-password" class="alliance-box-form-input" placeholder="' + this.settings.strings.joinAlliance.placeholder + '" autocomplete="off" autocomplete="off" >\
                            <div id="search-button" type="button" class="alliance-search-button" onclick="ig.alliance.goJoinAlliance()" style="'+btSize+'">' + this.settings.strings.joinAlliance.submitbutton + '</div>\
                        </div>\
                    </div>\
                    <div>&nbsp;</div>\
                </div>\
            ';
            this.createPopupDiv(containerDivId, "joinAllianceDiv", divElementContent);
            var closeButtonElement = ig.domHandler.getElementById("#" + closeDivId);

            // event - close button
            if (closeButtonElement !== null && typeof(closeButtonElement) !== "undefined") {
                ig.domHandler.addEvent(closeButtonElement, "click", function(event){
                    ig.game.playButton();
                    this.hideJoinAlliance();
                }.bind(this), false);
            }
        },
        goJoinAlliance:function(){
            ig.game.playButton();
            var allianceId = document.getElementById('div-alliance-id').value;
            if(document.getElementById('alliance-join-password')){
                var password = document.getElementById('alliance-join-password').value;
            }else{
                var password = '';
            }
            var gameId = this.settings.gameId;
            var userId = this.userInfo.id;

            var postData={
                    game_id : this.settings.gameId,
                    uid : userId,
                    alliance_id : allianceId,
                    password:password
                }

            if (grecaptcha !== null && typeof(grecaptcha) !== "undefined" && this.reCaptcha.loaded === true) {
                Number.prototype.map = undefined;                    
                try {
                    grecaptcha.ready(function() {
                        grecaptcha.execute(this.reCaptcha.siteKey, {action: 'submit'}).then(function(response) {

                            this.postJoinDataToServer(postData,response,
                                function(response) {
                                    this.showNotification(response.message);
                                    var origin='mainWindow';
                                    document.getElementById("div-origin").value='mainWindow';
                                    var allianceId=document.getElementById("div-alliance-id").value;
                                    this.hideJoinAlliance();
                                    this.hideAllianceRead();
                                    this.userInfo.alliance = allianceId;
                                    ig.game.svasData.alliance =allianceId;
                                    this.saveUserInfo();
                                    ig.game.client.saveGameData();
                                    this.viewAlliance(allianceId,origin);
                                }.bind(this), 
                                function(response) {
                                    this.showNotification(response.message);
                                    // console.log(response);    
                                }.bind(this)
                            );

                        }.bind(this));
                    }.bind(this));
                } catch(error) {}
            }

        },
        postJoinDataToServer:function(postData,reCaptchaValue,successfulCallback,errorCallback){


            if(this.settings.gameId !== null && typeof (this.settings.gameId) !== "undefined"){
                var url, method, timeout;

                url = this.settings.joinAlliance.url;
                method = this.settings.joinAlliance.method;
                timeout = this.settings.joinAlliance.timeout;

                var sendData = new FormData();
                sendData.append('game_id', postData.game_id);
                sendData.append('uid', postData.uid);
                sendData.append('alliance_id', postData.alliance_id);
                sendData.append('password', postData.password);
                sendData.append('g-recaptcha-response', reCaptchaValue);

                this.callXHR(method, url, timeout, sendData, function(response) {
                    if (successfulCallback !== null && typeof (successfulCallback) === "function") {
                        successfulCallback(response);
                    }
                }.bind(this), function(response) {
                    if (errorCallback !== null && typeof (errorCallback) === "function") {
                        errorCallback(response);
                    }
                }.bind(this));
            }


        },        


        updateMembersData:function(data,owner){
            // console.log(data);

            var content='';
            var membersId=[];
            var isLeader = false;
            document.getElementById('membersDiv').innerHTML=content;
            for(var i=0;i<data.members.length;i++){

                if(data.members[i].leader){
                    var leader = 'Leader';
                    if(data.members[i].uid==this.userInfo.id){
                            isLeader = true;
                    }
                }else{
                    var leader = 'Member';                                        
                    membersId.push(data.members[i].uid);
                }

                if(data.members[i].uid==this.userInfo.id){
                    var colorClass="textbackground";
                }else{
                    var colorClass="";                    
                }

                content +='\
                    <div class="alliance-span-10-of-10">\
                        <div class="alliance-column alliance-span-2-of-10 '+colorClass+'" style="height:25px;">\
                            <div class="alliance-box-listdata">'+leader+'</div>\
                        </div>\
                        <div class="alliance-column alliance-span-4-of-10 '+colorClass+'" style="height:25px;">\
                            <div class="alliance-box-listdata">'+data.members[i].nickname+'</div>\
                        </div>\
                        <div class="alliance-column alliance-span-2-of-10 '+colorClass+'" style="height:25px;text-align:right">\
                            <div class="alliance-box-listdata">'+data.members[i].experience+'</div>\
                        </div>\
                        ';
                
                        if(owner && !data.members[i].leader){
                            content +='<div class="alliance-column alliance-span-2-of-10 '+colorClass+'" style="height:25px;text-align:center;">\
                                <div class="alliance-box-listdata"><a href="#" onclick="ig.alliance.kickPlayer(\''+data.members[i].uid+'\',\''+data.alliance_id+'\')"><img src="'+this.settings.iconLogout+'" style="max-width:16px;max-height:16px;margin-top:-4px;"></a></div>\
                            </div>\
                            ';
                        }else{
                            content +='<div class="alliance-column alliance-span-2-of-10 '+colorClass+'" style="height:25px;text-align:center;">\
                                <div class="alliance-box-listdata">&nbsp;</div>\
                            </div>\
                            ';
                        }

                    content +='\
                    </div>\
                    ';
            }
            if(membersId.length>0 && isLeader==true){                
                document.getElementById('div-alliance-next-owner').value=membersId[0];                

                if(ig.ua.mobile){
                    document.getElementById("button-transfer").style = "margin-top:-6px;font-size:12px;min-height:24px;";                                    
                }else{
                    document.getElementById("button-transfer").style = "";                                    
                }
                document.getElementById("button-transfer").disabled = false;                
            }else{
                document.getElementById('div-alliance-next-owner').value="";                                
                document.getElementById("button-transfer").className = "alliance-readform-button-disabled";                

                if(ig.ua.mobile){
                    document.getElementById("button-transfer").style = "pointer-events: none;margin-top:-6px;font-size:12px;min-height:24px;";                
                }else{
                    document.getElementById("button-transfer").style = "pointer-events: none;";                                    
                }

                document.getElementById("button-transfer").disabled = true;                
            }

            this.btTransferProp.class = document.getElementById("button-transfer").className;
            this.btTransferProp.style = document.getElementById("button-transfer").style;
            this.btTransferProp.disabled = document.getElementById("button-transfer").disabled;


            document.getElementById('membersDiv').innerHTML=content;

            if(!this.isMemberAlliance(data.members)){
                if(ig.ua.mobile){
                    document.getElementById("button-gift").style = "pointer-events: none;margin-top:-6px;font-size:12px;min-height:24px;";                                    
                }else{
                    document.getElementById("button-gift").style = "pointer-events: none;";                                    
                }
                document.getElementById("button-gift").disabled = true;                
                document.getElementById("button-gift").className = "alliance-readform-button-disabled";                
            }

            if(ig.game.svasData.uid==0){
                document.getElementById("button-join").disabled = true;            
                if(ig.ua.mobile){
                    document.getElementById("button-join").style = "pointer-events: none;margin-top:-6px;font-size:12px;min-height:24px;";                                    
                }else{
                    document.getElementById("button-join").style = "pointer-events: none;";                                                        
                } 
                document.getElementById("button-join").className = "alliance-readform-button-disabled";                                
            }
        },
        kickPlayer:function(userId,allianceId){
            ig.game.playButton();
            // console.log('kick player ',userId,allianceId);
            var postData={
                    game_id : this.settings.gameId,
                    uid : userId,
                    alliance_id : allianceId,
                }


            if (grecaptcha !== null && typeof(grecaptcha) !== "undefined" && this.reCaptcha.loaded === true) {
                try {
                    Number.prototype.map = undefined;                    
                    grecaptcha.ready(function() {
                        grecaptcha.execute(this.reCaptcha.siteKey, {action: 'submit'}).then(function(response) {

                            this.postLeaveDataToServer(postData,response,
                                function(response) {
                                    this.showNotification(this.settings.strings.readAlliance.kickMessage);
                                    // console.log(response);
                                    var origin=document.getElementById("div-origin").value;
                                    var allianceId=document.getElementById("div-alliance-id").value;
                                    this.viewAlliance(allianceId,origin);
                                }.bind(this), 
                                function(response) {
                                    this.showNotification(response.message);
                                    // console.log(response);    
                                    var origin=document.getElementById("div-origin").value;
                                    var allianceId=document.getElementById("div-alliance-id").value;
                                    this.viewAlliance(allianceId,origin);
                                }.bind(this)
                            );

                        }.bind(this));
                    }.bind(this));
                } catch(error) {}
            }


        },

        leaveAllianceAction:function(){
            ig.game.playButton();
            var postData={
                    game_id : this.settings.gameId,
                    uid : this.userInfo.id,
                    alliance_id : document.getElementById("div-alliance-id").value,
                }

            if (grecaptcha !== null && typeof(grecaptcha) !== "undefined" && this.reCaptcha.loaded === true) {
                try {
                    Number.prototype.map = undefined;                    
                    grecaptcha.ready(function() {
                        grecaptcha.execute(this.reCaptcha.siteKey, {action: 'submit'}).then(function(response) {

                            this.postLeaveDataToServer(postData,response,
                                function(response) {
                                    this.userInfo.alliance = "";
                                    ig.game.svasData.alliance ="";
                                    ig.game.svasData.allianceIcon = null;
                                    this.saveUserInfo();
                                    ig.game.client.saveGameData();

                                    this.showNotification(this.settings.strings.leaveMessage);
                                    this.hideAllianceRead();
                                    this.showAllianceList();
                                }.bind(this), 
                                function(response) {
                                    this.showNotification(response.message);
                                    console.log(response);    
                                }.bind(this)
                            );

                        }.bind(this));
                    }.bind(this));
                } catch(error) {}
            }

        },


        createLoaderDiv: function() {
            var parentContainerElement = ig.domHandler.getElementById("#" + this.settings.popupContainterId);
            
            var divId = "alliance-loader-container";
            var divElementContent = '\
                <div class="alliance-loader-spinner">\
                    <div></div>\
                    <div></div>\
                    <div></div>\
                    <div></div>\
                    <div></div>\
                    <div></div>\
                    <div></div>\
                    <div></div>\
                    <div></div>\
                    <div></div>\
                    <div></div>\
                    <div></div>\
                </div>\
            ';

            this.createPopupDiv(divId, "loader", divElementContent);
            ig.domHandler.attr(this.divElements.loader, "class", "alliance-loader-container");
            ig.domHandler.appendChild(parentContainerElement, this.divElements.loader);
        },

        showLoader: function() {
            if (this.divElements.loader === null || typeof(this.divElements.loader) === "undefined" ) {
                this.createLoaderDiv();
            }
            ig.domHandler.show(this.divElements.loader);
        },

        hideLoader: function() {
            if (this.divElements.loader !== null && typeof(this.divElements.loader) !== "undefined" ) {
                ig.domHandler.hide(this.divElements.loader);
            }
        },



        clearNotifications: function(notificationElement) {
            if (notificationElement !== null && typeof (notificationElement) !== "undefined") {
                ig.domHandler.html(notificationElement, "");
                return true;
            }
            else {
                var loginNotificationElement = ig.domHandler.getElementById("#" + "login-notification"), 
                    registerNotificationElement = ig.domHandler.getElementById("#" + "register-notification");
            
                if (loginNotificationElement !== null && typeof (loginNotificationElement) !== "undefined") {
                    ig.domHandler.html(loginNotificationElement, "");
                }
                if (registerNotificationElement !== null && typeof (registerNotificationElement) !== "undefined") {
                    ig.domHandler.html(registerNotificationElement, "");
                }
                return true;
            }
        },

        updateGameId: function(gameId) {
            if ( gameId === null || typeof (gameId) === "undefined" ) {
                return false;
            }
            this.settings.gameId = gameId.toString();
        },

        updateUserInfo: function(id, nickname, pincode) {
            if ( this.updateUserId(id) && this.updateallianceName(nickname) && this.updateUserPincode(pincode)) {
                return this.saveUserInfo();
            }
        },

        updateUserId: function(id) {
            id = id.toString();
            if ( id === null || typeof (id) === "undefined" || id === "") {
                this.log("Invalid id");
                return false;
            }

            this.userInfo.id = id;
            return true;
        },

        updateallianceName: function(nickname) {
            nickname = nickname.toString();
            if (this.validateAllianceName(nickname) === true) {
                this.userInfo.nickname = nickname;
                this.saveUserInfo();
                this.hideUserRegistration();
                return true;
            }
            else {
                this.log("Invalid nickname");
                return false;
            }
        },

        updateUserPincode: function(pincode) {
            pincode = pincode.toString();
            if (this.validateUserPincode(pincode) === true) {
                this.userInfo.pincode = pincode;
                this.saveUserInfo();
                this.hideUserRegistration();
                return true;
            }
            else {
                this.log("Invalid pincode");
                return false;
            }
        },

        updateScoreTime: function(score, time) {
            score = parseInt(score) || 0;
            time = parseInt(time) || 0;

            if ( this.scoreData.currentGameScore !== score || this.scoreData.currentGameTime !== time ) {
                this.scoreData.pendingScoreSubmission = true;
            }

            this.scoreData.currentGameScore = score;
            this.scoreData.currentGameTime = time;
        },

        updateAllianceList: function(sort) {
            if(sort>0) ig.game.playButton();

            var leaderboardContentElement = ig.domHandler.getElementById("#alliance-listing-contents");
            if (leaderboardContentElement) {
                ig.domHandler.html(leaderboardContentElement, this.settings.strings.contentLoading);
            }

            this.getListingDataFromServer(sort,function(response) {
                this.log(" listing data received");
                this.listingData = response.data;
                var leaderboardHtml = "";
                // leaderboardHtml += '<div id="alliance-listing-entry" class="alliance-box-subheader">';
                if (this.listingData !== null && typeof(this.listingData) !== "undefined" && this.listingData.length > 0) {
                    var maximumEntry = Math.min(this.settings.listingMaxEntries, this.listingData.length);
                    for (var i = 0; i < maximumEntry; i++) {
                        var listingEntry = this.listingData[i];
                        var allianceNameShort=listingEntry.name.substr(0,8);
                        var allianceNameLong=listingEntry.name;
                        if(allianceNameLong.length>8){
                            allianceNameShort += '...';
                        }
                        
                        if(ig.ua.mobile){
                            var imageSize="width:40px;height:25px;margin-top: -2px;";
                            var numMargin="margin-right: 0px;padding-left:0px;";
                            var viewSize="width:20px;height:20px;";

                        }else{
                            var imageSize="width:60px;height:40px;margin-top: -8px;";
                            var numMargin="margin-right: -10px;padding-left:10px;";
                            var viewSize="width:26px;height:26px;";
                            var allianceName=listingEntry.name;
                        }
                        leaderboardHtml += '\
                            <div class="section group">\
                                <div class="col span_1_of_12 top-space-text" style="'+numMargin+'">' + parseInt( 1 + i )  + '</div>\
                                <div class="col span_1_of_12"><img src="'+this.settings.iconAlliance[listingEntry.icon_id]+'" style="margin-left:-10px;"></div>\
                                <div class="col span_2_of_12 top-space-text" ><span style="margin-left:5px;">' + listingEntry.shortcode  + '</span></div>\
                                <div class="col span_3_of_12 top-space-text"><div class="alliance-name-long">' + allianceNameLong + '</div><div class="alliance-name-short">' + allianceNameShort + '</div></div>\
                                <div class="col span_2_of_12 top-space-text" style="text-align:center">' + listingEntry.total_members + '/50 </div>\
                                <div class="col span_2_of_12 top-space-text" style="text-align:right;">' + listingEntry.total_experience + '</div>\
                                <div class="col span_2_of_12" style="text-align:center"><div class="divlink" onclick="ig.alliance.viewAlliance('+listingEntry.alliance_id+','+"'allianceListing'"+')"><img src="'+this.settings.iconView+'" style="'+viewSize+'"></div></div>\
                            </div>\
                        ';
                    }
                }

                if (leaderboardContentElement) {
                    ig.domHandler.html(leaderboardContentElement, leaderboardHtml);
                }


                this.updateOrientation()

            }.bind(this), function(response) {
                this.log("Error getting leaderboard data");

                if (leaderboardContentElement) {
                    ig.domHandler.html(leaderboardContentElement, this.settings.strings.contentError);
                }
            }.bind(this));


        },
        viewAlliance:function(allianceId,origin){
            ig.game.playButton();

            if(this.settings.gameId !== null && typeof (this.settings.gameId) !== "undefined"){
                var url, method, timeout;

                url = this.settings.readAlliance.url + ('?game_id=' + this.settings.gameId) + ('&alliance_id='+allianceId);
                method = this.settings.readAlliance.method;
                timeout = this.settings.readAlliance.timeout;

                this.callXHR(method, url, timeout, null, function(response) {
                    console.log(response);
                    
                    if(this.isMemberAlliance(response.data.members)){
                        this.showAllianceRead(response,origin);
                        console.log('member');
                    }else{
                        // if(this.userInfo.alliance!=''){
                        //     this.userInfo.alliance='';
                        //     ig.game.svasData.alliance='';
                        //     ig.game.client.saveGameData();
                        //     this.showAllianceList(response,origin);
                        // }else{
                            this.showAllianceRead(response,origin);
                            // this.showAllianceList(response,origin);
                        // }
                        console.log('non member');
                    }

                }.bind(this), function(response) {
                    console.log(response);
                    this.showNotification(response.message);
                    // this.showAllianceRead(response,origin);
                }.bind(this));
            }
        },
        isMemberAlliance:function(members){
            var result=false;
            for(var i=0;i<members.length;i++){
                if(members[i].uid==this.userInfo.id){
                    var result=true;
                }
            }
            return result;
        },
        showAllianceRead: function(data,origin) {
            if(origin=='mainWindow'){
                if (this.divElements["allianceRead"] === null || typeof(this.divElements["allianceRead"]) === "undefined" ) {
                    this.createAllianceReadDiv(data.data,origin);                 
                    ig.domHandler.show(this.divElements["allianceRead"]);
                }else{
                    this.updateAllianceReadDiv(data.data,origin);                                 
                    ig.domHandler.show(this.divElements["allianceRead"]);
                }

            }else{            
                if (this.divElements["allianceRead"] === null || typeof(this.divElements["allianceRead"]) === "undefined" ) {
                    this.createAllianceReadDiv(data.data,origin);                 
                    ig.domHandler.hide(this.divElements[origin]);
                    ig.domHandler.show(this.divElements["allianceRead"]);
                }else{
                    this.updateAllianceReadDiv(data.data,origin);                                 
                    ig.domHandler.hide(this.divElements[origin]);
                    ig.domHandler.show(this.divElements["allianceRead"]);
                }
            }

        },
        hideAllianceRead: function() {
            var origin=document.getElementById("div-origin").value;
            if(origin=='mainWindow'){
                if (this.divElements["allianceRead"] !== null && typeof(this.divElements["allianceRead"]) !== "undefined" ) {
                    ig.domHandler.hide(this.divElements["allianceRead"]);
                }
            }else{            
                if (this.divElements["allianceRead"] !== null && typeof(this.divElements["allianceRead"]) !== "undefined" ) {
                    ig.domHandler.hide(this.divElements["allianceRead"]);
                    ig.domHandler.show(this.divElements[origin]);
                    this.updateAllianceList(0);
                }
            }
        },


        doSearching: function() {
            ig.game.playButton();
            
            var leaderboardContentElement = ig.domHandler.getElementById("#" + "alliance-searching-contents");
            
            // this.updateAllianceListFooter();

            if (leaderboardContentElement) {
                ig.domHandler.html(leaderboardContentElement, this.settings.strings.contentLoading);
            }
            this.getSearchDataFromServer(function(response) {
                this.listingData = response.data;

                var leaderboardHtml = "";
                leaderboardHtml += '<div id="alliance-searching-entry" class="alliance-box-subheader">';
                if (this.listingData !== null && typeof(this.listingData) !== "undefined" && this.listingData.length > 0) {
                    var maximumEntry = Math.min(this.settings.listingMaxEntries, this.listingData.length);
                    for (var i = 0; i < maximumEntry; i++) {
                        var listingEntry = this.listingData[i];

                         if(ig.ua.mobile){
                            var imageSize="width:40px;height:25px;margin-top: -2px;";
                            var numMargin="margin-right: 0px;padding-left:0px;";
                            var viewSize="width:20px;height:20px;";
                         }else{
                            var imageSize="width:60px;height:40px;margin-top: -8px;";
                            var numMargin="margin-right: -10px;padding-left:10px;";
                            var viewSize="width:26px;height:26px;";
                         }

                         leaderboardHtml += '\
                            <div class="section group">\
                                <div class="col span_1_of_12 top-space-text" style="'+numMargin+'">' + parseInt( 1 + i )  + '</div>\
                                <div class="col span_1_of_12"><img src="'+this.settings.iconAlliance[listingEntry.icon_id]+'" style="'+imageSize+';margin-left:-10px;"></div>\
                                <div class="col span_2_of_12 top-space-text"><span style="margin-left:10px;">' + listingEntry.shortcode  + '</span></div>\
                                <div class="col span_3_of_12 top-space-text">' + listingEntry.name + '</div>\
                                <div class="col span_2_of_12 top-space-text" style="text-align:center">' + listingEntry.total_members + '/50 </div>\
                                <div class="col span_2_of_12 top-space-text" style="text-align:right;">' + listingEntry.total_experience + '</div>\
                                <div class="col span_2_of_12" style="text-align:center"><div class="divlink" onclick="ig.alliance.viewAlliance('+listingEntry.alliance_id+','+"'allianceSearch'"+')"><img src="'+this.settings.iconView+'" style="'+viewSize+'"></div></div>\
                            </div>\
                        ';
                    }
                }
                leaderboardHtml += '</div>';

                var leaderboardContentElement = ig.domHandler.getElementById("#" + "alliance-searching-contents");
                if (leaderboardContentElement !== null && typeof (leaderboardContentElement) !== "undefined") {
                    ig.domHandler.html(leaderboardContentElement, leaderboardHtml);
                }
            }.bind(this), function(response) {
                this.log("Error getting leaderboard data");

                if (leaderboardContentElement) {
                    ig.domHandler.html(leaderboardContentElement, this.settings.strings.contentError);
                }
            }.bind(this));
        },


        getListingDataFromServer: function(sortType,successfulCallback, errorCallback) {

            if(this.settings.gameId !== null && typeof (this.settings.gameId) !== "undefined"){
                var url, method, timeout;

                if(sortType==0){
                    var sortText='';
                }else if(sortType==1){
                    var sortText='&sort=descending&type=experience';
                }else if(sortType==2){
                    var sortText='&sort=ascending&type=experience';
                }else if(sortType==3){
                    var sortText='&sort=ascending&type=date';
                }else if(sortType==4){
                    var sortText='&sort=descending&type=date';
                }

                url = this.settings.listAlliance.url + ('?game_id=' + this.settings.gameId) + sortText;
                method = this.settings.listAlliance.method;
                timeout = this.settings.listAlliance.timeout;
                this.callXHR(method, url, timeout, null, function(response) {
                    // console.log(response);
                    if (successfulCallback !== null && typeof (successfulCallback) === "function") {
                        successfulCallback(response);
                    }
                }.bind(this), function(response) {
                    // console.log(response);
                    if (errorCallback !== null && typeof (errorCallback) === "function") {
                        errorCallback(response);
                    }
                }.bind(this));
            }
        },

        getSearchDataFromServer: function(successfulCallback, errorCallback) {

            if(this.settings.gameId !== null && typeof (this.settings.gameId) !== "undefined"){
                var url, method, timeout,keyword;

                keyword = document.getElementById('search-keyword').value;
                if(keyword=='') keyword='xxxxxxxxxxxxxxxxxxxxxxx';


                url = this.settings.searchAlliance.url + ('?game_id=' + this.settings.gameId) + ('&sort=descending&type=experience') + ('&keyword='+keyword);
                method = this.settings.searchAlliance.method;
                timeout = this.settings.searchAlliance.timeout;

                this.callXHR(method, url, timeout, null, function(response) {
                    console.log(response);
                    if(response.status!=200){
                        this.showNotification(response.message);                        
                    }
                    if (successfulCallback !== null && typeof (successfulCallback) === "function") {
                        successfulCallback(response);
                    }
                }.bind(this), function(response) {
                    console.log(response);
                    if (errorCallback !== null && typeof (errorCallback) === "function") {
                        errorCallback(response);
                    }
                }.bind(this));
            }
        },

        validateUserId: function(id) {
            return (id !== null && typeof (id) !== "undefined" && id.length > 0);
        },

        validateAllianceName: function(name) {
            var isValidname = true;
            var notificationMessage = "";

            var alphanumericRegex = new RegExp("^[a-zA-Z0-9]+$");
            if (!alphanumericRegex.test(name)) {
                isValidname = false;
                notificationMessage = this.settings.strings.allianceNameNotificationInvalid;
            }

            if (name.length < this.settings.allianceName.minLength) {
                isValidname = false;
                notificationMessage = this.settings.strings.allianceNameNotificationMinLength.replace("{minLength}", this.settings.allianceName.minLength);
            }

            if (name.length > this.settings.allianceName.maxLength) {
                isValidname = false;
                notificationMessage = this.settings.strings.allianceNameNotificationMaxLength.replace("{maxLength}", this.settings.allianceName.maxLength);
            }

            if (name === null || typeof (name) !== "string" || name === "") {
                isValidname = false;
                notificationMessage = this.settings.strings.allianceNameNotificationEmpty;
            }

            if(notificationMessage!==""){
                this.showNotification(notificationMessage);
            }

            return isValidname;
        },

        validateShortCode: function(code) {
            var isValidShortCode = true;
            var notificationMessage = "";

            var alphanumericRegex = new RegExp("^[a-zA-Z0-9]+$");
            if (!alphanumericRegex.test(code)) {
                isValidShortCode = false;
                notificationMessage = this.settings.strings.shortcodeNotificationInvalid;
            }

            if (code.length !== this.settings.allianceShortCode.fixedLength) {
                isValidShortCode = false;
                notificationMessage = this.settings.strings.shortcodeNotificationFixedLength.replace("{fixedLength}", this.settings.allianceShortCode.fixedLength);
            }

            if (code === null || typeof (code) !== "string" || code === "") {
                isValidShortCode = false;
                notificationMessage = this.settings.strings.shortcodeNotificationEmpty;
            }

            if(notificationMessage!==""){
                this.showNotification(notificationMessage);
            }

            return isValidShortCode;
        },        


        callXHR: function(method, url, timeout, data, successfulCallback, errorCallback) {
            this.showLoader();

            var xhr = new XMLHttpRequest();
            xhr.open(method, url, true);
            xhr.timeout = timeout || 5000;
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    var response = "";
                    try {
                        response = JSON.parse(xhr.responseText);
                        if (xhr.status === 200) {
                            if (successfulCallback !== null && typeof (successfulCallback) === "function") {
                                successfulCallback(response);
                            }
                        }
                        else {
                            if (errorCallback !== null && typeof (errorCallback) === "function") {
                                errorCallback(response);
                            }
                        }
                    } catch(error) {
                        // if (errorCallback !== null && typeof (errorCallback) === "function") {
                        //     errorCallback(response);
                        // }
                    }
                    this.hideLoader();
                }
            }.bind(this);

            if (data !== null && typeof (data) !== "undefined") {
                xhr.send(data);
            } else {
                xhr.send();
            }
        },


        log: function(message) {
            if (this.parameters.verbose) {
                switch (this.parameters.verbose) {
                    case "false":
                    case "off":
                    case "0":
                    case "":
                    case null:
                    case undefined:
                    break;

                    default:
                    case "true":
                    case "on":
                    case "1":
                        if (window.console && typeof(window.console.log) === "function") {
                            console.log("%c [" + this.NAME + " v" + this.VERSION + "] " + message + " ", "color: #F35E36; background: #0B31A1;");
                        }
                    break;

                    case "trace": 
                    case "2":
                        if (window.console && typeof(window.console.trace) === "function") {
                            console.trace("%c [" + this.NAME + " v" + this.VERSION + "] " + message + " ", "color: #F35E36; background: #0B31A1;");
                        }
                    break;
                }
            }
        }
    });
});