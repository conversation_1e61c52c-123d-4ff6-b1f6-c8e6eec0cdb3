// <PERSON>'s Textinput Container Class
// <EMAIL>
// ver 1.0

// Notes:

// Known Issues:
// - assumes that ig.size<PERSON>and<PERSON> and ig.dom<PERSON>andler has been initialized

// Usage Examples:
/*
var textinput = new ig.TextinputContainer("myTextInputName", 14, "Impact, Charcoal, sans-serif", 12, "default", 200, 40);
textinput.setPos(200, 50);
textinput.setRect(-100, -20, 200, 40);
ig.domHandler.css(textinput.textInputElement,
    {
        margin:"0px",
        border:"0px",
        padding:"0px",
        background:"none",
        color:"rgba(0,0,0,0)",
        "text-align":"center"
    }
);
this.textInput = textinput;

var output = this.textinput.val();
this.textinput.val("abc");
this.textinput.hide();
this.textinput.show();
*/

ig.module('plugins.textinput-container')
.requires(
)
.defines(function () {
ig.TextinputContainer = ig.Class.extend({
    containerName: 'text_input_container',
    textInputName: 'text_input',

    containerElement: null,
    textInputElement: null,

    textFontSize: 18,
    textFont: 'DroidSans, Impact, Charcoal, sans-serif',

    maxTextLength: 100,
    defaultValue: null,

    pos: { x: 0, y: 0 },
    rect: { x: 0, y: 0, w: 0, h: 0 },

    init: function (id, size, font, maxTextLength, defaultValue, w, h) {
        if (id) {
            this.textInputName = id;
            this.containerName = id + '_container';
        }
        if (size) {
            this.textFontSize = size;
        }
        if (font) {
            this.textFont = font;
        }
        if (maxTextLength != null) {
            this.maxTextLength = maxTextLength;
        }

        if (defaultValue != null) {
            this.defaultValue = defaultValue;
        } else {
            if (ig.game.sessionData.playerName != '') {
                this.defaultValue = ig.game.sessionData.playerName;
            } else {
                var idx = Math.floor((Math.random() * ig.game.names.length));
                this.defaultValue = ig.game.names[idx];
            }
        }

        if (!isNaN(w) && !isNaN(h)) {
            this.setRect(-w / 2, -h / 2, w, h);
        }

        this.spawnTextInput();
    },

    setRect: function (x, y, w, h) {
        if (isNaN(x) || isNaN(y) || isNaN(w) || isNaN(h)) {
            return;
        }

        this.rect.x = x;
        this.rect.y = y;
        this.rect.w = w;
        this.rect.h = h;

        // this.updateElementPosition();
    },

    setPos: function (x, y) {
        if (isNaN(x) || isNaN(y)) {
            return;
        }

        this.pos.x = x;
        this.pos.y = y;
        // this.updateElementPosition();
    },

    updateElementPosition: function () {
        var id = this.containerName;
        var elemExists = ig.domHandler.getElementById('#' + id);

        var widthRatio = ig.ua.mobile ? ig.sizeHandler.sizeRatio.x : ig.sizeHandler.scaleRatioMultiplier.x;
        var heightRatio = ig.ua.mobile ? ig.sizeHandler.sizeRatio.y : ig.sizeHandler.scaleRatioMultiplier.y;

        if (elemExists) {
            var pos = {};
            pos.x = this.pos.x + this.rect.x;
            pos.y = this.pos.y + this.rect.y;
            var rect = this.rect;

            var aspectRatioMin = Math.min(widthRatio, heightRatio);
            var div = this.containerElement;
            if (ig.ua.mobile)
            {
                var divleft = Math.floor(pos.x * widthRatio) + 'px';
                var divtop = Math.floor(pos.y * heightRatio) + 'px';
                var divwidth = Math.floor(rect.w * widthRatio) + 'px';
                var divheight = Math.floor(rect.h * heightRatio) + 'px';

                ig.domHandler.css(div
                    , {
                        float: 'left',
                        position: 'absolute',
                        left: divleft,
                        top: divtop,
                        width: divwidth,
                        height: divheight,
                        'z-index': 3
                    }
                );
            }
            else
            {
                var canvas = ig.domHandler.getElementById('#canvas');
                var offsets = ig.domHandler.getOffsets(canvas);

                var offsetLeft = offsets.left;
                var offsetTop = offsets.top;

                var divleft = Math.floor(offsetLeft + pos.x * aspectRatioMin) + 'px';
                var divtop = Math.floor(offsetTop + pos.y * aspectRatioMin) + 'px';
                var divwidth = Math.floor(rect.w * aspectRatioMin) + 'px';
                var divheight = Math.floor(rect.h * aspectRatioMin) + 'px';

                ig.domHandler.css(div
                    , {
                        float: 'left',
                        position: 'absolute',
                        left: divleft,
                        top: divtop,
                        width: divwidth,
                        height: divheight,
                        'z-index': 3
                    }
                );
            }
            ig.sizeHandler.dynamicClickableEntityDivs[id] = {};
            ig.sizeHandler.dynamicClickableEntityDivs[id].width = rect.w;
            ig.sizeHandler.dynamicClickableEntityDivs[id].height = rect.h;
            ig.sizeHandler.dynamicClickableEntityDivs[id].entity_pos_x = pos.x;
            ig.sizeHandler.dynamicClickableEntityDivs[id].entity_pos_y = pos.y;
        }
    },

    show: function () {
        if (this.containerElement) {
            ig.domHandler.show(this.containerElement);
            ig.domHandler.show(this.textInputElement);
        }
    },

    hide: function () {
        if (this.containerElement) {
            ig.domHandler.hide(this.containerElement);
            ig.domHandler.hide(this.textInputElement);
        }
    },

    val: function (new_value) {
        if (!this.textInputElement) return '';

        if (new_value == null) {
            return this.textInputElement.val();
        } else {
            return this.textInputElement.val(new_value);
        }
    },

    spawnTextInput: function () {
        var widthRatio = ig.sizeHandler.sizeRatio.x;
        var heightRatio = ig.sizeHandler.sizeRatio.y;

        var pos = {};
        pos.x = this.pos.x + this.rect.x;
        pos.y = this.pos.y + this.rect.y;
        var rect = this.rect;

        var id = this.containerName;
        var textInputId = this.textInputName;

        var elem1Exists = ig.domHandler.getElementById('#' + id);
        var elem2Exists = ig.domHandler.getElementById('#' + textInputId);

        if (elem1Exists && elem2Exists)
        {
            this.containerElement = $('#' + id);
            this.textInputElement = $('#' + textInputId);

            ig.domHandler.show(this.containerElement);
            ig.domHandler.show(this.textInputElement);
            return;
        }

        var div = ig.domHandler.create('div');
        ig.domHandler.attr(div, 'id', id);
        div.blur(function (event) {
            // event.stopPropagation();
        });
        this.containerElement = div;

        var newInput = ig.domHandler.create('input');
        ig.domHandler.attr(newInput, 'id', textInputId);
        newInput.blur(function (event) {
            event.stopPropagation();
        });
        this.textInputElement = newInput;

        var aspectRatioMin = Math.min(widthRatio, heightRatio);
        var fontSize = this.textFontSize * aspectRatioMin;

        if (ig.ua.mobile)
        {
            var divleft = Math.floor(pos.x * widthRatio) + 'px';
            var divtop = Math.floor(pos.y * heightRatio) + 'px';
            var divwidth = Math.floor(rect.w * widthRatio) + 'px';
            var divheight = Math.floor(rect.h * heightRatio) + 'px';

            ig.domHandler.css(div
                                , {
                                    float: 'left',
                                    position: 'absolute',
                                    left: divleft,
                                    top: divtop,
                                    width: divwidth,
                                    height: divheight,
                                    'z-index': 3
                                }
                            );
        }
        else
        {
            var canvas = ig.domHandler.getElementById('#canvas');
            var offsets = ig.domHandler.getOffsets(canvas);

            var offsetLeft = offsets.left;
            var offsetTop = offsets.top;

            var divleft = Math.floor(offsetLeft + pos.x * aspectRatioMin) + 'px';
            var divtop = Math.floor(offsetTop + pos.y * aspectRatioMin) + 'px';
            var divwidth = Math.floor(rect.w * aspectRatioMin) + 'px';
            var divheight = Math.floor(rect.h * aspectRatioMin) + 'px';

            ig.domHandler.css(div
                                , {
                                    float: 'left',
                                    position: 'absolute',
                                    left: divleft,
                                    top: divtop,
                                    width: divwidth,
                                    height: divheight,
                                    'z-index': 3
                                }
                            );
        }

        ig.domHandler.css(newInput
                            , {
                                width: '100%',
                                height: '100%',
                                //, margin:"0px"
                                //, border:"0px"
                                //, padding:"0px"
                                background: 'rgba(0,0,0,0)',
                                 border: 'none',
                                 outline: 'none',
                                //, color:"rgba(0,0,0,0)"
                                //, opacity:"0.00"
                                //, "text-align":"center"
                                 'font-family': this.textFont,
                                'font-size': fontSize + 'px',
                                'z-index': 3
                            }
                        );

        ig.domHandler.css(newInput
                            , {
                                //, background:"none"
                                //, color:"rgba(0,0,0,0)"
                                //, opacity:"0.00"
                            }
                        );

        newInput.attr('maxlength', this.maxTextLength);
        newInput.attr('size', this.maxTextLength);
        newInput.attr('autocomplete', 'off');
        newInput.attr('autocapitalize', 'off');
        newInput.attr('autocorrect', 'off');

        newInput.val(this.defaultValue);

        newInput.keyup(function (e) {
            if (e.keyCode == 13) {
                this.textInputElement.blur();
            }
        }.bind(this));

        ig.domHandler.addEvent(div, 'mousemove', ig.input.mousemove.bind(ig.input), false);

        ig.domHandler.appendChild(div, newInput);
        ig.domHandler.appendToBody(div);

        // // ADD TO HANDLER FOR RESIZING
        ig.sizeHandler.dynamicClickableEntityDivs[id] = {};
        ig.sizeHandler.dynamicClickableEntityDivs[id].width = rect.w;
        ig.sizeHandler.dynamicClickableEntityDivs[id].height = rect.h;
        ig.sizeHandler.dynamicClickableEntityDivs[id].entity_pos_x = pos.x;
        ig.sizeHandler.dynamicClickableEntityDivs[id].entity_pos_y = pos.y;

        ig.sizeHandler.dynamicClickableEntityDivs[textInputId] = {};
        ig.sizeHandler.dynamicClickableEntityDivs[textInputId]['font-size'] = this.textFontSize;

        // ADD TO HANDLER FOR RESIZING
        // dynamicClickableEntityDivs[id] = {};
        // dynamicClickableEntityDivs[id]['width'] = rect.w;
        // dynamicClickableEntityDivs[id]['height'] = rect.h;
        // dynamicClickableEntityDivs[id]['entity_pos_x'] = pos.x;
        // dynamicClickableEntityDivs[id]['entity_pos_y'] = pos.y;

        // dynamicClickableEntityDivs[textInputId] = {};
        // dynamicClickableEntityDivs[textInputId]['font-size'] = this.textFontSize;
    }
});
});
