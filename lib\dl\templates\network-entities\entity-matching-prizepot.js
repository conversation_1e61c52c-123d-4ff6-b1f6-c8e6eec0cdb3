ig.module(
    'dl.templates.network-entities.entity-matching-prizepot'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'game.entities.movecoin',
    'dl.templates.entities.entity-level',
    'dl.network.mixins.matching',
    'dl.templates.entities.backgrounds.entity-color-background',
    'dl.templates.entities.entity-text',
    'dl.templates.network-entities.entity-matching-players',
    'dl.game.utilities.entities-helper-mixin'
).defines(function () {
    'use strict';

    dl.EntityMatchingPrizePot = dl.Entity
        .extend(dl.MixinDocker)
        .extend({
            staticInstantiate: function () {
                this.parent();
                this.players = [];
                this.spacing = 25;
                this.showCrateStatus=false;
                return this;
            },
            postInit: function () {
                this.parent();
                this.initPlayers();
            },
            update:function(){
                this.parent();

                var xx=window.innerWidth;
                var yy=window.innerHeight;

                if (ig.sizeHandler.isPortraitOrientation) {
                    var size = 600;
                    var mult = 1;
                    var rowData = [2, 2]; // number of col in row
                    var offset = { x: 0.25, y: 0.25 };

                    if(ig.ua.mobile && !ig.ua.iPad){
                        rowData = [2, 2];
                        offset = { x: 0.5, y: 0.5 };
                    }else{
                        rowData = [4];
                        offset = { x: 0.16, y: 0.4};
                        mult = 1.5;
                    }


                    var maxCol = 0;
                    var index = -1;
                    for (var row = 0, rowLength = rowData.length; row < rowLength; row++) {
                        for (var col = 0, colLength = rowData[row]; col < colLength; col++) {
                            var startOffset = {
                                x: (-offset.x * colLength + offset.x) * 0.5,
                                y: (-offset.y * rowLength + offset.y) * 0.5
                            };
                            index += 1;

                            var player = this.avaPlayer[index];
                            player.c_DockerComponent.updateProperties({
                                dockerObject: this,
                                dockerPercent: {
                                    x: 0.5 + startOffset.x + offset.x * col,
                                    y: 0.5 + startOffset.y + offset.y * row
                                },
                                dockerOffset: { x: 0, y: 0 }
                            });

                            if (maxCol < colLength) {
                                maxCol = colLength;
                            }
                        }
                    }

                    this.setSize(size * maxCol, size * mult * rowData.length);


                }else{
                    if(this.players.length==2){
                        var offsetX = 0.5;
                    }else if(this.players.length==3){
                        var offsetX = 0.33;
                    }else {
                        var offsetX = 0.25;
                    }


                    var startOffsetX = (-offsetX * this.players.length + offsetX) * 0.5;
    
                    for(var i=0;i<this.players.length;i++){

                            var player = this.avaPlayer[i];
                            player.c_DockerComponent.updateProperties({
                                dockerObject: this,
                                dockerPercent: {
                                    x: 0.5 + startOffsetX + offsetX * i,
                                    y: 0.5 
                                },
                                dockerOffset: { x: 0, y: 0 }
                            });

                    }

                    this.setSize(600 * this.players.length, ig.system.height*2);
                }

            },
            initPlayers: function () {
                this.players = [];
                this.avaFrame=[];
                this.avaPlayer=[];
                this.avaName=[];
                // this.avaAlliance=[];
                // this.avaAllianceIcon=[];
                this.avaCoin=[];
                this.players = ig.game.playerMatched;
                // console.log(ig.game.matchingAllianceData);

                this.setSize(600 * this.players.length, ig.system.height*2);

                var offsetX = 0.25;
                var startOffsetX = (-offsetX * this.players.length + offsetX) * 0.5;
                for(var i=0;i<this.players.length;i++){

                    this.avaPlayer.push(this.spawnEntity(dl.EntityImage, {
                        _useParentScale: true,
                        c_DockerComponent: {
                            dockerObject: this,
                            dockerPercent: { x: 0.5 + startOffsetX + offsetX * i, y: 0.5 },
                            dockerOffset: { x: 0, y: 0 }
                        },
                        _scale:{x:0.5,y:0.5},
                        image: dl.preload['name-avatar']['avatar-frame-gray']
                    }));

                    this.avaPlayer[i].updateImage(dl.preload['name-avatar'].avatars[this.players[i].playerAvatarId]);


                    this.avaFrame.push(this.spawnEntity(dl.EntityImage, {
                        _useParentScale: true,
                        c_DockerComponent: {
                            dockerObject: this.avaPlayer[i],
                            dockerPercent: { x: 0.5, y: 0.5 },
                            dockerOffset: { x: 0, y: 0 }
                        },
                        image: dl.preload['name-avatar']['avatar-frame-gray']
                    }));
    

                    this.avaName.push(this.spawnEntity(dl.EntityText, {
                        _useParentScale: true,
                        c_DockerComponent: {
                            dockerObject: this.avaPlayer[i],
                            dockerPercent: { x: 0.5, y: 1 },
                            dockerOffset: { x: 0, y: this.spacing }
                        },
                        anchor: { x: 0.5, y: 0 },
                        c_TextComponent: {
                            fontSize: 40,
                            fontFamily: dl.configs.getConfig('FONT', 'bold').name
                        },
                        text: ''
                    }));

                    // this.avaAllianceIcon.push(this.spawnEntity(dl.EntityImage, {
                    //     _useParentScale: true,
                    //     c_DockerComponent: {
                    //         dockerObject: this.avaName[i],
                    //         dockerPercent: { x: 0, y: 1 },
                    //         dockerOffset: { x: -60, y: this.spacing+6 }
                    //     },
                    //     c_AnimationSheetComponent: {
                    //         _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                    //         _size: {x:110,y:80}
                    //     },
                    //     anchor: { x: 0, y: 0.2 },
                    //     image: dl.preload['emptyAllianceIcon']
                    // }));

                    // this.avaAlliance.push(this.spawnEntity(dl.EntityText, {
                    //         _useParentScale: true,
                    //         c_DockerComponent: {
                    //             dockerObject: this.avaName[i],
                    //             dockerPercent: { x: 0.4, y: 1 },
                    //             dockerOffset: { x: 20, y: this.spacing+6 }
                    //         },
                    //         anchor: { x: 0, y: 0 },
                    //         c_TextComponent: {
                    //             fontSize: 40,
                    //             fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                    //             textAlign: 'left', 
                    //             textBaseline: 'middle',
                    //         },
                    //         text: ''
                    //     }));


                    if (i === 0) {
                        this.avaName[i].updateTextColor(dl.configs.getConfig('TEXT_COLOR', 'PLAYER_1'));
                        this.avaFrame[i].updateImage(dl.preload['name-avatar']['avatar-frame-red']);
                    } else if (i === 1) {
                        this.avaName[i].updateTextColor(dl.configs.getConfig('TEXT_COLOR', 'PLAYER_2'));
                        this.avaFrame[i].updateImage(dl.preload['name-avatar']['avatar-frame-blue']);
                    } else if (i === 2) {
                        this.avaName[i].updateTextColor(dl.configs.getConfig('TEXT_COLOR', 'PLAYER_3'));
                        this.avaFrame[i].updateImage(dl.preload['name-avatar']['avatar-frame-green']);
                    } else if (i === 3) {
                        this.avaName[i].updateTextColor(dl.configs.getConfig('TEXT_COLOR', 'PLAYER_4'));
                        this.avaFrame[i].updateImage(dl.preload['name-avatar']['avatar-frame-yellow']);
                    }

                    this.avaName[i].updateText(this.players[i].playerName);
                    // if(ig.game.matchingAllianceData[i]) this.avaAlliance[i].updateText(ig.game.matchingAllianceData[i].allianceName);
                    // if(ig.game.matchingAllianceData[i]) this.avaAllianceIcon[i].updateImage(dl.preload['allianceIcon'+ig.game.matchingAllianceData[i].allianceIcon]);
          
                }             
            },
            coinMove:[],
            showCrate:function(){
                if(this.showCrateStatus) return;
                this.showCrateStatus = true;


                if(ig.ua.mobile && !ig.ua.iPad){
                    this.control.initCrateImage();
                }else{
                    if (ig.sizeHandler.isPortraitOrientation) {
                        this.control.initCrateImage();
                    }
                }

                this.crateImg=this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: dl.game.camera,
                        dockerPercent: { x: 0.5, y: 0.35 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:500,y:440}
                    },
                    image: dl.preload['chestclose']
                })
                // dl.TweenTemplate.fadeIn(this.crateImg,1000);


                this.crateCoin=this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.crateImg,
                        dockerPercent: { x: 0.1, y: 0 },
                        dockerOffset: { x: 0, y: -80 }
                    },
                    anchor: { x: 0, y: 0.5 },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:100,y:100}
                    },
                    image: dl.preload['coin']
                });

                var coinTarget = this.crateImg.pos;

                // dl.TweenTemplate.scaleIn(this.crateCoin, 1000);

                this.crateEntryFee=this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.crateImg,
                        dockerPercent: { x: 0.4, y: 0 },
                        dockerOffset: { x: 0, y: -80 }
                    },
                    anchor: { x: 0, y: 0.5 },
                    c_TextComponent: {
                        fontSize: 70,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    text: 0
                });
                // dl.TweenTemplate.scaleIn(this.crateEntryFee, 1000);


                this.avaCoin=[];
                this.avaEntryFee=[];
                var entryFee=[0,200,500,2500,1500]
                // this.coinMove=[];

                for(var i=0;i<this.players.length;i++){
                    this.coinMove.push([]);
                    this.avaCoin.push(this.spawnEntity(dl.EntityImage, {
                        _useParentScale: true,
                        c_DockerComponent: {
                            dockerObject: this.avaPlayer[i],
                            dockerPercent: { x: 0, y: 0 },
                            dockerOffset: { x: 0, y: -50 }
                        },
                        anchor: { x: 0, y: 0.5 },
                        c_AnimationSheetComponent: {
                            _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                            _size: {x:100,y:100}
                        },
                        image: dl.preload['coin']
                    }));

                    // dl.TweenTemplate.scaleIn(this.avaCoin[i], 1000);

                    this.avaEntryFee.push(this.spawnEntity(dl.EntityText, {
                        _useParentScale: true,
                        c_DockerComponent: {
                            dockerObject: this.avaPlayer[i],
                            dockerPercent: { x: 0.5, y: 0 },
                            dockerOffset: { x: 0, y: -50 }
                        },
                        anchor: { x: 0, y: 0.5 },
                        c_TextComponent: {
                            fontSize: 70,
                            fontFamily: dl.configs.getConfig('FONT', 'bold').name
                        },
                        text: entryFee[ig.game.client.roomArena]
                    }));
                    this.createMoveCoin(this.avaCoin[i].pos,coinTarget,this.avaEntryFee[i],entryFee[ig.game.client.roomArena]);

                }
            },

            createMoveCoin:function(sourcePos,targetPos,reduceObj,fee){
                if(ig.ua.mobile){
                    var length=10
                    var delay=160;
                }else{
                    var length=40
                    var delay=80;
                }

                var diff=fee/length;
                var j=1;
                for(i=1;i<=length;i++){
                    setTimeout(function(){
                        var val=(fee-(j*diff)).toFixed(0);
                        reduceObj.updateText(val);
                        var posx=ig.system.width/2 + sourcePos.x-50;
                        var posy=ig.system.height/2 + sourcePos.y-50;
                        ig.game.spawnEntityBackward(EntityMoveCoin,posx,posy,{control:this,target:targetPos,addVal:diff});
                        j++;
                    }.bind(this),1000+i*delay);    
                }

                setTimeout(function(){
                    // ig.game.director.jumpTo(LevelInstruction);
                    ig.game.director.loadLevel(6);
                },2600+500+i*delay);

                setTimeout(function(){
                    this.tweenOut();
                }.bind(this),2600+i*delay);
            },

            prize:0,
            addPrizeText:function(fee){
                var entryFee=[0,200,500,2500,1500];
                var max=entryFee[ig.game.client.roomArena];                
                this.prize +=Number(fee);                
                if(this.prize > (4*max)) this.prize=4*max;
                this.crateEntryFee.updateText(this.prize.toFixed(0));
                if(ig.ua.mobile && !ig.ua.iPad){
                    this.control.crateEntryFee.updateText(this.prize.toFixed(0));
                }else{
                    if (ig.sizeHandler.isPortraitOrientation) {
                        if(this.control && this.control.crateEntryFee) this.control.crateEntryFee.updateText(this.prize.toFixed(0));
                    }                    
                }
            },
            tweenOut: function () {
                var time = 400;
                if(window.prizepot.crateImg) window.prizepot.crateImg.kill();
                if(window.prizepot.crateCoin) window.prizepot.crateCoin.kill();
                if(window.prizepot.crateEntryFee) window.prizepot.crateEntryFee.kill();


                for(var i=0;i<this.players.length;i++){
                    this.avaPlayer[i].kill();
                    this.avaFrame[i].kill();
                    this.avaName[i].kill();
                    // this.avaAlliance[i].kill();
                    // this.avaAllianceIcon[i].kill();
                    this.avaCoin[i].kill();
                    this.avaEntryFee[i].kill();
          
                }    
                // dl.TweenTemplate.fadeOut(this.crateCoin, time);
                // dl.TweenTemplate.fadeOut(this.crateImg, time);
                // dl.TweenTemplate.fadeOut(this.crateEntryFee, time);
                // dl.TweenTemplate.fadeOut(this.control.title, time);

                this.crateCoin.kill();
                this.crateImg.kill();
                this.crateEntryFee.kill();
                this.control.title.kill();
            }
        });

    dl.enableCacheCanvas(dl.EntityMatchingPrizePot);
});
