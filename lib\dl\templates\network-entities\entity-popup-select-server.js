ig.module(
    'dl.templates.network-entities.entity-popup-select-server'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.popup',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.animation-sheet',
    'dl.templates.mixins.text',
    'dl.templates.entities.buttons.entity-button-image-text',
    'dl.templates.entities.buttons.entity-button-image',
    'dl.network.client.network-client-fake'
).defines(function () {
    'use strict';

    dl.EntityPopupSelectServer = dl.Entity
        .extend(dl.MixinPopup)
        .extend(dl.MixinDocker)
        .extend({
            postInit: function () {
                this.parent();

                this.content = this.spawnEntity(dl.EntityPopupSelectServer_Content, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    }
                });
            }
        });

    dl.EntityPopupSelectServer_Content = dl.Entity
        .extend(dl.MixinPopupContent)
        .extend(dl.MixinDocker)
        .extend(dl.MixinAnimationSheet)
        .extend(dl.MixinText)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this.image = dl.preload['popup-large'];
                this._useAnimationSheetAsSize = true;


                this.text = _STRINGS.NETWORK.POPUP_SELECT_SERVER.TITLE;

                return this;
            },

            postInit: function () {
                this.parent();

                this.redDot=[];
                this.thumbs=[];

                this.initButtons();

                this.tweenIn();

                this.fakeServers = [
                    new NetworkClientFake({
                        server_ip: SERVER_CONFIGS.SERVER_IPS[0],
                        http_port: SERVER_CONFIGS.HTTP_PORT,
                        https_port: SERVER_CONFIGS.HTTPS_PORT,
                        onUpdate: this.updateData.bind(this)
                    }),
                    new NetworkClientFake({
                        server_ip: SERVER_CONFIGS.SERVER_IPS[1],
                        http_port: SERVER_CONFIGS.HTTP_PORT,
                        https_port: SERVER_CONFIGS.HTTPS_PORT,
                        onUpdate: this.updateData.bind(this)
                    }),
                    new NetworkClientFake({
                        server_ip: SERVER_CONFIGS.SERVER_IPS[2],
                        http_port: SERVER_CONFIGS.HTTP_PORT,
                        https_port: SERVER_CONFIGS.HTTPS_PORT,
                        onUpdate: this.updateData.bind(this)
                    })
                ];
            },

            _initTextComponent: function () {
                this.c_TextComponent.updateProperties({
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                    textAlign: 'center', // [center|end|left|right|start];
                    textBaseline: 'middle', // [alphabetic|top|hanging|middle|ideographic|bottom];

                    fontSize: 46,

                    _docker: { x: 0.5, y: 0 },
                    _anchor: { x: 0.5, y: 0 },
                    _offset: { x: 0, y: 30 }
                });
            },

            initButtons: function () {
                var offset = 120;

                this.btn1 = this.spawnEntity(dl.EntityButtonImageText, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.3 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                        fontSize: 40,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    image: dl.preload['button-green'],
                    text: _STRINGS.NETWORK.POPUP_SELECT_SERVER.BUTTON_0,
                    onButtonClicked: this.onButtonSelected.bind(this, 0),
                    onPointerOver:function(){
                        if(this.redDot[0]){
                            this.redDot[0].c_DockerComponent.dockerOffset={ x: 90, y: 0 };
                            this.redDot[0].updatePos();
                        } 
                    }.bind(this),
                    onPointerLeave:function(){
                        if(this.redDot[0]){
                            this.redDot[0].c_DockerComponent.dockerOffset={ x: 70, y: 0 };
                            this.redDot[0].updatePos();
                        }                     
                    }.bind(this),
                    onPointerFirstClick:function(){
                        if(this.redDot[0]){
                            this.redDot[0].c_DockerComponent.dockerOffset={ x: 100, y: 0 };
                            this.redDot[0].updatePos();
                        }                                         
                    }.bind(this),
                });

                this.redDot.push(this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.btn1,
                        dockerPercent: { x: 0, y: 0.5 },
                        dockerOffset: { x: 70, y: 0 }
                    },
                    anchor: { x: 0.5, y: 0.5 },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:30,y:30}
                    },
                    image: dl.preload['circle-red'],
                    visible:false,
                }));


                this.thumbs.push(this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.btn1,
                        dockerPercent: { x: 1, y: 0.5 },
                        dockerOffset: { x: -70, y: 0 }
                    },
                    anchor: { x: 0.5, y: 0.5 },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:50,y:50}
                    },
                    image: dl.preload['thumbsup'],
                    visible:false,
                }));

                this.btn2 = this.spawnEntity(dl.EntityButtonImageText, {
                    c_DockerComponent: {
                        dockerObject: this.btn1,
                        dockerPercent: { x: 0.5, y: 1 },
                        dockerOffset: { x: 0, y: offset }
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                        fontSize: 40,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    image: dl.preload['button-green'],
                    text: _STRINGS.NETWORK.POPUP_SELECT_SERVER.BUTTON_1,
                    onButtonClicked: this.onButtonSelected.bind(this, 1),
                    onPointerOver:function(){
                        if(this.redDot[1]){
                            this.redDot[1].c_DockerComponent.dockerOffset={ x: 90, y: 0 };
                            this.redDot[1].updatePos();
                        } 
                    }.bind(this),
                    onPointerLeave:function(){
                        if(this.redDot[1]){
                            this.redDot[1].c_DockerComponent.dockerOffset={ x: 70, y: 0 };
                            this.redDot[1].updatePos();
                        }                     
                    }.bind(this),
                    onPointerFirstClick:function(){
                        if(this.redDot[1]){
                            this.redDot[1].c_DockerComponent.dockerOffset={ x: 100, y: 0 };
                            this.redDot[1].updatePos();
                        }                                         
                    }.bind(this),
                });

                this.redDot.push(this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.btn2,
                        dockerPercent: { x: 0, y: 0.5 },
                        dockerOffset: { x: 70, y: 0 }
                    },
                    anchor: { x: 0.5, y: 0.5 },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:30,y:30}
                    },
                    image: dl.preload['circle-red'],
                    visible:false,
                }));

                this.thumbs.push(this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.btn2,
                        dockerPercent: { x: 1, y: 0.5 },
                        dockerOffset: { x: -70, y: 0 }
                    },
                    anchor: { x: 0.5, y: 0.5 },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:50,y:50}
                    },
                    image: dl.preload['thumbsup'],
                    visible:false,
                }));


                this.btn3 = this.spawnEntity(dl.EntityButtonImageText, {
                    c_DockerComponent: {
                        dockerObject: this.btn2,
                        dockerPercent: { x: 0.5, y: 1 },
                        dockerOffset: { x: 0, y: offset }
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                        fontSize: 40,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    image: dl.preload['button-green'],
                    text: _STRINGS.NETWORK.POPUP_SELECT_SERVER.BUTTON_2,
                    onButtonClicked: this.onButtonSelected.bind(this, 2),
                    onPointerOver:function(){
                        if(this.redDot[2]){
                            this.redDot[2].c_DockerComponent.dockerOffset={ x: 90, y: 0 };
                            this.redDot[2].updatePos();
                        } 
                    }.bind(this),
                    onPointerLeave:function(){
                        if(this.redDot[2]){
                            this.redDot[2].c_DockerComponent.dockerOffset={ x: 70, y: 0 };
                            this.redDot[2].updatePos();
                        }                     
                    }.bind(this),
                    onPointerFirstClick:function(){
                        if(this.redDot[2]){
                            this.redDot[2].c_DockerComponent.dockerOffset={ x: 100, y: 0 };
                            this.redDot[2].updatePos();
                        }                                         
                    }.bind(this),
                });

                this.redDot.push(this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.btn3,
                        dockerPercent: { x: 0, y: 0.5 },
                        dockerOffset: { x: 70, y: 0 }
                    },
                    anchor: { x: 0.5, y: 0.5 },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:30,y:30}
                    },
                    image: dl.preload['circle-red'],
                    visible:false,
                }));

                this.thumbs.push(this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.btn3,
                        dockerPercent: { x: 1, y: 0.5 },
                        dockerOffset: { x: -70, y: 0 }
                    },
                    anchor: { x: 0.5, y: 0.5 },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:50,y:50}
                    },
                    image: dl.preload['thumbsup'],
                    visible:false,
                }));

                this.btnClose = this.spawnEntity(dl.EntityButtonImage, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 1, y: 0 },
                        dockerOffset: { x: -10, y: 10 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:100,y:100}
                    },
                    anchor: { x: 1, y: 0 },
                    image: dl.preload['popup-button-close'],
                    onButtonClicked: function () {
                        this.tweenOut(function () {
                            ig.game.popupStatus = false;
                            if(ig.game.defaultServer==10){
                                ig.game.defaultServer = 0;
                                ig.game.sessionData.defaultServer = 0;
                                dl.game.serverIP = SERVER_CONFIGS.SERVER_IP
                                ig.game.saveLocalStorage();
                                dl.game.closeNetwork();
                                dl.game.initNetwork();
                                ig.game.resetApp();
                                if(ig.svas) ig.svas.ready();
                            }
                            this.kill();
                        }.bind(this.parentInstance));
                    }.bind(this)
                });

                this.serverButtons = [this.btn1, this.btn2, this.btn3];

                this.serverNames = [
                    SERVER_CONFIGS.SERVER_IPS_NAME[0],
                    SERVER_CONFIGS.SERVER_IPS_NAME[1],
                    SERVER_CONFIGS.SERVER_IPS_NAME[2]
                ];
            },

            onButtonSelected: function (serverIndex) {
                var server = this.fakeServers[serverIndex];

                ig.game.defaultServer = serverIndex;
                ig.game.sessionData.defaultServer = serverIndex;

                dl.game.serverIP = server.server_ip;
                this.tweenOut(function () {
                    ig.game.saveLocalStorage();
                    dl.game.closeNetwork();
                    dl.game.initNetwork();
                    ig.game.resetApp();
                    if(ig.svas) ig.svas.ready();
                    this.kill();
                    ig.game.popupStatus = true;
                    ig.game.nameAvatar.nameInput.tweenOut(100);
                }.bind(this.parentInstance));
            },

            tweenIn: function (callback) {
                this.parent(callback);
            },

            tweenOut: function (callback) {
                for (var i = 0; i < this.fakeServers.length; i++) {
                    this.fakeServers[i].disconnect();
                }
                this.btnClose.setEnable(false);
                this.btn1.setEnable(false);
                this.btn2.setEnable(false);
                this.btn3.setEnable(false);

                this.parent(callback);
            },
            // updateDataCount:0,
            // update:function(){
            //     this.parent();
            //     this.updateDataCount +=1;
            //     if(this.updateDataCount % 25 !==0) return
            //     if(this.updateDataCount>50) this.updateDataCount=0;
            //     this.updateData();
            // },
            updateData: function () {
                var lowestLatency =99999;
                var latencyIndex  =10;
                for (var i = 0; i < this.fakeServers.length; i++) {
                    this.serverButtons[i].updateText(this.serverNames[i] + ' (' + this.fakeServers[i].getInfo() + ')');
                    this.serverButtons[i].setEnable(this.fakeServers[i].checkConnection());
                    if(this.fakeServers[i].latencyAverage>0 && this.fakeServers[i].latencyAverage<lowestLatency){
                        latencyIndex = i;
                        lowestLatency=this.fakeServers[i].latencyAverage;
                    }
                }

                for(var i=0;i<this.redDot.length;i++){

                    if(i==ig.game.serverActive){
                        this.redDot[i].visible=true;
                    }else{
                        this.redDot[i].visible=false;                        
                    }

                    if(i==latencyIndex){
                        this.thumbs[i].visible=true;
                    }else{
                        this.thumbs[i].visible=false;                        
                    }

                }
            }
        });
});
