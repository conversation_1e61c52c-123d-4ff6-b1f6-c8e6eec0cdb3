/**
 * Created by <PERSON><PERSON>
 * Handle user, messages and ping on server side
 */

if (typeof require !== 'undefined') {
    var SERVER_CONFIGS = require('../shared/server-configs.js').SERVER_CONFIGS;
}

var NetworkServer = function () {
    var self = {};

    self.init = function () {
        console.log('NetworkServer init!');
        self.userList = [];
        self.userHash = {};

        // handlers
        self.onClientConnectHandlers = [];
        self.onClientDisconnectHandlers = [];
        self.onClientMessageHandlers = [];
        self.onClientPingHandlers = [];
        self.onClientPingReplyHandlers = [];
        self.clientPingStatus=true;
    };

    // ----------------
    // Start Message's functions
    // ----------------
    self.sendMessage = function (socket, data) {
        if (!socket) return;

        socket.emit(SERVER_CONFIGS.TAGS.MESSAGE, data);
    };

    self.onClientMessage = function (socket, data) {
        if (!socket) return;
        if (!data) return;

        for (var i = 0, iLength = self.onClientMessageHandlers.length; i < iLength; i++) {
            var handler = self.onClientMessageHandlers[i];
            if (typeof (handler) === 'function') {
                handler(socket, data);
            }
        }
    };

    self.onClientConnect = function (socket) {
        if (!socket) return;

        var user = self.addUser(socket);
        if (!user) return;

        for (var i = 0, iLength = self.onClientConnectHandlers.length; i < iLength; i++) {
            var handler = self.onClientConnectHandlers[i];
            if (typeof (handler) === 'function') {
                handler(socket);
            }
        }
    };

    self.onClientDisconnect = function (socket) {
        if (!socket) return;

        for (var i = 0, iLength = self.onClientDisconnectHandlers.length; i < iLength; i++) {
            var handler = self.onClientDisconnectHandlers[i];
            if (typeof (handler) === 'function') {
                handler(socket);
            }
        }

        self.removeUser(socket);
    };
    // ----------------
    // End Message's functions
    // ----------------

    // ----------------
    // Start Ping's functions
    // ----------------

    self.onClientPing = function (socket, data) {
        if(!self.clientPingStatus) return;
        self.clientPingStatus=false;
        setTimeout(function(){
            self.clientPingStatus=true;
        }.bind(self),5000);

        if (!socket) return;
        var user = self.findUserById(socket.id);
        if (!user) return;

        data.pongTime = Date.now();
        socket.emit(SERVER_CONFIGS.TAGS.PING_REPLY, data);

        for (var i = 0, iLength = self.onClientPingHandlers.length; i < iLength; i++) {
            var handler = self.onClientPingHandlers[i];
            if (typeof (handler) === 'function') handler(socket, data);
        }
    };

    self.pingSocket = function (socket) {
        if (!socket) return;

        var user = self.findUserById(socket.id);
        if (!user) return;

        var now = Date.now();
        if ((now - user.lastPingTime) <= SERVER_CONFIGS.PING_DELAY) {
            return;
        }


        /**
         * Ping
         */
        socket.emit(SERVER_CONFIGS.TAGS.PING, {
            pingTime: now
        });

        // set lastPingTime for next time check
        user.lastPingTime = now;
    };

    self.onClientPingReply = function (socket, data) {

        if (!socket) return;

        var user = self.findUserById(socket.id);
        if (!user) return;

        if (!data) return;
        if (isNaN(data.pingTime) || data.pingTime === null) return;
        if (isNaN(data.pongTime) || data.pongTime === null) return;

        var roundTripTime = Date.now() - data.pingTime;
        user.lastTimeDiff = Date.now() - data.pongTime + roundTripTime / 2;

        var latency = Math.floor(roundTripTime / 2);
        user.calculateAverageTimeDiff(latency, data.pongTime);

        for (var i = 0, iLength = self.onClientPingReplyHandlers.length; i < iLength; i++) {
            var handler = self.onClientPingReplyHandlers[i];
            if (typeof (handler) === 'function') {
                handler(socket, data);
            }
        }
    };
    // ----------------
    // End Ping's functions
    // ----------------

    // ----------------
    // Start User's functions
    // ----------------
    self.addUser = function (socket) {
        if (!socket) return null;

        var user = new User(socket);
        self.userList.push(user);
        self.userHash[user.id] = user;

        return user;
    };

    self.removeUser = function (socket) {
        if (!socket) return false;

        var user = self.findUserById(socket.id);
        if (!user) return false;

        var index = self.userList.indexOf(user);
        self.userList.splice(index, 1);

        delete self.userHash[user.id];

        return true;
    };

    self.findUserById = function (id) {
        if (!isNaN(id) || id === null) return null;

        var user = self.userHash[id];
        return user;
    };
    // ----------------
    // End User's functions
    // ----------------

    self.init();
    return self;
};

var User = function (socket) {
    var self = {};

    self.init = function (socket) {
        self.socket = socket;
        self.id = socket.id;

        self.lastPingTime = 0;
        self.lastTimeDiff = 0;

        self.latencyAverage = 0;
        self.timeDiffAverage = 0;
        self._latencyLog = [];
        self._latencyLogSize = 10;
    };

    // self.calculateAverageTimeDiff = function (latency, pongTime) {
    //     if (isNaN(latency) || latency === null) return;
    //     if (isNaN(pongTime) || pongTime === null) return;

    //     if (self._latencyLog.length >= self._latencyLogSize) {
    //         self._latencyLog.shift();
    //     }

    //     self._latencyLog.push(latency);

    //     var sum = 0;
    //     for (var i = 0, iLength = self._latencyLog.length; i < iLength; i++) {
    //         sum += self._latencyLog[i];
    //     }

    //     self.latencyAverage = Math.round(sum / self._latencyLog.length);
    //     self.timeDiffAverage = Date.now() - (pongTime + self.latencyAverage);
    // };

    self.init(socket);
    return self;
};

/**
 * Export module
 */
if (typeof module !== 'undefined') {
    module.exports.NetworkServer = NetworkServer;
}
