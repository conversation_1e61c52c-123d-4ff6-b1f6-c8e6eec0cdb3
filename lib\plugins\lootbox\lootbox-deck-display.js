ig.module('plugins.lootbox.lootbox-deck-display')
    .requires(
        'plugins.lootbox.lootbox-card'
    )
    .defines(function () {
        ig.LootboxDeckDisplay = ig.Class.extend({

            zIndex: 9999,
            members: [],
            cards: [],
            isSelectable: true,
            maxSelection: 3,
            onCardSelected: null,

            width: 0,
            height: 0,
            nextButton: null,
            prevButton: null,
            deckOffsetTarget: 0,
            deckOffset: 0,
            scrollMax: 0,
            nextPrevDelay: 0.3,

            isExiting: false,

            init: function (cardData) {

                this.onCardSelected = new ig.LootboxSignal();

                this.scrollSpeed = ig.Lootbox.page.scrollSpeed;
                var i = 0;
                var spacing = ig.Lootbox.card.width * 0.08
                var pageW = ig.responsive ? ig.responsive.originalWidth : ig.system.width;
                var pageH = ig.responsive ? ig.responsive.originalHeight : ig.system.height;
                var totalW = ig.Lootbox.page.column * (ig.Lootbox.card.width + spacing) - spacing;
                var totalH = ig.Lootbox.page.row * (ig.Lootbox.card.height + spacing) - spacing;
                var offsetX = (pageW - totalW) / 2;
                var offsetY = (pageH - totalH) / 2;

                this.spacing = spacing;
                this.pageW = pageW;
                this.pageH = pageH;
                this.totalW = totalW;
                this.totalH = totalH;
                this.offsetX = offsetX;
                this.offsetY = offsetY;

                do {
                    for (var row = 0; row < ig.Lootbox.page.row; row++) {
                        for (var col = 0; col < ig.Lootbox.page.column; col++) {
                            var cardX = col * (ig.Lootbox.card.width + spacing) + offsetX;
                            var cardY = row * (ig.Lootbox.card.height + spacing) + offsetY;
                            var card = null;
                            if (i < cardData.length) {
                                var item = cardData[i];
                                card = this.spawnMember(ig.LootboxCard, cardX, cardY, { id: item.id, level: item.level, zIndex: 99999, originalX: cardX });
                                card.inputEnabled = true;
                                card.onClicked.add(this.onClickCard, this);
                            } else {
                                card = this.spawnMember(ig.LootboxCard, cardX, cardY, { id: -1, level: 1, zIndex: 99999, originalX: cardX });
                            }

                            this.cards.push(card);
                            this.checkCardAlpha(card, true);
                            i++;
                        }
                    }
                    offsetX += totalW + spacing
                    if (i < cardData.length) this.scrollMax += ig.Lootbox.page.column;
                } while (i < cardData.length);

                this.width = totalW;
                this.height = totalH;

                this.nextButton = this.spawnMember(ig.LootboxSimpleButton, pageW / 2 + totalW / 2 + spacing + ig.Lootbox.images.next.width / 2, pageH / 2, {
                    image: ig.Lootbox.images.next,
                    zIndex: 99999,
                    visible: false
                });
                this.nextButton.onClicked.add(this.onClickNext, this)

                this.prevButton = this.spawnMember(ig.LootboxSimpleButton, pageW / 2 - totalW / 2 - (spacing + ig.Lootbox.images.next.width / 2), pageH / 2, {
                    image: ig.Lootbox.images.prev,
                    zIndex: 99999,
                    visible: false
                });
                this.prevButton.onClicked.add(this.onClickPrev, this)

                this.nextButton.visible = false;
                this.prevButton.visible = false;
                ig.game.sortEntitiesDeferred()
            },

            onClickPrev: function () {
                this.deckOffsetTarget += this.scrollSpeed * (ig.Lootbox.card.width + this.spacing)
                if (this.deckOffsetTarget > 0) this.deckOffsetTarget = 0;
                ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList[ig.Lootbox.sounds.button]);
            },

            onClickNext: function () {
                this.deckOffsetTarget -= this.scrollSpeed * (ig.Lootbox.card.width + this.spacing)
                var limit = -this.scrollMax * (ig.Lootbox.card.width + this.spacing)
                console.log(this.deckOffsetTarget, limit)
                if (this.deckOffsetTarget < limit) this.deckOffsetTarget = limit;
                ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList[ig.Lootbox.sounds.button]);
            },

            checkCardAlpha: function (card, instant) {
                var x = ig.responsive ? card.anchoredPositionX : card.pos.x;
                if (x < this.offsetX - this.spacing || x > this.offsetX + this.totalW - ig.Lootbox.card.width + this.spacing) {
                    if (instant) card.alpha = 0;
                    card.alpha -= card.alpha / 2;
                    if (card.alpha < 0.05) card.alpha = 0;
                } else {
                    if (instant) card.alpha = 1;
                    card.alpha += (1 - card.alpha) / 2;
                    if (card.alpha > 0.95) card.alpha = 1;
                }
                if (card.alpha <= 0) card.visible = false;
                else card.visible = true;
            },

            update: function () {
                if (this.isExiting) return;
                this.deckOffset += (this.deckOffsetTarget - this.deckOffset) / 10

                for (var i = 0; i < this.cards.length; i++) {
                    var card = this.cards[i];
                    if (ig.responsive) {
                        card.anchoredPositionX = card.originalX + this.deckOffset;
                    } else {
                        card.pos.x = card.originalX + this.deckOffset;
                    }
                    this.checkCardAlpha(card);
                }

                var limit = -this.scrollMax * (ig.Lootbox.card.width + this.spacing)

                if (this.nextPrevDelay > 0) {
                    this.nextPrevDelay -= ig.system.tick;
                    return;
                }
                if (this.deckOffsetTarget == 0) this.prevButton.visible = false;
                else this.prevButton.visible = true;
                if (this.deckOffsetTarget == limit) this.nextButton.visible = false;
                else this.nextButton.visible = true;
            },

            onClickCard: function (card) {
                if (!this.isSelectable) return;
                if (card.isSelected) {
                    card.isSelected = false;
                    this.onCardSelected.dispatch(card);
                    return;
                }
                if (this.getSelectedCards().length >= this.maxSelection) this.clearSelection();
                card.isSelected = true;
                this.onCardSelected.dispatch(card);
            },

            clearSelection: function () {
                for (var i = 0; i < this.cards.length; i++) {
                    this.cards[i].isSelected = false;
                }
            },

            getSelectedCards: function () {
                var selected = []
                for (var i = 0; i < this.cards.length; i++) {
                    var card = this.cards[i];
                    if (card.isSelected) selected.push(card);
                }
                return selected;
            },

            spawnMember: function (classObject, x, y, settings) {
                var member = ig.game.spawnEntityBackward(classObject, x, y, settings);
                this.members.push(member);
                return member;
            },

            exit: function () {
                this.isExiting = true;
                for (var i = 0; i < this.members.length; i++) {
                    var member = this.members[i];
                    member.exit();
                }
            },

        });
    });
