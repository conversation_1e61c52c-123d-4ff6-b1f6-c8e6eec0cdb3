ig.module(
    'dl.templates.network-entities.entity-popup-gems'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.popup',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.animation-sheet',
    'dl.templates.mixins.text',
    'dl.templates.entities.buttons.entity-button-image-text',
    'dl.templates.entities.buttons.entity-button-image',
    'game.entities.controller-rv',
	'plugins.secure-ls'
).defines(function () {
    'use strict';

    dl.EntityPopupGems = dl.Entity
        .extend(dl.MixinPopup)
        .extend(dl.MixinDocker)
        .extend({
            postInit: function () {
                this.parent();

                var width=1130;
                var height=1400;

                this.content = this.spawnEntity(dl.EntityPopupGems_Content, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:width,y:height}
                    },
                });
            }
        });

    dl.EntityPopupGems_Content = dl.Entity
        .extend(dl.MixinPopupContent)
        .extend(dl.MixinDocker)
        .extend(dl.MixinAnimationSheet)
        .extend(dl.MixinText)
        .extend({
            tierLabel:[
                        {header:'Tier 1',detail:'x 50'},
                        {header:'Tier 2',detail:'x 100'},
                        {header:'Tier 3',detail:'x 350'},
                        {header:'Tier 4',detail:'x 500'},
                        {header:'Tier 5',detail:'x 750'},
                        {header:'Tier 6',detail:'x 1,500'},
                      ],
            tiersData: [
                {name: 'tier1', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false},
                {name: 'tier2', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false},
                {name: 'tier3', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false},
                {name: 'tier4', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false},
                {name: 'tier5', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false},
                {name: 'tier6', cd: 1440, timeOfWatch: 0, timeTarg: 0, active: false}
            ],
            rewards:[50,100,350,500,750,1500],
            update_panel_buttons_status: function (){

                for (var i=0;i<6;i++) {
                    if (this.tiersData_cur[i].active) {
                        this.btclaim[i].disableEntityUnder();
                    }
                    
                    if (this.tiersData_cur[i].cd_cur <= 0) {
                        this.btclaim[i].enableEntityUnder();
                    }
                    
                    if (i > 0) {
                        if (!this.tiersData_cur [i - 1].active) {
                            this.btclaim[i].disableEntityUnder();
                        }
                    }
                }

                for (var i=0;i<6;i++) {
                    if (i <= 0) {
                        this._set_btn_status(this.btclaim[i],((this.tiersData_cur [i].active) ? 'running' : 'clickable'));
                    } else {
                        if (this.check_past_buttons_status (i) && !this.tiersData_cur [i].active) {
                            this._set_btn_status (this.btclaim[i], 'clickable');
                        } else if (this.tiersData_cur [i].active) {
                            this._set_btn_status (this.btclaim[i], 'running');
                        } else {
                            this._set_btn_status (this.btclaim[i], 'locked');
                        }
                    }
                }                
            },    

            _set_btn_status: function (_btn, _status) {
                switch (_status) {
                    case 'clickable': 
                        _btn.updateText('Watch Video');
                        _btn.enableEntityUnder();
                        break;
                    case 'running': 
                        _btn.updateText('Running');
                        break;
                    case 'locked': 
                        _btn.updateText('Locked');
                        break;
                }
                _btn.status = _status;
            },

            rv_btn_pressed: function (_btnIndex){
                this.curRVBtn = _btnIndex;    
                this._apiSample = ig.game.spawnEntityBackward(EntityControllerRV, 0, 0);
                this._apiSample.sample_rv_start(this.rv_end_success.bind(this),this.rv_end_fail.bind (this));                                
                this.enable_buttons(false);
            },
            showNotification:function(text,color){
                dl.scene.spawnNotification({
                    c_DockerComponent: {
                            dockerObject: dl.game.camera,
                            dockerPercent: { x: 0.5, y: 0.1},
                            dockerOffset: { x: 0, y: 0 }
                        },
                    notificationDrawConfigs: {
                        contentConfigs: [{
                                type: 'text',
                                text: text,
                                fillStyle: dl.configs.TEXT_COLOR.BUTTON_TEXT,
                                fontSize: 46,
                                fontFamily: dl.configs.FONT.SOURCE_SANS.name
                            }],
                        backgroundConfigs: {
                            lineWidth: 2,
                            fillStyle: color,
                            strokeStyle: color,
                            box: { width: 800, height: 90, round: 10, padding: { x: 100, y: 5 } }
                        },
                    }
                });
            },            
            rv_end_success: function (){
                var _curTime = new Date().getTime();
                var _cdTime = this.tiersData_cur[this.curRVBtn].cd * 60 * 1000;
                
                this.tiersData_cur[this.curRVBtn].timeOfWatch = _curTime;
                this.tiersData_cur[this.curRVBtn].timeTarg = _curTime + _cdTime;
                this.tiersData_cur[this.curRVBtn].active = true;

                var rewardVal=this.rewards[this.curRVBtn];
                ig.game.client.saveGems(rewardVal);

                this.showNotification(this.popup.txtStr_success,dl.configs.NOTIFICATION_COLORS.PLAYER_3);
                this.save_data();
                // this.spawn_popup (this.popup.txtStr_success);    
                this._apiSample.kill();
                this.update_panel_buttons_status();
            },
            rv_end_fail: function (){                
                this.update_panel_buttons_status ();
                
                this.showNotification(this.popup.txtStr_fail,dl.configs.NOTIFICATION_COLORS.PLAYER_1);
                // this.spawn_popup(this.popup.txtStr_fail);
                // ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList[this.soundsID.error]);
                this._apiSample.kill();
            },
            spawn_popup: function (_text){
                ig.game.spawnEntityBackward(EntityControllerPopup, 0, 0, {control:this,txt: _text});
            },
            enable_buttons: function (_status){
                for (var i=0;i<this.btclaim.length;i++) {
                    if(_status){
                        this.btclaim[i].enableEntityUnder();
                    }else{
                        this.btclaim[i].disableEntityUnder();
                    }
                }
            },                    
            tier_dur_ends: function (_tierIndex) {
                
                // Plugin code
                this.tiersData_cur [_tierIndex].timeOfWatch = 0;
                this.tiersData_cur [_tierIndex].timeTarg = 0;
                this.tiersData_cur [_tierIndex].active = false;
                
                this.save_data ();
                this.update_panel_buttons_status ();
            },
            resetData:function(){

                for(var i=0;i<this.tiersData_cur.length;i++){
                    this.tiersData_cur [i].timeOfWatch = 0;
                    this.tiersData_cur [i].timeTarg = 0;
                    this.tiersData_cur [i].active = false;

                }
                this.save_data ();
                this.update_panel_buttons_status ();

            },
            
            // Popup Settings
            popup: {
                img: new ig.Image ('media/graphics/sprites/tiered-rv/popup.png'),
                size: {x: 1000, y: 140},
                
                posOffset: {x: 60, y: 0}, // Offset from screen center, anchored at center
                
                dur: 2,
                // dur: 10,
                
                txtFont: '50px montserrat-bold',
                txtFillStyle: '#404040',
                txtPosOffset: {x: 500, y: 80}, // Offset from popup
                
                txtStr_success: 'You have received the reward!',
                txtStr_fail: 'RV ad has errored. Try again later.',
            },
            
            // Sounds
            soundsID: {
                click: "click",
                reward: "reward",
                error: "error"
            },
            
            //////////// PLUGIN VARIABLES ////////////
            saveName: null,
            
            isShow: false,
            
            tiersData_cur: null,
            curRVBtn: 0,
            
            gameResolution: {x: 0, y: 0},
            winBox: null,
            winBox_pos: null,


            pluginiInit: function (){
                this.tiersData_cur = this.tiersData;
                this.load_data();                
                this.calc_remaining_time ();

                ig.game.disableEntities();
                setTimeout(function(){
                    this.btnClose.enableEntityUnder();
                    this.update_panel_buttons_status();
                }.bind(this),200);
            },
                        
            get_tier_is_active: function (_tierIndex){
                return this.tiersData_cur [_tierIndex].active;
            },
            
            get_highest_active_tier_index: function (){
                for (var i = this.tiersData_cur.length - 1; i >= 0; i--) {
                    if (this.tiersData_cur [i].active) {
                        return i;
                    }
                }
            },
                        
            check_past_buttons_status: function (_index){
                for (var i = 0; i < _index; i++) {
                    if (!this.tiersData_cur [i].active) {
                        return false;
                    }
                }
                
                return true;
            },
            
            
            save_data: function (){
                ig.game.gemsData=this.tiersData_cur;
                var dataString = JSON.stringify(this.tiersData_cur);    
                ig.ls.set(this.saveName, dataString);            	
            },
            
            load_data: function (){
                if (!ig.ls) {
                    ig.ls = new SecureLS({ encodingType: 'aes' });
                    this.saveName = this.hash(ig.game.name + "-tiered-gems-data-01");
                }
                this.tiersData_cur=ig.game.gemsData;
            },
            
            clear_data: function (){
                this.tiersData_cur = this.tiersData;
                this.save_data();
            },
            
            calc_remaining_time: function (){
                var _curTime = new Date ().getTime ();
                
                for (var i in this.tiersData_cur) {
                    if (this.tiersData_cur [i].active && this.tiersData_cur[i].timeTarg <= _curTime) {
                        this.tier_dur_ends (i);
                    }
                }
                this.calcTime();
            },
            calcTime:function(){

                for(var i=0;i<this.tiersData_cur.length;i++){
                    var _time = new Date().getTime();
                    var _targ = this.tiersData_cur[i].timeTarg - _time;
                    if(this.btclaim[i].status=='running'){   
                        var text=this.convert_time_text (_targ);
                        this.btclaim[i].updateText(text);
                    }
                }
            },
            convert_time_text: function (time){
                var timeAd=Number(time);
                timeAd /= 1000;
                
                var hours   = Math.floor(timeAd / 3600);
                var minutes = Math.floor((timeAd % 3600) / 60);
                var seconds = Math.floor(timeAd % 60);
                
                var hours_txt,minutes_txt,seconds_txt='';
    
                if (hours < 10) hours_txt = "0" + hours.toString() + ":";
                else hours_txt = hours.toString()+":";
                
                if (minutes < 10) minutes_txt  = "0" + minutes.toString() + ":";
                else minutes_txt = minutes.toString()+":";
                
                if (seconds < 10) seconds_txt  = "0" + seconds.toString();
                else seconds_txt = seconds.toString();
    
                return hours_txt + minutes_txt + seconds_txt;
            },
            
            hash: function (s){
                var hash = 0, i, chr;
                if (s.length === 0) return hash;
                for (i = 0; i < s.length; i++) {
                    chr = s.charCodeAt(i);
                    hash = ((hash << 5) - hash) + chr;
                    hash |= 0; // Convert to 32bit integer
                }
                var hexHash = hash.toString(36);
                return hexHash;
            },


            //framework part
            staticInstantiate: function () {
                this.parent();

                this.image = dl.preload['popup-large-wide-long'];

                this._useAnimationSheetAsSize = true;

                this.text = 'GET MORE GEMS';
                this.panel=[];
                this.coins=[];
                this.textHeader=[];
                this.textDetail=[];
                this.btclaim=[];
                return this;
            },

            postInit: function () {
                this.parent();
                this.initText();
                this.initButtons();
                this.initImage();
                this.tweenIn();
                this.pluginiInit();
                window.gemsplugin=this;
            },
            currentIdx:0,
            initImage:function(){

                for(var i=0;i<6;i++){
                    var posx=i*300+60;
                    var halfSize=150;
                    var offsetObject=30;
                    var offsetX=i*60;
                    var offsetY=0;

                    if(i>2){
                        var posx=(i-3)*300+60;
                        var halfSize=150;
                        var offsetObject=30;
                        var offsetX=(i-3)*60;                        
                        var offsetY=530;
                    }

                    this.panel.push( this.spawnEntity(dl.EntityImage, {
                        _useParentScale: true,
                        c_DockerComponent: {
                            dockerObject: this,
                            dockerPercent: { x: 0, y: 0},
                            dockerOffset: { x: posx+offsetX, y: 300+offsetY }
                        },
                        c_AnimationSheetComponent: {
                            _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                            _size: {x:300,y:480}
                        },
                        image: dl.preload['grey-square'],
                        anchor: { x: 0, y: 0 },
                    }));
    
                    this.panel.push( this.spawnEntity(dl.EntityImage, {
                        _useParentScale: true,
                        c_DockerComponent: {
                            dockerObject: this,
                            dockerPercent: { x: 0, y: 0 },
                            dockerOffset: { x: posx+offsetObject+offsetX, y: 380+offsetY }
                        },
                        c_AnimationSheetComponent: {
                            _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                            _size: {x:240,y:280}
                        },
                        image: dl.preload['white-square'],
                        anchor: { x: 0, y: 0 },
                    }));

                    this.panel.push(this.spawnEntity(dl.EntityImage, {
                        _useParentScale: true,
                        c_DockerComponent: {
                            dockerObject: this,
                            dockerPercent: { x: 0, y: 0},
                            dockerOffset: { x: posx+offsetObject+offsetObject+offsetX+20, y: 400+offsetY }
                        },
                        c_AnimationSheetComponent: {
                            _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                            _size: {x:140,y:180}
                        },
                        image: dl.preload['gems3'],
                        anchor: { x: 0, y: 0 },
                    }));

                    this.textHeader.push(this.spawnEntity(dl.EntityText, {
                        c_DockerComponent: {
                            dockerObject: this,
                            dockerPercent: {x:0,y:0},
                            dockerOffset: { x: posx+halfSize+offsetX, y: 620+offsetY }
                        },
                        c_TextComponent: {
                            fillStyle: '#202020',
                            fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                            textAlign: 'center', 
                            textBaseline: 'middle',
                            fontSize: 40,
                        },
                        anchor: { x: 0.5, y: 0.5 },
                        text:this.tierLabel[i].detail,
                    }));


                    this.textHeader.push(this.spawnEntity(dl.EntityText, {
                        c_DockerComponent: {
                            dockerObject: this,
                            dockerPercent: {x: 0,y:0},
                            dockerOffset: { x: posx+halfSize+offsetX, y: 340+offsetY }
                        },
                        c_TextComponent: {
                            fillStyle: '#202020',
                            fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                            textAlign: 'center', 
                            textBaseline: 'middle',
                            fontSize: 40,
                        },
                        anchor: { x: 0.5, y: 0.5 },
                        text:this.tierLabel[i].header,
                    }));

                    this.btclaim.push(this.spawnEntity(dl.EntityButtonImageText,{
                        c_DockerComponent: {
                            dockerObject: this,
                            dockerPercent: { x: 0, y: 0 },
                            dockerOffset: { x: posx+offsetObject+offsetX, y: 680+offsetY }
                        },
                        c_AnimationSheetComponent: {
                            _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                            _size: {x:250,y:80}
                        },
                        c_TextComponent: {
                            fillStyle: '#ffffff',
                            fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                            textAlign: 'center', 
                            textBaseline: 'middle',
                            fontSize: 30,
                        },
                        anchor: { x: 0, y: 0 },
                        image: dl.preload['button-green-small'],
                        text:'Play',
                        buttonId:i,
                        control:this,
                        status:'locked',
                        onButtonClicked: function() {
                            this.control.rv_btn_pressed(this.buttonId);
                        }
                    }));                    
                }
            },
            initText:function(){

                this.text1 = this.spawnEntity(dl.EntityText, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: {x:0.5,y:0},
                        dockerOffset: { x: 0, y: 200 }
                    },
                    c_TextComponent: {
                        fillStyle: '#505050',
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                        textAlign: 'center', 
                        textBaseline: 'middle',
                        fontSize: 50,
                    },
                    anchor: { x: 0.5, y: 0.5 },
                    text:'Watch Daily Videos to Get More Gems!'
                });


            },

            _initTextComponent: function () {

                this.c_TextComponent.updateProperties({
                    fillStyle: '#e43630',
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                    textAlign: 'center', // [center|end|left|right|start];
                    textBaseline: 'middle', // [alphabetic|top|hanging|middle|ideographic|bottom];

                    fontSize: 70,

                    _docker: { x: 0.5, y: 0.08 },
                    _anchor: { x: 0.5, y: 0.5 },
                    _offset: { x: 0, y: 0 }
                });
            },
            update:function(){
                this.parent();
                this.calc_remaining_time ();
            },
            initButtons: function () {

                this.btnClose = this.spawnEntity(dl.EntityButtonImage, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 1, y: 0 },
                        dockerOffset: { x: -10, y: 10 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:100,y:100}
                    },
                    anchor: { x: 1, y: 0 },
                    image: dl.preload['popup-button-close'],
                    onButtonClicked: function () {
                        this.tweenOut(function () {
                            this.kill();
                        }.bind(this.parentInstance));
                    }.bind(this)
                });

            },

            tweenIn: function (callback) {
                this.parent(callback);
            },

            tweenOut: function (callback) {
                this.parent(callback);
            }
        });
});
