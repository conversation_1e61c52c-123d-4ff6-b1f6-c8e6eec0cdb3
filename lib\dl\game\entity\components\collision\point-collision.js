ig.module(
    'dl.game.entity.components.collision.point-collision'
).requires(
    'dl.game.entity.components.collision.collision',
    'dl.game.entity.components.collision.helpers.point'
).defines(function () {
    'use strict';

    dl.PointCollision = dl.CollisionComponent.extend({
        staticInstantiate: function (entity) {
            this.parent(entity);

            this._collisionType = dl.CollisionComponent.TYPES.POINT;

            this.pos = { x: 0, y: 0 };

            // bind events
            this.entity.onEvent('positionChanged', this.updateAll.bind(this));

            return this;
        },

        onPropertiesChanged: function (properties) {
            this.updateAll();
        },

        updateAll: function () {
            this.updatePos();
            this.updateBoundingBox();
        },

        updatePos: function () {
            this.pos.x = this.entity.pos.x;
            this.pos.y = this.entity.pos.y;
        },

        updateBoundingBox: function () {
            this.collisionBoundingBox.xMin = this.entity.pos.x;
            this.collisionBoundingBox.yMin = this.entity.pos.y;
            this.collisionBoundingBox.xMax = this.entity.pos.x;
            this.collisionBoundingBox.yMax = this.entity.pos.y;
        },

        touches: function (other) {
            switch (other._collisionType) {
                case dl.CollisionComponent.TYPES.POINT: {
                    return dl.CollisionHelpers_Point.point(
                        this.pos.x,
                        this.pos.y,
                        other.pos.x,
                        other.pos.y
                    );
                } break;
                case dl.CollisionComponent.TYPES.RECTANGLE: {
                    return dl.CollisionHelpers_Point.rectangle(
                        this.pos.x,
                        this.pos.y,
                        other.pos.x - other.size.x * 0.5,
                        other.pos.y - other.size.y * 0.5,
                        other.size.x,
                        other.size.y
                    );
                } break;
                case dl.CollisionComponent.TYPES.CIRCLE: {
                    return dl.CollisionHelpers_Point.circle(
                        this.pos.x,
                        this.pos.y,
                        other.pos.x,
                        other.pos.y,
                        other.radius
                    );
                } break;
                default: {
                    dl.warn('Undefined type [' + other._collisionType + ']');
                } break;
            }
            return false;
        },

        drawCollision: function (ctx) {
            ctx.save();
            ctx.beginPath();
            var width = 4;
            ctx.ellipse(
                this.pos.x - width * 0.2, this.pos.y - width * 0.2,
                width, width,
                0, 0,
                Math.PI * 2);
            ctx.closePath();
            ctx.fillStyle = 'green';
            ctx.globalAlpha = 0.5;
            ctx.fill();
            ctx.restore();
        }
    });
});
