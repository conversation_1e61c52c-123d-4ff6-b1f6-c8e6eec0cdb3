ig.module(
    'game.levels.title-private'
).requires(
    'game.levels.title',
    'dl.templates.entities.buttons.entity-button-image',
    'dl.templates.network-entities.entity-room-code'
).defines(function () {
    LevelTitlePrivate = {
        entities: [
            { type: 'EntityTitlePrivateController', x: 0, y: 0 }
        ],
        layer: [],
        useCustomLib: true
    };

    EntityTitlePrivateController = EntityTitleController.extend({
        checkPendingInviteCode: function () {
            // do nothing
        },

        initEntities: function () {
            this.parent();
            this.initRoomCode();
        },

        initSubButtons: function () {
            this.parent();

            this.btnLeave = this.spawnEntity(dl.EntityButtonImage, {
                c_DockerComponent: {
                    dockerObject: this.btnFullscreen,
                    dockerPercent: { x: 0, y: 1 },
                    dockerOffset: { x: 0, y: 25 }
                },
                anchor: { x: 0, y: 0 },
                image: dl.preload['button-exit'],
                onButtonClicked: function () {
                    this.tweenOut(function () {
                        ig.game.minimalTweenFlag = true;
                        ig.game.director.jumpTo(LevelTitle);
                    }.bind(this), true);
                }.bind(this)
            });
        },

        initMainButtons: function () {
            this.btnEnter = this.spawnEntity(dl.EntityButtonImageText, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0.5, y: 0.9 },
                    dockerOffset: { x: 0, y: 0 }
                },
                c_TextComponent: {
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                    fontSize: 40,
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name
                },
                image: dl.preload['button-green'],
                text: _STRINGS.Game.ENTER_PRIVATE_ROOM,
                onButtonClicked: function () {
                    this.enterPrivateMatch();
                }.bind(this)
            });
        },

        initRoomCode: function () {
            this.roomCode = this.spawnEntity(dl.EntityRoomCode, {
                c_DockerComponent: {
                    dockerObject: dl.game.camera,
                    dockerPercent: { x: 0.5, y: 0.79 },
                    dockerOffset: { x: 0, y: 0 }
                }
            });
        },

        tweenIn: function (callback, minimalTween) {
            var time = dl.configs.getConfig('HOME_LEVEL_TWEEN_TIME', 'IN');
            if (!minimalTween) {
                dl.TweenTemplate.fadeIn(this.title, time);

                this.nameAvatar.tweenIn(time);

                dl.TweenTemplate.fadeIn(this.btnFullscreen, time);
                // dl.TweenTemplate.fadeIn(this.btnMoreGame, time);
                dl.TweenTemplate.fadeIn(this.btnSound, time);
            }

            dl.TweenTemplate.fadeIn(this.btnLeave, time, callback);
            dl.TweenTemplate.fadeIn(this.btnEnter, time);
            dl.TweenTemplate.fadeIn(this.roomCode, time);
        },

        tweenOut: function (callback, minimalTween) {
            var time = dl.configs.getConfig('HOME_LEVEL_TWEEN_TIME', 'OUT');

            if (!minimalTween) {
                dl.TweenTemplate.fadeOut(this.title, time);

                this.nameAvatar.tweenOut(time);

                dl.TweenTemplate.fadeOut(this.btnFullscreen, time);
                // dl.TweenTemplate.fadeOut(this.btnMoreGame, time);
                dl.TweenTemplate.fadeOut(this.btnSound, time);
            }

            dl.TweenTemplate.fadeOut(this.btnLeave, time, callback);
            dl.TweenTemplate.fadeOut(this.btnEnter, time);
            dl.TweenTemplate.fadeOut(this.roomCode, time);
        }
    });
});
