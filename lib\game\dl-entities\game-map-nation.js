ig.module(
    'game.dl-entities.game-map-nation'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker'
).defines(function () {
    dl.EntityGameMapNation = dl.Entity
        .extend(dl.MixinDocker)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this.color = '#D1CFD1';

                return this;
            },

            postInit: function () {
                this.parent();
            },

            assignCapital: function (capital) {
                this.capital = capital;
            },

            removePlayer: function () {
                this.nationData.assignedPlayer = null;
                this.color = '#D1CFD1';
            },

            setColor: function (color) {
                this.color = color;
            },

            update: function(){
                this.parent();
                if(this.capital){
                    ig.game.managers.color.updateNationColorByCapital(this);
                }
            },

            draw: function (ctx) {
                this.parent(ctx);
                ctx.save();
                var tmpNation = {
                    x: -this.nationData.mapDimension.width * 0.5 + this.nationData.initialPoint.x,
                    y: -this.nationData.mapDimension.height * 0.5 + this.nationData.initialPoint.y
                };


                ctx.beginPath();
                ctx.moveTo(tmpNation.x, tmpNation.y);
                for (var i = 0; i < this.nationData.polygon.length; i++) {
                    ctx.lineTo(tmpNation.x + this.nationData.polygon[i].x, tmpNation.y + this.nationData.polygon[i].y);
                }
                ctx.closePath();
                ctx.fillStyle = this.color;
                ctx.fill();
                ctx.lineWidth = 2;
                ctx.strokeStyle = '#ffffff';
                ctx.stroke();
                ctx.restore();

            }
        });

    // Enable cache
    // dl.enableCacheCanvas(dl.EntityGameMapContinent);
});
