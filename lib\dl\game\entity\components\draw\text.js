ig.module(
    'dl.game.entity.components.draw.text'
).requires(
    'dl.game.entity.components.draw.draw'
).defines(function () {
    'use strict';

    /**
     * Draw text
     * @example:
     *  Init component:
            this.c_TextComponent = this.addComponent(dl.TextComponent);
        *  Define in class:
            this.c_TextComponent.updateProperties({
                text: "",
                fillStyle: "white",
                textAlign: "center", //[center|end|left|right|start];
                textBaseline: "middle", //[alphabetic|top|hanging|middle|ideographic|bottom];

                fontSize: 24,
                fontFamily: "Arial",
                maxWidth: undefined,

                _docker: { x: 0.5, y: 0.5 },
                _anchor: { x: 0.5, y: 0.5 },
                _offset: { x: 0, y: 0 }
            });
        *  In settings:
            c_TextComponent: {
                text: "",
                fillStyle: "white",
                textAlign: "center", //[center|end|left|right|start];
                textBaseline: "middle", //[alphabetic|top|hanging|middle|ideographic|bottom];

                fontSize: 24,
                fontFamily: "Arial",
                maxWidth: undefined,

                _docker: { x: 0.5, y: 0.5 },
                _anchor: { x: 0.5, y: 0.5 },
                _offset: { x: 0, y: 0 }
            }
        */

    dl.TextComponent = dl.EntityDrawComponent.extend({
        staticInstantiate: function (entity) {
            this.parent(entity);

            this.text = '';
            this.textLines = [];

            // context properties
            this.fillStyle = 'white';
            this.textAlign = 'center'; // [center|end|left|right|start];
            this.textBaseline = 'middle'; // [alphabetic|top|hanging|middle|ideographic|bottom];

            this.fontSize = 24;
            this.fontFamily = 'Arial';
            this.maxWidth = undefined;
            this.shadow = false;
            this.shadowColor = 'white';
            this.shadowBlur = 10;

            // Measure properties
            this.__canvas = document.createElement('canvas');
            this.__context = this.__canvas.getContext('2d');

            this._useMeasuredSize = true;
            this.__measuredSize = { x: 16, y: 16 };
            this.__textWidth = 0;
            this.__fontHeight = 0;
            this.__actualHeight = 0;

            return this;
        },

        setComponentName: function () {
            this._componentName = 'dl_TextComponent';
        },

        draw: function (ctx) {
            ctx.save();
            ctx.fillStyle = this.fillStyle;
            if (this.shadow) {
                ctx.shadowOffsetX = 0;
                ctx.shadowOffsetY = 0;
                ctx.shadowColor = this.shadowColor;
                ctx.shadowBlur = this.shadowBlur;
            }
            ctx.textAlign = this.textAlign;
            ctx.textBaseline = this.textBaseline;
            ctx.font = this.fontSize + 'px' + ' ' + this.fontFamily;

            // translate to center entity
            ctx.translate(this.entity.pos.x, this.entity.pos.y);
            // scale
            ctx.translate(this.pos.x, this.pos.y);
            ctx.scale(this.entity.scale.x, this.entity.scale.y);
            ctx.translate(-this.pos.x, -this.pos.y);
            // draw
            var drawPos = this.getTextDrawPos();
            for (var i = 0; i < this.textLines.length; i++) {
                var text = this.textLines[i];
                if (typeof this.maxWidth === 'undefined') {
                    ctx.fillText(text,
                        drawPos.x,
                        drawPos.y + i * this.__fontHeight);
                } else {
                    ctx.fillText(text,
                        drawPos.x,
                        drawPos.y + i * this.__fontHeight,
                        this.maxWidth);
                }
            }
            ctx.restore();

            if (dl.debug) {
                if (dl.debug.increaseDrawCall) dl.debug.increaseDrawCall();
                if (dl.debug.drawComponent) dl.debug.drawComponent(this);
            }
        },

        onPropertiesChanged: function (properties) {
            this.measureSize();
            this.reposition();
        },

        measureLine: function (textLines, ctx) {
            var metrics = ctx.measureText(textLines[0]);

            /**
             * Measure the size
             * Get the metric with highest width
             */
            for (var i = 1; i < textLines.length; i++) {
                var newMetrics = ctx.measureText(textLines[i]);
                if (newMetrics.width >= metrics.width) {
                    metrics = newMetrics;
                }
            }

            return metrics;
        },

        measureSize: function () {
            if (!dl.check.isDefined(this.text)) this.text = '';
            this.text = this.text.toString();
            this.textLines = this.text.split('<br>');

            var ctx = this.__canvas.getContext('2d');
            ctx.save();
            ctx.font = this.fontSize + 'px' + ' ' + this.fontFamily;

            /**
             * Measure the size
             * Get the metric with highest width
             */
            var metrics = this.measureLine(this.textLines, ctx);
            ctx.restore();

            this.__textWidth = metrics.width;
            this.__fontHeight = metrics.fontBoundingBoxAscent + metrics.fontBoundingBoxDescent;
            this.__actualHeight = metrics.actualBoundingBoxAscent + metrics.actualBoundingBoxDescent;

            // some browser doesn't support
            if (isNaN(this.__fontHeight) || this.__fontHeight == null) {
                this.__fontHeight = this.fontSize;
            }

            this.__measuredSize = {
                x: this.__textWidth,
                y: this.__fontHeight * this.textLines.length
            };

            if (this._useMeasuredSize) {
                this._size.x = this.__measuredSize.x;
                this._size.y = this.__measuredSize.y;
            }

            return this.__measuredSize;
        },

        getTextDrawPos: function () {
            var pos = {
                x: this.pos.x,
                y: this.pos.y
            };

            /**
             * Horizontal
             */
            switch (this.textAlign) {
                case 'start':
                case 'left': {
                    pos.x -= this.__textWidth * 0.5;
                } break;

                case 'end':
                case 'right': {
                    pos.x += this.__textWidth * 0.5;
                } break;

                case 'center':
                default: {
                } break;
            }

            /**
             * Vertical
             */
            switch (this.textBaseline) {
                case 'top':
                case 'hanging': {
                    pos.y -= this.__fontHeight * 0.5;
                } break;

                case 'ideographic':
                case 'bottom': {
                    pos.y += this.__fontHeight * 0.5;
                } break;

                case 'alphabetic': {
                    dl.warn('Not support alphabetic for now- Forcing to middle');
                    this.textBaseline = 'middle';
                }
                case 'middle':
                default: {

                } break;
            }
            // offset multiline
            if (this.textLines.length > 1) {
                pos.y -= (this.textLines.length - 1) * 0.5 * this.__fontHeight;
            }

            return pos;
        },

        updateSize: function () {
            this.size.x = this._size.x * this.entity.scale.x;
            this.size.y = this._size.y * this.entity.scale.y;

            this.parent();
        }
    });
});
