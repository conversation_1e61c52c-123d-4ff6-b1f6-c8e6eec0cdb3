/**
 * Notification plugins
 * Created by: <PERSON><PERSON>/


/**
 * Usage:
 * In the entity that will spawn notification
 *      Add 'dl-plugins.notification.notification-mixin' in requires
 *      Extend dl.Notification.Mixin: .extend(dl.Notification.Mixin)
 *      To spawn notification, call: 
 *              NotificationParentEntity.spawnNotification(EntityNotificationConfigs);
 *          With:
 *              EntityNotificationConfigs: configs for notification
 */

dl.scene.spawnNotification({
    notificationDrawConfigs: {
        contentConfigs: [
            {
                type: "image", // important - type of content to be draw

                image: dl.preload.title, // image display in notification
                size: { x: 50, y: 50 }, // image size
                padding: 4 // extra space outside image
            },
            {
                type: "text", // important - type of content to be draw

                text: "Text display in notification", // text display in notification
                fillStyle: "black",
                fontSize: 24,
                fontFamily: "Arial",
                padding: 10
            }
        ],
        backgroundConfigs: {
            lineWidth: 2,
            fillStyle: "lightblue",
            strokeStyle: "black",

            box: {
                width: 500, // content min width
                height: 60, // content min height
                round: 10, // round curves distance
                padding: { x: 100, y: 5 } // extra space outside the content area
            }
        }
    }
});