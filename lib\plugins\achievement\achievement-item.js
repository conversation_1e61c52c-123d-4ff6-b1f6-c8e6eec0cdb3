ig.module('plugins.achievement.achievement-item')
    .requires(
        'plugins.achievement.achievement-game-object'
    )
    .defines(function () {
        ig.AchievementItem = ig.AchievementGameObject.extend({

            zIndex: 8999,
            entryType: "fadeIn",
            exitType: "fadeOut",

            pagingOffsetX: 0,
            pagingOffsetY: 0,
            pagingAlpha: 0,

            isCompleted: false,
            isClaimable: false,
            inputEnabled: true,

            completedCenterYOffset: -9999,
            claimCenterYOffset: -9999,

            init: function (x, y, settings) {
                this.parent(x, y, settings);

                this.originalX = x;
                this.originalY = y;
                this.forceDraw = true;
                var id = settings.id;
                var data = ig.Achievement.getAchievementData(id);
                this.data = data;
                this.iconImage = data.icon;

                this.nameTextRenderer = new ig.AchievementTextRenderer();
                this.nameTextRenderer.font = ig.Achievement.popup.item.name.font;
                this.nameTextRenderer.color = ig.Achievement.popup.item.name.color;
                this.nameTextRenderer.align = "left";
                this.nameTextRenderer.text = data.name

                this.descriptionTextRenderer = new ig.AchievementTextRenderer();
                this.descriptionTextRenderer.font = ig.Achievement.popup.item.description.font;
                this.descriptionTextRenderer.color = ig.Achievement.popup.item.description.color;
                this.descriptionTextRenderer.align = "left";
                this.descriptionTextRenderer.text = data.description

                this.rewardTextRenderer = new ig.AchievementTextRenderer();
                this.rewardTextRenderer.font = ig.Achievement.popup.item.reward.font;
                this.rewardTextRenderer.color = ig.Achievement.popup.item.reward.color;
                this.rewardTextRenderer.align = "left";
                this.rewardTextRenderer.text = ig.Achievement.strings.reward + data.reward

                this.progressTextRenderer = new ig.AchievementTextRenderer();
                this.progressTextRenderer.font = ig.Achievement.popup.item.progress.font;
                this.progressTextRenderer.color = ig.Achievement.popup.item.progress.color;
                this.progressTextRenderer.align = "center";
                this.progressTextRenderer.text = data.progress + " / " + data.goal

                this.completedTextRenderer = new ig.AchievementTextRenderer();
                this.completedTextRenderer.font = ig.Achievement.popup.item.completedSign.font;
                this.completedTextRenderer.color = ig.Achievement.popup.item.completedSign.textColor;
                this.completedTextRenderer.align = "center";
                this.completedTextRenderer.valign = "center";
                this.completedTextRenderer.text = ig.Achievement.strings.completed;

                this.claimTextRenderer = new ig.AchievementTextRenderer();
                this.claimTextRenderer.font = ig.Achievement.popup.item.claimButton.font;
                this.claimTextRenderer.color = ig.Achievement.popup.item.claimButton.textColor;
                this.claimTextRenderer.align = "center";
                this.claimTextRenderer.valign = "center";
                this.claimTextRenderer.text = ig.Achievement.strings.claim;

                this.width = ig.Achievement.popup.item.width;
                this.height = ig.Achievement.popup.item.height;
                this.size = {
                    x: ig.Achievement.popup.item.width,
                    y: ig.Achievement.popup.item.height
                }

                this.onClicked.add(this.onClickThis, this);

                ig.game.sortEntitiesDeferred();
            },

            onClickThis: function () {
                // if (this.isAnyInputInsideClaimButton()) {
                    this.onClickClaim();
                // }
            },

            onClickClaim: function () {                
                if (!this.isClaimable || !ig.Achievement.allowClaim || this.pagingAlpha < 0.9) return;
                ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList[ig.Achievement.sounds.claim]);

                this.onClaim.dispatch(this.id);
                ig.Achievement.claimReward(this.id)

                var x = 0;
                var y = 0;
                if (ig.responsive) {
                    x = this.anchoredPositionX;
                    y = this.anchoredPositionY;
                } else {
                    x = this.pos.x;
                    y = this.pos.y;
                }

                x += ig.Achievement.popup.item.claimButton.x-34;
                y += ig.Achievement.popup.item.claimButton.y;

                var fxText = ig.Achievement.strings.rewardFx + (this.data.value+Math.ceil(this.data.value*ig.game.multiplierValue/100))+' coins';
                ig.game.spawnEntityBackward(ig.AchievementRewardFx, x, y, { zIndex: 9999, text: fxText });
                ig.game.sortEntitiesDeferred();
            },

            exit: function () {
                this.parent();
            },

            update: function () {

                if (this.page != this.currentPage) {
                    this.pagingAlpha -= this.pagingAlpha / 2;
                } else {
                    this.pagingAlpha += (1 - this.pagingAlpha) / 5;
                }

                if (ig.responsive) {
                    this.anchoredPositionX += (this.originalX + this.pagingOffsetX - this.anchoredPositionX) / 5;
                } else {
                    this.pos.x += (this.originalX + this.pagingOffsetX - this.pos.x) / 5;
                }


                if (ig.Achievement.isAchievementAchieved(this.id)) {
                    if (ig.Achievement.isAchievementClaimed(this.id)) {
                        this.isClaimable = false;
                        this.isCompleted = true;
                    } else {
                        this.isClaimable = true;
                        this.isCompleted = false;
                    }
                } else {
                    this.isClaimable = false;
                    this.isCompleted = false;
                }

                this.parent();
            },

            draw: function () {
                var ctx = ig.system.context;
                ctx.save();

                ctx.globalAlpha = this.alpha * this.pagingAlpha;
                ig.AchievementRoundedRect.drawRoundedRect(ctx, this.pos.x, this.pos.y+this.control.offsetY, ig.Achievement.popup.item.width, ig.Achievement.popup.item.height, ig.Achievement.popup.item.radius, ig.Achievement.popup.item.color);

                var progressBarHeight = ig.Achievement.popup.item.progress.bar.height;
                var padding = ig.Achievement.popup.item.innerSpacing;
                var textSpacing = ig.Achievement.popup.item.textSpacing;
                var iconSize = ig.Achievement.popup.item.height - padding * 2 - textSpacing - progressBarHeight;
                var iconX = this.pos.x + padding
                var iconY = this.pos.y + padding +this.control.offsetY

                // var progressWidth = Math.round((ig.Achievement.popup.item.width - padding * 2) * this.data.progress / this.data.goal);
                // if (this.data.progress > 0 && progressWidth < ig.Achievement.popup.item.progress.bar.radius * 2) progressWidth = ig.Achievement.popup.item.progress.bar.radius * 2


                ctx.lineWidth = ig.Achievement.popup.item.progress.bar.height;
                ctx.lineCap = "round";
                var barY = this.pos.y + ig.Achievement.popup.item.height - ig.Achievement.popup.item.progress.bar.height - padding + ctx.lineWidth / 2;
                var barBgWidth = ig.Achievement.popup.item.width - padding * 2 - ctx.lineWidth / 2;
                var barWidth = Math.round(barBgWidth * this.data.progress / this.data.goal);
                if (this.data.progress > 0 && barWidth < ctx.lineWidth / 2) barWidth = ctx.lineWidth / 2 + 1;
                ctx.beginPath();
                ctx.strokeStyle = ig.Achievement.popup.item.progress.bar.colorBg;
                ctx.moveTo(this.pos.x + padding + ctx.lineWidth / 2, barY+this.control.offsetY);
                ctx.lineTo(this.pos.x + padding + barBgWidth, barY+this.control.offsetY)
                ctx.stroke()
                ctx.closePath();

                if (barWidth > 0) {
                    ctx.beginPath();
                    ctx.strokeStyle = ig.Achievement.popup.item.progress.bar.color;
                    ctx.moveTo(this.pos.x + padding + ctx.lineWidth / 2, barY+this.control.offsetY);
                    ctx.lineTo(this.pos.x + padding + barWidth, barY+this.control.offsetY)
                    ctx.stroke()
                    ctx.closePath();
                }

                var x = this.pos.x + iconSize + padding * 2+60;
                var y = this.pos.y + this.getFontHeight(ig.Achievement.popup.item.name.font) + padding;
                this.nameTextRenderer.draw(x, y+this.control.offsetY)

                y += this.getFontHeight(ig.Achievement.popup.item.description.font) + textSpacing
                this.descriptionTextRenderer.draw(x, y+this.control.offsetY)

                y += this.getFontHeight(ig.Achievement.popup.item.reward.font) + textSpacing
                this.rewardTextRenderer.draw(x, y+this.control.offsetY)

                var progressFontHeight = this.getFontHeight(ig.Achievement.popup.item.progress.font)
                x = this.pos.x + ig.Achievement.popup.item.width / 2
                y = this.pos.y+this.control.offsetY + ig.Achievement.popup.item.height - ig.Achievement.popup.item.progress.bar.height - padding + progressFontHeight + ((ig.Achievement.popup.item.progress.bar.height - progressFontHeight) / 2) + ig.Achievement.popup.item.progress.textOffsetY;
                this.progressTextRenderer.draw(x, y)

                if (this.iconImage.drawImageCtx) {
                    this.iconImage.drawImageCtx(ctx, 0, 0, this.iconImage.width, this.iconImage.height, iconX, iconY, iconSize+40, iconSize)
                } else {
                    ctx.drawImage(this.iconImage.data, 0, 0, this.iconImage.width, this.iconImage.height, iconX, iconY, iconSize+40, iconSize)
                }




                if (this.isCompleted) {
                    if (this.completedCenterYOffset == -9999) {
                        this.completedCenterYOffset = this.calculateCenterYOffset(this.completedTextRenderer);
                    }
                    ig.AchievementRoundedRect.drawRoundedRect(
                        ctx,
                        this.pos.x + ig.Achievement.popup.item.completedSign.x - ig.Achievement.popup.item.completedSign.width / 2,
                        this.pos.y+this.control.offsetY + ig.Achievement.popup.item.completedSign.y - ig.Achievement.popup.item.completedSign.height / 2,
                        ig.Achievement.popup.item.completedSign.width,
                        ig.Achievement.popup.item.completedSign.height,
                        10,
                        ig.Achievement.popup.item.completedSign.color
                    );
                    this.completedTextRenderer.draw(this.pos.x + ig.Achievement.popup.item.completedSign.x, this.pos.y+this.control.offsetY + ig.Achievement.popup.item.completedSign.y + ig.Achievement.popup.item.completedSign.textOffsetY + this.completedCenterYOffset)
                }



                if (this.isClaimable) {
                    if (this.claimCenterYOffset == -9999) {
                        this.claimCenterYOffset = this.calculateCenterYOffset(this.claimTextRenderer);
                    }
                    var claimWidth = ig.Achievement.popup.item.claimButton.width
                    var claimHeight = ig.Achievement.popup.item.claimButton.height


                    if (this.hasTouchInside ) { //&& this.isAnyInputInsideClaimButton()
                        claimWidth *= 0.9;
                        claimHeight *= 0.9;
                    }

                    ig.AchievementRoundedRect.drawShadowedRoundedRect(
                        ctx,
                        this.pos.x + ig.Achievement.popup.item.claimButton.x - claimWidth / 2,
                        this.pos.y+this.control.offsetY + ig.Achievement.popup.item.claimButton.y - claimHeight / 2,
                        claimWidth,
                        claimHeight,
                        10,
                        ig.Achievement.popup.item.claimButton.color,
                        ig.Achievement.popup.item.claimButton.shadowColor,
                        5
                    );

                    this.claimTextRenderer.draw(this.pos.x + ig.Achievement.popup.item.claimButton.x, this.pos.y+this.control.offsetY + ig.Achievement.popup.item.claimButton.y + ig.Achievement.popup.item.claimButton.textOffsetY + this.claimCenterYOffset)
                }

                ctx.globalAlpha = 1;
                ctx.restore();

                this.parent();


            },

            calculateCenterYOffset: function (textRenderer) {
                var ctx = ig.system.context;
                ctx.save();
                ctx.font = textRenderer.font;
                var metrics = ctx.measureText(textRenderer.text);
                var fontsize = parseInt(textRenderer.font.split("px")[0].split(" ").pop());
                var centerYOffset = (fontsize / 2) - metrics.actualBoundingBoxAscent;
                ctx.restore();
                return centerYOffset;
            },

            isAnyInputInsideClaimButton: function () {
                var hasTouchInsideClaimButton = this.isInsideClaimButtonBounds(ig.AchievementTouch.mouseX, ig.AchievementTouch.mouseY);

                if (!hasTouchInsideClaimButton) {
                    for (var i = 0; i < ig.multitouchInput.touches.length; i++) {
                        var t = ig.multitouchInput.touches[i];
                        if (this.isInsideClaimButtonBounds(t.x, t.y)) hasTouchInsideClaimButton = true;
                    }
                }

                if (!hasTouchInsideClaimButton) {
                    for (var i = 0; i < ig.AchievementTouch.touches.length; i++) {
                        var t = ig.AchievementTouch.touches[i];
                        if (this.isInsideClaimButtonBounds(t.x, t.y)) hasTouchInsideClaimButton = true;
                    }
                }

                return hasTouchInsideClaimButton;
            },

            isInsideClaimButtonBounds: function (x, y) {
                var w = ig.Achievement.popup.item.claimButton.width;
                var h = ig.Achievement.popup.item.claimButton.height;
                var claimX = this.pos.x + ig.Achievement.popup.item.claimButton.x - w / 2
                var claimY = this.pos.y + ig.Achievement.popup.item.claimButton.y - h / 2
                var boundLeft = claimX;
                var boundTop = claimY;
                var boundRight = boundLeft + w;
                var boundBottom = boundTop + h;
                // console.log("hasTouch", x.toFixed(), y.toFixed(), boundLeft.toFixed(), boundRight.toFixed(), boundTop.toFixed(), boundBottom.toFixed());
                if (this.boundLeft > this.boundRight) {
                    var temp = boundLeft;
                    boundLeft = boundRight;
                    boundRight = temp;
                }

                if (boundTop > boundBottom) {
                    var temp = boundTop;
                    boundTop = boundBottom;
                    boundBottom = temp;
                }

                if (x < boundLeft) return false;
                if (y < boundTop) return false;
                if (x > boundRight) return false;
                if (y > boundBottom) return false;


                return true;
            },

            getFontHeight: function (font) {
                return parseInt(font.split("px")[0]);
            }



        });
    });
