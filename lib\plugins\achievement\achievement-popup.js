ig.module('plugins.achievement.achievement-popup')
    .requires(
        'plugins.achievement.achievement-game-object'
    )
    .defines(function () {
        ig.AchievementPopup = ig.AchievementGameObject.extend({

            zIndex: 99,
            onClaim: null,
            onBack: null,
            entryType: "fadeIn",
            exitType: "fadeOut",
            soldierbg:new ig.Image("media/graphics/sprites/game/soldier4.jpeg"),
            items: [],
            page: 0,
            multiplierValue:0,
            init: function (x, y, settings) {

                this.soldierbg = dl.preload['utcbox'+ig.game.today];

                this.parent(0, 0, settings);

                if(ig.game.showUpgradeStatus) {
                    this.kill();
                }
                ig.Achievement.loadData();
                this.forceDraw = true;
                this.onClaim = new ig.AchievementSignal();
                this.onBack = new ig.AchievementSignal();

                var unclaimed = [];
                var ongoing = [];
                var completed = [];



                for (var i = 0; i < ig.Achievement.list.length; i++) {

                    var item = ig.game.spawnEntityBackward(ig.AchievementItem, 0, 0, { id: i,control:this });
                    item.onClaim = this.onClaim;
                    this.items.push(item);
                    var itemData = ig.Achievement.getAchievementData(i);
                    if (itemData.achieved) {
                        if (itemData.claimed) {
                            completed.push(item);
                        } else {
                            unclaimed.push(item);
                        }
                    } else {
                        ongoing.push(item);
                    }
                }

                var newArray;
                if (ig.Achievement.popup.autoSortItems) 
                    newArray = [].concat(unclaimed, ongoing, completed);
                else 
                    newArray = this.items;

                for (var i = 0; i < newArray.length; i++) {
                    var item = newArray[i];
                    var x = ig.system.width / 2 - ig.Achievement.popup.item.width / 2;
                    var y = (ig.system.height - ig.Achievement.popup.height) / 2 + ig.Achievement.popup.itemsStartY + (ig.Achievement.popup.item.height + ig.Achievement.popup.item.spacing) * (i % ig.Achievement.popup.itemsPerPage);

                    if (ig.responsive) {
                        x = ig.responsive.originalWidth / 2 - ig.Achievement.popup.item.width / 2;
                        y = (ig.responsive.originalHeight - ig.Achievement.popup.height) / 2 + ig.Achievement.popup.itemsStartY + (ig.Achievement.popup.item.height + ig.Achievement.popup.item.spacing) * (i % ig.Achievement.popup.itemsPerPage);
                    }

                    x += Math.floor(i / ig.Achievement.popup.itemsPerPage) * (ig.Achievement.popup.item.width + ig.Achievement.popup.item.spacing)

                    if (ig.responsive) {
                        item.anchoredPositionX = x;
                        item.anchoredPositionY = y+140;
                    } else {
                        item.pos.x = x;
                        item.pos.y = y+140;
                    }
                    item.originalX = x;
                    item.originalY = y;
                    item.page = Math.floor(i / ig.Achievement.popup.itemsPerPage)
                }

                this.prevButton = ig.game.spawnEntityBackward(ig.AchievementRoundedButton, ig.system.width / 2 - ig.Achievement.popup.paging.button.width * 1.5, ig.system.height / 2 + ig.Achievement.popup.height / 2 - ig.Achievement.popup.paging.button.height, {control:this, symbol: "prev", symbolColor: ig.Achievement.popup.paging.button.symbolColor, color: ig.Achievement.popup.paging.button.color, shadowColor: ig.Achievement.popup.paging.button.shadowColor, symbolSize: ig.Achievement.popup.paging.button.symbolSize, width: ig.Achievement.popup.paging.button.width, height: ig.Achievement.popup.paging.button.height, zIndex: 99999 });
                this.nextButton = ig.game.spawnEntityBackward(ig.AchievementRoundedButton, ig.system.width / 2 + ig.Achievement.popup.paging.button.width * 1.5, ig.system.height / 2 + ig.Achievement.popup.height / 2 - ig.Achievement.popup.paging.button.height, {control:this, symbol: "next", symbolColor: ig.Achievement.popup.paging.button.symbolColor, color: ig.Achievement.popup.paging.button.color, shadowColor: ig.Achievement.popup.paging.button.shadowColor, symbolSize: ig.Achievement.popup.paging.button.symbolSize, width: ig.Achievement.popup.paging.button.width, height: ig.Achievement.popup.paging.button.height, zIndex: 99999 });

                this.closeButton = ig.game.spawnEntityBackward(ig.AchievementRoundedButton, (ig.system.width + ig.Achievement.popup.width) / 2 - ig.Achievement.popup.closeButton.width / 2 - ig.Achievement.popup.closeButton.offsetX, (ig.system.height - ig.Achievement.popup.height) / 2 + ig.Achievement.popup.closeButton.height / 2 + ig.Achievement.popup.closeButton.offsetY, {control:this, symbol: "exit", symbolColor: ig.Achievement.popup.closeButton.symbolColor, symbolSize: ig.Achievement.popup.closeButton.symbolSize, width: ig.Achievement.popup.closeButton.width, height: ig.Achievement.popup.closeButton.height, color: ig.Achievement.popup.closeButton.color, shadowColor: ig.Achievement.popup.closeButton.shadowColor, zIndex: 99999 });
                this.closeButton.onClicked.addOnce(this.onClickClose, this)

                this.upgradeButton = ig.game.spawnEntityBackward(ig.AchievementRoundedButton, (ig.system.width + ig.Achievement.popup.width) / 2 - ig.Achievement.popup.upgradeButton.width / 2 - ig.Achievement.popup.upgradeButton.offsetX, (ig.system.height - ig.Achievement.popup.height) / 2 + ig.Achievement.popup.upgradeButton.height / 2 + ig.Achievement.popup.upgradeButton.offsetY+this.soldierbg.height+10, {control:this, symbol: "upgrade", symbolColor: ig.Achievement.popup.upgradeButton.symbolColor, symbolSize: ig.Achievement.popup.upgradeButton.symbolSize, width: ig.Achievement.popup.upgradeButton.width, height: ig.Achievement.popup.upgradeButton.height, color: ig.Achievement.popup.upgradeButton.color, shadowColor: ig.Achievement.popup.upgradeButton.shadowColor, zIndex: 99999 });
                this.upgradeButton.onClicked.addOnce(this.onClickUpgrade, this)

                this.pagingDisplay = ig.game.spawnEntityBackward(ig.AchievementRoundedButton, ig.system.width / 2, ig.system.height / 2 + ig.Achievement.popup.height / 2 - ig.Achievement.popup.paging.button.height, {control:this, text: "", textColor: ig.Achievement.popup.paging.display.textColor, font: ig.Achievement.popup.paging.display.font, color: ig.Achievement.popup.paging.display.color, offsetY: ig.Achievement.popup.paging.display.textOffsetY, shadowDistance: 0, width: ig.Achievement.popup.paging.button.width * 1.8, height: ig.Achievement.popup.paging.button.height, zIndex: 99999 });
                this.pagingDisplay.usePressedTween = false;
                this.pagingDisplay.inputEnabled = false;

                this.nextButton.onClicked.add(this.onClickNext, this)
                this.prevButton.onClicked.add(this.onClickPrev, this)

                if (ig.responsive) {
                    this.prevButton.anchoredPositionX = ig.responsive.originalWidth / 2 - ig.Achievement.popup.paging.button.width * 1.5
                    this.prevButton.anchoredPositionY = ig.responsive.originalHeight / 2 + ig.Achievement.popup.height / 2 - ig.Achievement.popup.paging.button.height;

                    this.nextButton.anchoredPositionX = ig.responsive.originalWidth / 2 + ig.Achievement.popup.paging.button.width * 1.5
                    this.nextButton.anchoredPositionY = ig.responsive.originalHeight / 2 + ig.Achievement.popup.height / 2 - ig.Achievement.popup.paging.button.height;

                    this.pagingDisplay.anchoredPositionX = ig.responsive.originalWidth / 2
                    this.pagingDisplay.anchoredPositionY = ig.responsive.originalHeight / 2 + ig.Achievement.popup.height / 2 - ig.Achievement.popup.paging.button.height;

                    this.closeButton.anchoredPositionX = (ig.responsive.originalWidth + ig.Achievement.popup.width) / 2 - ig.Achievement.popup.closeButton.width / 2 - ig.Achievement.popup.closeButton.offsetX;
                    this.closeButton.anchoredPositionY = (ig.responsive.originalHeight - ig.Achievement.popup.height) / 2 + ig.Achievement.popup.closeButton.height / 2 + ig.Achievement.popup.closeButton.offsetY;

                    this.upgradeButton.anchoredPositionX = (ig.responsive.originalWidth + ig.Achievement.popup.width) / 2 - ig.Achievement.popup.upgradeButton.width / 2 - ig.Achievement.popup.upgradeButton.offsetX;
                    this.upgradeButton.anchoredPositionY = (ig.responsive.originalHeight - ig.Achievement.popup.height) / 2 + ig.Achievement.popup.upgradeButton.height / 2 + ig.Achievement.popup.upgradeButton.offsetY+this.soldierbg.height+200;
                }


                var posx=(ig.system.width - ig.Achievement.popup.width) / 2;
                var posy=(ig.system.height - ig.Achievement.popup.height) / 2;
                var sizex=ig.Achievement.popup.width;
                var sizey=ig.Achievement.popup.height;                

                this.progressTextRenderer = new ig.AchievementTextRenderer();
                this.progressTextRenderer.font = ig.Achievement.popup.item.progress.font;
                this.progressTextRenderer.color = ig.Achievement.popup.item.progress.color;
                this.progressTextRenderer.align = "center";
                this.progressTextRenderer.text = "0 / 20";
                ig.game.sortEntitiesDeferred();
            },
            onClickUpgrade:function(){

                ig.game.showUpgradeStatus=true;
                this.onBack.dispatch();
                ig.game.disableEntities();

                ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.click2);

                this.tween({ offsetY: -2000 }, 0.4, { easing: ig.Tween.Easing.Back.EaseIn }).start();
                setTimeout(function(){
                    var upgrade = ig.game.spawnEntityBackward(EntityUpgrade,0,0,{control:this,zIndex:this.zIndex+10});
                    this.closeButton.kill();
                    this.nextButton.kill();
                    this.prevButton.kill();
                    this.upgradeButton.kill();
                    this.pagingDisplay.kill();
                    for (var i = 0; i < this.items.length; i++) {
                        var item = this.items[i];
                        item.kill();
                    }
                    this.kill();
                }.bind(this),500);

            },
            onClickPrev: function () {
                ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.click2);
                this.page -= 1;
                if (this.page < 0) this.page = 0;
            },
            onClickNext: function () {
                ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.click2);
                this.page += 1;
                var limit = Math.ceil(this.items.length / ig.Achievement.popup.itemsPerPage) - 1;
                if (this.page > limit) this.page = limit;
            },
            onClickClose: function () {
                this.onBack.dispatch();
                this.exit();
                ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.click2);
            },
            show: function () {
                this.offsetY= -2000;
                this.tween({ offsetY: 0 }, 1, { easing: ig.Tween.Easing.Back.EaseOut }).start();

            },

            exit: function () {

                this.tween({ offsetY: -2000 }, 0.4, { easing: ig.Tween.Easing.Back.EaseIn }).start();
                setTimeout(function(){
                    this.closeButton.kill();
                    this.nextButton.kill();
                    this.prevButton.kill();
                    this.upgradeButton.kill();
                    this.pagingDisplay.kill();
                    for (var i = 0; i < this.items.length; i++) {
                        var item = this.items[i];
                        item.kill();
                    }
                    this.kill();
                    ig.game.enableEntities();
                }.bind(this),500);

            },

            update: function () {
                for (var i = 0; i < this.items.length; i++) {
                    var item = this.items[i];
                    item.pagingOffsetX = -this.page * (ig.Achievement.popup.item.width + ig.Achievement.popup.item.spacing)
                    item.currentPage = this.page;
                }
                this.pagingDisplay.text = (this.page + 1) + " / " + Math.ceil(this.items.length / ig.Achievement.popup.itemsPerPage)
                this.parent();

                if(ig.game.svasData.uid==0){
                    ig.game.client.getUpgradeMultiplier();                    
                }


            },

            draw: function () {
                var ctx = ig.system.context;


                ctx.save();
                ctx.textBaseline = "top";
                ctx.fillStyle = ig.Achievement.overlay.color;
                ctx.globalAlpha = ig.Achievement.overlay.alpha * this.alpha;
                ctx.fillRect(0, 0, ig.system.width, ig.system.height);
                ctx.globalAlpha = this.alpha;



                var posx=(ig.system.width - ig.Achievement.popup.width) / 2;
                var posy=(ig.system.height - ig.Achievement.popup.height) / 2;
                var sizex=ig.Achievement.popup.width;
                var sizey=ig.Achievement.popup.height;                
                ig.AchievementRoundedRect.drawRoundedRect(ctx, posx, posy+this.offsetY, sizex , sizey, ig.Achievement.popup.radius, ig.Achievement.popup.color);

                this.soldierbg.drawImage(
                    0,
                    0,
                    this.soldierbg.width,  
                    this.soldierbg.height,
                    posx+20,
                    posy+20+this.offsetY,
                    sizex-40,  
                    sizey/5-30
                );                

                ctx.fillStyle='#ffffff';
                ctx.textAlign = "left";
                ctx.textBaseline = "Alphabetic";
                ctx.font = "60px montserrat-bold";

                var nameIndex=ig.game.today-1;
                if(nameIndex<0) nameIndex=6;
                if(ig.game.dayText2==''){
                    var txt='';
                }else{
                    var txt=ig.game.upgradeName[nameIndex];
                }
                ctx.fillText(txt,posx+50,posy+20+this.offsetY+sizey/5-120);




                ctx.fillStyle='#000000';

                ctx.font = "35px montserrat";
                ctx.fillText('Complete the following actions to claim your ',posx+30,posy+20+this.offsetY+sizey/5-20);
                ctx.fillText('rewards for today.',posx+30,posy+20+this.offsetY+sizey/5+20);

                ctx.font = "40px montserrat-bold";
                ctx.fillText('Duration : '+ig.game.utcTextDuration,posx+30,posy+20+this.offsetY+sizey/5+75);

                ctx.font = "50px montserrat-bold";
                ctx.fillText('Bonus Multiplier : '+ig.game.multiplierValue+' %',posx+30,posy+20+this.offsetY+sizey/5+150);

                var completedCount = 0;
                var totalCount = ig.Achievement.data.achievements.length;
                for (var i = 0; i < ig.Achievement.data.achievements.length; i++) {
                    var item = ig.Achievement.data.achievements[i];
                    if (item.claimed) completedCount++
                }


                ctx.lineWidth = ig.Achievement.popup.item.progress.bar.height;
                ctx.lineCap = "round";

                var barX = (ig.system.width - ig.Achievement.popup.item.width) / 2
                var barY = (ig.system.height - ig.Achievement.popup.height) / 2 + ig.Achievement.popup.title.offsetY + ig.Achievement.popup.item.progress.bar.height + ctx.lineWidth / 2;
                var barBgWidth = ig.Achievement.popup.item.width - ctx.lineWidth / 2;
                var barWidth = Math.round(barBgWidth * completedCount / totalCount);
                
                if (completedCount > 0 && barWidth < ctx.lineWidth / 2) 
                    barWidth = ctx.lineWidth / 2 + 1;

                ctx.globalAlpha = 1;

                ctx.restore();

                this.parent();


            },



        });
    });
