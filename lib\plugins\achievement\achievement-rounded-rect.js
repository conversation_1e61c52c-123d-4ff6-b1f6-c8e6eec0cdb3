ig.module('plugins.achievement.achievement-rounded-rect')
    .requires(
        'plugins.achievement.achievement-game-object'
    )
    .defines(function () {
        ig.AchievementRoundedRect = {

            cache: {

            },

            drawShadowedRoundedRect: function (ctx, x, y, width, height, radius, color, shadowColor, shadowDistance) {
                if (width == 0) return;
                if (height == 0) return;
                if (!color) color = "#ffffff";
                if (!shadowColor) shadowColor = "#666666";
                if (!radius) radius = 10;

                var sourceCanvas = this.getShadowedRoundedRectCache(width, height, radius, color, shadowColor, shadowDistance)
                ctx.drawImage(sourceCanvas, x, y);
            },

            getShadowedRoundedRectCache: function (width, height, radius, color, shadowColor, shadowDistance) {
                var key = "canvasShadowed|" + width + "|" + height + "|" + radius + "|" + color + "|" + shadowColor + "|" + shadowDistance;
                if (this.cache[key]) return this.cache[key];

                var canvas = ig.$new('canvas');
                canvas.width = width;
                canvas.height = height;
                var ctx = canvas.getContext('2d');

                this.drawRoundedRect(ctx, 0, 0, width, height, radius, shadowColor);
                this.drawRoundedRect(ctx, 0, 0, width, height - shadowDistance, radius, color);

                this.cache[key] = canvas;
                return canvas;
            },

            drawRoundedRect: function (ctx, x, y, width, height, radius, color) {
                if (width == 0) return;
                if (height == 0) return;
                if (!color) color = "#ffffff";
                width = Math.round(width)
                height = Math.round(height)
                radius = Math.round(radius)

                var sourceCanvas = this.getCache(width, height, radius, color)
                ctx.drawImage(sourceCanvas, x, y, width, height);
            },

            getCache: function (width, height, radius, color) {
                var key = "canvas|" + width + "|" + height + "|" + radius + "|" + color;
                if (this.cache[key]) return this.cache[key];

                var canvas = ig.$new('canvas');
                canvas.width = width;
                canvas.height = height;
                var ctx = canvas.getContext('2d');
                var r = radius;

                ctx.fillStyle = color;
                ctx.fillRect(r, 0, width - r * 2, height)
                ctx.fillRect(0, r, width, height - r * 2)

                ctx.beginPath();
                ctx.arc(r, r, r, 0, Math.PI * 2);
                ctx.fill();
                ctx.closePath();

                ctx.beginPath();
                ctx.arc(width - r, r, r, 0, Math.PI * 2);
                ctx.fill();
                ctx.closePath();

                ctx.beginPath();
                ctx.arc(r, height - r, r, 0, Math.PI * 2);
                ctx.fill();
                ctx.closePath();

                ctx.beginPath();
                ctx.arc(width - r, height - r, r, 0, Math.PI * 2);
                ctx.fill();
                ctx.closePath();

                this.cache[key] = canvas;
                return canvas;
            }



        };
    });
