ig.module(
	'dl.core.system.timer'
).requires(
	'dl.dl'
).defines(function () {
	'use strict';

	dl.SystemTimer = ig.Class.extend({
		staticInstantiate: function () {
			this.__lastTick = dl.SystemTimer.TIME_NOW;

			return this;
		},
		tick: function () {
			var delta = dl.SystemTimer.TIME_NOW - this.__lastTick;
			this.__lastTick = dl.SystemTimer.TIME_NOW;

			return delta;
		}
	});

	dl.SystemTimer.TIME_NOW = Number.MIN_VALUE;
	dl.SystemTimer.LAST_STEP = 0;
	dl.SystemTimer.MAX_STEP = dl.FPS_INTERVAL;
	dl.SystemTimer.Step = function () {
		var current = performance.now();
		var delta = current - dl.SystemTimer.LAST_STEP;

		dl.SystemTimer.TIME_NOW += Math.min(delta, dl.SystemTimer.MAX_STEP);
		dl.SystemTimer.LAST_STEP = current;
	};

	dl.Timer = ig.Class.extend({
		staticInstantiate: function (startTime, duration) {
			this._startTime = startTime || this.getCurrentTime();
			this._duration = duration || 0;
			this._endTime = this._startTime + this._duration;
			this.__pausedAt = 0;

			return this;
		},
		getCurrentTime: function () {
			dl.warnImplement(this.constructor.name);
		},
		start: function (startTime, duration) {
			this._startTime = startTime || this.getCurrentTime();
			this._duration = duration || 0;
			this._endTime = this._startTime + this._duration;
			this.__pausedAt = 0;
		},
		reset: function () {
			this._startTime = this.getCurrentTime();
			this._endTime = this._startTime + this._duration;
			this.__pausedAt = 0;
		},
		delta: function () {
			return (this.__pausedAt || this.getCurrentTime()) - this._endTime;
		},
		timePassed: function () {
			return (this.__pausedAt || this.getCurrentTime()) - this._startTime;
		},
		pause: function () {
			if (!this.__pausedAt) {
				this.__pausedAt = this.getCurrentTime();
			}
		},
		resume: function () {
			if (this.__pausedAt) {
				this._startTime += this.getCurrentTime() - this.__pausedAt;
				this._endTime = this._startTime + this._duration;

				this.__pausedAt = 0;
			}
		},
		addTime: function (duration) {
			this._duration += duration;
			this._endTime = this._startTime + this._duration;
		},
		getPercentPassed: function () {
			var percent = this.timePassed() / this._duration;
			return percent.limit(0, 1);
		},
		getPercentLeft: function () {
			var percent = this.delta() / this._duration;
			return -percent.limit(-1, 0);
		},
		isFinished: function () {
			return this.delta() >= 0;
		}
	});

	/**
	 * Game timer
	 * Effected by game loop
	 */
	dl.GameTimer = dl.Timer.extend({
		staticInstantiate: function (duration) {
			this.parent(dl.SystemTimer.TIME_NOW, duration);

			if (dl.game) {
				// bind events
				this.onPauseGameBound = this.onPauseGame.bind(this);
				this.onResumeGameBound = this.onResumeGame.bind(this);
				dl.game.onEvent('pause', this.onResumeGameBound);
				dl.game.onEvent('resume', this.onResumeGameBound);
			} else {
				dl.warn('Game timer start before the game');
			}
		},
		getCurrentTime: function () {
			return dl.SystemTimer.TIME_NOW;
		},
		kill: function(){
			dl.game.offEvent('pause', this.onResumeGameBound);
			dl.game.offEvent('resume', this.onResumeGameBound);
		},
		start: function (duration) {
			this.parent(this.getCurrentTime(), duration);
		},
		onPauseGame: function () {
			if (this.__pausedAt) {
				this.__previousPauseAt = this.__pausedAt;
			}

			this.pause();
		},
		onResumeGame: function () {
			if (this.__previousPauseAt) {
				this.__pausedAt = this.__previousPauseAt;
				this.__previousPauseAt = 0;
				return;
			}

			this.resume();
		}
	});

	/**
	 * Global Timer
	 * Not effected by game loop
	 */
	dl.GlobalTimer = dl.Timer.extend({
		staticInstantiate: function (startTime, duration) {
			this.parent(startTime, duration);
		},
		getCurrentTime: function () {
			return Date.now();
		},
		start: function (startTime, duration) {
			this.parent(startTime, duration);
		}
	});
});
