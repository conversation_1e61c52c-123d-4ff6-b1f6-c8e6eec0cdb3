ig.module(
    'dl.debug.performances'
).requires(
    'dl.debug.debug'
).defines(function () {
    'use strict';

    dl.debug.updateNumber = 0;
    dl.debug.startMeasurePerformance = function (name) {
        if (!this.performance) {
            this.performance = {};
        }
        if (!this.performance[name]) {
            this.performance[name] = {
                start: 0,
                end: 0,
                logs: Array(100).fill(0)
            };
        }

        this.performance[name].start = performance.now();
    };

    dl.debug.endMeasurePerformance = function (name, indexMultiply) {
        if (!this.performance) return;
        if (!this.performance[name]) return;

        this.performance[name].end = performance.now();
        var currentPerformance = this.performance[name].end - this.performance[name].start;
        this.performance[name].logs.shift();
        this.performance[name].logs.push(currentPerformance);

        // draw
        dl.debug.drawMeasurePerformance(name, indexMultiply);
    };

    dl.debug.drawMeasurePerformance = function (name, indexMultiply) {
        if (!indexMultiply) indexMultiply = 0;
        var offset = (indexMultiply * dl.system.height * 0.2);

        var ctx = dl.debug.getContext();
        ctx.save();
        ctx.beginPath();
        ctx.moveTo(0, dl.system.height);
        var drawHeight = dl.debug.FONT_SIZE * 2;
        var logsLength = this.performance[name].logs.length;
        var total = 0;
        var highest = 0;
        for (var i = 0; i < logsLength; i++) {
            var log = this.performance[name].logs[i];

            total += log;
            if (log >= highest) highest = log;

            ctx.lineTo(
                (dl.system.width / (logsLength - 1)) * i,
                dl.system.height - (log * drawHeight) - offset
            );
        }
        ctx.lineTo(dl.system.width, dl.system.height);
        ctx.closePath();
        ctx.stroke();

        ctx.font = dl.debug.FONT_SIZE + 'px Arial';
        ctx.fillText(
            'Performance: ' + (name || 'default'),
            0,
            dl.system.height - offset,
            dl.system.width * 0.3);
        ctx.fillText(
            'Highest: ' + highest,
            dl.system.width * 0.35,
            dl.system.height - offset,
            dl.system.width * 0.3);
        ctx.fillText(
            'Avg: ' + (total / logsLength),
            dl.system.width * 0.7,
            dl.system.height - offset,
            dl.system.width * 0.3);

        ctx.restore();
    };

    dl.debug.enableDebugPerformance = function () {
        MyGame.inject({
            run: function () {
                dl.debug.updateNumber += 1;
                dl.debug.startMeasurePerformance();
                this.parent();
                dl.debug.endMeasurePerformance();
            }
        });
    };
});
