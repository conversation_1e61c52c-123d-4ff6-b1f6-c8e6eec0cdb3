/**
 * Created by <PERSON><PERSON>
 * Handle room events on client side
 */
ig.module(
	'dl.network.client.room-event-handler'
).defines(function () {
	RoomEventHandler = {};

	RoomEventHandler.handleEvent = function (room, event) {
		if (!event) return;

		switch (event.type) {
			case RoomEvent.EVENT_TYPE.PLAYER_JOIN:
				return RoomEventHandler.onPlayerJoin(room, event.info);

			case RoomEvent.EVENT_TYPE.PLAYER_LEAVE:
				return RoomEventHandler.onPlayerLeave(room, event.info);

			case RoomEvent.EVENT_TYPE.PLAYER_READY:
				return RoomEventHandler.onPlayerReady(room, event.info);

			case RoomEvent.EVENT_TYPE.AUTO_START_STATE_CHANGE:
				return RoomEventHandler.onAutoStartStateChange(room, event.info);

			case RoomEvent.EVENT_TYPE.KICK_PLAYER:
				return RoomEventHandler.onKickPlayer(room, event.info);

			case RoomEvent.EVENT_TYPE.SEND_EMOJI:
				return RoomEventHandler.onSendEmoji(room, event.info);

			case RoomEvent.EVENT_TYPE.ROOM_START:
				return RoomEventHandler.onRoomStart(room, event.info);

			case RoomEvent.EVENT_TYPE.ROOM_STARTED:
				return RoomEventHandler.onRoomStarted(room, event.info);

			case RoomEvent.EVENT_TYPE.ROOM_END:
				return RoomEventHandler.onRoomEnd(room, event.info);

			case RoomEvent.EVENT_TYPE.CLIENT_GAME_EVENT:
				return RoomEventHandler.onClientGameEvent(room, event.info);

			case RoomEvent.EVENT_TYPE.NETWORK_GAME_EVENT:
				return RoomEventHandler.onNetworkGameEvent(room, event.info);

			case RoomEvent.EVENT_TYPE.SYNC_PLAYER_COUNT:
				return RoomEventHandler.onSyncPlayerCount(room, event.info);
		}
	};

	// ----------------
	// Start utilities functions
	// ----------------
	RoomEventHandler.onRoomDataUpdate = function (room) {
		if (typeof room.onRoomUpdateCallback === 'function') {
			room.onRoomUpdateCallback(room);
		}
	};

	RoomEventHandler.checkCurrentLevel = function (checkLevel) {
		if (ig.game.director.levels[ig.game.director.currentLevel] != checkLevel) return false;

		return true;
	};
	// ----------------
	// End utilities functions
	// ----------------

	RoomEventHandler.onPlayerJoin = function (room, info) {
		if (!RoomEventHandler.checkCurrentLevel(LevelMatching) &&
			!RoomEventHandler.checkCurrentLevel(LevelMatchingPrivate)) return false;

			if(!ig.game.popupStatus){
				dl.scene.spawnNotification({
					notificationDrawConfigs: {
						contentConfigs: [
							{
								type: 'text',
								text: _STRINGS.NETWORK.NOTIFICATION_MATCHING_PLAYER_JOINED.replace('<replace>', info.playerName),
								fillStyle: dl.configs.TEXT_COLOR.GRAY,
								fontSize: 46,
								fontFamily: dl.configs.FONT.SOURCE_SANS.name
							}],
						backgroundConfigs: {
							lineWidth: 2,
							fillStyle: dl.configs.NOTIFICATION_COLORS.NEUTRAL,
							strokeStyle: dl.configs.NOTIFICATION_COLORS.NEUTRAL,
							box: { width: 800, height: 90, round: 10, padding: { x: 100, y: 5 } }
						},
						notifSfx:true,
					}
				});
			}
		return true;
	};

	RoomEventHandler.onPlayerLeave = function (room, info) {
		if (!RoomEventHandler.checkCurrentLevel(LevelMatching) &&
			!RoomEventHandler.checkCurrentLevel(LevelMatchingPrivate) &&
			!RoomEventHandler.checkCurrentLevel(LevelGame)) return false;

			if(!ig.game.popupStatus){
				dl.scene.spawnNotification({
					notificationDrawConfigs: {
						contentConfigs: [
							{
								type: 'text',
								text: _STRINGS.NETWORK.NOTIFICATION_MATCHING_PLAYER_LEFT.replace('<replace>', info.playerName),
								fillStyle: dl.configs.TEXT_COLOR.GRAY,
								fontSize: 46,
								fontFamily: dl.configs.FONT.SOURCE_SANS.name
							}],
						backgroundConfigs: {
							lineWidth: 2,
							fillStyle: dl.configs.NOTIFICATION_COLORS.NEUTRAL,
							strokeStyle: dl.configs.NOTIFICATION_COLORS.NEUTRAL,
							box: { width: 800, height: 90, round: 10, padding: { x: 100, y: 5 } }
						}
					}
				});
			}
		if (RoomEventHandler.checkCurrentLevel(LevelGame) && ig.game.managers && ig.game.managers.game) {
			ig.game.managers.game.onPlayerLeave(info);
		}

		return true;
	};

	RoomEventHandler.onPlayerReady = function (room, info) {
		// handled in matching onRoomUpdate

		return true;
	};

	RoomEventHandler.onAutoStartStateChange = function (room, info) {
		// handled in matching onRoomUpdate

		return true;
	};

	RoomEventHandler.onKickPlayer = function (room, info) {
		console.log('To be implement', info);

		return true;
	};

	RoomEventHandler.onSendEmoji = function (room, info) {
		if (ig.game.game.emojiBar) {
			ig.game.game.emojiBar.onSendEmoji(info);
		}

		return true;
	};

	RoomEventHandler.onRoomStart = function (room, info) {
		if (!RoomEventHandler.checkCurrentLevel(LevelMatching) &&
			!RoomEventHandler.checkCurrentLevel(LevelMatchingPrivate)) return false;

		if (typeof room.onRoomStartCallback === 'function') {
			room.onRoomStartCallback(info);
		}

		return true;
	};

	RoomEventHandler.onRoomStarted = function (room, info) {
		if (!RoomEventHandler.checkCurrentLevel(LevelMatching) &&
			!RoomEventHandler.checkCurrentLevel(LevelMatchingPrivate)) return false;

		if (typeof room.onRoomStartedCallback === 'function') {
			room.onRoomStartedCallback(info);
		}

		return true;
	};

	RoomEventHandler.onRoomEnd = function (room, info) {
		console.log('To be implement');

		return true;
	};

	RoomEventHandler.onClientGameEvent = function (room, info) {
		console.log('To be implement');

		return true;
	};

	RoomEventHandler.onNetworkGameEvent = function (room, info) {
		room.networkGame.addPendingEvent(info);

		return true;
	};
	RoomEventHandler.onSyncPlayerCount = function (room, info) {
		try{
			ig.game.managers.game.updateCapitalSoldier(info);
			return true;
		}catch(e){}
	};

});
