/**
 * Created by <PERSON><PERSON>
 * Count down timer
 */

var CountDownTimer = function (duration, callback) {
	var self = {};
	self.init = function (duration, callback) {
		self.createdTime = 0;
		self.duration = 0;
		self.callback = null;

		self.timeUp = false;
		self.pause = true;
		self._timeLeft = 0;

		self.reset(callback);
		self.setDuration(duration);
	};

	self.setDuration = function (time) {
		self.duration = time;
	};

	self.start = function () {
		self.pause = false;
	};

	self.reset = function (callback) {
		self.createdTime = Date.now();
		if (typeof callback === 'function') self.callback = callback;

		self.timeUp = false;
		self.pause = true;
		self._timeLeft = 0;
	};

	self.addTime = function (duration) {
		self.duration += duration;
		self.getTimeLeft();
	};

	self.update = function () {
		if (self.pause) return;
		if (self.timeUp) return;

		self.getTimeLeft();

		if (self.timeLeft <= 0) {
			self.timeLeft = 0;
			self.timeUp = true;

			self.executeCallback();
		}
	};

	self.getTimeLeft = function () {
		self.timeLeft = self.duration - (Date.now() - self.createdTime);

		return self.timeLeft;
	};

	self.forceFinished = function (doCallback) {
		self.timeLeft = 0;
		self.timeUp = true;

		if (doCallback) self.executeCallback();
	};

	self.executeCallback = function () {
		if (typeof self.callback === 'function') {
			self.callback();
			self.callback = null;
		}
	};

	self.init(duration, callback);
	return self;
};

/**
 * Export module
 */
if (typeof module !== 'undefined') {
	module.exports.CountDownTimer = CountDownTimer;
}
