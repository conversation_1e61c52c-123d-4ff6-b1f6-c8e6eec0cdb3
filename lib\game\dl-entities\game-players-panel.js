ig.module(
    'game.dl-entities.game-players-panel'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.animation-sheet',
    'game.dl-entities.game-player-info'
).defines(function () {
    dl.EntityGamePlayersPanel = dl.Entity
        .extend(dl.MixinDocker)
        .extend(dl.MixinAnimationSheet)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this._useAnimationSheetAsSize = true;

                this.totalPlayer = ig.game.client.roomMaxPlayer;
                this.players = [];

                return this;
            },

            postInit: function () {
                this.parent();
                this.initPlayers();
            },

            initPlayers: function () {
                this.players = [];

                for (var i = 0; i < this.totalPlayer; i++) {
                    var player = this.spawnEntity(dl.EntityGamePlayerInfo, {});
                    this.players.push(player);
                }

                this.onUpdateOrientation();
            },

            spawnBubble: function (index, playerId) {
                var ownerInfo = null;

                for (var i = 0; i < this.players.length; i++) {
                    if (this.players[i].playerData.playerId == playerId) {
                        ownerInfo = this.players[i];
                        break;
                    }
                }
                
                // if(!ig.ua.mobile){
                    dl.scene.spawnChatBubble({
                        chatBubbleDrawConfigs: {
                            textConfigs: {
                                text: ' ', // text display in chat bubble
                                fillStyle: 'black',
                                textAlign: 'center', // [center|left|right];
                                fontSize: 24,
                                fontFamily: 'Arial'
                            },
                            avatarConfigs: {
                                image: dl.preload.emojiIcons[index], // image display in chat bubble
                                size: { x: 100, y: 100 }, // image size
                                padding: { x: 4, y: 4 } // extra space outside image
                            },
                            bubbleConfigs: {
                                lineWidth: 2,
                                fillStyle: 'lightblue',
                                strokeStyle: 'black',

                                shadowColor: 'black',
                                shadowBlur: 4,
                                shadowOffsetX: 4,
                                shadowOffsetY: 4,

                                box: {
                                    width: 200, // content min width
                                    height: 160, // content min height
                                    round: 10, // round curves distance
                                    padding: { x: 10, y: 2 } // extra space outside the content area
                                },
                                tail: {
                                    length: 20, // tail length
                                    width: 15, // tail width
                                    direction: { x: 0.5, y: 0 } // tail direction, will be update if input invalid (0-1)
                                }
                            }
                        },
                        chatBubbleAppearTime: 400, // appear time - millisecond
                        chatBubbleAliveTime: 2000, // alive time - millisecond
                        chatBubbleDisappearTime: 200, // disappear time - millisecond
                        chatBubbleDocker: ownerInfo,
                        chatBubblePercent: { x: 0.16, y: 0.7 }, // position percent of ChatBubbleParentEntity (0-1) related to the ChatBubbleParentEntity position and size
                        chatBubbleOffset: { x: 0, y: -15 }, // extra offset from position percent of ChatBubbleParentEntity
                        chatBubbleAlpha: 1 // chat bubble alpha
                    });
                // }
            },
            update:function(){
                this.parent();
                this.changeOrientation();
            },
            changeOrientation: function () {
                // this.parent();

                if (!this.players) return;
                if (!this.players.length) return;

                var offsetX = 0.25;
                switch (this.totalPlayer) {
                    case 2:
                        offsetX = 0.5;
                        break;

                    case 4:
                    default:
                        offsetX = 0.25;
                        break;
                }

                var xx=window.innerWidth;
                var yy=window.innerHeight;

                if(ig.ua.mobile && !ig.ua.iPad && xx<yy){
                    offsetX = 0.5;

                    if(this.totalPlayer<=2){
                        var posY1=0.5;
                        var posY2=0.5;
                    }else{
                        var posY1=0.2;
                        var posY2=0.6;                        
                    }


                    var startOffsetX = (-offsetX * 2 + offsetX) * 0.5;
                    var j=0;
                    for (var i = 0; i < this.totalPlayer; i++) {
                        var player = this.players[i];

                        if(i<=1){
                            player.c_DockerComponent.updateProperties({
                                dockerObject: this,
                                dockerPercent: { x: 0.5 + startOffsetX + offsetX * i, y: posY1 },
                                dockerOffset: { x: 0, y: 0 }
                            });

                        }else{
                            player.c_DockerComponent.updateProperties({
                                dockerObject: this,
                                dockerPercent: { x: 0.5 + startOffsetX + offsetX * j, y: posY2 },
                                dockerOffset: { x: 0, y: 0 }
                            });
                            j +=1;
                        }
                    }

                }else{
                    var startOffsetX = (-offsetX * this.totalPlayer + offsetX) * 0.5;
                    for (var i = 0; i < this.totalPlayer; i++) {
                        var player = this.players[i];
                        player.c_DockerComponent.updateProperties({
                            dockerObject: this,
                            dockerPercent: { x: 0.5 + startOffsetX + offsetX * i, y: 0 },
                            dockerOffset: { x: 0, y: 75 }
                        });
                    }

                }

            },

            updateData: function (data) {
                if (!data) return;
                for (var i = 0; i < this.totalPlayer; i++) {
                    var playerData = data.playerList[i];
                    this.players[i].updateData(playerData);
                }
                // console.log(this.players);
            },

            tweenIn: function (time) {
                // dl.TweenTemplate.scaleIn(this, time);
                // for (var i = 0; i < this.totalPlayer; i++) {
                //     var player = this.players[i];
                //     player.tweenIn(time);
                // }
            },

            tweenOut: function (time) {
                dl.TweenTemplate.scaleOut(this, time);
                for (var i = 0; i < this.totalPlayer; i++) {
                    var player = this.players[i];
                    player.tweenOut(time);
                }
            }

            // updateOrientation: function () {
            //     this.onUpdateOrientation();
            // }
        });

    // Enable cache
    dl.enableCacheCanvas(dl.EntityGamePlayersPanel);
});
