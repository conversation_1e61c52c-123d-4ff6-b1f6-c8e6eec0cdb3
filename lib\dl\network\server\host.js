
if (typeof require !== 'undefined') {
	var SERVER_MESSAGE = require('../shared/server-message.js').SERVER_MESSAGE;

	var NameAndAvatar = require('../shared/name-and-avatar.js').NameAndAvatar;
	var RoomPassword = require('../shared/room-password.js').RoomPassword;

	var Room = require('./room.js').Room;

    var apiKey='6052521b7625e31d4ee9cc706732484fcf850877';
    var gameId='1651673491261401';
    var sk    ='MTU2MTY3MzQ5MTI2NDA5Ng==';

}

var Host = function (server) {
	var self = {};

	self.init = function (server) {
		self.hostName = 'War Nations';
		self.minClientVersion = 0.01;

		self.server = server;
		// room properties
		self.lastRoomId = -1;
		self.roomList = [];
		self.roomHash = {};

		self.initHandlers();
		self.interval = setInterval(self.updateRooms, 1000 / 5); // 60 FPS - 1000/60

        self.chievementlist = [
            /*0*/    { name: "Collect Daily Login", description: "Get reward for daily login", reward: "500 coins", goal: 1,value:500},
            /*1*/    { name: "Tount Enemy With Emoji", description: "Use emoji to get reward", reward: "1500 coins", goal: 3,value:1500},
            /*2*/    { name: "Win 1 Match", description: "Get 1'st position in 1 match", reward: "2500 coins", goal: 1,value:2500},
            /*3*/    { name: "Win 2 Match", description: "Get 1'st position in 2 match", reward: "4000 coins", goal: 2,value:4000},
            /*4*/    { name: "Win 5 Match", description: "Get 1'st position in 5 match", reward: "7500 coins", goal: 5,value:7500},
            /*5*/    { name: "Win 10 Match", description: "Get 1'st position in 10 match", reward: "12000 coins", goal: 10,value:12000},
            /*6*/    { name: "Win 20 Match", description: "Get 1'st position in 20 match", reward: "15000 coins", goal: 20,value:15000}
        ];


        self.shopData = [
                              {name:'FORT',
                               data:[
                                        {buyTitle:'Fort Level 1',buyStatus:1,buyImg:'skin1',buyPrice:700},
                                        {buyTitle:'Fort Level 2',buyStatus:0,buyImg:'skin1',buyPrice:700},
                                        {buyTitle:'Fort Level 3',buyStatus:0,buyImg:'skin2',buyPrice:1200},
                                        {buyTitle:'Fort Level 4',buyStatus:0,buyImg:'skin3',buyPrice:2000},
                                        {buyTitle:'Fort Level 5',buyStatus:0,buyImg:'skin4',buyPrice:3000},
                                        {buyTitle:'Fort Level 6',buyStatus:0,buyImg:'skin5',buyPrice:5000}
                                    ]
                              },
                              {name:'MISSILE SILO',
                               data:[
                                        {buyTitle:'Missile Level 1',buyStatus:1,buyImg:'skin1',buyPrice:700},
                                        {buyTitle:'Missile Level 2',buyStatus:0,buyImg:'skin1',buyPrice:700},
                                        {buyTitle:'Missile Level 3',buyStatus:0,buyImg:'skin2',buyPrice:1200},
                                        {buyTitle:'Missile Level 4',buyStatus:0,buyImg:'skin3',buyPrice:2000},
                                        {buyTitle:'Missile Level 5',buyStatus:0,buyImg:'skin4',buyPrice:3000},
                                        {buyTitle:'Missile Level 6',buyStatus:0,buyImg:'skin5',buyPrice:5000}
                                    ]
                              },
                              {name:'PROPAGANDA TOWER',
                               data:[
                                        {buyTitle:'Tower Level 1',buyStatus:1,buyImg:'skin1',buyPrice:700},
                                        {buyTitle:'Tower Level 2',buyStatus:0,buyImg:'skin1',buyPrice:700},
                                        {buyTitle:'Tower Level 3',buyStatus:0,buyImg:'skin2',buyPrice:1200},
                                        {buyTitle:'Tower Level 4',buyStatus:0,buyImg:'skin3',buyPrice:2000},
                                        {buyTitle:'Tower Level 5',buyStatus:0,buyImg:'skin4',buyPrice:3000},
                                        {buyTitle:'Tower Level 6',buyStatus:0,buyImg:'skin5',buyPrice:5000}
                                    ]
                              },
                              {name:'WEATHER DOME',
                               data:[
                                        {buyTitle:'Dome Level 1',buyStatus:1,buyImg:'skin1',buyPrice:700},
                                        {buyTitle:'Dome Level 2',buyStatus:0,buyImg:'skin1',buyPrice:700},
                                        {buyTitle:'Dome Level 3',buyStatus:0,buyImg:'skin2',buyPrice:1200},
                                        {buyTitle:'Dome Level 4',buyStatus:0,buyImg:'skin3',buyPrice:2000},
                                        {buyTitle:'Dome Level 5',buyStatus:0,buyImg:'skin4',buyPrice:3000},
                                        {buyTitle:'Dome Level 6',buyStatus:0,buyImg:'skin5',buyPrice:5000}
                                    ]
                              },
                     ];

		// console.log('Init host: ', self.hostName);
	};

	self.initHandlers = function () {
		self.server.onClientConnectHandlers.push(self.onClientConnect);
		self.server.onClientDisconnectHandlers.push(self.onClientDisconnect);
		self.server.onClientMessageHandlers.push(self.onClientMessage);
	};

	// ----------------
	// Start server's handler functions
	// ----------------
	self.onClientConnect = function (socket) {
		// Send confirm message to the user just connect
		self.server.sendMessage(socket, {
			type: SERVER_MESSAGE.USER.CONFIRMED,
			data: self.server.userList.length
		});

		/**
		 * Send User connected to all connected users
		 * Except the user just connect
		 */
		var msg = {
			type: SERVER_MESSAGE.USER.CONNECTED,
			data: self.server.userList.length
		};

		for (var i = 0, il = self.server.userList.length; i < il; i++) {
			var other_user = self.server.userList[i];
			if (other_user) {
				if (other_user.id == socket.id) continue;
				self.server.sendMessage(other_user.socket, msg);
			}
		}
	};

	self.onClientDisconnect = function (socket) {
		self.leaveRoom(socket, null);

		// Send User disconnected to all connected users
		var msg = {
			type: SERVER_MESSAGE.USER.DISCONNECTED,
			data: self.server.userList.length - 1
		};

		for (var i = 0, il = self.server.userList.length; i < il; i++) {
			var other_user = self.server.userList[i];
			if (other_user) {
				self.server.sendMessage(other_user.socket, msg);
			}
		}
	};

	self.onClientMessage = function (socket, msg) {
		if (!msg) return;

		switch (msg.type) {
			case SERVER_MESSAGE.CLIENT.REQUEST_ROOM:
				self.onClientRequestRoom(socket, msg.data);
				break;
			case SERVER_MESSAGE.CLIENT.LEAVE_ROOM:
				self.onClientLeaveRoom(socket, msg.data);
				break;
			case SERVER_MESSAGE.CLIENT.ROOM_EVENT:
				self.onClientRoomEvent(socket, msg.data);
				break;
			case SERVER_MESSAGE.CLIENT.CHECK_PASSWORD:
				self.onClientCheckPassword(socket, msg.data);
				break;
			case SERVER_MESSAGE.CLIENT.CHECK_NAME:
				self.onClientCheckName(socket, msg.data);
				break;
			case SERVER_MESSAGE.CLIENT.SVAS_GET_PLAYER_DATA:
				self.getUserData(socket, msg.data);
				break;
			case SERVER_MESSAGE.CLIENT.SVAS_REG_NEW_PLAYER_DATA:
				self.regNewPlayer(socket, msg.data);
				break;
			case SERVER_MESSAGE.CLIENT.SVAS_SAVE_GAME_DATA:
				self.saveGameData(socket, msg.data);
				break;
			case SERVER_MESSAGE.CLIENT.SVAS_SAVE_CLAIM_ACHIEVEMENT:
				self.saveClaim(socket, msg.data);
				break;
			case SERVER_MESSAGE.CLIENT.SVAS_SAVE_RV_VALUE:
				self.saveRv(socket, msg.data);
				break;
            case SERVER_MESSAGE.CLIENT.SVAS_SAVE_GEMS_VALUE:
                self.saveGems(socket, msg.data);
                break;
			case SERVER_MESSAGE.CLIENT.SVAS_DEDUCT_UPGRADE:
				self.deductUpgrade(socket, msg.data);
				break;
            case SERVER_MESSAGE.CLIENT.SVAS_DEDUCT_GEMS:
                self.deductGems(socket, msg.data);
                break;
            case SERVER_MESSAGE.CLIENT.ALLIANCE_GET_CLAIM:
                self.getGiftList(socket, msg.data);
                break;
            case SERVER_MESSAGE.CLIENT.ALLIANCE_CLAIM_GIFT:
                self.claimGift(socket, msg.data);
                break;
		}
	};


    self.callXHR=function(url,postdata,method,successCallback,errorCallback){

        var axios = require('axios');
        var response = axios({
                method: method,
                url: url,
                data: postdata,
                headers: { 
                    "Content-Type": "multipart/form-data",
                    "Authorization": apiKey
                },
            })
            .then(function (response){
                if (successCallback !== null && typeof (successCallback) === "function") {
                    successCallback(response);
                }
            })
            .catch(function (response){
                if (errorCallback !== null && typeof (errorCallback) === "function") {
                    errorCallback(response);
                }
            });     
    };
    self.getGiftList=function(socket,data){            

        var allianceId=data.allianceId;
        var receiverId=data.receiverId;

        var url="https://svas-consumer.marketjs-cloud.com/api/alliance_gifts/list?game_id="+gameId+"&alliance_id="+allianceId+"&receiver_id="+receiverId;
        self.callXHR(url,null,'get',
            function(response){                
                self.server.sendMessage(socket, {
                    type: SERVER_MESSAGE.HOST.ALLIANCE_GET_CLAIM_SUCCESS,
                    data: response.data
                });
            },
            function(response){
                // console.log(response);
            }
        );
    };


    self.claimGift=function(socket,data){            

        var FormData = require('form-data');        
        var postdata = new FormData();
        postdata.append('game_id',gameId);
        postdata.append('alliance_id',data.allianceId.toString());
        postdata.append('gift_id',data.giftId.toString());
        postdata.append('receiver_uid',data.receiverId.toString());
        var url="https://svas-consumer.marketjs-cloud.com/api/user/alliance_gifts/claim";

        self.callXHR(url,postdata,'post',
            function(response){
                self.getGiftList(socket,data);
                self.getUserData(socket,data);
            },
            function(response){
                // console.log(response);
            }
        );
    };


    self.regNewPlayer=function(socket,data){            

        var FormData = require('form-data');        
        var postdata = new FormData();
        postdata.append('game_id',gameId);
        postdata.append('uid',data.playerSvasId.toString());
        postdata.append('data','0');
        var url="https://svas-consumer.marketjs-cloud.com/api/user/data/update";

        self.callXHR(url,postdata,'post',
            function(response){
            	self.getUserData(socket,data);
            },
            function(response){
            }
        );
    };

    self.saveGameData=function(socket,data){            
        var FormData = require('form-data');        
        var postdata = new FormData();

        postdata.append('game_id',gameId);
        postdata.append('uid',data.playerSvasId.toString());
        postdata.append('data',JSON.stringify(data));
        var url="https://svas-consumer.marketjs-cloud.com/api/user/data/update";

        self.callXHR(url,postdata,'post',
            function(response){
            	self.getUserData(socket,data);
            },
            function(response){
            }
        );
    };

   self.saveClaim=function(socket,data){            

   		var coinsdata=self.chievementlist[data.achievementId].value;
        var bonusMultiplier = data.bonusMultiplier;

        var coins=coinsdata+Math.ceil(coinsdata*bonusMultiplier/100);
        
        var FormData = require('form-data');        

        var postdata = new FormData();
            postdata.append('game_id',gameId.toString());
            postdata.append('uid',data.playerSvasId.toString());
            postdata.append('virtual_currency1', coins.toString());
            postdata.append('virtual_currency2', 0);
            postdata.append('virtual_currency3', 0);
            postdata.append('virtual_currency4', 0);
            postdata.append('virtual_currency5', 0);

        var url="https://svas-consumer.marketjs-cloud.com/api/user/virtual_currency/add";

        self.callXHR(url,postdata,'post',
            function(response){
                self.saveGameData(socket,data.alldata);
				// self.server.sendMessage(socket, {
				// 	type: SERVER_MESSAGE.HOST.SVAS_CLAIM_SUCCESS,
				// 	data: data
				// });
            },
            function(response){
            }
        );
    };

   self.saveRv=function(socket,data){            

   		var coins=data.rvValue;
        var FormData = require('form-data');        

        var postdata = new FormData();
            postdata.append('game_id',gameId.toString());
            postdata.append('uid',data.playerSvasId.toString());
            postdata.append('virtual_currency1', coins.toString());
            postdata.append('virtual_currency2', 0);
            postdata.append('virtual_currency3', 0);
            postdata.append('virtual_currency4', 0);
            postdata.append('virtual_currency5', 0);

        var url="https://svas-consumer.marketjs-cloud.com/api/user/virtual_currency/add";

        self.callXHR(url,postdata,'post',
            function(response){
				self.server.sendMessage(socket, {
					type: SERVER_MESSAGE.HOST.SVAS_RV_SUCCESS,
					data: data
				});
            },
            function(response){
            }
        );
    };


   self.saveGems=function(socket,data){            

        var coins=data.gemsValue;
        var FormData = require('form-data');        

        var postdata = new FormData();
            postdata.append('game_id',gameId.toString());
            postdata.append('uid',data.playerSvasId.toString());
            postdata.append('virtual_currency1', 0);
            postdata.append('virtual_currency2', coins.toString());
            postdata.append('virtual_currency3', 0);
            postdata.append('virtual_currency4', 0);
            postdata.append('virtual_currency5', 0);

        var url="https://svas-consumer.marketjs-cloud.com/api/user/virtual_currency/add";

        self.callXHR(url,postdata,'post',
            function(response){
                self.server.sendMessage(socket, {
                    type: SERVER_MESSAGE.HOST.SVAS_GEMS_SUCCESS,
                    data: data
                });
            },
            function(response){
            }
        );
    };
    self.deductUpgrade=function(socket,data){

        var coins=data.deductValue;
        var FormData = require('form-data');        
        var player1 = new FormData();
        player1.append('game_id',gameId);
        player1.append('uid',data.playerSvasId.toString());
        player1.append('virtual_currency1', coins);
        player1.append('virtual_currency2', 0);
        player1.append('virtual_currency3', 0);
        player1.append('virtual_currency4', 0);
        player1.append('virtual_currency5', 0);

        var url="https://svas-consumer.marketjs-cloud.com/api/user/virtual_currency/deduct";
        self.callXHR(url,player1,'post',function(response){
            self.saveGameData(socket,data.alldata);

			// self.server.sendMessage(socket, {
			// 	type: SERVER_MESSAGE.HOST.SVAS_DEDUCT_SUCCESS,
			// 	data: data
			// });
        },function(response){

        });

    };

    self.deductGems=function(socket,data){

        var coins=self.shopData[data.buildingIndex].data[data.skinIndex].buyPrice;
        var FormData = require('form-data');        
        var player1 = new FormData();
        player1.append('game_id',gameId);
        player1.append('uid',data.playerSvasId.toString());
        player1.append('virtual_currency1', 0);
        player1.append('virtual_currency2', coins);
        player1.append('virtual_currency3', 0);
        player1.append('virtual_currency4', 0);
        player1.append('virtual_currency5', 0);

        var url="https://svas-consumer.marketjs-cloud.com/api/user/virtual_currency/deduct";
        self.callXHR(url,player1,'post',function(response){
            self.server.sendMessage(socket, {
                type: SERVER_MESSAGE.HOST.SVAS_DEDUCT_GEMS_SUCCESS,
                data: data
            });
        },function(response){

        });

    };


    self.getUserData=function(socket,data){            

        var FormData = require('form-data');        
        var postdata = new FormData();
        postdata.append('game_id',gameId);
        postdata.append('uid',data.playerSvasId.toString());
        var url="https://svas-consumer.marketjs-cloud.com/api/user/data/read";

        self.callXHR(url,postdata,'post',
            function(response){
                var resdata=response.data.message.data;
                // console.log(resdata);
                self.getCurrency(socket,data,resdata);
            },
            function(response){
                //new user from other game
                if(response && response.response && response.response.data && response.response.data.status && response.response.data.status==404){
                    self.regNewPlayer(socket,data);            
                }
            }
        );
    };
    
    self.getCurrency=function(socket,data,playerdata){

	        var FormData = require('form-data');        
	        var postdata = new FormData();
	        postdata.append('game_id',gameId);
	        postdata.append('uid',data.playerSvasId.toString());
            var url="https://svas-consumer.marketjs-cloud.com/api/user/virtual_currency/read";

            var pdata=JSON.parse(playerdata);

            var date = new Date();
            var day = date.getDate();
            var month = date.getMonth() + 1;
            var year = date.getFullYear();
            var currentDate = day+'-'+month+'-'+year;

			const weekday = ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];
			const monthname = ["January","February","March","April","May","June","July","August","September","October","November","December"];
            var dateUTC = date.getDate();
            var dayUTC = date.getUTCDay();
            var daynameUTC=weekday[dayUTC];
            var monthUTC = date.getMonth() + 1;
            var yearUTC = date.getFullYear();
            var serverDate={date:dateUTC,month:monthUTC,year:yearUTC,day:daynameUTC,daynumber:dayUTC,monthname:monthname[monthUTC-1],rawDate:date};

            // if(currentDate!=pdata.lastLogin){
            if(currentDate!=pdata.lastLogin){
            	pdata.loginStatus = 0;
            }else{
            	pdata.loginStatus = 1;            	
            }

            self.callXHR(url,postdata,'post',
                function(response){
                    var resdata=response.data.message;
                    var res ={playerData:pdata,playerCurrency:resdata,serverDate:serverDate};
	                // console.log(res);
					self.server.sendMessage(socket, {
						type: SERVER_MESSAGE.HOST.SVAS_PLAYER_DATA_RESULT,
						data: res
					});

                },
                function(response){
	                // console.log(response);
                }
            );    	
    };
    self.postScore=function(data,roomArena){

    	if(data.length==2){
			if(roomArena==1){
				var prize=400;
			}else if(roomArena==2){
				var prize=1000;
			}else if(roomArena==3){
				var prize=5000;
			}else if(roomArena==4){
                var prize=3000;
            }

			var svasId=data[0].playerSvasId;
			var score=prize;
			self.postWinData(svasId,score);

			var svasId2=data[1].playerSvasId;
			var score2=0;
			self.postLoseData(svasId2,0);

    	}else if(data.length==3){
            if(roomArena==1){
                var prize=400;
            }else if(roomArena==2){
                var prize=1000;
            }else if(roomArena==3){
                var prize=5000;
            }else if(roomArena==4){
                var prize=4500;
            }

            var svasId=data[0].playerSvasId;
            var score=prize;
            self.postWinData(svasId,score);

            var svasId2=data[1].playerSvasId;
            var score2=0;
            self.postLoseData(svasId2,0);

            var svasId3=data[2].playerSvasId;
            var score3=0;
            self.postLoseData(svasId3,0);

        }else{

            // console.log(data);

			if(roomArena==1){
				var prize=800;
			}else if(roomArena==2){
				var prize=2000;
			}else if(roomArena==3){
				var prize=10000;
			}else if(roomArena==4){
                var prize=6000;
            }

			var svasId1=data[0].playerSvasId;
			var score1=0.6*prize;
			self.postWinData(svasId1,score1);
            self.addPlayerExperience(svasId1,4);
            
            if(svasId1!='0' && data[0].playerAlliance!=''){
                self.createAllianceGift(svasId1,data[0].playerAlliance,(score1*0.05).toFixed(0),data[0].playerName,roomArena);
            }

			var svasId2=data[1].playerSvasId;
			var score2=0.25*prize;
			self.postLoseData(svasId2,score2);
            self.addPlayerExperience(svasId2,3);

			var svasId3=data[2].playerSvasId;
			var score3=0.1*prize;
			self.postLoseData(svasId3,score3);
            self.addPlayerExperience(svasId3,2);

			var svasId4=data[3].playerSvasId;
			var score4=0.05*prize;
			self.postLoseData(svasId4,score4);
            self.addPlayerExperience(svasId4,1);
    	}
    };

    self.addPlayerExperience = function(svasId,exp){

        if(svasId!='0'){

            var FormData = require('form-data');        
            var giftdata = new FormData();
                giftdata.append('game_id',gameId.toString());
                giftdata.append('uid',svasId.toString());
                giftdata.append('alliance_experience',exp.toString());

            var url="https://svas-consumer.marketjs-cloud.com/api/user/alliance_experience/add";
            self.callXHR(url,giftdata,'post',
                            function(response){
                                // console.log(response.data);
                            },
                            function(response){
                                // console.log(response.data);
                            }
                        );
        }
    };

    self.createAllianceGift = function(svasId,allianceId,coin,playerName,arenaId){

            if(arenaId==1){
                var message=playerName+' wins "Fresh Meat" battle. '+playerName+' gifted you '+coin+' coins for your support';
            }else if(arenaId==2){
                var message=playerName+' wins "Skilled Hands" battle. '+playerName+' gifted you '+coin+' coins for your support';
            }else if(arenaId==3){
                var message=playerName+' wins "Pro Gammer" battle. '+playerName+' gifted you '+coin+' coins for your support';
            }


            var FormData = require('form-data');        
            var giftdata = new FormData();
                giftdata.append('game_id',gameId.toString());
                giftdata.append('uid',svasId.toString());
                giftdata.append('alliance_id',allianceId.toString());
                giftdata.append('message',message.toString());
                giftdata.append('virtual_currency1', coin.toString());

            var url="https://svas-consumer.marketjs-cloud.com/api/user/alliance_gifts/create";
            self.callXHR(url,giftdata,'post',
                            function(response){
                                // console.log(response.data);
                            },
                            function(response){
                                // console.log(response.data);
                            }
                        );

    };

    self.postWinData = function(svasId,score){
		if(svasId!='0'){
	        var FormData = require('form-data');        

            var postScoreWin = new FormData();
	            postScoreWin.append('game_id',gameId.toString());
	            postScoreWin.append('uid',svasId.toString());
	            postScoreWin.append('virtual_currency1', score.toString());
	            postScoreWin.append('virtual_currency2', 0);
	            postScoreWin.append('virtual_currency3', 0);
	            postScoreWin.append('virtual_currency4', 0);
	            postScoreWin.append('virtual_currency5', 0);

            var url="https://svas-consumer.marketjs-cloud.com/api/user/virtual_currency/add";
            self.callXHR(url,postScoreWin,'post');

            var postStatsWin = new FormData();
                postStatsWin.append('game_id',gameId);
                postStatsWin.append('uid',svasId.toString());
                postStatsWin.append('stats_wins', 1);
                postStatsWin.append('stats_losses', 0);

            var url="https://svas-consumer.marketjs-cloud.com/api/user/stats/update";
            self.callXHR(url,postStatsWin,'post',
                function(response){
	                // console.log(response);
                },
                function(response){
	                // console.log(response);
                }
            );
		}

    };


    self.postLoseData = function(svasId,score){

		if(svasId!='0'){
	        var FormData = require('form-data');        

            var postScoreLose = new FormData();
	            postScoreLose.append('game_id',gameId);
	            postScoreLose.append('uid',svasId.toString());
	            postScoreLose.append('virtual_currency1', score);
	            postScoreLose.append('virtual_currency2', 0);
	            postScoreLose.append('virtual_currency3', 0);
	            postScoreLose.append('virtual_currency4', 0);
	            postScoreLose.append('virtual_currency5', 0);

            var url="https://svas-consumer.marketjs-cloud.com/api/user/virtual_currency/add";
            self.callXHR(url,postScoreLose,'post');

            var postStatsLose = new FormData();
                postStatsLose.append('game_id',gameId);
                postStatsLose.append('uid',svasId.toString());
                postStatsLose.append('stats_wins', 0);
                postStatsLose.append('stats_losses', 1);
            var url="https://svas-consumer.marketjs-cloud.com/api/user/stats/update";
            self.callXHR(url,postStatsLose,'post');
		}

    };

    self.deductArenaFee=function(players,arenaId){

    	for(var i=0;i<players.length;i++){
    		if(players[i].playerSvasId!=0){
    			if(arenaId==1){
    				var coinValue=200;
    			}else if(arenaId==2){
    				var coinValue=500;
    			}else if(arenaId==3){
    				var coinValue=2500;
    			}else if(arenaId==4){
                    var coinValue=1500;
                }
    			
    			self.deductCoins(players[i].playerSvasId,coinValue);
    		}
    	}
    };

    self.deductCoins = function(svasId,coins){

        var FormData = require('form-data');        
        var deductPlayerCoins = new FormData();
        deductPlayerCoins.append('game_id',gameId);
        deductPlayerCoins.append('uid',svasId.toString());
        deductPlayerCoins.append('virtual_currency1', coins);
        deductPlayerCoins.append('virtual_currency2', 0);
        deductPlayerCoins.append('virtual_currency3', 0);
        deductPlayerCoins.append('virtual_currency4', 0);
        deductPlayerCoins.append('virtual_currency5', 0);

        var url="https://svas-consumer.marketjs-cloud.com/api/user/virtual_currency/deduct";
        self.callXHR(url,deductPlayerCoins,'post');

    };

	// ----------------
	// End server's handler functions
	// ----------------

	// ----------------
	// Start client's message functions
	// ----------------
	self.onClientRequestRoom = function (socket, requestInfo) {
		if (!socket) return;

		// Check request
		if (!requestInfo) {
			self.joinRoomReject(socket, SERVER_MESSAGE.REQUEST_REJECT_REASON.INVALID_REQUEST);
			return false;
		}

		// Check client version
		if (!self.checkVersion(requestInfo.clientVersion)) {
			self.joinRoomReject(socket, SERVER_MESSAGE.REQUEST_REJECT_REASON.VERSION_MISMATCH);
			return false;
		}

		// Find room
		self.findRoomAndJoin(socket, requestInfo);
	};

	self.onClientLeaveRoom = function (socket, data) {
		self.leaveRoom(socket, data);
	};

	self.onClientRoomEvent = function (socket, data) {
		if (!socket) return false;
		var user = self.server.findUserById(socket.id);
		if (!user) return false;
		var room = self.findRoomById(user.roomId);
		if (!room) return false;
		var player = room.findPlayerById(user.playerId);
		if (!player) return false;

		data.clientPlayerId = player.playerId;
		room.handleClientEvent(data);
	};

	self.onClientCheckName = function (socket, requestInfo) {
		if (!socket) return;

		var returnCode = NameAndAvatar.validatePlayerName(requestInfo.playerName);

		self.server.sendMessage(socket, {
			type: SERVER_MESSAGE.HOST.CHECK_NAME_RESULT,
			data: {
				returnCode: returnCode
			}
		});
	};

	self.onClientCheckPassword = function (socket, requestInfo) {
		if (!socket) return;

		var returnCode = RoomPassword.checkPassword(requestInfo.roomPassword);

        var maxPlayer = 0;
        var fullStatus = false;
        for(var i=0;i<=self.roomList.length;i++){
            if(self.roomList[i] && self.roomList[i].roomPassword && self.roomList[i].roomPassword && requestInfo.roomPassword==self.roomList[i].roomPassword){
                var maxplayer=self.roomList[i].roomMaxPlayer;
                if(self.roomList[i].playerList.length==self.roomList[i].roomMaxPlayer){
                    fullStatus=true;
                    break;
                }
            }            
        }

        if(fullStatus){
            returnCode=411;
            maxPlayer=0;
        }

		self.server.sendMessage(socket, {
			type: SERVER_MESSAGE.HOST.CHECK_PASSWORD_RESULT,
			data: {
				returnCode: returnCode,
                maxPlayer:maxplayer
			}
		});
	};
	// ----------------
	// End client's message functions
	// ----------------

	// ----------------
	// Start room's functions
	// ----------------
	self.joinRoomSucceed = function (socket, data) {
		self.server.sendMessage(socket, {
			type: SERVER_MESSAGE.HOST.REQUEST_ROOM_SUCCEED,
			data: data
		});
	};

	self.joinRoomReject = function (socket, rejectReason) {
		self.server.sendMessage(socket, {
			type: SERVER_MESSAGE.HOST.REQUEST_ROOM_REJECTED,
			data: rejectReason
		});
	};

	self.joinRoom = function (room, socket, requestInfo) {
		// Validate input
		if (!room || !socket || !requestInfo || room.roomClose) {
			self.joinRoomReject(socket, SERVER_MESSAGE.REQUEST_REJECT_REASON.SERVER_ERROR);
			return false;
		}

		// Add player to room
		var player = room.addPlayer(socket, {
			playerName: requestInfo.playerName,
			playerAvatarId: requestInfo.playerAvatarId,
			playerSvasId: requestInfo.playerSvasId,
            playerAlliance: requestInfo.playerAlliance
		});

		if (player) {
			// update user info so we know which room and player id
			// to use by default when we receive a client message
			var user = self.server.findUserById(socket.id);
			if (user) {
				user.roomId = room.roomId;
				user.playerId = player.playerId;
			}
		} else {
			self.joinRoomReject(socket, SERVER_MESSAGE.REQUEST_REJECT_REASON.SERVER_ERROR);
			return false;
		}
	};

	self.leaveRoom = function (socket) {
		if (!socket) return null;
		var user = self.server.findUserById(socket.id);
		if (!user) return null;
		var room = self.findRoomById(user.roomId);
		if (!room) return null;
		var player = room.findPlayerById(user.playerId);
		if (!player) return null;

		room.removePlayer(player);
		if (room.checkEmptyRoom()) {
			self.shutDownRoom(room.roomId);
		}
	};

	self.findRoomAndJoin = function (socket, requestInfo) {
		var room = self.getJoinableRoom(requestInfo);
		if (room && !room.roomClose) {
			self.joinRoom(room, socket, requestInfo);
			return true;
		} else {
			self.joinRoomReject(socket, SERVER_MESSAGE.REQUEST_REJECT_REASON.CANNOT_FIND_ROOM);
			return false;
		}
	};

	self.getJoinableRoom = function (requestInfo) {
		var foundRoom = null;
		for (var i = 0, il = self.roomList.length; i < il; i++) {
			var room = self.roomList[i];
			var actualRoom = self.findRoomById(room.roomId);

			if (actualRoom.canJoin(requestInfo)) {
				foundRoom = room;
				break;
			}
		}

		// Not found room, create new one
		if (!foundRoom) {
			foundRoom = self.createRoom(requestInfo);
		}

		return foundRoom;
	};

	self.findRoomById = function (roomId) {
		if (self.roomHash) {
			if (typeof self.roomHash[roomId] != 'undefined') {
				return self.roomHash[roomId];
			}
		}

		var foundRoom = null;
		for (var i = 0, il = self.roomList.length; i < il; i++) {
			if (self.roomList[i].roomId == roomId) {
				foundRoom = self.roomList[i];
				if (self.roomHash) {
					self.roomHash[foundRoom.roomId] = foundRoom;
				}
				break;
			}
		}
		return foundRoom;
	};

	self.createRoom = function (requestInfo) {
		var room = new Room(self, ++self.lastRoomId, requestInfo);

		self.roomList.push(room);
		self.roomHash[room.roomId] = room;

		return room;
	};

	self.shutDownRoom = function (roomId) {
		var room = self.findRoomById(roomId);
		if (!room) return false;

		var foundIndex = self.roomList.indexOf(room);
		if (foundIndex >= 0) {
			self.roomList.splice(foundIndex, 1);
			delete self.roomHash[room.roomId];
		}
		room.playerList = [];
		room = null;

		// console.log('Shutdown Room: ', roomId);
		return true;
	};

	self.updateRooms = function () {
		for (var i = 0, il = self.roomList.length; i < il; i++) {
			var room = self.roomList[i];
			if (room) {
				room.update();
			}
		}
	};
	// ----------------
	// End room's functions
	// ----------------

	// ----------------
	// End utilities functions
	// ----------------
	self.checkVersion = function (targetVersion) {
		if (isNaN(targetVersion) || targetVersion === null) return false;
		if (targetVersion < self.minClientVersion) return false;

		return true;
	};
	// ----------------
	// End utilities functions
	// ----------------

	self.init(server);
	return self;
};

/**
 * Export module
 */
if (typeof module !== 'undefined') {
	module.exports.Host = Host;
}
