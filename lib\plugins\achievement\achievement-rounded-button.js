ig.module('plugins.achievement.achievement-rounded-button')
    .requires(
        'plugins.achievement.achievement-game-object'
    )
    .defines(function () {
        ig.AchievementRoundedButton = ig.AchievementSimpleButton.extend({

            // btclose: new ig.Image("media/graphics/sprites/buttons/button-kick.png"),
            zIndex: 999,
            entryType: "fadeIn",
            exitType: "fadeOut",
            radius: 10,
            color: "#555555",
            shadowColor: "#222222",
            shadowDistance: 5,
            symbol: "none",
            symbolColor: "#ffffff",
            symbolSize: 40,
            init: function (x, y, settings) {
                this.btclose = dl.preload['popup-button-close'];

                this.parent(x, y, settings);
                if(!this.control) {
                    this.control={offsetY:0};
                }
            },

            exit: function () {
                this.parent();
            },

            update: function () {
                this.parent();
            },

            draw: function () {


                this.parent();
            },

            drawObject: function (x, y) {

                var ctx = ig.system.context;
                ctx.save();

                ctx.globalAlpha = this.alpha;
                ctx.textBaseline = "top";


                if (this.shadowDistance > 0) {
                    ig.AchievementRoundedRect.drawShadowedRoundedRect(ctx, x, y+this.control.offsetY, this.width, this.height, this.radius, this.color, this.shadowColor, this.shadowDistance);
                } else {
                    ig.AchievementRoundedRect.drawRoundedRect(ctx, x, y+this.control.offsetY, this.width, this.height, this.radius, this.color);
                }
                ctx.restore();

                ctx.save();

                var cX = x + this.width / 2;
                var cY = y+this.control.offsetY + this.height / 2 - this.shadowDistance / 2;

                if (this.symbol == "exit") {
                    // ctx.lineWidth = Math.ceil(this.symbolSize / 3);
                    // ctx.lineCap = "square";
                    // ctx.strokeStyle = this.symbolColor;
                    // ctx.beginPath()
                    // ctx.moveTo(cX - (this.symbolSize / 2), cY - (this.symbolSize / 2));
                    // ctx.lineTo(cX + (this.symbolSize / 2), cY + (this.symbolSize / 2))
                    // ctx.closePath();
                    // ctx.stroke();

                    // ctx.beginPath()
                    // ctx.moveTo(cX - this.symbolSize / 2, cY + this.symbolSize / 2);
                    // ctx.lineTo(cX + this.symbolSize / 2, cY - this.symbolSize / 2)

                    // ctx.closePath();
                    // ctx.stroke();

                    this.btclose.drawImage(
                        0,
                        0,
                        this.btclose.width,
                        this.btclose.height,
                        cX-45,
                        cY-50,
                        100,
                        100
                    );

                } else if (this.symbol == "next") {
                    ctx.fillStyle = this.symbolColor;
                    ctx.beginPath();
                    ctx.moveTo(cX - (this.symbolSize / 3), cY - (this.symbolSize / 2));
                    ctx.lineTo(cX - (this.symbolSize / 3), cY + (this.symbolSize / 2))
                    ctx.lineTo(cX + (this.symbolSize / 2), cY)
                    ctx.closePath();
                    ctx.fill();
                } else if (this.symbol == "prev") {
                    ctx.fillStyle = this.symbolColor;
                    ctx.beginPath();
                    ctx.moveTo(cX + (this.symbolSize / 3), cY - (this.symbolSize / 2));
                    ctx.lineTo(cX + (this.symbolSize / 3), cY + (this.symbolSize / 2))
                    ctx.lineTo(cX - (this.symbolSize / 2), cY)
                    ctx.closePath();
                    ctx.fill();
                }else if (this.symbol == "upgrade") {
                    ctx.fillStyle='#ffffff';
                    ctx.textAlign = "center";
                    ctx.textBaseline = "middle";
                    ctx.font = "36px Arial";
                    ctx.fillText("Upgrade",cX,cY);
                }

                ctx.globalAlpha = 1;
                ctx.restore();

                this.parent(x, y)
            }
        });
    });
