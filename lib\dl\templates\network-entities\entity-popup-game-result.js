ig.module(
    'dl.templates.network-entities.entity-popup-game-result'
).requires(
    'dl.game.entity',
    'dl.network.mixins.home',
    'dl.templates.mixins.popup',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.animation-sheet',
    'dl.templates.entities.buttons.entity-button-image-text',
    'dl.templates.entities.buttons.entity-button-image',
    'dl.templates.entities.entity-text'
).defines(function () {
    'use strict';

    dl.EntityPopupGameResult = dl.Entity
        .extend(dl.MixinPopup)
        .extend(dl.MixinDocker)
        .extend({
            postInit: function () {
                this.parent();

                // ig.system.fps=dl.FPS_INTERVAL;
                this.content = this.spawnEntity(dl.EntityPopupGameResult_Content, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:1500,y:1300}
                    },
                });
            }
        });

    dl.EntityPopupGameResultSpectator = dl.Entity
        .extend(dl.MixinPopup)
        .extend(dl.MixinDocker)
        .extend({
            postInit: function () {
                this.parent();

                this.content = this.spawnEntity(dl.EntityPopupGameResultSpectator_Content, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:838,y:900}
                    },
                });
            }
        });

    dl.EntityPopupGameResult_Content = dl.Entity
        .extend(dl.MixinPopupContent)
        .extend(dl.MixinDocker)
        .extend(dl.MixinAnimationSheet)
        .extend(MixinNetworkHome)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this.image = dl.preload['popup-large-long'];
                this._useAnimationSheetAsSize = true;
                this.rankings = null;
                this.headerText = null;
                this.subtitleText = null;

                if(ig.game.client.roomMaxPlayer==2){
                    var val = [800,2000,10000,3000];
                }else if(ig.game.client.roomMaxPlayer==3){
                    var val = [800,2000,10000,4500];
                }else if(ig.game.client.roomMaxPlayer==4){
                    var val = [800,2000,10000,6000];
                }

                this.prizeSum = val[ig.game.arenaId-1];
                ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.result);
                return this;
            },

            postInit: function () {
                this.parent();
                this.showCrate();
                this.initHeaderText();
                this.initRankingAssets();
                this.initButtons();
                this.tweenIn(function () {
                    // setTimeout(function(){
                        this.startMove();
                    // }.bind(this),600);
                }.bind(this));
            },

            showCrate:function(){
                if(this.showCrateStatus) return;
                this.showCrateStatus = true;

                this.crateImg=this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.36 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:400,y:360}
                    },
                    image: dl.preload['chestopen']
                })

                this.crateCoin=this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.crateImg,
                        dockerPercent: { x: 0.9, y: 0 },
                        dockerOffset: { x: 10, y: 0 }
                    },
                    anchor: { x: 0, y: 0 },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:80,y:80}
                    },
                    image: dl.preload['coin']
                });

                this.cratePrize=this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.crateCoin,
                        dockerPercent: { x: 1, y: 0 },
                        dockerOffset: { x: 20, y: 10 }
                    },
                    anchor: { x: 0, y: 0 },
                    c_TextComponent: {
                        fontSize: 50,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    text: this.prizeSum
                });
            },

            offlineResultCoin:function(rankings){

                if(ig.game.client.roomMaxPlayer==2){

                    if(ig.game.arenaId==1){
                        var prize=400;
                    }else if(ig.game.arenaId==2){
                        var prize=1000;
                    }else if(ig.game.arenaId==3){
                        var prize=5000;
                    }else if(ig.game.arenaId==4){
                        var prize=3000;
                    }

                    var prizeValue=[]
                        prizeValue.push(prize*0.8);
                        prizeValue.push(prize*0.2);


                    for(var i=0;i<rankings.length;i++){
                        if(ig.game.client.isMyPlayerId(this.rankings[i].playerId)){
                            ig.game.svasData.coins +=prizeValue[i];
                            ig.game.balance=ig.game.svasData.coins;             
                            ig.game.sessionData.balance=ig.game.svasData.coins;             
                            ig.game.saveLocalStorage();                    
                        }
                    }

                }else if(ig.game.client.roomMaxPlayer==3){

                    if(ig.game.arenaId==1){
                        var prize=400;
                    }else if(ig.game.arenaId==2){
                        var prize=1000;
                    }else if(ig.game.arenaId==3){
                        var prize=5000;
                    }else if(ig.game.arenaId==4){
                        var prize=4500;
                    }

                    var prizeValue=[]
                        prizeValue.push(prize*0.7);
                        prizeValue.push(prize*0.2);
                        prizeValue.push(prize*0.1);


                    for(var i=0;i<rankings.length;i++){
                        if(ig.game.client.isMyPlayerId(this.rankings[i].playerId)){
                            ig.game.svasData.coins +=prizeValue[i];
                            ig.game.balance=ig.game.svasData.coins;             
                            ig.game.sessionData.balance=ig.game.svasData.coins;             
                            ig.game.saveLocalStorage();                    
                        }
                    }

                }else{

                    if(ig.game.arenaId==1){
                        var prize=800;
                    }else if(ig.game.arenaId==2){
                        var prize=2000;
                    }else if(ig.game.arenaId==3){
                        var prize=10000;
                    }else if(ig.game.arenaId==4){
                        var prize=6000;
                    }

                    var prizeValue=[]
                        prizeValue.push(0.6*prize);
                        prizeValue.push(0.25*prize);
                        prizeValue.push(0.1*prize);
                        prizeValue.push(0.05*prize);

                    for(var i=0;i<rankings.length;i++){
                        if(ig.game.client.isMyPlayerId(this.rankings[i].playerId)){
                            ig.game.svasData.coins +=prizeValue[i];
                            ig.game.balance=ig.game.svasData.coins;             
                            ig.game.sessionData.balance=ig.game.svasData.coins;             
                            ig.game.saveLocalStorage();                    
                        }
                    }
                }
            },
            initHeaderText: function () {                

                this.rankings = ig.game.managers.game.getPlayerRankings();

                if(ig.game.svasData.uid==0){
                    this.offlineResultCoin(this.rankings);
                }
                if(ig.game.client.isMyPlayerId(this.rankings[0].playerId)){

                    this.headerText = _STRINGS.NETWORK.POPUP_RESULT_TITLE_HEADER_WIN;
                    this.subtitleText = _STRINGS.NETWORK.POPUP_RESULT_TITLE_SUBTITLE_WIN;
                    ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.win);

                    if(!ig.Achievement.getAchievementData(2).achieved){
                       ig.Achievement.incrementProgress(2, 1);   
                    }else{
                        if(!ig.Achievement.getAchievementData(3).achieved){
                           ig.Achievement.incrementProgress(3, 1);   
                        }else{
                            if(!ig.Achievement.getAchievementData(4).achieved){
                               ig.Achievement.incrementProgress(4, 1);   
                            }else{
                                if(!ig.Achievement.getAchievementData(5).achieved){
                                   ig.Achievement.incrementProgress(5, 1);   
                                }else{
                                   ig.Achievement.incrementProgress(6, 1);   
                                }
                            }
                        }
                    }
                    ig.game.client.saveGameData();
                }else{
                    this.headerText = _STRINGS.NETWORK.POPUP_RESULT_TITLE_HEADER_LOSE;
                    this.subtitleText = _STRINGS.NETWORK.POPUP_RESULT_TITLE_SUBTITLE_LOSE;
                    ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.lose);
                }

                this.header = this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.08 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    anchor: { x: 0.5, y: 0.5 },
                    c_TextComponent: {
                        fillStyle: '#e43630',
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                        fontSize: 60
                    },
                    text: this.headerText
                });

                this.subtitle = this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.header,
                        dockerPercent: { x: 0.5, y: 1 },
                        dockerOffset: { x: 0, y: 25 }
                    },
                    anchor: { x: 0.5, y: 0.5 },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'GRAY'),
                        fontSize: 50
                    },
                    text: this.subtitleText
                });
            },

            initRankingAssets: function () {

                // console.log(this.rankings);

                var offsetX = 0.25;
                var startOffsetX = (-offsetX * this.rankings.length + offsetX) * 0.5;
                this.avaFrame=[];
                this.avaRank=[];
                this.avaPlayer=[];
                this.avaName=[];
                this.avaCoin=[];
                this.avaPrize=[];
                for(var i=0;i<this.rankings.length;i++){

                    this.avaPlayer.push(this.spawnEntity(dl.EntityImage, {
                        _useParentScale: this,
                        c_DockerComponent: {
                            dockerObject: this,
                            dockerPercent: { x: 0.5 + startOffsetX + offsetX * i, y: 0.72 },
                            dockerOffset: { x: 0, y: 0 }
                        },
                        c_AnimationSheetComponent: {
                            _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                            _size: { x: 200, y: 200 }
                        },
                    }));

                    this.avaPlayer[i].updateImage(dl.preload['name-avatar'].avatars[this.rankings[i].playerAvatarId]);


                    this.avaFrame.push(this.spawnEntity(dl.EntityImage, {
                        _useParentScale: true,
                        c_DockerComponent: {
                            dockerObject: this.avaPlayer[i],
                            dockerPercent: { x: 0.5, y: 0.5 },
                            dockerOffset: { x: 0, y: 0 }
                        },
                        c_AnimationSheetComponent: {
                            _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                            _size: { x: 200, y: 200 }
                        },
                        image: dl.preload['name-avatar']['avatar-frame-gray']
                    }));

                    if(i==0){
                        var rankSize={ x: 120, y: 150 };
                    }else{
                        var rankSize={ x: 100, y: 100 };                        
                    }

                    this.avaRank.push(this.spawnEntity(dl.EntityImage, {
                        _useParentScale: true,
                        c_DockerComponent: {
                            dockerObject: this.avaPlayer[i],
                            dockerPercent: { x: 0.9, y: 0.9 },
                            dockerOffset: { x: 0, y: 0 }
                        },
                        c_AnimationSheetComponent: {
                            _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                            _size: rankSize
                        },
                        anchor: { x: 0.5, y: 0.5 },
                        image: dl.preload['rank'+i]
                    }));

    
                    this.avaName.push(this.spawnEntity(dl.EntityText, {
                        _useParentScale: true,
                        c_DockerComponent: {
                            dockerObject: this.avaPlayer[i],
                            dockerPercent: { x: 0.5, y: 1 },
                            dockerOffset: { x: 0, y: 40 }
                        },
                        anchor: { x: 0.5, y: 0 },
                        c_TextComponent: {
                            fontSize: 40,
                            fontFamily: dl.configs.getConfig('FONT', 'bold').name
                        },
                        text: 'playerName'
                    }));

                    if (this.rankings[i].playerId === 0) {
                        this.avaName[i].updateTextColor(dl.configs.getConfig('TEXT_COLOR', 'PLAYER_1'));
                        this.avaFrame[i].updateImage(dl.preload['name-avatar']['avatar-frame-red']);
                    } else if (this.rankings[i].playerId === 1) {
                        this.avaName[i].updateTextColor(dl.configs.getConfig('TEXT_COLOR', 'PLAYER_2'));
                        this.avaFrame[i].updateImage(dl.preload['name-avatar']['avatar-frame-blue']);
                    } else if (this.rankings[i].playerId === 2) {
                        this.avaName[i].updateTextColor(dl.configs.getConfig('TEXT_COLOR', 'PLAYER_3'));
                        this.avaFrame[i].updateImage(dl.preload['name-avatar']['avatar-frame-green']);
                    } else if (this.rankings[i].playerId === 3) {
                        this.avaName[i].updateTextColor(dl.configs.getConfig('TEXT_COLOR', 'PLAYER_4'));
                        this.avaFrame[i].updateImage(dl.preload['name-avatar']['avatar-frame-yellow']);
                    }

                    this.avaName[i].updateText(this.rankings[i].playerName);
          


                    this.avaCoin.push(this.spawnEntity(dl.EntityImage, {
                        _useParentScale: true,
                        c_DockerComponent: {
                            dockerObject: this.avaPlayer[i],
                            dockerPercent: { x: 0, y: 0 },
                            dockerOffset: { x: 0, y: -50 }
                        },
                        anchor: { x: 0, y: 0.5 },
                        c_AnimationSheetComponent: {
                            _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                            _size: {x:80,y:80}
                        },
                        image: dl.preload['coin']
                    }));

                    this.avaPrize.push(this.spawnEntity(dl.EntityText, {
                        _useParentScale: true,
                        c_DockerComponent: {
                            dockerObject: this.avaPlayer[i],
                            dockerPercent: { x: 0.5, y: 0 },
                            dockerOffset: { x: 0, y: -50 }
                        },
                        anchor: { x: 0, y: 0.5 },
                        c_TextComponent: {
                            fontSize: 40,
                            fontFamily: dl.configs.getConfig('FONT', 'bold').name
                        },
                        value:0,
                        text:0
                    }));

                }             

            },
            startMove:function(){
                var sourcePos={x:this.crateImg.pos.x+ig.system.width/2,y:this.crateImg.pos.y+ig.system.height/2};

                for(var i=0;i<this.rankings.length;i++){
                    var targetPos=this.avaCoin[i].pos;
                    var addObj = this.avaPrize[i];                    

                    if(ig.game.client.roomMaxPlayer==2){
                        
                        if(i==0){
                            var prizeValue=0.8*this.prizeSum;
                        }else if(i==1){
                            var prizeValue=0.2*this.prizeSum;
                        }

                    }else if(ig.game.client.roomMaxPlayer==3){
                        if(i==0){
                            var prizeValue=0.7*this.prizeSum;
                        }else if(i==1){
                            var prizeValue=0.2*this.prizeSum;
                        }else{
                            var prizeValue=0.1*this.prizeSum;                            
                        }
                    }else{
                        if(i==0){
                            var prizeValue=0.6*this.prizeSum;
                        }else if(i==1){
                            var prizeValue=0.25*this.prizeSum;
                        }else if(i==2){
                            var prizeValue=0.1*this.prizeSum;
                        }else if(i==3){
                            var prizeValue=0.05*this.prizeSum;
                        }
                    }

                    this.createMoveCoin(sourcePos,targetPos,addObj,this.cratePrize,prizeValue,i);
                }
            },
            addCoin:function(obj,val){
                obj.value +=val;
                obj.updateText('+'+obj.value.toFixed(0));
            },
            deductCoin:function(obj,val){
                this.prizeSum -=val;
                if(this.prizeSum<0) this.prizeSum=0;
                obj.updateText(this.prizeSum.toFixed(0));
            },
            createMoveCoin:function(sourcePos,targetPos,addObj,deductObj,fee,idx){
                var j=1;
                
                if(ig.ua.mobile){
                    if(this.prizeSum==10000){
                        var coinLength=Math.floor(fee/400);//50                    
                    }else if(this.prizeSum==2000){
                        var coinLength=Math.floor(fee/200);//25                                        
                    }else if(this.prizeSum==800){
                        var coinLength=Math.floor(fee/40);//20                                        
                    }else if(this.prizeSum==3000){
                        var coinLength=Math.floor(fee/260);//20                                        
                    }else if(this.prizeSum==4500){
                        var coinLength=Math.floor(fee/300);//20                                        
                    }else if(this.prizeSum==6000){
                        var coinLength=Math.floor(fee/350);//20                                        
                    }

                    var diff=fee/coinLength;
                    var spawnTime = 70;
                }else{
                    if(this.prizeSum==10000){
                        var coinLength=Math.floor(fee/100);//50                    
                    }else if(this.prizeSum==2000){
                        var coinLength=Math.floor(fee/50);//25                                        
                    }else if(this.prizeSum==800){
                        var coinLength=Math.floor(fee/20);//20                                        
                    }else if(this.prizeSum==3000){
                        var coinLength=Math.floor(fee/60);//20                                        
                    }else if(this.prizeSum==4500){
                        var coinLength=Math.floor(fee/70);//20                                        
                    }else if(this.prizeSum==6000){
                        var coinLength=Math.floor(fee/80);//20                                        
                    }
                    var diff=fee/coinLength;
                    var spawnTime = 70;                    
                }

                for(i=1;i<=coinLength;i++){
                    setTimeout(function(){
                        var posx=sourcePos.x-50;
                        var posy=sourcePos.y-50;
                        ig.game.spawnEntityBackward(EntityMoveCoinResult,posx,posy,{control:this,target:targetPos,addVal:diff,addObj:addObj,deductObj:deductObj});
                        j++;
                    }.bind(this),500+i*spawnTime);    
                }

                if(idx==0){
                    setTimeout(function(){
                        this.btnHome.enableEntityUnder();
                        this.btnNextGame.enableEntityUnder();
                    }.bind(this),1200+coinLength*spawnTime);    
                }

            },

            initButtons: function () {

                this.btnHome = this.spawnEntity(dl.EntityButtonImageText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0, y: 1 },
                        dockerOffset: { x: 10, y: -10 }
                    },
                    anchor: { x: 0, y: 1 },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:400,y:90}
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                        fontSize: 35,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    image: dl.preload['button-red'],
                    text: 'Back To Home',
                    onButtonClicked: this.onButtonSelected.bind(this, 1)
                });
                this.btnHome.disableEntityUnder()


                if(ig.game.client.roomArena==4){
                    var btcaption="Rematch";
                }else{
                    var btcaption="Find Next Match";
                }

                this.btnNextGame = this.spawnEntity(dl.EntityButtonImageText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 1, y: 1 },
                        dockerOffset: { x: -10, y: -10 }
                    },
                    anchor: { x: 1, y: 1 },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:400,y:90}
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                        fontSize: 35,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    image: dl.preload['button-green'],
                    text: btcaption,
                    onButtonClicked: this.onButtonSelected.bind(this, 2)
                });

                this.btnNextGame.disableEntityUnder();
            },
            onButtonSelected: function (type) {
                ig.game.client.cancelRequestRoom();
                switch (type) {
                    case 1:
                        this.tweenOut(function () {
                            this.kill();
                            ig.game.popupStatus=false;
                            ig.game.client.roomPassword="";
                            ig.game.director.loadLevel(1);
                        }.bind(this.parentInstance));
                        break;
                    case 2:
                        ig.game.popupStatus=false;
                        if (ig.game.client.roomPassword && ig.game.client.roomPassword!='') {
                            this.enterPrivateMatch()
                        }else{
                            this.enterRandomMatch();
                        }
                        break;
                }
            },

            tweenIn: function (callback) {
                this.parent(callback);
            },

            tweenOut: function (callback) {
                this.btnNextGame.setEnable(false);
                this.btnHome.setEnable(false);
                this.parent(callback);
            },

            preUpdatePopupData: function () {
                this.rankings = ig.game.managers.game.getPlayerRankings();


                // if (this.rankings[0]) {
                //     this.rank1.preUpdateData(this.rankings[0]);
                // }

                // if (this.rankings[1]) {
                //     this.rank2.preUpdateData(this.rankings[1]);
                // }

                // if (this.rankings[2]) {
                //     this.rank3.preUpdateData(this.rankings[2]);
                // }

                // if (this.rankings[3]) {
                //     this.rank4.preUpdateData(this.rankings[3]);
                // }
            },

            updatePopupData: function () {
                this.rankings = ig.game.managers.game.getPlayerRankings();

                // if (this.rankings[0]) {
                //     this.rank1.updateData(this.rankings[0]);
                // }

                // if (this.rankings[1]) {
                //     this.rank2.updateData(this.rankings[1]);
                // }

                // if (this.rankings[2]) {
                //     this.rank3.updateData(this.rankings[2]);
                // }

                // if (this.rankings[3]) {
                //     this.rank4.updateData(this.rankings[3]);
                // }
            },

            update: function () {
                this.parent();
                if (ig.game && ig.game.managers && ig.game.managers.game) {
                    this.updatePopupData();
                }
            }
        });



    dl.EntityPopupGameResultSpectator_Content = dl.Entity
        .extend(dl.MixinPopupContent)
        .extend(dl.MixinDocker)
        .extend(dl.MixinAnimationSheet)
        .extend(MixinNetworkHome)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this.image = dl.preload['popup-large-long'];
                this._useAnimationSheetAsSize = true;
                this.rankings = null;
                this.headerText = null;
                this.subtitleText = null;
                return this;
            },

            postInit: function () {
                this.parent();

                this.initHeaderText();

                this.initButtons();
            },

            initHeaderText: function () {
                var haveWon = ig.game.managers.game.haveWon;
                if (haveWon) {
                    this.headerText = _STRINGS.NETWORK.POPUP_RESULT_TITLE_HEADER_WIN;
                    this.subtitleText = _STRINGS.NETWORK.POPUP_RESULT_TITLE_SUBTITLE_WIN;
                    ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.win);
                } else {
                    this.headerText = _STRINGS.NETWORK.POPUP_RESULT_TITLE_HEADER_LOSE;
                    this.subtitleText = _STRINGS.NETWORK.POPUP_RESULT_TITLE_SUBTITLE_LOSE;
                    ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.lose);
                }
                this.header = this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.14 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    anchor: { x: 0.5, y: 0.5 },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'GRAY'),
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                        fontSize: 60
                    },
                    text: this.headerText
                });

                this.subtitle = this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.header,
                        dockerPercent: { x: 0.5, y: 1 },
                        dockerOffset: { x: 0, y: 30 }
                    },
                    anchor: { x: 0.5, y: 0.5 },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'GRAY'),
                        fontSize: 50
                    },
                    text: this.subtitleText
                });

            },

            initButtons: function () {

                var xx=window.innerWidth;
                var yy=window.innerHeight;

                if(ig.ua.mobile && !ig.ua.iPad){
                    if(yy>xx){
                        var btsize={x:180,y:180};
                        var offset = 0.15;
                    }else{
                        var btsize={x:180,y:180};
                        var offset = 0.15;
                    }

                    if (ig.game.client.roomMaxPlayer == 4) {
                        var posY=1.1;
                    }else{                        
                        var posY=0.8;
                    }


                }else{
                    var offset = 0.14;
                    var btsize={x:140,y:140};

                    if (ig.game.client.roomMaxPlayer == 4) {
                        var posY=1;
                    }else{                        
                        var posY=0.82;
                    }
                }


                this.btnSpectator = this.spawnEntity(dl.EntityButtonImageText, {
                    c_DockerComponent: {
                        dockerObject: this.subtitle,
                        dockerPercent: {x:0.5,y:1},
                        dockerOffset: {x:0,y:60}
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:580,y:140}
                    },
                    anchor: {x:0.5,y:0},
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                        fontSize: 40,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    image: dl.preload['button-green'],
                    text: 'Watch The Game <br> As Spectator',
                    onButtonClicked: this.onButtonSelected.bind(this,2)
                });


                this.btnNextGame = this.spawnEntity(dl.EntityButtonImageText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.btnSpectator,
                        dockerPercent: {x:0.5,y:1},
                        dockerOffset: {x:0,y:60}
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:580,y:120}
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                        fontSize: 40,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    anchor: {x:0.5,y:0},
                    image: dl.preload['button-green'],
                    text: 'Find The Next Match',
                    onButtonClicked: this.onButtonSelected.bind(this,3)
                });


                this.btnHome = this.spawnEntity(dl.EntityButtonImageText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.btnNextGame,
                        dockerPercent: {x:0.5,y:1},
                        dockerOffset: {x:0,y:60}
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:580,y:120}
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                        fontSize: 40,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    anchor: {x:0.5,y:0},
                    image: dl.preload['button-red'],
                    text: 'Go Home',
                    onButtonClicked: this.onButtonSelected.bind(this,1)
                });

                // this.btnHome = this.spawnEntity(dl.EntityButtonImage, {
                //     _useParentScale: true,
                //     c_DockerComponent: {
                //         dockerObject: this,
                //         dockerPercent: { x: 0.5 - offset - offset/2, y: posY },
                //         dockerOffset: { x: 0, y: -40 }
                //     },
                //     anchor: { x: 0.5, y: 1 },
                //     c_AnimationSheetComponent: {
                //         _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                //         _size: btsize
                //     },
                //     image: dl.preload['button-home'],
                //     onButtonClicked: this.onButtonSelected.bind(this, 1)
                // });

                // this.btnSpectator = this.spawnEntity(dl.EntityButtonImage, {
                //     _useParentScale: true,
                //     c_DockerComponent: {
                //         dockerObject: this,
                //         dockerPercent: { x: 0.5 , y: posY },
                //         dockerOffset: { x: 0, y: -40 }
                //     },
                //     anchor: { x: 0.5, y: 1 },
                //     c_AnimationSheetComponent: {
                //         _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                //         _size: btsize
                //     },
                //     image: dl.preload['button-spectator'],
                //     onButtonClicked: this.onButtonSelected.bind(this,2)
                // });

                // this.btnNextGame = this.spawnEntity(dl.EntityButtonImage, {
                //     _useParentScale: true,
                //     c_DockerComponent: {
                //         dockerObject: this,
                //         dockerPercent: { x: 0.5 + offset + offset/2, y: posY },
                //         dockerOffset: { x: 0, y: -40 }
                //     },
                //     anchor: { x: 0.5, y: 1 },
                //     c_AnimationSheetComponent: {
                //         _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                //         _size: btsize
                //     },
                //     image: dl.preload['button-next'],
                //     onButtonClicked: this.onButtonSelected.bind(this, 3)
                // });

            },
            onButtonSelected:function(type){

                switch (type) {
                    case 1:
                            ig.game.client.cancelRequestRoom();
                            this.tweenOut(function () {
                                ig.game.popupStatus=false;
                                this.kill();
                                ig.game.director.loadLevel(1);
                                // ig.game.director.jumpTo(LevelTitle);
                            }.bind(this.parentInstance));
                        break;
                    case 2:
                            this.tweenOut(function () {
                                ig.game.managers.game.showSpectatorText();
                                ig.game.popupStatus=false;
                                this.kill();
                            }.bind(this.parentInstance));
                        break;
                    case 3:
                            ig.game.popupStatus=false;
                            ig.game.client.cancelRequestRoom();
                            this.enterRandomMatch();
                        break;
                }

            },

            tweenIn: function (callback) {
                this.parent(callback);
            },

            tweenOut: function (callback) {
                this.btnNextGame.setEnable(false);
                this.btnHome.setEnable(false);

                this.parent(callback);
            },

            preUpdatePopupData: function () {
                this.rankings = ig.game.managers.game.getPlayerRankings();

                if (this.rankings[0]) {
                    this.rank1.preUpdateData(this.rankings[0]);
                }

                if (this.rankings[1]) {
                    this.rank2.preUpdateData(this.rankings[1]);
                }

                if (this.rankings[2]) {
                    this.rank3.preUpdateData(this.rankings[2]);
                }

                if (this.rankings[3]) {
                    this.rank4.preUpdateData(this.rankings[3]);
                }
            },

            updatePopupData: function () {

                this.rankings = ig.game.managers.game.getPlayerRankings();

                if (this.rankings[0]) {
                    this.rank1.updateData(this.rankings[0]);
                }

                if (this.rankings[1]) {
                    this.rank2.updateData(this.rankings[1]);
                }

                if (this.rankings[2]) {
                    this.rank3.updateData(this.rankings[2]);
                }

                if (this.rankings[3]) {
                    this.rank4.updateData(this.rankings[3]);
                }

            },

            update: function () {
                this.parent();
                // if (ig.game && ig.game.managers && ig.game.managers.game) {
                //     this.updatePopupData();
                // }
            }
        });



    dl.EntityPlayerRanking = dl.Entity
        .extend(dl.MixinDocker)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this._size = { x: 652, y: 118 };
                this.updateFrequencyTime = 2;
                this.updateFrequencyTimer = new ig.Timer(this.updateFrequencyTime);
                // small size
                // this._size = { x: 554, y: 100 };

                // image to be declared in parent
                this.playerData = null;
                return this;
            },

            postInit: function () {
                this.parent();

                this.initEntities();
            },

            initEntities: function () {
                this.rankImage = this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    image: dl.preload.ranking['player' + this.playerNum]
                });

                this.playerAvatar = this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.29, y: 0.525 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: { x: 72, y: 72 }
                    },
                    image: dl.preload['name-avatar']['avatar-empty']
                });

                this.playerName = this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.4, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                        fontSize: 50,
                        maxWidth: this.size.x * 0.7
                    },
                    anchor: { x: 0, y: 0.5 },
                    text: 'playerName'
                });

                this.playerRank = this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.087, y: 0.52 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'PLAYER_1'),
                        fontFamily: dl.configs.getConfig('FONT', 'SOURCE_SANS').name,
                        textAlign: 'center',
                        fontSize: 80,
                        maxWidth: 80
                    },
                    anchor: { x: 0.5, y: 0.5 },
                    text: '1'
                });
            },
            updateDataCount2:0,
            preUpdateData: function (data) {
                this.updateDataCount2 +=1;
                if(this.updateDataCount2 % 4!==0) return
                if(this.updateDataCount2>20) this.updateDataCount2=0;

                var playerNumber = ig.game.managers.game.getPlayerNumber(data.playerId);
                if (playerNumber) {
                    var rank = data.playerRank + 1;
                    this.rankImage.updateImage(dl.preload.ranking['player' + playerNumber]);
                    this.playerAvatar.updateImage(dl.preload['name-avatar'].avatars[data.playerAvatarId]);
                    this.playerName.updateText(data.playerName);
                    this.playerRank.updateTextColor(dl.configs.getConfig('TEXT_COLOR', 'PLAYER_' + playerNumber));
                    this.playerRank.updateText(rank);
                    this.playerRank.cacheRequestRedraw();
                }
            },

            updateDataCount:0,
            updateData: function (data) {
                this.updateDataCount +=1;
                if(this.updateDataCount % 4!==0) return
                if(this.updateDataCount>20) this.updateDataCount=0;

                if (this.updateFrequencyTimer.delta() > 0) {
                    this.updateFrequencyTimer.reset();
                    var playerNumber = ig.game.managers.game.getPlayerNumber(data.playerId);
                    if (playerNumber) {
                        var rank = data.playerRank + 1;
                        this.rankImage.updateImage(dl.preload.ranking['player' + playerNumber]);
                        this.playerAvatar.updateImage(dl.preload['name-avatar'].avatars[data.playerAvatarId]);
                        this.playerName.updateText(data.playerName);
                        this.playerRank.updateTextColor(dl.configs.getConfig('TEXT_COLOR', 'PLAYER_' + playerNumber));
                        this.playerRank.updateText(rank);
                        this.playerRank.cacheRequestRedraw();
                    }
                }
            }
        });
});
