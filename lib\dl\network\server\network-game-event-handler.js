/**
 * Created by <PERSON><PERSON>
 * Handle NetworkGame events on server side
 */
if (typeof require !== 'undefined') {
    var NetworkGameEvent = require('../shared/network-game-event.js').NetworkGameEvent;

    var NETWORK_GAME_CONFIGS = require('../shared/network-game-configs.js').NETWORK_GAME_CONFIGS;

    var NetworkGameUtilities = require('../shared/network-game-utilities.js').NetworkGameUtilities;
    var Utilities = require('../shared/utilities.js').Utilities;

    var randomFile = require('select-random-file');
    var fs = require('fs');


}

var NetworkGameEventHandler = {};

NetworkGameEventHandler.handleEvent = function (networkGame, event, clientPlayerId) {
    switch (event.type) {
        // ----------------
        // Start Instruction's functions
        // ----------------
        case NetworkGameEvent.EVENT_TYPE.START_INSTRUCTION:
            {
                if(event.networkGameData){ 
                    return NetworkGameEventHandler.onStartInstruction(networkGame, event);
                }
            } break;
        case NetworkGameEvent.EVENT_TYPE.END_INSTRUCTION:
            {
                if(event.networkGameData){ 
                    return NetworkGameEventHandler.onEndInstruction(networkGame, event);
                }
            } break;
        // ----------------
        // End Instruction's functions
        // ----------------

        // ----------------
        // Start count down functions
        // ----------------
        case NetworkGameEvent.EVENT_TYPE.START_COUNT_DOWN:
            {
                if(event.networkGameData){ 
                    return NetworkGameEventHandler.onStartCountDown(networkGame, event);
                }
            } break;
        case NetworkGameEvent.EVENT_TYPE.END_COUNT_DOWN:
            {
                if(event.networkGameData){ 
                    return NetworkGameEventHandler.onEndCountDown(networkGame, event);
                }
            } break;
        // ----------------
        // End count down functions
        // ----------------

        // ----------------
        // Start Game's functions
        // ----------------
        case NetworkGameEvent.EVENT_TYPE.START_GAME:
            {
                if(event.networkGameData){ 
                    return NetworkGameEventHandler.onStartGame(networkGame, event);
                }
            } break;
        case NetworkGameEvent.EVENT_TYPE.GAME_UPDATE:
            {   
                if(event.networkGameData){ 
                    return NetworkGameEventHandler.onGameUpdate(networkGame, event);
                }
            } break;
        case NetworkGameEvent.EVENT_TYPE.END_GAME:
            {
                if(event.networkGameData){ 
                    return NetworkGameEventHandler.onEndGame(networkGame, event);
                }
            } break;
        // ----------------
        // End Game's functions
        // ----------------

        // ----------------
        // Start custom functions
        // ----------------
        // case NetworkGameEvent.EVENT_TYPE.CAPITAL_ATTACK:{
        //     if(event.info && event.info.attackData && event.info.attackData.playerId!=null && clientPlayerId!=null){ //&& event.info.attackData.playerId==clientPlayerId
        //         return NetworkGameEventHandler.onCapitalAttack(networkGame, event, clientPlayerId);
        //     }
        // }break;

        case NetworkGameEvent.EVENT_TYPE.CAPITAL_ATTACK:{
            if(event.info && 
               event.info.attackData && 
               event.info.attackData.playerId!=null && 
               clientPlayerId!=null && 
               typeof(event.info.attackData.key)!=="undefined"
               ){
                    var capitalList = Array.from(networkGame.networkGameData.mapData.capitalData);
                    var maxSoldier=0;
                    var idplayer=null;
                    for(var i=0;i<capitalList.length;i++){
                        if(capitalList[i].capitalIndex == event.info.attackData.capitalIndex){
                            maxSoldier=capitalList[i].soldierCount;
                            if(capitalList[i].assignedPlayer && capitalList[i].assignedPlayer.playerId){ 
                                idplayer = capitalList[i].assignedPlayer.playerId;
                            }
                        }
                    }

                    // idplayer==event.info.attackData.playerId &&
                    if((Number(Utilities.decrypt('1!2@3#4$5%6^7&8*',event.info.attackData.key.toString())) % networkGame.room.roomKey)===0 &&
                        event.info.attackData.soldierCount <= (maxSoldier+1) &&
                        event.info.attackData.capitalIndex !== event.info.attackData.currentTargetCapitalIndex
                    ){
                        return NetworkGameEventHandler.onCapitalAttack(networkGame, event, clientPlayerId);
                    }
            }
        }break;


        case NetworkGameEvent.EVENT_TYPE.PLAYER_LEAVE:{
            return NetworkGameEventHandler.onPlayerLeave(networkGame, event);
        }break;
        case NetworkGameEvent.EVENT_TYPE.PLAYER_ELIMINATED:{
                return NetworkGameEventHandler.onPlayerEliminated(networkGame, event);
        }break;
        case NetworkGameEvent.EVENT_TYPE.INCREMENT_SOLDIER:{
            // return NetworkGameEventHandler.onSoldierIncrement(networkGame, event);
        }break;
        case NetworkGameEvent.EVENT_TYPE.DECREMENT_SOLDIER:{
            return NetworkGameEventHandler.onSoldierDecrement(networkGame, event);
        }break;
        case NetworkGameEvent.EVENT_TYPE.CHANGE_OWNER:{
            return NetworkGameEventHandler.onChangeOwner(networkGame, event);
        }break;
        case NetworkGameEvent.EVENT_TYPE.SYNC_SOLDIER_COUNT:{
            return NetworkGameEventHandler.onSyncPlayerCount(networkGame, event);
        }break;

        // ----------------
        // End custom functions
        // ----------------
    };

    return false;
};

// ----------------
// Start Lifecycle functions
// ----------------
NetworkGameEventHandler.init = function (networkGame) {
    networkGame.networkGameData.networkGameState = NETWORK_GAME_CONFIGS.NETWORK_GAME_STATE.INITIALIZING;
};

NetworkGameEventHandler.start = function (networkGame) {
    var dir = HOME_PATH+'/lib/dl/network/shared/maps';
    // choose a random filename from the maps directory
    // actual importing of the file is inside onNetworkGameStart
    randomFile(dir, function (_err, file) {
        // console.log(`The chosen map is: ${file}`); 
        if(file!=='.DS_Store'){
            NetworkGameEventHandler.onNetworkGameStart(networkGame, dir, file);
            this.handleEvent(networkGame, networkGame.createEvent(NetworkGameEvent.EVENT_TYPE.START_INSTRUCTION));
        }else{
            NetworkGameEventHandler.start(networkGame);
        }

    }.bind(this));
};

NetworkGameEventHandler.update = function (networkGame) {

    if (networkGame.networkGameData.networkGameState == NETWORK_GAME_CONFIGS.NETWORK_GAME_STATE.GAME) {
        // clean timers
        if (networkGame.networkGameData.soldierTimers && networkGame.networkGameData.soldierTimers.length > 0) {
            networkGame.networkGameData.soldierTimers = networkGame.networkGameData.soldierTimers.filter(function (obj) {
                return !obj.timer.timeUp;
            });
        }
        this.handleEvent(networkGame, networkGame.createEvent(NetworkGameEvent.EVENT_TYPE.GAME_UPDATE));
    }

};

NetworkGameEventHandler.end = function (_networkGame) {

};

// ----------------
// End Lifecycle functions
// ----------------

// ----------------
// Start Instruction's functions
// ----------------

NetworkGameEventHandler.onStartInstruction = function (networkGame, event) {
    event.info.startTime = Date.now();
    event.info.duration  = NETWORK_GAME_CONFIGS.INSTRUCTION_TIME;

    networkGame.networkGameData.networkGameState = NETWORK_GAME_CONFIGS.NETWORK_GAME_STATE.INSTRUCTION;
    networkGame.instructionTimer = networkGame.startTimer(event.info.duration, function (networkGame) {
        networkGame.instructionTimer = null;
        this.handleEvent(networkGame, networkGame.createEvent(NetworkGameEvent.EVENT_TYPE.END_INSTRUCTION));
    }.bind(this, networkGame));

    networkGame.sendEventToClient(event);
    networkGame.instructionTimer.start();
};

NetworkGameEventHandler.onEndInstruction = function (networkGame, event) {
    var transitionTimer = networkGame.startTimer(NETWORK_GAME_CONFIGS.LEVEL_TRANSITION_TIME, function (networkGame) {
        // Process to next event
        this.handleEvent(networkGame, networkGame.createEvent(NetworkGameEvent.EVENT_TYPE.START_COUNT_DOWN));
    }.bind(this, networkGame));

    networkGame.sendEventToClient(event);
    transitionTimer.start();
};
// ----------------
// End Instruction's functions
// ----------------

// ----------------
// Start count down functions
// ----------------
NetworkGameEventHandler.onStartCountDown = function (networkGame, event) {
    event.info.startTime = Date.now();
    event.info.duration = NETWORK_GAME_CONFIGS.COUNT_DOWN_TIME;

    networkGame.networkGameData.networkGameState = NETWORK_GAME_CONFIGS.NETWORK_GAME_STATE.COUNT_DOWN;
    networkGame.countDownTimer = networkGame.startTimer(NETWORK_GAME_CONFIGS.COUNT_DOWN_TIME, function (networkGame) {
        networkGame.countDownTimer = null;
        // Process to next event
        this.handleEvent(networkGame, networkGame.createEvent(NetworkGameEvent.EVENT_TYPE.END_COUNT_DOWN));
    }.bind(this, networkGame));

    networkGame.sendEventToClient(event);
    networkGame.countDownTimer.start();
};

NetworkGameEventHandler.onEndCountDown = function (networkGame, event) {
    networkGame.sendEventToClient(event);

    // Process to next event
    this.handleEvent(networkGame, networkGame.createEvent(NetworkGameEvent.EVENT_TYPE.START_GAME));
};
// ----------------
// End count down functions
// ----------------

// ----------------
// Start Game's functions
// ----------------
NetworkGameEventHandler.onStartGame = function (networkGame, event) {


    if (networkGame.networkGameData) {
        var players = networkGame.networkGameData.playerList;
        if(serverHost){
            serverHost.deductArenaFee(players,networkGame.room.roomArena);
        }
    }

    var timeNow = Date.now();
    event.info.startTime = timeNow;
    event.info.duration = NETWORK_GAME_CONFIGS.GAME_TIME;

    networkGame.networkGameData.serverStartTime = timeNow;
    networkGame.networkGameData.networkGameState = NETWORK_GAME_CONFIGS.NETWORK_GAME_STATE.GAME;

    var capitalList = Array.from(networkGame.networkGameData.mapData.capitalData);

    for (var i = 0; i < capitalList.length; i++) {
        if (capitalList[i].assignedPlayer) {
            // start timer for soldier increasing
            capitalList[i].timerPaused = false;
        }
    }
    networkGame.networkGameData.mapData.capitalData = [];
    networkGame.networkGameData.mapData.capitalData = capitalList;

    networkGame.incrementTimer = networkGame.startTimer(NETWORK_GAME_CONFIGS.SOLDIER_INCREMENT_TIME, function (networkGame) {
        networkGame.incrementTimer = null;
        // Process to next event
        this.handleEvent(networkGame, networkGame.createEvent(NetworkGameEvent.EVENT_TYPE.INCREMENT_SOLDIER));
    }.bind(this, networkGame)).start();

    networkGame.sendEventToClient(event);
};

var delayLoop = true;
NetworkGameEventHandler.onGameUpdate = function (networkGame, event) {

    if(!delayLoop) return;

    networkGame.networkGameData.soldierList = networkGame.networkGameData.soldierList.filter(function (soldier) {
        return !soldier.isKilled;
    });

    networkGame.sendEventToClient(event);

    delayLoop=false;
    setTimeout(function(){
        delayLoop = true;
    },1000);
};

NetworkGameEventHandler.onEndGame = function (networkGame, event) {
    // networkGame.networkGameData.networkGameState = NETWORK_GAME_CONFIGS.NETWORK_GAME_STATE.ENDED;
    // NetworkGameUtilities.reCalculatePlayerRank(networkGame);
    // networkGame.sendEventToClient(event);

    // console.log('end game');
    networkGame.room.endRoom();
};
// ----------------
// End Game's functions
// ----------------

// ----------------
// Start custom functions
// ----------------
NetworkGameEventHandler.onNetworkGameStart = function (networkGame, dir, fileName) {
    networkGame.networkGameData.mapData = [];
    networkGame.networkGameData.soldierTimers = [];
    networkGame.networkGameData.soldierList = [];

    // var mapData = MAPS[Utilities.randomInt(0, MAPS.length - 1)]; -> OLD METHOD, SLOW
    // Import the file based on the fileName
    // console.log(fileName);
    var mapData = JSON.parse(fs.readFileSync(dir + '/' + fileName).toString());
    // var continentDetails = mapData.layers.find(function (obj) {
    //     return obj.name.toLowerCase() == 'continent';
    // });

    var mapName = mapData.name
    var mapDimension = {
        width: mapData.tilewidth,
        height: mapData.tileheight
    };

    // filter nations from the imported map
    var nations = mapData.layers.find(function (obj) {
        return obj.name.toLowerCase() == 'nations';
    });
    var nationCount = nations.objects.length;
    // generate nation list with it's properties
    var nationList = [];
    for (var i = 0; i < nationCount; i++) {
        nationList.push({
            initialPoint: { x: nations.objects[i].x, y: nations.objects[i].y },
            nationType: nations.objects[i].type,
            nationName: nations.objects[i].name,
            polygon: nations.objects[i].polygon,
            mapDimension: mapDimension,
            nationIndex: nations.objects[i].properties[0].value
        });
    }
    // filter capitals from the imported map
    var capitals = mapData.layers.find(function (obj) {
        return obj.name.toLowerCase() == 'capitals';
    });

    var capitalCount = capitals.objects.length;
    // generate capital list with it's properties
    var capitalList = [];
    for (var i = 0; i < capitalCount; i++) {
        capitalList.push({
            initialPoint: { x: capitals.objects[i].x, y: capitals.objects[i].y },
            capitalType: capitals.objects[i].type,
            capitalName: capitals.objects[i].name,
            mapDimension: mapDimension,
            capitalIndex: capitals.objects[i].properties[0].value,
            soldierCount: 0,
            lastAction: 0,
            countLastUpdate: 0,
            timerPaused: true,
            aabb: {
                x: capitals.objects[i].x - NETWORK_GAME_CONFIGS.CAPITAL_SIZE * 0.5,
                y: capitals.objects[i].y - NETWORK_GAME_CONFIGS.CAPITAL_SIZE * 0.5,
                w: NETWORK_GAME_CONFIGS.CAPITAL_SIZE,
                h: NETWORK_GAME_CONFIGS.CAPITAL_SIZE
            }
        });
    }

    // link the nation and capitals
    for (var i = 0; i < nationList.length; i++) {
        for (var j = 0; j < capitalList.length; j++) {
            if (nationList[i].nationIndex == capitalList[j].capitalIndex) {
                capitalList[j].nation = nationList[i];
            }
        }
    }

    // distribute players
    var players = Array.from(networkGame.networkGameData.playerList);
    var nationCount = nationList.length;
    var playerNations = [];
    var playerCapitals = [];
    var neutralCapitals = [];

    neutralCapitals = capitalList.filter(function (obj) {
        return obj.capitalType == 'NCN';
    });

    playerNations = nationList.filter(function (obj) {
        return obj.nationType == 'PN';
    });

    playerCapitals = capitalList.filter(function (obj) {
        return obj.capitalType == 'PCN';
    });

    var randomIndex = 0;
    for (var i = 0; i < players.length; i++) {
        randomIndex = Math.floor(Math.random() * playerNations.length);

        while (playerNations[randomIndex].occupied) {
            randomIndex = Math.floor(Math.random() * playerNations.length);
        }

        for (var nl = 0; nl < nationList.length; nl++) {
            if (nationList[nl].nationIndex == playerNations[randomIndex].nationIndex) {
                nationList[nl].assignedPlayer = players[i];
                nationList[nl].occupied = true;
            }
        }

        for (var cl = 0; cl < capitalList.length; cl++) {
            if (!capitalList[cl].occupied && capitalList[cl].capitalIndex == playerNations[randomIndex].nationIndex) {
                capitalList[cl].assignedPlayer = players[i];
                capitalList[cl].occupied = true;
                players[i].occupiedCapitals.push(capitalList[cl].capitalIndex);
            }
        }
    }

    // init soldiers
    var playerSoldierInitialCount = NETWORK_GAME_CONFIGS.PLAYER_SOLDIER_INITIAL_COUNT;
    var neutralSoldierRange = NETWORK_GAME_CONFIGS.NEUTRAL_SOLDIER_RANGE;
    var buildingSoldierCount = NETWORK_GAME_CONFIGS.BUILDING_SOLDIER_INITIAL_COUNT;
    // var randomBuilding = NETWORK_GAME_CONFIGS.BUILDINGS[Utilities.randomInt(0, NETWORK_GAME_CONFIGS.BUILDINGS.length - 1)];
    // var randomBuilding = NETWORK_GAME_CONFIGS.BUILDINGS[2];
    var count = 0;
    for (var i = 0; i < playerCapitals.length; i++) {
        if (playerCapitals[i].occupied && playerCapitals[i].soldierCount == 0) {
            count = playerSoldierInitialCount;
        } else if (!playerCapitals[i].occupied && playerCapitals[i].soldierCount == 0) {
            count = Utilities.randomInt(neutralSoldierRange[0], neutralSoldierRange[1]);
        }
        playerCapitals[i].soldierCount = count;
    }


    var randomCount = 0;
    var randData=[0,0,0,0,0,0,0,0,0,0,1,1,1,1,1];
    var idx = Utilities.randomInt(0, randData.length - 1);

    var randomIndex = Utilities.randomInt(0, neutralCapitals.length - 1);
    for (var i = 0; i < neutralCapitals.length; i++) {

        // if (i==randomIndex) {
        if (i==randomIndex && randData[idx]==0) {
            neutralCapitals[i].soldierCount = buildingSoldierCount;
            neutralCapitals[i].assignedPlayer = null;
            neutralCapitals[i].isBuilding = true;
            // neutralCapitals[i].buildingData = NETWORK_GAME_CONFIGS.BUILDINGS[1];
            neutralCapitals[i].buildingData = NETWORK_GAME_CONFIGS.BUILDINGS[Utilities.randomInt(0, NETWORK_GAME_CONFIGS.BUILDINGS.length - 1)];
        } else if (neutralCapitals[i].soldierCount == 0) {
            randomCount = Utilities.randomInt(neutralSoldierRange[0], neutralSoldierRange[1]);
            neutralCapitals[i].soldierCount = randomCount;
            neutralCapitals[i].assignedPlayer = null;
        }
    }

    // reset player scores
    for (var i = 0; i < players.length; i++) {
        players[i].playerScore = 100;
        players[i].playerScoreDec = 40;
        players[i].eliminated = false;
    }

    // for (var i = 0; i < players.length; i++) {
    //     for (var j = 0; j < capitalList.length; j++) {
    //         if (capitalList[j].assignedPlayer && players[i].playerId == capitalList[j].assignedPlayer.playerId) {
    //             players[i].playerScore++;
    //         }
    //     }
    // }

    var mapData = {
        nationData: nationList,
        capitalData: capitalList,
        // continentDetails: continentDetails,
        mapDimension: mapDimension,
        mapName:mapName
    };
    networkGame.networkGameData.playerList = [];
    networkGame.networkGameData.playerList = players;
    networkGame.networkGameData.mapData = mapData;
};

NetworkGameEventHandler.updatePlayer = function (_networkGame, _player, _serverTime) {
    // player.serverUpdateTime = serverTime;
};

NetworkGameEventHandler.onCapitalAttack = function (networkGame, event) {

    var soldierCountSpawn=event.info.attackData.soldierCount;

    var sourceCapital = null;
    var targetCapital = null;
    var playerId = event.info.attackData.playerId;
    var player = networkGame.networkGameData.findPlayerById(playerId);
    var capitalList = networkGame.networkGameData.mapData.capitalData;
    for (var i = 0; i < capitalList.length; i++) {
        // pause capital soldier increase timer during attack
        if (capitalList[i].capitalIndex == event.info.attackData.capitalIndex) {
            sourceCapital = capitalList[i];
            sourceCapital.timerPaused = true;
        }

        if (capitalList[i].capitalIndex == event.info.attackData.currentTargetCapitalIndex) {
            targetCapital = capitalList[i];
        }
    }
    NetworkGameEventHandler.onClearSpawnTimers(networkGame, {
        sourceCapitalIndex: event.info.attackData.capitalIndex
    });
    // get angle of source capital to target capital
    var angleToTarget = Utilities.calcAngle(
        sourceCapital.initialPoint.x, sourceCapital.initialPoint.y,
        targetCapital.initialPoint.x, targetCapital.initialPoint.y
    );

    var angleToTargetRadians = Utilities.degToRad(angleToTarget);
    // get the positions where the soldier will spawn before marching
    var angleLists = Utilities.generateOffsetListBasedOnAngle(NETWORK_GAME_CONFIGS.ANGLE_INTERVAL, NETWORK_GAME_CONFIGS.DISTANCE_FROM_SOURCE, angleToTarget,5);
    // calculate the distance between two capitals
    var distance = Utilities.distanceBetweenTwoPoints(sourceCapital.initialPoint, targetCapital.initialPoint);
    // estimate the travel time needed to reach the target capital
    var travelTime = (distance / NETWORK_GAME_CONFIGS.SOLDIER_MOVEMENT_SPEED) * 1000;
    var soldierCount = soldierCountSpawn;
    var maxSoldier = soldierCountSpawn;
    var spawnLimit = 5;
    var batchIndex = 0;
    var delay = 0;
    var currentIndex=0;


    NetworkGameEventHandler.spawnSoldier(networkGame, player, {
        sourceCapital: sourceCapital,
        targetCapital: targetCapital,
        soldierCount: soldierCount,
    });


    return true;
};

NetworkGameEventHandler.onPlayerLeave = function (networkGame, event) {
    if (networkGame.networkGameData &&
        networkGame.networkGameData.mapData &&
        networkGame.networkGameData.mapData.capitalData) {
            var capitalList = Array.from(networkGame.networkGameData.mapData.capitalData);

            for (var i = 0; i < capitalList.length; i++) {
                if (capitalList[i].assignedPlayer && capitalList[i].assignedPlayer.playerId == event.info.playerId) {
                    capitalList[i].assignedPlayer = null;
                }
            }

            networkGame.networkGameData.mapData.capitalData = [];
            networkGame.networkGameData.mapData.capitalData = capitalList;
        }
    return true;
};

NetworkGameEventHandler.showRankingStatus=true;
NetworkGameEventHandler.onPlayerEliminated = function (networkGame, event) {
    if (networkGame.networkGameData) {
        var players = networkGame.networkGameData.playerList;
        var playerRankings=[];
        var eliminatedCount=0;

        for (var i = 0; i < players.length; i++) {

            if (players[i].playerId == event.info.playerId && !players[i].eliminated) {
                players[i].eliminated = true;
                players[i].playerScore -= players[i].playerScoreDec;

                for (var j = 0; j < players.length; j++) {
                    players[j].playerScoreDec -=10;
                }

            }

            if(players[i].eliminated==true){
                eliminatedCount +=1;                
            }

            var dat={playerId:players[i].playerId,playerSvasId:players[i].playerSvasId,playerScore:players[i].playerScore,playerAlliance:players[i].playerAlliance,playerName:players[i].playerName};
            playerRankings.push(dat);

        }


        if(NetworkGameEventHandler.showRankingStatus && ((eliminatedCount>=3 && players.length==4) || (eliminatedCount>=1 && players.length==2)) ){

            NetworkGameEventHandler.showRankingStatus=false;
            setTimeout(function(){
                NetworkGameEventHandler.showRankingStatus=true;
            }.bind(NetworkGameEventHandler),2000);

            var finalRanking = playerRankings.sort(function (a, b) {
                return b.playerScore - a.playerScore;
            });

            if(serverHost){
                serverHost.postScore(finalRanking,networkGame.room.roomArena);
            }
        }
    }

    return true;
};


NetworkGameEventHandler.onSoldierDecrement = function (networkGame, event) {
    // console.log(event);
    if (networkGame.networkGameData) {
        var tg=new Date()
        var capitalList = Array.from(networkGame.networkGameData.mapData.capitalData);
        for (var i = 0; i < capitalList.length; i++) {
            if (capitalList[i].capitalIndex == event.info.capitalIndex) {
                
                if(isNaN(capitalList[i].soldierCount) || capitalList[i].soldierCount<0) capitalList[i].soldierCount=0;
                if(isNaN(event.info.soldierCount) || event.info.soldierCount<0) event.info.soldierCount=0;

                if (!event.info.isAttack) {

                    // console.log('decrease not attack '+tg.getHours()+':'+tg.getMinutes()+':'+tg.getSeconds());
                    capitalList[i].soldierCount -= Math.round(event.info.soldierCount);
                    if (capitalList[i].soldierCount < 0) {
                        capitalList[i].soldierCount = 0;
                    }
                } else {
                    if ((capitalList[i].assignedPlayer && capitalList[i].assignedPlayer.playerId != event.info.attackerPlayerId) || !capitalList[i].assignedPlayer) {
                        // console.log('Enemy nation, decreasing soldier');

                        // console.log('decrease attack '+tg.getHours()+':'+tg.getMinutes()+':'+tg.getSeconds());
                        capitalList[i].soldierCount -= event.info.soldierCount;
                        if (capitalList[i].soldierCount <= 0) {
                            // console.log('Capturing capital');
                            capitalList[i].soldierCount = 0;
                            this.handleEvent(networkGame, networkGame.createEvent(NetworkGameEvent.EVENT_TYPE.CHANGE_OWNER, {
                                capitalIndex: capitalList[i].capitalIndex,
                                sourceCapitalIndex: event.info.sourceCapitalIndex,
                                playerId: event.info.attackerPlayerId
                            }));
                        }
                    } else {
                        // console.log('Ally nation, increasing soldier');
                        capitalList[i].soldierCount += event.info.soldierCount;
                        if (capitalList[i].soldierCount > 100) {
                            capitalList[i].soldierCount = 100;
                        }
                    }
                }
                capitalList[i].countLastUpdate = Date.now();
            }
        }

        networkGame.networkGameData.mapData.capitalData = [];
        networkGame.networkGameData.mapData.capitalData = capitalList;

        // this.handleEvent(networkGame, networkGame.createEvent(NetworkGameEvent.EVENT_TYPE.GAME_UPDATE));
    }
    return true;
};


NetworkGameEventHandler.changeOwnserStatus = true;
NetworkGameEventHandler.onSyncPlayerCount = function (networkGame, event) {

    if(event.info && networkGame.networkGameData && networkGame.networkGameData.mapData && networkGame.networkGameData.mapData.capitalData){
        try{
            var capitalList = Array.from(networkGame.networkGameData.mapData.capitalData);
            for (var i = 0; i < capitalList.length; i++) {
                if (capitalList[i].capitalIndex == event.info.capitalIndex) {
                    capitalList[i].soldierCount = event.info.soldierCount;
                    capitalList[i].countLastUpdate = Date.now();

                    if(event.info.screenId==0){
                        var dataupdate={capitalIndex:capitalList[i].capitalIndex,soldierCount:Math.floor(capitalList[i].soldierCount),screenId:event.info.screenId};    
                        networkGame.sendSyncSoldierData(dataupdate);
                    }

                    if(event.info.soldierCount<=0){
                        if ((capitalList[i].assignedPlayer && capitalList[i].assignedPlayer.playerId != event.info.attackerPlayerId && event.info.attackedStatus) || !capitalList[i].assignedPlayer) {
                            
                                this.handleEvent(networkGame, networkGame.createEvent(NetworkGameEvent.EVENT_TYPE.CHANGE_OWNER, {
                                    capitalIndex: capitalList[i].capitalIndex,
                                    sourceCapitalIndex: event.info.sourceCapitalIndex,
                                    playerId: event.info.attackerPlayerId
                                }));                                            
                        }

                    }
                }
            }
        }catch(e){}
    }

    return true;
};


NetworkGameEventHandler.onChangeOwner = function (networkGame, event) {
    if (networkGame.networkGameData) {
        var playerList = Array.from(networkGame.networkGameData.playerList);
        var player = null;
        for (var i = 0; i < playerList.length; i++) {
            if (playerList[i].playerId == event.info.playerId) {
                player = playerList[i];
                break;
            }
        }
        if(!player) return;

        var capitalList = Array.from(networkGame.networkGameData.mapData.capitalData);
        var sourceCapitalIndex =null;
        var isWeatherDome = false;
        var isMissileSilo = false;
        var isPropagandaTower = false;
        var weatherDomeEffect = 0;
        var missileSiloCountResult = 0;
        var propagandaDuration = 0;
        var bldgCode = null;
        for (var i = 0; i < capitalList.length; i++) {
            if (capitalList[i].capitalIndex == event.info.capitalIndex) {

                sourceCapitalIndex = capitalList[i].capitalIndex;
                // is captured building a weather dome?
                if (capitalList[i].isBuilding && capitalList[i].buildingData.BLDG_CODE == 'FORT') {
                    bldgCode = capitalList[i].buildingData.BLDG_CODE;
                }

                if (capitalList[i].isBuilding && capitalList[i].buildingData.BLDG_CODE == 'WD') {
                    isWeatherDome = true;
                    weatherDomeEffect = capitalList[i].buildingData.EFFECT;
                    bldgCode = capitalList[i].buildingData.BLDG_CODE;
                }

                // is captured building a missile silo?
                if (capitalList[i].isBuilding && capitalList[i].buildingData.BLDG_CODE == 'MS') {
                    isMissileSilo = true;
                    missileSiloCountResult = capitalList[i].buildingData.EFFECT;
                    bldgCode = capitalList[i].buildingData.BLDG_CODE;
                }

                // is captured building a propaganda tower?
                if (capitalList[i].isBuilding && capitalList[i].buildingData.BLDG_CODE == 'PT') {
                    isPropagandaTower = true;
                    propagandaDuration = capitalList[i].buildingData.DURATION;
                    bldgCode = capitalList[i].buildingData.BLDG_CODE;
                }
                // store previous owner
                event.info.previousOwner = capitalList[i].assignedPlayer;
                // assign new owner
                capitalList[i].assignedPlayer = null;
                capitalList[i].assignedPlayer = player;
                capitalList[i].soldierCount = 1;
                capitalList[i].haveShield = true;
                event.info.newOwner = player;
                event.info.startTime = Date.now();
                event.info.duration = NETWORK_GAME_CONFIGS.SHIELD_DURATION;
                event.info.capitalIndex = capitalList[i].capitalIndex;
                // activate shield

                networkGame.startTimer(NETWORK_GAME_CONFIGS.SHIELD_DURATION, function (networkGame) {
                    // Process to next event
                    this.handleEvent(networkGame, networkGame.createEvent(NetworkGameEvent.EVENT_TYPE.REMOVE_SHIELD));
                }.bind(this, networkGame)).start();

                // clean/kill previous timers/soldiers
                // NetworkGameEventHandler.forceFinishMarchTimers(networkGame, capitalList[i].capitalIndex);
                // NetworkGameEventHandler.onClearSpawnTimers(networkGame, { sourceCapitalIndex: event.info.sourceCapitalIndex, targetCapitalIndex: capitalList[i].capitalIndex });

                networkGame.sendEventToClient(event);
                // console.log('Assigning player ' + player.playerName + ' to capital ' + capitalList[i].capitalIndex);
            }
        }

        var soldierList = networkGame.networkGameData.soldierList;
        for (var i = 0; i < soldierList.length; i++) {
            if (soldierList[i].targetCapitalIndex == event.info.capitalIndex) {
                soldierList[i].isKilled = true;
            }
        }

        try{
        // setTimeout(function(){
            if (bldgCode) {
                // deduct 15 soldiers to all enemy owned nation if wd
                if (isWeatherDome) {
                    for (var i = 0; i < capitalList.length; i++) {
                        if (capitalList[i].assignedPlayer && capitalList[i].assignedPlayer.playerId != player.playerId) {
                            capitalList[i].soldierCount -= weatherDomeEffect;
                            if (capitalList[i].soldierCount <= 0) {
                                capitalList[i].soldierCount = 0;
                            }
                        }
                    }
                }
                // kill all soldiers of one random enemy capital
                if (isMissileSilo) {
                    var enemyCapitals = [];
                    for (var i = 0; i < capitalList.length; i++) {
                        if (capitalList[i].assignedPlayer && capitalList[i].assignedPlayer.playerId != player.playerId) {
                            enemyCapitals.push(capitalList[i]);
                        }
                    }
                    // console.log(enemyCapitals.length)
                    var randomCapital = enemyCapitals[Utilities.randomInt(0, enemyCapitals.length - 1)];

                    networkGame.startTimer(NETWORK_GAME_CONFIGS.MISSILE_SILO_TIMER, function () {
                        var capitalList = Array.from(networkGame.networkGameData.mapData.capitalData);
                        for (var i = 0; i < capitalList.length; i++) {
                            if (capitalList[i].assignedPlayer && randomCapital && randomCapital.capitalIndex && capitalList[i].capitalIndex == randomCapital.capitalIndex) {
                                capitalList[i].soldierCount = missileSiloCountResult;
                                if (capitalList[i].soldierCount <= 0) {
                                    capitalList[i].soldierCount = 0;
                                }
                            }
                        }
                        networkGame.networkGameData.mapData.capitalData = [];
                        networkGame.networkGameData.mapData.capitalData = capitalList;
                    }.bind(this, networkGame, randomCapital, missileSiloCountResult)).start();
                }
                // make all enemy capital produce soldiers slowly by 50%
                if (isPropagandaTower) {
                    for (var i = 0; i < capitalList.length; i++) {
                        if (capitalList[i].assignedPlayer && capitalList[i].assignedPlayer.playerId != player.playerId) {
                            capitalList[i].underPropaganda = true;
                            capitalList[i].propagandaPause = true;
                        }
                    }
                    // console.log('Propaganda Effect Started');

                    networkGame.startTimer(propagandaDuration, function () {
                        // console.log('Propaganda Effect Ended');
                        var capitalList = Array.from(networkGame.networkGameData.mapData.capitalData);
                        for (var i = 0; i < capitalList.length; i++) {
                            if (capitalList[i].assignedPlayer && capitalList[i].underPropaganda) {
                                capitalList[i].underPropaganda = false;
                                capitalList[i].propagandaPause = false;
                            }
                        }
                        networkGame.networkGameData.mapData.capitalData = [];
                        networkGame.networkGameData.mapData.capitalData = capitalList;
                    }.bind(this, networkGame)).start();
                }

                var event = networkGame.createEvent(NetworkGameEvent.EVENT_TYPE.CAPTURE_BLDG, {
                    player: player,
                    targetCapitalIndex: randomCapital ? randomCapital.capitalIndex : -1,
                    sourceCapitalIndex: sourceCapitalIndex,
                    duration: propagandaDuration,
                    timestamp: Date.now(),
                    bldgCode: bldgCode
                });
                networkGame.sendEventToClient(event);
            }

        // }.bind(this),3000);
        }catch(e){}

        networkGame.networkGameData.mapData.capitalData = [];
        networkGame.networkGameData.mapData.capitalData = capitalList;

        // player info
        var players = Array.from(networkGame.networkGameData.playerList);

        // reset player scores
        // for (var i = 0; i < players.length; i++) {
        //     players[i].playerScore = 0;
        // }

        // for (var i = 0; i < players.length; i++) {
        //     for (var j = 0; j < capitalList.length; j++) {
        //         if (capitalList[j].assignedPlayer && players[i].playerId == capitalList[j].assignedPlayer.playerId) {
        //             players[i].playerScore++;
        //         }
        //     }

            // if (players[i].playerScore <= 0) {
            //     players[i].eliminated = true;
            // }
        // }

        NetworkGameUtilities.reCalculatePlayerRank(networkGame);


        // this.handleEvent(networkGame, networkGame.createEvent(NetworkGameEvent.EVENT_TYPE.GAME_UPDATE));
    }
    return true;
};

// ----------------
// End custom functions
// ----------------

// --------------
// Utility Functions that needs networkGame
// --------------
NetworkGameEventHandler.spawnSoldier = function (networkGame, playerData, spawnData) {
    if (!networkGame) return null;
    if (!networkGame.networkGameData) return null;


    var soldier = {
        uid: Utilities.generateUID(),
        playerId: playerData.playerId,
        sourceCapitalIndex: spawnData.sourceCapital.capitalIndex,
        targetCapitalIndex: spawnData.targetCapital.capitalIndex,
        spawnSoldier:spawnData.soldierCount,
    };
    networkGame.networkGameData.soldierList.push(soldier);

    var event = networkGame.createEvent(NetworkGameEvent.EVENT_TYPE.SPAWN_SOLDIER, {
        soldier: soldier,
        player: playerData
    });

    networkGame.sendEventToClient(event);

    return soldier;
};


NetworkGameEventHandler.onClearSpawnTimers = function (networkGame, timer) {
    if (networkGame.networkGameData) {
        var haveCleared = false;
        var soldierTimers = Array.from(networkGame.networkGameData.soldierTimers);
        // console.log(timer);
        if (typeof timer.targetCapitalIndex !== 'undefined') {
            for (var i = 0; i < soldierTimers.length; i++) {
                if (soldierTimers[i].sourceCapitalIndex == timer.sourceCapitalIndex && soldierTimers[i].targetCapitalIndex == timer.targetCapitalIndex) {
                    // console.log('clear');
                    haveCleared = true;
                    soldierTimers[i].timer.forceFinished(false);
                }
            }
        } else {
            for (var i = 0; i < soldierTimers.length; i++) {
                if (soldierTimers[i].sourceCapitalIndex == timer.sourceCapitalIndex) {
                    // console.log('clear');
                    haveCleared = true;
                    soldierTimers[i].timer.forceFinished(false);
                }
            }
        }
        if (haveCleared) {
            soldierTimers = soldierTimers.filter(function (timer) {
                return !timer.timerUp;
            });
            networkGame.networkGameData.soldierTimers = [];
            networkGame.networkGameData.soldierTimers = soldierTimers;

            var event = networkGame.createEvent(NetworkGameEvent.EVENT_TYPE.CLEAR_SPAWN_TIMERS, {
                data: timer
            });
            networkGame.sendEventToClient(event);
        }
    }
    return true;
};



NetworkGameEventHandler.findCapitalByIndex = function (networkGame, index) {
    if (!networkGame) return null;
    if (!networkGame.networkGameData) return null;

    var capitalList = Array.from(networkGame.networkGameData.mapData.capitalData);
    var capital = null;
    for (var i = 0; i < capitalList.length; i++) {
        if (capitalList[i].capitalIndex == index) {
            capital = capitalList[i];
            break;
        }
    }

    return capital;
};

// --------------
// End Utility Functions that needs networkGame
// --------------

/**
 * Export module
 */
if (typeof module !== 'undefined') {
    module.exports.NetworkGameEventHandler = NetworkGameEventHandler;
}
