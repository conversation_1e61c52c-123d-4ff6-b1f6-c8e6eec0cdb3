ig.module(
    'dl.game.entity.components.collision.checkers.pointer'
).requires(
    'dl.game.pointer',
    'dl.game.entity.components.collision.collision'
).defines(function () {
    'use strict';

    dl.Pointer.inject({
        staticInstantiate: function () {
            this.parent();
            // collision property
            this._collisionType = dl.CollisionComponent.TYPES.POINT;
            this.collisionTag = dl.Pointer.COLLISION_TAG;
            this.collisionTargetTag = dl.Pointer.COLLISION_TARGET_TAG;

            // object collide with pointer
            this._objectArray = [];
            this._objectArrayLastFrame = [];

            // skip check under flag
            this._skipCheckUnderFlag = false;

            return this;
        },

        onCollision: function (other) {
            if (other.collisionTag & dl.Pointer.COLLISION_BLOCK_INPUT_TAG) {
                this._skipCheckUnderFlag = true;
            }

            // push all collision under the pointer to handle later
            this._objectArray.push(other);
        },

        checkCollisionEntities: function (entities) {
            this._skipCheckUnderFlag = false;

            this._checkCollisionEntitiesRecursive(entities, false);

            this.handlePointerEventLastFrame();
            this.handlePointerEvent();
        },

        _checkCollisionEntitiesRecursive: function (entities) {
            // itorate from top down
            for (var entityIndex = entities.length - 1; entityIndex >= 0; entityIndex--) {
                var entity = entities[entityIndex];

                if (entity._entities) {
                    this._checkCollisionEntitiesRecursive(entity._entities);
                }

                // skip check lower entities
                if (this._skipCheckUnderFlag) return;
                this._checkCollisionEntity(entity);
            }
        },

        _checkCollisionEntity: function (entity) {

            if(entity && entity.getComponents){
            // Skip if no collision components
                var collisionComponents = entity.getComponents('dl_CollisionComponent');
                if (!collisionComponents) return;

                for (var componentIndex = 0; componentIndex < collisionComponents.length; componentIndex++) {
                    var collision = collisionComponents[componentIndex];

                    if (collision.touches(this)) {
                        this._checkCollisionPair(collision, this);
                    }
                }
            }   
        },

        _checkCollisionPair: function (aCollision, bCollision) {
            if (bCollision.collisionTargetTag & aCollision.collisionTag) {
                bCollision.onCollision(aCollision);
            }
            if (aCollision.collisionTargetTag & bCollision.collisionTag) {
                aCollision.onCollision(bCollision);
            }
        },

        handlePointerEventLastFrame: function () {
            for (var i = 0; i < this._objectArrayLastFrame.length; i++) {
                var object = this._objectArrayLastFrame[i];
                if (!this._objectArray.includes(object)) {
                    if (dl.check.isFunction(object.leave)) {
                        object.leave(this);
                    }
                }
            }
            // Clear after handled
            this._objectArrayLastFrame = [];
        },

        handlePointerEvent: function () {
            // iterate from hightest entity to lowest
            for (var i = this._objectArray.length - 1; i >= 0; i--) {
                var object = this._objectArray[i];
                if (dl.check.isFunction(object.over)) {
                    object.over(this);
                }
                this.clickObject(object);
                this._objectArrayLastFrame.push(object);
            }
            // Clear after handled
            this._objectArray = [];
        },

        clickObject: function (object) {
            // if first pressed
            if (this._isFirstPressed) {
                if (dl.check.isFunction((object.clicked))) {
                    object.clicked(this);
                }
            }
            // if pressed
            if (this._isPressed && !this._isReleased && !this._isFirstPressed) {
                if (dl.check.isFunction((object.clicking))) {
                    object.clicking(this);
                }
            }
            // if released
            if (this._isReleased) {
                if (dl.check.isFunction((object.released))) {
                    object.released(this);
                }
            }
        }
    });

    dl.Pointer.COLLISION_TAG = dl.CollisionComponent.GetCollisionTag('POINTER');
    dl.Pointer.COLLISION_BLOCK_INPUT_TAG = dl.CollisionComponent.GetCollisionTag('BLOCK_INPUT');
    dl.Pointer.COLLISION_TARGET_TAG = dl.CollisionComponent.GetCollisionTag('POINTER_LISTENER') ^ dl.Pointer.COLLISION_BLOCK_INPUT_TAG;
    dl.Pointer.AddCollisionTargetTag = function (newTag) {
        dl.Pointer.COLLISION_TARGET_TAG ^= newTag;
    };
});
