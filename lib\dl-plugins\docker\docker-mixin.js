ig.module(
    'dl-plugins.docker.docker-mixin'
).requires(
    'dl-plugins.docker.docker-component'
).defines(function () {
    'use strict';

    /**
     * Docker Mixin Plugin
     * Provides easy integration of Docker functionality into entities
     * 
     * Usage:
     *   dl.Entity.extend(dl.Docker.Mixin).extend({
     *       // your entity implementation
     *   });
     * 
     * The mixin automatically:
     * - Adds Docker component during initComponents
     * - Provides helper methods for common docker operations
     * - Maintains backward compatibility with existing docker usage
     */

    dl.Docker.Mixin = {
        /**
         * Initialize components including Docker component
         */
        initComponents: function () {
            this.parent();
            this._initDockerComponent();
        },

        /**
         * Initialize Docker component with default settings
         * Can be overridden in child classes for custom configuration
         */
        _initDockerComponent: function () {
            // Only initialize if not already present
            if (!this.c_DockerComponent) {
                this.c_DockerComponent = this.addComponent(dl.Docker.Component, {
                    dockerObject: dl.game.camera,  // Default to camera
                    dockerPercent: { x: 0.5, y: 0.5 },  // Center
                    dockerOffset: { x: 0, y: 0 }   // No offset
                });
            }
        },

        /**
         * Dock this entity to another object
         * @param {Object} dockerObject - Object to dock to
         * @param {Object} options - Optional configuration
         * @param {Object} options.percent - Docker percentage {x, y} (default: {x: 0.5, y: 0.5})
         * @param {Object} options.offset - Docker offset {x, y} (default: {x: 0, y: 0})
         */
        dockTo: function (dockerObject, options) {
            if (!this.c_DockerComponent) {
                this._initDockerComponent();
            }
            this.c_DockerComponent.setDockerObject(dockerObject, options);
        },

        /**
         * Dock this entity to the camera
         * @param {Object} options - Optional configuration
         * @param {Object} options.percent - Docker percentage {x, y} (default: {x: 0.5, y: 0.5})
         * @param {Object} options.offset - Docker offset {x, y} (default: {x: 0, y: 0})
         */
        dockToCamera: function (options) {
            if (!this.c_DockerComponent) {
                this._initDockerComponent();
            }
            this.c_DockerComponent.dockToCamera(options);
        },

        /**
         * Update docker positioning configuration
         * @param {Object} config - Configuration object
         * @param {Object} config.dockerObject - Object to dock to
         * @param {Object} config.dockerPercent - Docker percentage {x, y}
         * @param {Object} config.dockerOffset - Docker offset {x, y}
         */
        updateDockerConfig: function (config) {
            if (!this.c_DockerComponent) {
                this._initDockerComponent();
            }
            this.c_DockerComponent.updateProperties(config);
        },

        /**
         * Enable or disable docker positioning
         * @param {boolean} enabled - Whether docker positioning should be enabled
         */
        setDockerEnabled: function (enabled) {
            if (!this.c_DockerComponent) {
                this._initDockerComponent();
            }
            this.c_DockerComponent.enable = enabled;
        },

        /**
         * Get current docker configuration
         * @returns {Object} Current docker configuration
         */
        getDockerConfig: function () {
            if (!this.c_DockerComponent) {
                return null;
            }
            return {
                dockerObject: this.c_DockerComponent.dockerObject,
                dockerPercent: this.c_DockerComponent.dockerPercent,
                dockerOffset: this.c_DockerComponent.dockerOffset,
                enabled: this.c_DockerComponent.enable
            };
        },

        /**
         * Dock to top-left corner of target
         * @param {Object} dockerObject - Object to dock to
         * @param {Object} offset - Optional offset {x, y}
         */
        dockToTopLeft: function (dockerObject, offset) {
            this.dockTo(dockerObject, {
                percent: { x: 0, y: 0 },
                offset: offset || { x: 0, y: 0 }
            });
        },

        /**
         * Dock to top-right corner of target
         * @param {Object} dockerObject - Object to dock to
         * @param {Object} offset - Optional offset {x, y}
         */
        dockToTopRight: function (dockerObject, offset) {
            this.dockTo(dockerObject, {
                percent: { x: 1, y: 0 },
                offset: offset || { x: 0, y: 0 }
            });
        },

        /**
         * Dock to bottom-left corner of target
         * @param {Object} dockerObject - Object to dock to
         * @param {Object} offset - Optional offset {x, y}
         */
        dockToBottomLeft: function (dockerObject, offset) {
            this.dockTo(dockerObject, {
                percent: { x: 0, y: 1 },
                offset: offset || { x: 0, y: 0 }
            });
        },

        /**
         * Dock to bottom-right corner of target
         * @param {Object} dockerObject - Object to dock to
         * @param {Object} offset - Optional offset {x, y}
         */
        dockToBottomRight: function (dockerObject, offset) {
            this.dockTo(dockerObject, {
                percent: { x: 1, y: 1 },
                offset: offset || { x: 0, y: 0 }
            });
        },

        /**
         * Dock to center of target
         * @param {Object} dockerObject - Object to dock to
         * @param {Object} offset - Optional offset {x, y}
         */
        dockToCenter: function (dockerObject, offset) {
            this.dockTo(dockerObject, {
                percent: { x: 0.5, y: 0.5 },
                offset: offset || { x: 0, y: 0 }
            });
        }
    };
});
