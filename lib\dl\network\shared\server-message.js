/**
 * Created by <PERSON><PERSON> <PERSON>
 * Defining server messages
 */

var SERVER_MESSAGE = {};

// Message's tags of User
SERVER_MESSAGE.USER = {
    CONFIRMED: 1,
    CONNECTED: 2,
    DISCONNECTED: 3
};

// Message's tags of server host
SERVER_MESSAGE.HOST = {
    REQUEST_ROOM_SUCCEED: 101,
    REQUEST_ROOM_REJECTED: 102,
    ROOM_EVENT: 103,
    PLAYER_KICKED: 104,
    CHECK_PASSWORD_RESULT: 105,
    CHECK_NAME_RESULT: 106,
    SVAS_PLAYER_DATA_RESULT: 406,
    SVAS_CLAIM_SUCCESS:407,
    SVAS_RV_SUCCESS:408,
    SVAS_DEDUCT_SUCCESS:409,
    SVAS_GEMS_SUCCESS:410,
    SVAS_DEDUCT_GEMS_SUCCESS:411,
    ALLIANCE_GET_CLAIM_SUCCESS:412
};

// Message's tags of client
SERVER_MESSAGE.CLIENT = {
    REQUEST_ROOM: 201,
    LEAVE_ROOM: 202,
    ROOM_EVENT: 203,
    CHECK_PASSWORD: 204,
    CHECK_NAME: 205,
    SVAS_GET_PLAYER_DATA: 305,
    SVAS_REG_NEW_PLAYER_DATA:306,
    SVAS_SAVE_GAME_DATA:307,
    SVAS_SAVE_CLAIM_ACHIEVEMENT:308,
    SVAS_SAVE_RV_VALUE:309,
    SVAS_DEDUCT_UPGRADE:310,
    SVAS_SAVE_GEMS_VALUE:311,
    SVAS_DEDUCT_GEMS:312,
    ALLIANCE_GET_CLAIM:313,
    ALLIANCE_CLAIM_GIFT:314
};

// Message's tags when request room
SERVER_MESSAGE.REQUEST_REJECT_REASON = {
    UNKNOWN: 0,
    SERVER_ERROR: 1,
    INVALID_REQUEST: 2,
    VERSION_MISMATCH: 3,
    CANNOT_FIND_ROOM: 4
};

/**
 * Export module
 */
if (typeof module !== 'undefined') {
    module.exports.SERVER_MESSAGE = SERVER_MESSAGE;
}
