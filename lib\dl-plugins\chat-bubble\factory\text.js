ig.module(
    'dl-plugins.chat-bubble.factory.text'
).defines(function () {
    'use strict';

    dl.ChatBubble.Factory.Text = {
        generate: function (configs) {
            var canvas = document.createElement('canvas');
            var context = canvas.getContext('2d');

            var size = this.measure(configs, context);

            // update canvas size
            canvas.width = size.x;
            canvas.height = size.y;

            this.draw(configs, context);

            return {
                configs: configs,
                canvas: canvas
            };
        },

        measureLine: function (ctx, textLines) {
            var metrics = ctx.measureText(textLines[0]);
            for (var i = 1; i < textLines.length; i++) {
                var newMetrics = ctx.measureText(textLines[i]);
                if (newMetrics.width >= metrics.width) {
                    metrics = newMetrics;
                }
            }

            return metrics;
        },

        measure: function (configs, ctx) {
            var text = configs.text.toString();
            var textLines = text.split('<br>');

            ctx.save();
            ctx.font = configs.fontSize + 'px' + ' ' + configs.fontFamily;
            var metrics = this.measureLine(ctx, textLines);
            ctx.restore();

            configs.textData = {
                textLines: textLines,
                textWidth: metrics.width,
                fontHeight: metrics.fontBoundingBoxAscent + metrics.fontBoundingBoxDescent
            };

            // some browser doesn't support
            if (isNaN(configs.textData.fontHeight) || configs.textData.fontHeight == null) {
                configs.textData.fontHeight = configs.fontSize;
            }

            return {
                x: configs.textData.textWidth,
                y: configs.textData.fontHeight * configs.textData.textLines.length
            };
        },

        getTextDrawPos: function (configs, ctx) {
            var pos = {
                x: ctx.canvas.width * 0.5,
                y: ctx.canvas.height * 0.5
            };

            switch (configs.textAlign) {
                case 'start':
                case 'left': {
                    pos.x -= configs.textData.textWidth * 0.5;
                } break;

                case 'end':
                case 'right': {
                    pos.x += configs.textData.textWidth * 0.5;
                } break;

                case 'center':
                default: {
                } break;
            }

            // offset multiline
            if (configs.textData.textLines.length > 1) {
                pos.y -= (configs.textData.textLines.length - 1) * 0.5 * configs.textData.fontHeight;
            }

            return pos;
        },

        draw: function (configs, ctx) {
            ctx.save();

            ctx.fillStyle = configs.fillStyle;
            ctx.textAlign = configs.textAlign;
            ctx.textBaseline = 'middle';
            ctx.font = configs.fontSize + 'px' + ' ' + configs.fontFamily;

            var drawPos = this.getTextDrawPos(configs, ctx);
            for (var i = 0; i < configs.textData.textLines.length; i++) {
                ctx.fillText(configs.textData.textLines[i],
                    drawPos.x,
                    drawPos.y + i * configs.textData.fontHeight);
            }

            ctx.restore();
        }
    };

    dl.ChatBubble.Factory.Text.DEFAULT_CONFIGS = {
        text: '',
        fillStyle: 'black',
        textAlign: 'center', // [center|left|right];
        fontSize: 24,
        fontFamily: 'Arial'
    };
});
