ig.module(
    'dl.templates.network-entities.entity-popup-select-max-player'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.popup',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.animation-sheet',
    'dl.templates.mixins.text',
    'dl.templates.entities.buttons.entity-button-image-text',
    'dl.templates.entities.buttons.entity-button-image'
).defines(function () {
    'use strict';

    dl.EntityPopupSelectMaxPlayer = dl.Entity
        .extend(dl.MixinPopup)
        .extend(dl.MixinDocker)
        .extend({
            postInit: function () {
                this.parent();

                this.content = this.spawnEntity(dl.EntityPopupSelectMaxPlayer_Content, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    }
                });
            }
        });

    dl.EntityPopupSelectMaxPlayer_Content = dl.Entity
        .extend(dl.MixinPopupContent)
        .extend(dl.MixinDocker)
        .extend(dl.MixinAnimationSheet)
        .extend(dl.MixinText)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this.image = dl.preload['popup-large'];
                this._useAnimationSheetAsSize = true;

                this.text = _STRINGS.NETWORK.POPUP_SELECT_MAX_PLAYER_TITLE;

                return this;
            },

            postInit: function () {
                this.parent();

                this.initButtons();

                this.tweenIn();
            },

            _initTextComponent: function () {
                this.c_TextComponent.updateProperties({
                    fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                    textAlign: 'center', // [center|end|left|right|start];
                    textBaseline: 'middle', // [alphabetic|top|hanging|middle|ideographic|bottom];

                    fontSize: 46,

                    _docker: { x: 0.5, y: 0.15 },
                    _anchor: { x: 0.5, y: 0.5 },
                    _offset: { x: 0, y: 0 }
                });
            },

            initButtons: function () {
                var offset = 0.2;

                this.btnPlayer_2 = this.spawnEntity(dl.EntityButtonImageText, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.55 - offset },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                        fontSize: 40,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    image: dl.preload['button-green'],
                    text: _STRINGS.NETWORK.POPUP_SELECT_MAX_PLAYER_BUTTON_2,
                    onButtonClicked: this.onButtonSelected.bind(this, 2)
                });

                this.btnPlayer_3 = this.spawnEntity(dl.EntityButtonImageText, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.55  },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                        fontSize: 40,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    image: dl.preload['button-green'],
                    text: _STRINGS.NETWORK.POPUP_SELECT_MAX_PLAYER_BUTTON_3,
                    onButtonClicked: this.onButtonSelected.bind(this, 3)
                });

                this.btnPlayer_4 = this.spawnEntity(dl.EntityButtonImageText, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.55 + offset },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                        fontSize: 40,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    image: dl.preload['button-green'],
                    text: _STRINGS.NETWORK.POPUP_SELECT_MAX_PLAYER_BUTTON_4,
                    onButtonClicked: this.onButtonSelected.bind(this, 4)
                });

                this.btnClose = this.spawnEntity(dl.EntityButtonImage, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 1, y: 0 },
                        dockerOffset: { x: -10, y: 10 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:100,y:100}
                    },
                    anchor: { x: 1, y: 0 },
                    image: dl.preload['popup-button-close'],
                    onButtonClicked: function () {
                        ig.game.client.roomMaxPlayer = 0;

                        this.tweenOut(function () {
                            this.kill();
                            // ig.game.title.notifdot.alpha=ig.game.title.notifdot.tempAlpha;
                        }.bind(this.parentInstance));
                    }.bind(this)
                });
            },

            onButtonSelected: function (maxPlayer) {
                switch (maxPlayer) {
                    case 2:
                        ig.game.client.roomMaxPlayer = 2;
                        break;
                    case 3:
                        ig.game.client.roomMaxPlayer = 3;
                        break;
                    case 4:
                        ig.game.client.roomMaxPlayer = 4;
                        break;
                    default:
                        ig.game.client.roomMaxPlayer = 0;
                        break;
                }

                this.tweenOut(function () {
                    this.kill();
                }.bind(this.parentInstance));
            },

            tweenIn: function (callback) {
                this.parent(callback);
            },

            tweenOut: function (callback) {
                this.btnClose.setEnable(false);
                this.btnPlayer_2.setEnable(false);
                this.btnPlayer_4.setEnable(false);

                this.parent(callback);
            }
        });
});
