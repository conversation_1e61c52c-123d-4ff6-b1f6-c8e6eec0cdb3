ig.module(
    'dl.game.entity.cache-canvas'
).defines(function () {
    'use strict';

    dl.EntityMixinCacheCanvas = {
        staticInstantiate: function () {
            this.parent();

            // canvas
            this.cacheCanvas = document.createElement('canvas');
            // context
            this.cacheContext = this.cacheCanvas.getContext('2d');
            // flags
            this.__cacheFirstDrawFlag = false;
            this._cacheSkipRedrawFlag = false;
            this._cacheRedrawFlag = true;

            this.cacheCanvasEnabled = true;

            // bind events
            this.onEvent('sizeChanged', function () {
                this._cacheResizeCanvas(this.size.x, this.size.y);
            }.bind(this));

            this.bindRedrawEvents(this);

            return this;
        },

        toImage: function () {
            var dataURL = this.cacheCanvas.toDataURL('image/png');
            var newTab = window.open('about:blank', 'Image from canvas');
            newTab.document.write("<img src='" + dataURL + "' alt='from canvas'/>");
        },

        bindRedrawEvents: function (entity) {
            // bind events
            entity.onEvent('componentDrawChanged', this.cacheRequestRedraw.bind(this));
            entity.onEvent('buttonStateChanged', this.cacheRequestRedraw.bind(this));
        },

        cacheRequestRedraw: function (redraw) {
            if (!dl.check.isDefined(redraw)) redraw = true;

            this._cacheRedrawFlag = redraw;

            // trigger for parent Entity
            if (this.parentInstance &&
                typeof this.parentInstance.cacheRequestRedraw === 'function') {
                this.parentInstance.cacheRequestRedraw();
            }
        },

        cacheSkipRedraw: function () {
            this._cacheSkipRedrawFlag = true;

            // trigger for parent Entity
            if (this.parentInstance &&
                typeof this.parentInstance.cacheSkipRedraw === 'function') {
                this.parentInstance.cacheSkipRedraw();
            }
        },

        _cacheResizeCanvas: function (width, height) {
            if (!this.cacheCanvas) return;

            this.cacheCanvas.width = width;
            this.cacheCanvas.height = height;

            this.cacheRequestRedraw();
        },

        _cacheCheckRedraw: function () {
            if (!this.__cacheFirstDrawFlag) return true;
            if (this._cacheSkipRedrawFlag) return false;

            return this._cacheRedrawFlag;
        },

        // override
        draw: function (ctx) {
            if(!this.cacheContext) return;
            if (this._cacheCheckRedraw()) {
                this.cacheContext.resetTransform();
                this.cacheContext.clearRect(0, 0, this.cacheCanvas.width, this.cacheCanvas.height);

                this.cacheContext.save();
                this.cacheContext.translate(
                    -(this.pos.x - this.cacheCanvas.width * 0.5),
                    -(this.pos.y - this.cacheCanvas.height * 0.5));
                this.parent(this.cacheContext);
                this.cacheContext.restore();

                this.__cacheFirstDrawFlag = true;
                this._cacheRedrawFlag = false;
            }
            this._cacheSkipRedrawFlag = false;

            if (this.cacheCanvas.width != 0 &&
                this.cacheCanvas.height != 0) {
                ctx.drawImage(
                    this.cacheCanvas,
                    this.pos.x - this.cacheCanvas.width * 0.5,
                    this.pos.y - this.cacheCanvas.height * 0.5);
            }

            if (dl.debug && dl.debug.increaseDrawCall) {
                dl.debug.increaseDrawCall();
            }
        },

        // override
        spawnEntity: function (entityClass, settings) {
            var entity = this.parent(entityClass, settings);
            this.bindRedrawEvents(entity);

            return entity;
        }
    };

    /**
     * Enable cache canvas for object class
     * Should enable after defined class
     * @param {*} objectClass
     */
    dl.enableCacheCanvas = function (objectClass) {
        objectClass.inject(dl.EntityMixinCacheCanvas);
    };
});
