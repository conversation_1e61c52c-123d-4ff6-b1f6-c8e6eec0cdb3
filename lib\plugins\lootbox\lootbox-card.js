ig.module('plugins.lootbox.lootbox-card')
    .requires(
        'plugins.lootbox.lootbox-game-object'
    )
    .defines(function () {
        ig.LootboxCard = ig.LootboxGameObject.extend({


            id: 0,
            zIndex: 9999,
            level: 1,
            faceDown: false,
            isSelected: false,
            isCustomCard: false,
            customName: null,
            customLevel: null,
            customImage: null,
            customDescription: null,
            isNameOnTop: false,

            isGreyedOut: false,
            isMergeable: false,
            mergeAnimValue: 1,
            mergeAnimValueModifier: 0.05,
            showExp: true,


            init: function (x, y, settings) {
                this.parent(x, y, settings);
                this.width = ig.Lootbox.card.width;
                this.height = ig.Lootbox.card.height;
                this.forceDraw = true;

                if (!ig.cardFrontBgCanvas) {

                    ig.cardFrontBgCanvas = ig.$new('canvas');
                    ig.cardFrontBgCanvas.width = ig.Lootbox.card.width * 2;
                    ig.cardFrontBgCanvas.height = ig.Lootbox.card.height * 2;
                    var ctx = ig.cardFrontBgCanvas.getContext("2d");
                    var x = 0;
                    var y = 0;
                    ctx.save();
                    ctx.scale(2, 2);
                    var r = ig.Lootbox.card.roundedRadius;
                    ctx.fillStyle = ig.Lootbox.card.frontColor;
                    ctx.fillRect(x + r, y, this.width - r * 2, this.height)
                    ctx.fillRect(x, y + r, this.width, this.height - r * 2)

                    ctx.beginPath();
                    ctx.arc(x + r, y + r, r, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.closePath();

                    ctx.beginPath();
                    ctx.arc(x + this.width - r, y + r, r, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.closePath();

                    ctx.beginPath();
                    ctx.arc(x + r, y + this.height - r, r, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.closePath();

                    ctx.beginPath();
                    ctx.arc(x + this.width - r, y + this.height - r, r, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.closePath();
                    ctx.restore();
                }

                if (!ig.greyCanvas) {
                    ig.greyCanvas = ig.$new('canvas');
                    ig.greyCanvas.width = ig.Lootbox.card.width * 2;
                    ig.greyCanvas.height = ig.Lootbox.card.height * 2;
                    var ctx = ig.greyCanvas.getContext("2d");
                    var x = 0;
                    var y = 0;
                    ctx.save();
                    ctx.scale(2, 2);
                    var r = ig.Lootbox.card.roundedRadius;
                    ctx.fillStyle = "#000000";
                    ctx.fillRect(x + r, y, this.width - r * 2, this.height)
                    ctx.fillRect(x, y + r, this.width, this.height - r * 2)

                    ctx.beginPath();
                    ctx.arc(x + r, y + r, r, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.closePath();

                    ctx.beginPath();
                    ctx.arc(x + this.width - r, y + r, r, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.closePath();

                    ctx.beginPath();
                    ctx.arc(x + r, y + this.height - r, r, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.closePath();

                    ctx.beginPath();
                    ctx.arc(x + this.width - r, y + this.height - r, r, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.closePath();
                    ctx.restore();
                }

                ig.game.sortEntitiesDeferred();
            },

            onClickUpgrade: function () {
                if (ig.Lootbox.getExpProgress(this.id) >= 1) {
                    ig.Lootbox.levelUpCard(this.id);
                    ig.Lootbox.saveData();
                    this.exp = ig.Lootbox.getExpFromFirstCardWithId(this.id);
                    this.level = ig.Lootbox.getLevelFromFirstCardWithId(this.id);
                    for (var i = 0; i < 100; i++) {
                        if (ig.responsive) ig.game.spawnEntityBackward(ig.LootboxParticleOut, this.upgradeButton.anchoredPositionX, this.upgradeButton.anchoredPositionY, { delay: i * 0.002, fillColor: "#ffffff" })
                        else ig.game.spawnEntityBackward(ig.LootboxParticleOut, this.upgradeButton.pos.x, this.upgradeButton.pos.y, { delay: i * 0.002, fillColor: "#ffffff" })
                    }
                }
                this.checkAndSpawnUpgradeButton();
            },

            checkAndSpawnUpgradeButton: function () {
                if (!this.upgradeButton) {
                    this.upgradeButton = ig.game.spawnEntityBackward(ig.LootboxSimpleButton, 0, 0, {
                        image: ig.Lootbox.images.simple,
                        font: ig.Lootbox.page.button.font,
                        textColor: ig.Lootbox.page.button.textColor ? ig.Lootbox.page.button.textColor : "#ffffff",
                        offsetY: ig.Lootbox.page.button.offsetY,
                        text: ig.Lootbox.strings.upgradeButton,
                        zIndex: this.zIndex + 1
                    });
                    this.usePressedTween = false;
                    this.upgradeButton.onClicked.add(this.onClickUpgrade, this);
                    this.upgradeButton.visible = false;
                }

                if (this.level < ig.Lootbox.card.levelMax && ig.Lootbox.getExpProgress(this.id) >= 1) {
                    this.upgradeButton.visible = true;
                } else {
                    this.upgradeButton.visible = false;
                }
            },

            exit: function () {
                this.parent();
                if (this.upgradeButton) this.upgradeButton.exit();
            },
            update: function () {
                this.parent();
                if (this.upgradeButton && this.upgradeButton.visible) {
                    if (ig.responsive) {
                        this.upgradeButton.anchoredPositionX = this.anchoredPositionX;
                        this.upgradeButton.anchoredPositionY = this.anchoredPositionY;
                        if (this.anchorX == 0) this.upgradeButton.anchoredPositionX += this.width * this.scaleX * 0.5;
                        if (this.anchorY == 0) this.upgradeButton.anchoredPositionY += this.height * this.scaleY * 0.5;
                    } else {
                        this.upgradeButton.pos.x = this.pos.x;
                        this.upgradeButton.pos.y = this.pos.y;

                        if (this.anchorX == 0) this.upgradeButton.pos.x += this.width * this.scaleX * 0.5;
                        if (this.anchorY == 0) this.upgradeButton.pos.y += this.height * this.scaleY * 0.5;
                    }

                    var scale = (this.width / this.upgradeButton.width) * this.scaleX * 0.9;


                    this.upgradeButton.normalScale = scale;
                    this.upgradeButton.scaleX = scale;
                    this.upgradeButton.scaleY = scale;
                }
            },

            flip: function (delay) {
                if (!delay) delay = 0;
                this.tween({}, delay, {
                    onComplete: function () {
                        var originalScale = this.scaleX;
                        var originalAnchor = this.anchorY;
                        this.tween({ scaleX: 0, anchorY: originalAnchor + 0.1 }, 0.25, {
                            easing: ig.Tween.Easing.Quadratic.EaseOut,
                            onComplete: function () {
                                this.faceDown = !this.faceDown;
                                this.tween({ scaleX: originalScale, anchorY: originalAnchor }, 0.25, {
                                    easing: ig.Tween.Easing.Quadratic.EaseOut,
                                    onComplete: function () {

                                    }.bind(this)
                                }).start();
                            }.bind(this)
                        }).start();
                    }.bind(this)
                }).start();
            },

            flipSwitch: function (newId, newLevel, isGreyedOut, callback, isCustom, newCustomName, newCustomImage, newCustomDescription) {
                if (!isGreyedOut) isGreyedOut = false;
                var originalScale = this.scaleX;
                var originalAnchor = this.anchorY;
                this.customDescription = "";
                this.tween({ scaleX: 0, anchorY: originalAnchor + 0.1 }, 0.15, {
                    easing: ig.Tween.Easing.Quadratic.EaseOut,
                    onComplete: function () {
                        this.faceDown = false;
                        this.id = newId;
                        this.level = newLevel;
                        this.isGreyedOut = isGreyedOut;
                        if (isCustom) {
                            this.isCustomCard = isCustom;
                            this.customName = newCustomName;
                            this.customLevel = newLevel;
                            this.customImage = newCustomImage;
                            this.customDescription = newCustomDescription;
                        }
                        this.tween({ scaleX: originalScale, anchorY: originalAnchor }, 0.15, {
                            easing: ig.Tween.Easing.Quadratic.EaseOut,
                            onComplete: function () {
                                if (callback) callback();
                            }.bind(this)
                        }).start();
                    }.bind(this)
                }).start();
            },

            addExpTo: function (card, delay) {

                var id = this.id;
                var level = ig.Lootbox.getLevelFromFirstCardWithId(id);
                var exp = ig.Lootbox.getExpFromFirstCardWithId(id);

                if (!ig.Lootbox.isCardWithIdAlreadyExist(id)) {
                    ig.Lootbox.addCardData(id, 0, 0);
                    ig.Lootbox.saveData();
                }

                if (!delay) delay = 0.01;
                this.delayedCall(delay, function () {
                    card.flipSwitch(id, level, false, null, false)
                    var tweenObject;
                    if (ig.responsive) {
                        tweenObject = { scaleX: card.scaleX, scaleY: card.scaleY, anchoredPositionX: card.anchoredPositionX, anchoredPositionY: card.anchoredPositionY };
                    } else {
                        tweenObject = { scaleX: card.scaleX, scaleY: card.scaleY, pos: { x: card.pos.x, y: card.pos.y } };
                    }

                    this.tween(tweenObject, 0.3, {
                        delay: 0.2,
                        easing: ig.Tween.Easing.Quadratic.EaseIn, onComplete: function () {
                            for (var i = 0; i < 50; i++) {
                                if (ig.responsive) ig.game.spawnEntityBackward(ig.LootboxParticleOut, card.anchoredPositionX, card.anchoredPositionY, { delay: i * 0.002, fillColor: "#ffffff" })
                                else ig.game.spawnEntityBackward(ig.LootboxParticleOut, card.pos.x, card.pos.y, { delay: i * 0.002, fillColor: "#ffffff" })
                            }

                            ig.Lootbox.incrementExpOfFirstCardWithId(id, 1);
                            ig.Lootbox.saveData();

                            card.exp = exp + 1;
                            this.exit();
                        }.bind(this)
                    }).start();
                }.bind(this))

            },

            drawObject: function (x, y) {
                ig.system.context.globalAlpha = this.alpha
                if (this.id < 0) {
                    this.drawEmpty(x, y);
                } else if (this.faceDown) {
                    this.drawBack(x, y);
                } else {
                    this.drawFront(x, y);
                }
                ig.system.context.globalAlpha = 1;
            },

            drawFront: function (x, y) {
                var ctx = ig.system.context;
                var r = ig.Lootbox.card.roundedRadius;
                ctx.save();

                if (this.isSelected) {

                    ctx.save();

                    var ascend = Math.floor(this.width * 0.05);
                    ctx.lineJoin = 'round';

                    ctx.strokeStyle = ig.Lootbox.card.selectedColor
                    ctx.lineWidth = Math.floor(this.width * 0.08);
                    ctx.strokeRect(x + r, y - ascend, this.width - r * 2, this.height + ascend)
                    ctx.strokeRect(x, y + r - ascend, this.width, this.height - r * 2 + ascend)

                    ctx.beginPath();
                    ctx.arc(x + r, y + r - ascend, r, 0, Math.PI * 2);
                    ctx.stroke();
                    ctx.closePath();

                    ctx.beginPath();
                    ctx.arc(x + this.width - r, y + r - ascend, r, 0, Math.PI * 2);
                    ctx.stroke();
                    ctx.closePath();

                    ctx.beginPath();
                    ctx.arc(x + r, y + this.height - r, r, 0, Math.PI * 2);
                    ctx.stroke();
                    ctx.closePath();

                    ctx.beginPath();
                    ctx.arc(x + this.width - r, y + this.height - r, r, 0, Math.PI * 2);
                    ctx.stroke();
                    ctx.closePath();

                    ctx.restore();

                    ctx.fillStyle = ig.Lootbox.card.backAccentColor;
                    ctx.fillRect(x + r, y, this.width - r * 2, this.height)
                    ctx.fillRect(x, y + r, this.width, this.height - r * 2)
                    ctx.beginPath();

                    ctx.arc(x + r, y + this.height - r, r, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.closePath();

                    ctx.beginPath();
                    ctx.arc(x + this.width - r, y + this.height - r, r, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.closePath();

                    ctx.translate(0, -ascend)
                }

                //draw bg front
                ctx.drawImage(ig.cardFrontBgCanvas, 0, 0, ig.Lootbox.card.width * 2, ig.Lootbox.card.height * 2, x, y, this.width, this.height);

                var image = ig.Lootbox.card.icons[this.id];
                if (this.isCustomCard) image = this.customImage;
                var ratio = 0.8;
                var imageSize = Math.floor(this.width * ratio);

                if (this.isMergeable) {
                    this.mergeAnimValue += ig.system.tick * 10;
                    var sizeAnim = Math.sin(this.mergeAnimValue) * (imageSize * this.mergeAnimValueModifier);
                    imageSize += sizeAnim;
                }

                var drawOffsetX = Math.floor((this.width - imageSize) / 2);
                var drawOffsetY = Math.floor((this.height - imageSize) / 2);
                if (image) {
                    if (image.drawImageCtx) {
                        image.drawImageCtx(ctx, 0, 0, image.width, image.height, x + drawOffsetX, y + drawOffsetY, imageSize, imageSize)
                    } else {
                        ctx.drawImage(image.data, 0, 0, image.width, image.height, x + drawOffsetX, y + drawOffsetY, imageSize, imageSize)
                    }
                }

                var levelWidth = 0.2 * this.width;
                var levelY = 0.1 * this.height + r;
                var levelRadius = 0.07 * this.height;
                var levelTextSize = Math.floor(levelRadius * 1) + "px";
                var levelText = this.isCustomCard ? ig.Lootbox.card.levelPrefix + this.customLevel : ig.Lootbox.card.levelPrefix + this.level;
                var isLevelDrawn = true;
                if (this.isCustomCard && this.customLevel == null) isLevelDrawn = false;

                if (isLevelDrawn) {
                    if (ig.Lootbox.isUpgradeMode) {
                        var progress = ig.Lootbox.getExpProgress(this.id);
                        if (progress > 1) progress = 1;
                        if (this.level == ig.Lootbox.upgradeRequirements.length) {
                            levelText = "MAX"
                            progress = 1;
                        }
                        levelY = r;


                        if (this.showExp) {
                            ctx.fillStyle = ig.Lootbox.card.frontAccentColor;

                            // ctx.fillStyle = ig.Lootbox.card.textColor;
                            ctx.font = levelTextSize + " " + ig.Lootbox.card.font;
                            ctx.textAlign = "center";
                            ctx.fillText(levelText, x + (levelWidth + levelRadius * 0.7) / 2, y + levelY + levelRadius * 0.4);

                            // ctx.fillRect(x + levelWidth + r, y + levelY - levelRadius / 2, this.width - levelWidth - r * 2, levelRadius)
                            ctx.save();
                            ctx.strokeStyle = ig.Lootbox.card.barEmptyColor;
                            ctx.lineWidth = r;
                            ctx.lineCap = "round";
                            var lineX = x + levelWidth + r;
                            var lineY = y + levelY;
                            var lineWidth = this.width - levelWidth - r * 2;
                            var fillWidth = Math.floor(progress * lineWidth);
                            ctx.beginPath();
                            ctx.moveTo(lineX, lineY)
                            ctx.lineTo(lineX + lineWidth, lineY)
                            ctx.stroke();

                            if (fillWidth < 1) fillWidth = 1;
                            ctx.strokeStyle = ig.Lootbox.card.barFillColor;
                            ctx.beginPath();
                            ctx.moveTo(lineX, lineY)
                            ctx.lineTo(lineX + fillWidth, lineY)
                            ctx.stroke();
                            // ctx.strokeRect(x + levelWidth + r, y + levelY - levelRadius / 2, this.width - levelWidth - r * 2, levelRadius)

                            ctx.restore()
                        }
                    } else {
                        ctx.fillStyle = ig.Lootbox.card.frontAccentColor;
                        ctx.beginPath();
                        ctx.arc(x + levelWidth, y + levelY, levelRadius, 0, Math.PI * 2);
                        ctx.fill();
                        ctx.closePath();
                        ctx.fillRect(x, y + levelY - levelRadius, levelWidth, levelRadius * 2)

                        ctx.fillStyle = ig.Lootbox.card.textColor;
                        ctx.font = levelTextSize + " " + ig.Lootbox.card.font;
                        ctx.textAlign = "center";
                        ctx.fillText(levelText, x + (levelWidth + levelRadius * 0.7) / 2, y + levelY + levelRadius * 0.4);
                    }

                }

                var nameWidth = 0.65 * this.width;
                var nameRadius = levelRadius;
                var nameX = x + (this.width - nameWidth) / 2;
                var nameY = y + 0.95 * this.height - r;
                var nameTextSize = Math.floor(nameRadius * 1) + "px";
                var nameText = this.isCustomCard ? this.customName : ig.Lootbox.card.names[this.id];
                var isNameDrawn = true;
                if (this.isNameOnTop) nameY = y + 0.1 * this.height + r
                if (this.isCustomCard && !this.customName) isNameDrawn = false;
                if (isNameDrawn) {
                    ctx.fillStyle = ig.Lootbox.card.frontAccentColor;
                    ctx.beginPath();
                    ctx.arc(nameX, nameY, nameRadius, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.closePath();

                    ctx.beginPath();
                    ctx.arc(nameX + nameWidth, nameY, nameRadius, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.closePath();

                    ctx.fillRect(nameX, nameY - nameRadius, nameWidth, nameRadius * 2)

                    ctx.fillStyle = ig.Lootbox.card.textColor;
                    ctx.font = nameTextSize + " " + ig.Lootbox.card.font;
                    ctx.textAlign = "center";
                    ctx.fillText(nameText, x + this.width / 2, nameY + nameRadius * 0.4);
                }



                if (this.isNameOnTop && this.customDescription) {
                    var descY = y + 0.95 * this.height - r;
                    ctx.fillStyle = ig.Lootbox.card.frontAccentColor;
                    ctx.font = nameTextSize + " " + ig.Lootbox.card.font;
                    ctx.textAlign = "center";
                    var lines = this.customDescription.split(/\r?\n/);
                    var lineHeight = Math.floor(nameRadius * 1.25);
                    var startY = descY - lineHeight * lines.length / 2;
                    for (var i = 0; i < lines.length; i++) {
                        var line = lines[i];
                        ctx.fillText(line, x + this.width / 2, startY + lineHeight * i);
                    }

                }

                if (this.isGreyedOut && this.drawFront) {
                    ig.system.context.globalAlpha = this.alpha * 0.3;
                    ctx.drawImage(ig.greyCanvas, 0, 0, ig.Lootbox.card.width * 2, ig.Lootbox.card.height * 2, x, y, this.width, this.height);
                    ig.system.context.globalAlpha = this.alpha
                }

                ctx.restore();
            },

            drawBack: function (x, y) {
                var ctx = ig.system.context;
                var r = ig.Lootbox.card.roundedRadius;
                ctx.save();
                ctx.fillStyle = ig.Lootbox.card.backAccentColor;
                ctx.fillRect(x + r, y, this.width - r * 2, this.height)
                ctx.fillRect(x, y + r, this.width, this.height - r * 2)

                ctx.beginPath();
                ctx.arc(x + r, y + r, r, 0, Math.PI * 2);
                ctx.fill();
                ctx.closePath();

                ctx.beginPath();
                ctx.arc(x + this.width - r, y + r, r, 0, Math.PI * 2);
                ctx.fill();
                ctx.closePath();

                ctx.beginPath();
                ctx.arc(x + r, y + this.height - r, r, 0, Math.PI * 2);
                ctx.fill();
                ctx.closePath();

                ctx.beginPath();
                ctx.arc(x + this.width - r, y + this.height - r, r, 0, Math.PI * 2);
                ctx.fill();
                ctx.closePath();

                var edge = Math.floor(this.width * 0.07);
                ctx.fillStyle = ig.Lootbox.card.backColor;
                ctx.fillRect(x + r + edge, y + edge, this.width - (r + edge) * 2, this.height - edge * 2)
                ctx.fillRect(x + edge, y + r + edge, this.width - edge * 2, this.height - (r + edge) * 2)

                var smallR = ((this.width - edge * 2) - (this.width - (r + edge) * 2)) / 2;
                // console.log(smallR)
                ctx.beginPath();
                ctx.arc(x + edge + smallR, y + edge + smallR, smallR, 0, Math.PI * 2);
                ctx.fill();
                ctx.closePath();

                ctx.beginPath();
                ctx.arc(x + this.width - edge - smallR, y + edge + smallR, smallR, 0, Math.PI * 2);
                ctx.fill();
                ctx.closePath();

                ctx.beginPath();
                ctx.arc(x + edge + smallR, y + this.height - edge - smallR, r, 0, Math.PI * 2);
                ctx.fill();
                ctx.closePath();

                ctx.beginPath();
                ctx.arc(x + this.width - edge - smallR, y + this.height - edge - smallR, smallR, 0, Math.PI * 2);
                ctx.fill();
                ctx.closePath();

                ctx.restore();
            },

            drawEmpty: function (x, y) {
                var ctx = ig.system.context;
                var r = ig.Lootbox.card.roundedRadius;
                ctx.save();
                ctx.fillStyle = ig.Lootbox.card.emptyColor;
                ctx.fillRect(x + r, y, this.width - r * 2, this.height)
                ctx.fillRect(x, y + r, this.width, this.height - r * 2)

                ctx.beginPath();
                ctx.arc(x + r, y + r, r, 0, Math.PI * 2);
                ctx.fill();
                ctx.closePath();

                ctx.beginPath();
                ctx.arc(x + this.width - r, y + r, r, 0, Math.PI * 2);
                ctx.fill();
                ctx.closePath();

                ctx.beginPath();
                ctx.arc(x + r, y + this.height - r, r, 0, Math.PI * 2);
                ctx.fill();
                ctx.closePath();

                ctx.beginPath();
                ctx.arc(x + this.width - r, y + this.height - r, r, 0, Math.PI * 2);
                ctx.fill();
                ctx.closePath();

                ctx.restore();
            }
        });
    });
