ig.module(
    'dl.templates.network-entities.entity-popup-select-arena'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.popup',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.animation-sheet',
    'dl.templates.mixins.text',
    'dl.templates.entities.buttons.entity-button-image-text',
    'dl.templates.entities.buttons.entity-button-image'
).defines(function () {
    'use strict';

    dl.EntityPopupSelectArena = dl.Entity
        .extend(dl.MixinPopup)
        .extend(dl.MixinDocker)
        .extend({
            postInit: function () {
                this.parent();

                var xx=window.innerWidth;
                var yy=window.innerHeight;
                if(ig.ua.mobile && !ig.ua.iPad){
                    if(xx<yy){ //portrait
                        var scale={x:0.8,y:0.8};
                    }else{  //landscape              
                        var scale={x:1,y:1};                       
                    }    
                }else{
                        var scale={x:1,y:1};
                     }      

                var width=1800;
                var height=1640;

                this.content = this.spawnEntity(dl.EntityPopupSelectArena_Content, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    _scale: scale,
                    _useParentScaleDynamic: true,
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:width,y:height}
                    },
                });
            }
        });

    dl.EntityPopupSelectArena_Content = dl.Entity
        .extend(dl.MixinPopupContent)
        .extend(dl.MixinDocker)
        .extend(dl.MixinAnimationSheet)
        .extend(dl.MixinText)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this.image = dl.preload['popup-large-wide-long'];

                this._useAnimationSheetAsSize = true;

                this.text = 'SELECT ARENA';

                return this;
            },
            updateOrientation:function(){
                var xx=window.innerWidth;
                var yy=window.innerHeight;
                if(ig.ua.mobile && !ig.ua.iPad){
                    if(xx<yy){ //portrait
                        var scale={x:0.8,y:0.8};
                    }else{  //landscape              
                        var scale={x:1,y:1};                       
                    }    
                }else{
                        var scale={x:1,y:1};
                     }      

                this._scale=scale;
                this.updateScale();
            },

            postInit: function () {
                this.parent();
                this.initImageArena();
                this.initText();
                this.initButtons();

                this.tweenIn();
            },
            initImageArena:function(){

                this.imgArena1frame = this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.17, y: 0.13 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:560,y:900}
                    },
                    anchor: { x: 0.5, y: 0 },
                    image: dl.preload['box-arena']
                });

                this.imgArena1 = this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.imgArena1frame,
                        dockerPercent: { x: 0.5, y: 0 },
                        dockerOffset: { x: 0, y: 130 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:500,y:360},
                    },
                    anchor: { x: 0.5, y: 0 },
                    image: dl.preload['soldier1']
                });


                this.imgArena2frame = this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.13 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:560,y:900}
                    },
                    anchor: { x: 0.5, y: 0 },
                    image: dl.preload['box-arena']
                });

                this.imgArena2 = this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.imgArena2frame,
                        dockerPercent: { x: 0.5, y: 0 },
                        dockerOffset: { x: 0, y: 130 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:500,y:360}
                    },
                    anchor: { x: 0.5, y: 0 },
                    image: dl.preload['soldier2']
                });

                this.imgArena3frame = this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.83, y: 0.13 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:560,y:900}
                    },
                    anchor: { x: 0.5, y: 0 },
                    image: dl.preload['box-arena']
                });

                this.imgArena3 = this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.imgArena3frame,
                        dockerPercent: { x: 0.5, y: 0 },
                        dockerOffset: { x: 0, y: 130 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:500,y:360}
                    },
                    anchor: { x: 0.5, y: 0 },
                    image: dl.preload['soldier3']
                });


                this.imgArena4frame = this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.5, y: 0.7 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:1750,y:440}
                    },
                    anchor: { x: 0.5, y: 0 },
                    image: dl.preload['box-arena2']
                });

                this.imgArena4 = this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.imgArena4frame,
                        dockerPercent: { x: 0, y: 0 },
                        dockerOffset: { x: 40, y: 40 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:500,y:360}
                    },
                    anchor: { x: 0, y: 0 },
                    image: dl.preload['soldier4']
                });


            },
            initText:function(){

                this.text1 = this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                    _useParentScale: true,
                        dockerObject: this.imgArena1frame,
                        dockerPercent: {x:0.5,y:0},
                        dockerOffset: { x: 0, y: 30 }
                    },
                    anchor: { x: 0.5, y: 0 },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                        textAlign: 'center', 
                        textBaseline: 'middle',
                        fontSize: 46,
                    },
                    text:'Fresh Meat'
                });

                this.text2 = this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.imgArena2frame,
                        dockerPercent: {x:0.5,y:0 },
                        dockerOffset: { x: 0, y:30}
                    },
                    anchor: { x: 0.5, y: 0 },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                        textAlign: 'center', 
                        textBaseline: 'middle',
                        fontSize: 46,
                    },
                    text:'Skilled Hands'
                });

                this.text3 = this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.imgArena3frame,
                        dockerPercent: {x:0.5,y:0},
                        dockerOffset: { x: 0, y: 30 }
                    },
                    anchor: { x: 0.5, y: 0 },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                        textAlign: 'center', 
                        textBaseline: 'middle',
                        fontSize: 46,
                    },
                    text:'Pro Gamer'
                });

                this.text4 = this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.imgArena4,
                        dockerPercent: {x:1,y:0},
                        dockerOffset: { x:50, y: 0 }
                    },
                    anchor: { x: 0, y: 0 },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                        textAlign: 'center', 
                        textBaseline: 'middle',
                        fontSize: 60,
                    },
                    text:'Private Battle'
                });


                this.detail1 = this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.imgArena1,
                        dockerPercent: {x:0.5,y:1},
                        dockerOffset: { x: 0, y: 40 }
                    },
                    anchor: { x: 0.5, y: 0 },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                        textAlign: 'left', 
                        textBaseline: 'middle',
                        fontSize: 40,
                    },
                    text:''
                });
                this.coin11 = this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.detail1,
                        dockerPercent: { x: 0.46, y: 0.146 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:50,y:50}
                    },
                    image: dl.preload['coin']
                });

                this.coin12 = this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.detail1,
                        dockerPercent: { x: 0.68, y: 0.82 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:50,y:50}
                    },
                    image: dl.preload['coin']
                });

                this.detail2 = this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.imgArena2,
                        dockerPercent: {x:0.5,y:1},
                        dockerOffset: { x: 0, y: 40 }
                    },
                    anchor: { x: 0.5, y: 0 },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                        textAlign: 'left', 
                        textBaseline: 'middle',
                        fontSize: 40,
                    },
                    text:''
                });

                this.coin21 = this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.detail2,
                        dockerPercent: { x: 0.46, y: 0.15 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:50,y:50}
                    },
                    image: dl.preload['coin']
                });

                this.coin22 = this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.detail2,
                        dockerPercent: { x: 0.68, y: 0.82 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:50,y:50}
                    },
                    image: dl.preload['coin']
                });
                this.detail3 = this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.imgArena3,
                        dockerPercent: {x:0.5,y:1},
                        dockerOffset: { x: 0, y: 40 }
                    },
                    anchor: { x: 0.5, y: 0 },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                        textAlign: 'left', 
                        textBaseline: 'middle',
                        fontSize: 40,
                    },
                    text:''
                });
                this.coin31 = this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.detail3,
                        dockerPercent: { x: 0.46, y: 0.15 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:50,y:50}
                    },
                    image: dl.preload['coin']
                });

                this.coin32 = this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.detail3,
                        dockerPercent: { x: 0.68, y: 0.82 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:50,y:50}
                    },
                    image: dl.preload['coin']
                });

                this.detail4 = this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.imgArena4,
                        dockerPercent: {x:1,y:0},
                        dockerOffset: { x:50, y: 120 }
                    },
                    anchor: { x: 0, y: 0 },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                        textAlign: 'left', 
                        textBaseline: 'middle',
                        fontSize: 40,
                    },
                    text:''
                });
                this.coin41 = this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.detail4,
                        dockerPercent: { x: 0.46, y: 0.14 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:50,y:50}
                    },
                    image: dl.preload['coin']
                });

                this.coin42 = this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.detail4,
                        dockerPercent: { x: 0.68, y: 0.82 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:50,y:50}
                    },
                    image: dl.preload['coin']
                });

            },
            _initTextComponent: function () {

                this.c_TextComponent.updateProperties({
                    fillStyle: '#f22547',
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                    textAlign: 'center', // [center|end|left|right|start];
                    textBaseline: 'middle', // [alphabetic|top|hanging|middle|ideographic|bottom];

                    fontSize: 56,

                    _docker: { x: 0.5, y: 0.03 },
                    _anchor: { x: 0.5, y: 0.5 },
                    _offset: { x: 0, y: 0 }
                });
            },
            delay:200,
            playerOnline1:0,
            update:function(){
                this.parent();

                if(this.delay>200){
                    this.delay=0;
                    this.playerOnline1=Utilities.randomInt(100, 200);
                    this.playerOnline2=Utilities.randomInt(150, 250);
                    this.playerOnline3=Utilities.randomInt(100, 300);
                    this.playerOnline4=Utilities.randomInt(100, 200);


                    if(ig.game.client.roomMaxPlayer==2){
                        if(this.detail1) this.detail1.updateText('Prize : 400 <br>Player Online : '+this.playerOnline1+' <br>Entry Fee : 200');
                        if(this.detail2) this.detail2.updateText('Prize : 1000 <br>Player Online : '+this.playerOnline2+' <br>Entry Fee : 500');
                        if(this.detail3) this.detail3.updateText('Prize : 5000 <br>Player Online : '+this.playerOnline3+' <br>Entry Fee : 2500');
                        if(this.detail4) this.detail4.updateText('Prize : 3000 <br>Player Online : '+this.playerOnline4+' <br>Entry Fee : 1500');
                    }else{
                        if(this.detail1) this.detail1.updateText('Prize :         800 <br>Player Online : '+this.playerOnline1+' <br>Entry Fee :         200');
                        if(this.detail2) this.detail2.updateText('Prize :         2000 <br>Player Online : '+this.playerOnline2+' <br>Entry Fee :         500');
                        if(this.detail3) this.detail3.updateText('Prize :         10000 <br>Player Online : '+this.playerOnline3+' <br>Entry Fee :         2500');
                        if(this.detail4) this.detail4.updateText('Prize :          6000 <br>Player Online : '+this.playerOnline3+' <br>Entry Fee :         1500');
                    }

                }
                this.delay ++;
            },
            initButtons: function () {

                this.findMatch1 = this.spawnEntity(dl.EntityButtonImageText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.detail1,
                        dockerPercent: { x: 0.5, y: 1 },
                        dockerOffset: { x: 0, y: 40 }
                    },
                    anchor: { x: 0.5, y: 0 },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:400,y:120}
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                        fontSize: 50,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    image: dl.preload['button-green'],
                    text: 'Find Match',
                    onButtonClicked: this.selectArena.bind(this, 1, 200)
                });

                this.findMatch2 = this.spawnEntity(dl.EntityButtonImageText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.detail2,
                        dockerPercent: { x: 0.5, y: 1 },
                        dockerOffset: { x: 0, y: 40 }
                    },
                    anchor: { x: 0.5, y: 0 },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:400,y:120}
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                        fontSize: 50,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    image: dl.preload['button-green'],
                    text: 'Find Match',
                    onButtonClicked: this.selectArena.bind(this, 2,500)
                });

                this.findMatch3 = this.spawnEntity(dl.EntityButtonImageText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.detail3,
                        dockerPercent: { x: 0.5, y: 1 },
                        dockerOffset: { x: 0, y: 40 }
                    },
                    anchor: { x: 0.5, y: 0 },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:400,y:120}
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                        fontSize: 50,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    image: dl.preload['button-green'],
                    text: 'Find Match',
                    onButtonClicked: this.selectArena.bind(this, 3,2500)
                });


                this.findMatch4 = this.spawnEntity(dl.EntityButtonImageText, {
                    _useParentScale: true,
                    c_DockerComponent:{ 
                        dockerObject: this.imgArena4frame,
                        dockerPercent: { x: 0.8, y: 0.5 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    anchor: { x: 0.5, y: 0.5 },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:500,y:120}
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                        fontSize: 50,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    image: dl.preload['button-green'],
                    text: 'Setup / Join',
                    onButtonClicked: this.selectArena.bind(this, 4,1500)
                });


                this.leaderboard1 = this.spawnEntity(dl.EntityButtonImageText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.2, y: 0.08 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                        fontSize: 40,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:340,y:80}
                    },
                    image: dl.preload['button-red'],
                    text: 'The Rich List',
                    onButtonClicked: this.showLeaderboard.bind(this, 1)
                });


                this.leaderboard2 = this.spawnEntity(dl.EntityButtonImageText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.4, y: 0.08 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                        fontSize: 40,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:340,y:80}
                    },
                    image: dl.preload['button-red'],
                    text: 'Most Wins',
                    onButtonClicked: this.showLeaderboard.bind(this, 2)
                });

                this.leaderboard3 = this.spawnEntity(dl.EntityButtonImageText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.6, y: 0.08 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                        fontSize: 40,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:340,y:80}
                    },
                    image: dl.preload['button-red'],
                    text: 'Most Losses',
                    onButtonClicked: this.showLeaderboard.bind(this, 3)
                });

                this.leaderboard4 = this.spawnEntity(dl.EntityButtonImageText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 0.8, y: 0.08 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'BUTTON_TEXT'),
                        fontSize: 40,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:340,y:80}
                    },
                    image: dl.preload['button-red'],
                    text: 'Best Win Rate',
                    onButtonClicked: this.showLeaderboard.bind(this, 4)
                });



                this.btnClose = this.spawnEntity(dl.EntityButtonImage, {
                    c_DockerComponent: {
                        dockerObject: this,
                        dockerPercent: { x: 1, y: 0 },
                        dockerOffset: { x: -10, y: 10 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:100,y:100}
                    },
                    anchor: { x: 1, y: 0 },
                    image: dl.preload['popup-button-close'],
                    onButtonClicked: function () {
                        ig.game.client.roomArena = 0;
                        this.tweenOut(function () {
                            this.kill();
                            // ig.game.title.notifdot.alpha=ig.game.title.notifdot.tempAlpha;                            
                        }.bind(this.parentInstance));
                    }.bind(this)
                });
            },
            showLeaderboard: function (boardId) {
                if(boardId==1){
                    try {
                        ig.svas.showLeaderboard("virtual_currency1");
                    } catch(e){}                                
                }else if(boardId==2){
                    try {
                        ig.svas.showLeaderboard("stats_wins");
                    } catch(e){}                                
                }else if(boardId==3){
                    try {
                        ig.svas.showLeaderboard("stats_losses");
                    } catch(e){}                                
                }else if(boardId==4){
                    try {
                        ig.svas.showLeaderboard("stats_win_rate");
                    } catch(e){}                                
                }                
            },
            selectArena: function (arenaId,minCoins) {
                switch (arenaId) {
                    case 1:
                        if(ig.game.svasData.coins<minCoins){
                            this.showNotification();
                            return;
                        }else{
                            ig.game.client.roomArena = 1;
                        }
                        break;

                    case 2:
                        if(ig.game.svasData.coins<minCoins){
                            this.showNotification();
                            return;
                        }else{
                            ig.game.client.roomArena = 2;
                        }
                        break;

                    case 3:
                        if(ig.game.svasData.coins<minCoins){
                            this.showNotification();
                            return;
                        }else{
                            ig.game.client.roomArena = 3;
                        }
                        break;

                    case 4:
                        if(ig.game.svasData.coins<minCoins){
                            this.showNotification();
                            return;
                        }else{
                            ig.game.client.roomArena = 4;
                        }
                        break;
                }
                ig.game.arenaId=arenaId;
                this.tweenOut(function () {
                    this.kill();
                }.bind(this.parentInstance));
            },
            showNotification:function(){

                dl.scene.spawnNotification({
                    c_DockerComponent: {
                            dockerObject: dl.game.camera,
                            dockerPercent: { x: 0.5, y: 0.08},
                            dockerOffset: { x: 0, y: 0 }
                        },
                    notificationDrawConfigs: {
                        contentConfigs: [{
                                type: 'text',
                                text: "You don't have enough coins !!!",
                                fillStyle: dl.configs.TEXT_COLOR.BUTTON_TEXT,
                                fontSize: 46,
                                fontFamily: dl.configs.FONT.SOURCE_SANS.name
                            }],
                        backgroundConfigs: {
                            lineWidth: 2,
                            fillStyle: dl.configs.NOTIFICATION_COLORS.PLAYER_1,
                            strokeStyle: dl.configs.NOTIFICATION_COLORS.PLAYER_1,
                            box: { width: 800, height: 90, round: 10, padding: { x: 100, y: 5 } }
                        },
                    }
                });

            },
            tweenIn: function (callback) {
                this.parent(callback);
            },

            tweenOut: function (callback) {
                this.btnClose.setEnable(false);
                this.findMatch1.setEnable(false);
                this.findMatch2.setEnable(false);
                this.findMatch3.setEnable(false);

                this.leaderboard1.setEnable(false);
                this.leaderboard2.setEnable(false);
                this.leaderboard3.setEnable(false);
                this.leaderboard4.setEnable(false);

                this.parent(callback);
            }
        });
});
