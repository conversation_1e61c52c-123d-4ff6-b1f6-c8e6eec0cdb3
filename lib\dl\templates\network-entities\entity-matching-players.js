ig.module(
    'dl.templates.network-entities.entity-matching-players'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'dl.templates.network-entities.name-and-avatar.entity-name-avatar-matching'
).defines(function () {
    'use strict';

    dl.EntityMatchingPlayers = dl.Entity
        .extend(dl.MixinDocker)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this.totalPlayer = ig.game.client.roomMaxPlayer;
                this.players = [];
                ig.game.playerMatched =[];
                return this;
            },

            postInit: function () {
                this.parent();

                this.initPlayers();
            },

            initPlayers: function () {
                this.players = [];

                for (var i = 0; i < this.totalPlayer; i++) {
                    var player = this.spawnEntity(dl.EntityNameAvatarMatching, {control:this});
                    this.players.push(player);
                }

                this.onUpdateOrientation();
            },

            onUpdateOrientation: function () {
                this.parent();

                if (!this.players) return;
                if (!this.players.length) return;

                var size = 600;
                var mult = 1;
                if (ig.sizeHandler.isPortraitOrientation) {
                    var rowData = [2, 2]; // number of col in row
                    var offset = { x: 0.25, y: 0.25 };

                    if(ig.ua.mobile && !ig.ua.iPad){
                        switch (this.totalPlayer) {
                            case 2:
                                rowData = [2];
                                offset = { x: 0.5, y: 0.5 };
                                break;

                            case 4:
                            default:
                                rowData = [2, 2];
                                offset = { x: 0.5, y: 0.5 };
                                break;
                        }
                    }else{
                        switch (this.totalPlayer) {
                            case 2:
                                rowData = [2];
                                offset = { x: 0.5, y: 0.5 };
                                break;

                            case 4:
                            default:
                                rowData = [4];
                                offset = { x: 0.16, y: 0.4};
                                mult = 1.5;
                                break;
                        }
                    }

                    var maxCol = 0;
                    var index = -1;
                    for (var row = 0, rowLength = rowData.length; row < rowLength; row++) {
                        for (var col = 0, colLength = rowData[row]; col < colLength; col++) {
                            var startOffset = {
                                x: (-offset.x * colLength + offset.x) * 0.5,
                                y: (-offset.y * rowLength + offset.y) * 0.5
                            };
                            index += 1;

                            var player = this.players[index];
                            player.c_DockerComponent.updateProperties({
                                dockerObject: this,
                                dockerPercent: {
                                    x: 0.5 + startOffset.x + offset.x * col,
                                    y: 0.5 + startOffset.y + offset.y * row
                                },
                                dockerOffset: { x: 0, y: 0 }
                            });

                            if (maxCol < colLength) {
                                maxCol = colLength;
                            }
                        }
                    }

                    this.setSize(size * maxCol, size*mult * rowData.length);
                } else {

                    var offsetX = 0.25;

                    switch (this.totalPlayer) {
                        case 2:
                            offsetX = 0.5;
                            break;

                        case 3:
                            offsetX = 0.33;
                            break;

                        case 4:
                        default:
                            offsetX = 0.25;
                            break;
                    }
                    var startOffsetX = (-offsetX * this.totalPlayer + offsetX) * 0.5;

                    for (var i = 0; i < this.totalPlayer; i++) {
                        var player = this.players[i];
                        player.c_DockerComponent.updateProperties({
                            dockerObject: this,
                            dockerPercent: { x: 0.5 + startOffsetX + offsetX * i, y: 0.5 },
                            dockerOffset: { x: 0, y: 0 }
                        });
                    }
                    this.setSize(size * this.totalPlayer, size*1.1);
                }
            },
            // updateDataCount:0,
            updateData: function (data) {
                // this.updateDataCount +=1;
                // if(this.updateDataCount % 10 !==0) return
                // if(this.updateDataCount>50) this.updateDataCount=0;

                if (!data) return;

                ig.game.playerMatched=data.playerList;
                for (var i = 0; i < this.totalPlayer; i++) {
                    var playerData = data.playerList[i];
                    this.players[i].updateData(playerData);

                    // update matching level buttons
                    if (!playerData) continue;
                    var isMe = ig.game.client.isMyPlayerId(playerData.playerId);
                    if (isMe) {
                        this.parentInstance.updateButtonsData(playerData);
                    }
                }
            },

            disableAll: function () {
                for (var i = 0; i < this.totalPlayer; i++) {
                    this.players[i].disableButton();
                }
            },

            tweenIn: function (time) {
                dl.TweenTemplate.fadeIn(this, time);
                for (var i = 0; i < this.totalPlayer; i++) {
                    var player = this.players[i];
                    player.tweenIn(time);
                }
            },

            tweenOut: function (time) {
                dl.TweenTemplate.fadeOut(this, time);
                for (var i = 0; i < this.totalPlayer; i++) {
                    var player = this.players[i];
                    player.tweenOut(time);
                }
            }
        });

    // Enable cache
    dl.enableCacheCanvas(dl.EntityMatchingPlayers);
});
