ig.module(
    'dl.templates.network-entities.entity-room-code'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.animation-sheet',
    'dl.templates.mixins.text'
).defines(function () {
    'use strict';

    dl.EntityRoomCode = dl.Entity
        .extend(dl.MixinDocker)
        .extend(dl.MixinAnimationSheet)
        .extend(dl.MixinText)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this._useAnimationSheetAsSize = true;
                this.image = dl.preload['room-code-background'];
                this.updatePos();

                return this;
            },

            postInit: function () {
                this.parent();

                if (ig.game.client.roomPassword) {
                    var server=SERVER_CONFIGS.SERVER_IPS_NAME[ig.game.defaultServer]
                    this.setRoomCode(ig.game.client.roomPassword,server);
                } else {
                    this.setRoomCode('error');
                }
            },

            _initTextComponent: function () {

                this.c_AnimationSheetComponent.updateProperties({
                    _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                    _size: {x:600,y:160}
                });

                this.c_TextComponent.updateProperties({
                    fillStyle: "#000000",
                    fontSize: 36,
                    fontFamily: dl.configs.getConfig('FONT', 'bold').name
                });
            },

            setRoomCode: function (roomCode,server) {

                this.updateText('You are in private room<br>Invite Code: '+roomCode+'<br>Server: '+server);
                // this.updateText(_STRINGS.NETWORK.ROOM_CODE.replace('<replace1>', roomCode));
                
            }
        });

    // Enable cache
    dl.enableCacheCanvas(dl.EntityRoomCode);
});
