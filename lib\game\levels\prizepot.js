ig.module(
    'game.levels.prizepot'
).requires(
    'dl.templates.entities.entity-level',
    'dl.templates.mixins.docker',
    'dl.templates.entities.backgrounds.entity-color-background',
    'dl.templates.entities.entity-text',
    'dl.templates.network-entities.entity-matching-prizepot'
).defines(function () {
    LevelPrizePot = {
        entities: [
            { type: 'EntityPrizePotController', x: 0, y: 0 }
        ],
        layer: [],
        useCustomLib: true
    };

    EntityPrizePotController = dl.EntityLevel
        .extend({
            postInit: function () {
                this.parent();
                this.initEntities();
                // this.tweenIn();
            },

            initEntities: function () {
                this.initBackgroundAndTitle();
                this.initPlayerInfo();

                window.prizepot = this;
            },

            initBackgroundAndTitle: function () {
                this.background = this.spawnEntity(dl.EntityColorBackground, {});

                this.title = this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    orientationData: {
                        portrait: {
                            _orientationScale: { x: 0.75, y: 0.75 }
                        },
                        landscape: {
                            _orientationScale: { x: 1, y: 1 }
                        }
                    },
                    c_DockerComponent: {
                        dockerObject: dl.game.camera,
                        dockerPercent: { x: 0.5, y: 0.1 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_TextComponent: {
                        fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                        // fillStyle: '#f22547',
                        fontSize: 100,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    text: 'Reward Pot'
                });

                // dl.TweenTemplate.scaleOutIn(this.title, dl.configs.getConfig('TEXT_CHANGE_TWEEN_TIME'), function () {
                // }.bind(this));

                if(ig.game.showFPS){            
                    this.fps = this.spawnEntity(dl.EntityText, {
                        _useParentScale: true,
                        c_DockerComponent: {
                            dockerObject: dl.game.camera,
                            dockerPercent: { x: 0, y: 0.3 },
                            dockerOffset: { x: 50, y: 0 }
                        },
                        c_TextComponent: {
                            fillStyle: dl.configs.getConfig('TEXT_COLOR', 'DEFAULT'),
                            fontFamily: dl.configs.getConfig('FONT', 'bold').name,
                            fontSize: 40
                        },
                        anchor:{x:0,y:0},
                        text:ig.system.fps.toFixed(0)
                    });
                }
            },
            initCrateImage:function(){
                this.crateImg=this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: dl.game.camera,
                        dockerPercent: { x: 0.5, y: 0.35 },
                        dockerOffset: { x: 0, y: 0 }
                    },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:500,y:440}
                    },
                    image: dl.preload['chestclose']
                })
                // dl.TweenTemplate.fadeIn(this.crateImg,1000);                

                this.crateCoin=this.spawnEntity(dl.EntityImage, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.crateImg,
                        dockerPercent: { x: 0.1, y: 0 },
                        dockerOffset: { x: 0, y: -80 }
                    },
                    anchor: { x: 0, y: 0.5 },
                    c_AnimationSheetComponent: {
                        _drawType: dl.AnimationSheetComponent.DRAW_SIZE.CUSTOM,
                        _size: {x:100,y:100}
                    },
                    image: dl.preload['coin']
                });

                // dl.TweenTemplate.scaleIn(this.crateCoin, 1000);

                this.crateEntryFee=this.spawnEntity(dl.EntityText, {
                    _useParentScale: true,
                    c_DockerComponent: {
                        dockerObject: this.crateImg,
                        dockerPercent: { x: 0.4, y: 0 },
                        dockerOffset: { x: 0, y: -80 }
                    },
                    anchor: { x: 0, y: 0.5 },
                    c_TextComponent: {
                        fontSize: 70,
                        fontFamily: dl.configs.getConfig('FONT', 'bold').name
                    },
                    text: 0
                });
                // dl.TweenTemplate.scaleIn(this.crateEntryFee, 1000);


            },
            update:function(){
                this.parent(); 
                if(ig.game.showFPS){            
                    this.fps.updateText(ig.system.fps.toFixed(0)+' fps');
                }                
            },

            initPlayerInfo: function () {
                this.matchingPlayers = this.spawnEntity(dl.EntityMatchingPrizePot, {
                    _useParentScale: true,
                    orientationData: {
                        landscape: {
                            c_DockerComponent: {
                                dockerObject: dl.game.camera,
                                dockerPercent: { x: 0.5, y: 0.5 },
                                dockerOffset: { x: 0, y: 0 }
                            }
                        },
                        portrait: {
                            c_DockerComponent: {
                                dockerObject: dl.game.camera,
                                dockerPercent: { x: 0.5, y: 0.57 },
                                dockerOffset: { x: 0, y: 0 }
                            }
                        }
                    },
                    control:this
                });  

                var xx=window.innerWidth;
                var yy=window.innerHeight;
                if(ig.ua.mobile){
                    if(xx<yy){
                        var targetY=0.7;
                    }else{
                        var targetY=0.8;
                    }
                }else{
                    var targetY=0.8;
                }

                this.matchingPlayers.createTween()
                    .to({
                        c_DockerComponent: {dockerPercent:{y:targetY}} 
                    }, 700, {
                        easing: ig.Tween.Easing.Cubic.EaseOut,
                        onPropertiesChanged: function () {
                            this.updatePos();
                        }.bind(this.matchingPlayers)
                    })
                    .start({
                        onCompleteTween: function () {
                             this.showCrate();
                        }.bind(this.matchingPlayers)
                    });                

            },

            tweenIn: function (callback) {
                var time = dl.configs.getConfig('MATCHING_LEVEL_TWEEN_TIME', 'IN');
                // dl.TweenTemplate.fadeIn(this.title, time);
            },

            tweenOut: function (callback) {
                var time = dl.configs.getConfig('MATCHING_LEVEL_TWEEN_TIME', 'OUT');
                // dl.TweenTemplate.fadeOut(this.title, time, callback);
            }
        });
});
