/**
 * Created by <PERSON><PERSON>
 * Usage:
 * .extend(dl.MyGameExtends);
 */

ig.module(
    'dl.my-game-extends'
).requires(
    'dl.dl',
    // load old engine
    'impact.game',
    // data
    'dl.data.configs',
    'dl.data.preload',
    // core
    'dl.core.system',
    'dl.game.camera',
    'dl.game.pointer',
    'dl.game.input-handler',
    'dl.game.entity.components.collision.checkers.pointer',
    'dl.game.entity.components.collision.checkers.entities',
    // injects
    'dl.game.utilities.entities-disable',
    'dl.debug.debugger'
).defines(function () {
    'use strict';

    /**
     * Add mixins to prototype
     */
    ig.Game.inject(dl.EventHandlerMixin);

    /**
     * Extends game
     */
    dl.MyGameExtends = {
        entitiesBackward: [],
        /**
         * overriding MyGame
         */
        finalize: function () {
            dl.game = this;

            this.dlInit();
            this.parent();
            if (dl.debug) {
                dl.debug.enableDebugFeature();
            }
        },

        /**
         * overriding MyGame
         */
        initData: function () {
            var parentData = this.parent();

            if (typeof parentData.music === 'undefined') parentData.music = true;
            if (typeof parentData.sound === 'undefined') parentData.sound = true;

            return parentData;
        },
        dlInit: function () {
            this.camera = new dl.Camera();
            this.pointer = new dl.Pointer();
            this.inputHandler = new dl.InputHandler();

            this.loadData();
        },

        loadData: function () {
            var music = ig.game.sessionData.music ? 1 : 0;
            var sound = ig.game.sessionData.sound ? 1 : 0;

            ig.soundHandler.bgmPlayer.volume(music);
            ig.soundHandler.sfxPlayer.volume(sound);
        },

        /**
         * overriding ig.Game
         */
        loadLevel: function (data) {
            ig.game.useCustomLib = !!(data && data.useCustomLib);

            this.camera.reset();
            dl.system.clearEvent();
            dl.game.clearEvent();
            dl.game.camera.clearEvent();
            this.parent(data);
        },

        /**
         * overloading ig.Game
         */
        run: function () {
            if (ig.game.useCustomLib) {
                // handle input
                this.pointer.update();
                this.inputHandler.update();
                this.camera.update();

                // updates
                this.update();
                this.lateUpdate();

                // draw
                this.draw();
            } else {
                this.parent();
            }

            // this.dctf();
        },

        /**
         * overloading ig.Game
         */

        updateCollision: function () {
            // check all entities collision with each other
            dl.EntitiesCollisionChecker.checkCollisions(this.entities);

            // check pointer collision with all entities
            if (this.pointer) {
                this.pointer.checkCollisionEntities(this.entities);
            }
        },

        lateUpdate: function () {
            this.updateCollision();

            for (var i = 0; i < this.entities.length; i++) {
                var entity = this.entities[i];
                if (!entity.killed && entity.lateUpdate) {
                    entity.lateUpdate();
                }
            }
        },
        updateDataCount:0,
        updateOrientation: function () {
            this.updateDataCount +=1;
            if(this.updateDataCount % 10 !==0) return
            if(this.updateDataCount>50) this.updateDataCount=0;

            if (!ig.game.useCustomLib) return;

            for (var i = 0; i < this.entities.length; i++) {
                var entity = this.entities[i];
                if (!entity.killed && typeof(entity.updateOrientation)=='function') {
                    entity.updateOrientation();
                }
            }
            if(ig.alliance){
                ig.alliance.updateOrientation();
            }
        },

        /**
         * overloading ig.Game
         */
        // drawCount:0,
        draw: function () {
            // if(ig.ua.mobile){
            //     this.drawCount +=1;
            //     if((this.drawCount % 2)!==0) return;
            //     if(this.drawCount>10) this.drawCount=0;
            // }

            if (ig.game.useCustomLib) {
                // clear screen
                // dl.system.clear('grey');

                // get context
                // var ctx = dl.system.context;
                var ctx = ig.system.context;

                // ctx.save();
                ctx.resetTransform();
                // ctx.fillStyle = 'grey';
                // ctx.restore();

                ctx.save();
                // translate context with camera
                if (this.camera) {
                    ctx = this.camera.translateContext(ctx);
                }

                // draw game
                this._drawEntities(ctx);
                // draw pointer
                this.pointer.draw(ctx);
                ctx.restore();

                this.updateBackward();
                this.drawBackward();
            } else {
                this.parent();
            }
        },

        _drawEntities: function (ctx) {
            if (!ctx) return;

            for (var i = 0; i < this.entities.length; i++) {
                var entity = this.entities[i];
                if (entity.killed) continue;
                if (!entity.visible) continue;

                if (entity.alpha != 1) {
                    ctx.save();
                    ctx.globalAlpha = entity.alpha;
                    entity.draw(ctx);
                    ctx.restore();
                } else {
                    entity.draw(ctx);
                }
            }
        },

        /**
         * Overloading MyGame
         */
        pauseGame: function () { },

        /**
         * Overloading MyGame
         */
        resumeGame: function () { },


        update: function () {


            // load new level?
            if (this._levelToLoad) {
                this.loadLevel(this._levelToLoad);
                this._levelToLoad = null;
            }

            // update entities
            this.updateEntities();
            this.checkEntities();

            // remove all killed entities
            for (var i = 0; i < this._deferredKill.length; i++) {
                this._deferredKill[i].erase();
                this.entities.erase(this._deferredKill[i]);
            }
            this._deferredKill = [];

            // sort entities?
            if (this._doSortEntities || this.autoSort) {
                this.sortEntities();
                this._doSortEntities = false;
            }

            // update background animations
            for (var tileset in this.backgroundAnims) {
                var anims = this.backgroundAnims[tileset];
                for (var a in anims) {
                    anims[a].update();
                }
            }

            if (ig.game.useCustomLib) {
                for (var i = 0; i < this.entities.length; i++) {
                    var entity = this.entities[i];
                    if (!entity.killed) {
                        entity.update();
                    }
                }

                // Modified timer utility to handle multiple timers
                for (var i = 0; i < this.timerUtil.length; i++) {
                    if (this.timerUtil[i].time && this.timerUtil[i].time.delta() > 0) {
                        this.timerUtil[i].time = null;
                        if (typeof this.timerUtilCallback[i] == 'function') {
                            this.timerUtilCallback[i]();
                            this.timerUtilCallback[i] = null;
                        }
                    }
                }

                this.timerUtil = this.timerUtil.filter(function (value) {
                    return value.time != null;
                });

                this.timerUtilCallback = this.timerUtilCallback.filter(function (value) {
                    return value != null;
                });

                // delayed spawner
                for (var i = 0; i < this.spawnDelayUtil.length; i++) {
                    if (this.spawnDelayUtil[i].time && this.spawnDelayUtil[i].time.delta() > 0) {
                        this.spawnDelayUtil[i].time = null;
                        if (typeof this.spawnDelayUtilCallback[i].callback == 'function') {
                            this.spawnDelayUtilCallback[i].callback();
                            this.spawnDelayUtilCallback[i].callback = null;
                        }
                    }
                }

                this.spawnDelayUtil = this.spawnDelayUtil.filter(function (value) {
                    return value.time != null;
                });

                this.spawnDelayUtilCallback = this.spawnDelayUtilCallback.filter(function (value) {
                    return value.callback != null;
                });
            } else {
                this.parent();
            }
        },

        spawnEntityBackward: function (type, x, y, settings) {
            var entityClass = typeof (type) === 'string'
                ? ig.global[type]
                : type;

            if (!entityClass) {
                throw ("Can't spawn entity of type " + type);
            }
            var ent = new (entityClass)(x, y, settings || {});
            this.entitiesBackward.push(ent);
            return ent;
        },

        updateBackward: function () {
            // Insert all entities into a spatial hash and check them against any
            // other entity that already resides in the same cell. Entities that are
            // bigger than a single cell, are inserted into each one they intersect
            // with.

            // A list of entities, which the current one was already checked with,
            // is maintained for each entity.

            var hash = {};
            for (var e = 0; e < this.entitiesBackward.length; e++) {
                var entity = this.entitiesBackward[e];

                // Skip entities that don't check, don't get checked and don't collide
                if (
                    entity.type == ig.Entity.TYPE.NONE &&
                    entity.checkAgainst == ig.Entity.TYPE.NONE &&
                    entity.collides == ig.Entity.COLLIDES.NEVER
                ) {
                    continue;
                }

                var checked = {},
                    xmin = Math.floor(entity.pos.x / this.cellSize),
                    ymin = Math.floor(entity.pos.y / this.cellSize),
                    xmax = Math.floor((entity.pos.x + entity.size.x) / this.cellSize) + 1,
                    ymax = Math.floor((entity.pos.y + entity.size.y) / this.cellSize) + 1;

                for (var x = xmin; x < xmax; x++) {
                    for (var y = ymin; y < ymax; y++) {

                        // Current cell is empty - create it and insert!
                        if (!hash[x]) {
                            hash[x] = {};
                            hash[x][y] = [entity];
                        }
                        else if (!hash[x][y]) {
                            hash[x][y] = [entity];
                        }

                        // Check against each entity in this cell, then insert
                        else {
                            var cell = hash[x][y];
                            for (var c = 0; c < cell.length; c++) {

                                // Intersects and wasn't already checkd?
                                if (entity.touches(cell[c]) && !checked[cell[c].id]) {
                                    checked[cell[c].id] = true;
                                    ig.Entity.checkPair(entity, cell[c]);
                                }
                            }
                            cell.push(entity);
                        }
                    } // end for y size
                } // end for x size
            } // end for entities



            for (var i = 0; i < this.entitiesBackward.length; i++) {
                var ent = this.entitiesBackward[i];
                if (!ent._killed) {
                    ent.update();
                }
            }

            this.entitiesBackward = this.entitiesBackward.filter(function (entity) {
                return !entity._killed;
            });
        },

        sortEntities: function () {
            this.parent();

            this.entitiesBackward.sort(this.sortBy);
        },

        drawBackward: function () {
            for (var i = 0; i < this.entitiesBackward.length; i++) {
                this.entitiesBackward[i].draw();
            }
        },
    };
});
