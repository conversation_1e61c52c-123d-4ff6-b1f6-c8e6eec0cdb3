ig.module('plugins.achievement.achievement-reward-fx')
    .requires(
        'plugins.achievement.achievement-rounded-button'
    )
    .defines(function () {
        ig.AchievementRewardFx = ig.AchievementRoundedButton.extend({

            zIndex: 9999,
            usePressedTween: false,
            shadowDistance: 0,

            init: function (x, y, settings) {
                this.parent(x, y, settings);
                this.timedLife = ig.Achievement.popup.item.rewardEffect.timedLife;
                this.upSpeed = ig.Achievement.popup.item.rewardEffect.upSpeed;
                this.width = ig.Achievement.popup.item.rewardEffect.width;
                this.height = ig.Achievement.popup.item.rewardEffect.height;
                this.color = ig.Achievement.popup.item.rewardEffect.color;
                this.textColor = ig.Achievement.popup.item.rewardEffect.textColor;
                this.font = ig.Achievement.popup.item.rewardEffect.font;
            },

            exit: function () {
                this.parent();
            },

            update: function () {
                this.parent();
                if (ig.responsive) {
                    this.anchoredPositionY -= this.upSpeed * ig.system.tick;
                } else {
                    this.pos.y -= this.upSpeed * ig.system.tick;
                }
            },

            draw: function () {
                this.parent();
            },

        });
    });
