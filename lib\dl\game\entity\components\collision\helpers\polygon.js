ig.module(
    'dl.game.entity.components.collision.helpers.polygon'
).requires(
    'dl.game.entity.components.collision.helpers.point',
    'dl.game.entity.components.collision.helpers.segment'
).defines(function () {
    'use strict';

    dl.CollisionHelpers_Polygon = {
        /**
         * polygon-point collision
         * @param {array} polygon array of point({x:0,y:0}) in polygon
         * @param {number} px of point
         * @param {number} py of point
         * @return {boolean}
         */
        point: function (polygon, px, py) {
            return dl.CollisionHelpers_Point.polygon(px, py, polygon);
        },

        /**
         * rectangle-polygon collision
         * @param {number} x top-left corner of rectangle
         * @param {number} y top-left corner of rectangle
         * @param {number} w width of rectangle
         * @param {number} h height of rectangle
         * @param {array} polygon array of point({x:0,y:0}) in polygon
         * @return {boolean}
         */
        rectangle: function (polygon, x, y, w, h) {
            var rectPolygon = [
                { x: x, y: y },
                { x: x + w, y: y },
                { x: x + w, y: y + h },
                { x: x, y: y + h }
            ];
            return this.polygon(polygon, rectPolygon);
        },

        /**
         * polygon-circle collision
         * @param {array} polygon array of point({x:0,y:0}) in polygon
         * @param {number} cx center x of circle
         * @param {number} cy center y of circle
         * @param {number} cr radius of circle
         * @return {boolean}
         */
        circle: function (polygon, cx, cy, cr) {
            if (this.point(polygon, cx, cy)) {
                return true;
            }

            for (var i = 0; i < polygon.length - 1; i++) {
                if (dl.CollisionHelpers_Segment.circle(
                    polygon[i].x, polygon[i].y,
                    polygon[i + 1].x, polygon[i + 1].y,
                    cx, cy, cr)) {
                    return true;
                }
            }
            return dl.CollisionHelpers_Segment.circle(
                polygon[0].x, polygon[0].y,
                polygon[polygon.length - 1].x, polygon[polygon.length - 1].y,
                cx, cy, cr);
        },

        /**
         * polygon-polygon collision
         * convex polygon
         * http://stackoverflow.com/questions/10962379/how-to-check-intersection-between-2-rotated-rectangles
         * @param {array} polygon1 array of point({x:0,y:0}) in polygon 1
         * @param {array} polygon2 array of point({x:0,y:0}) in polygon 2
         * @return {boolean}
         */
        polygon: function (polygon1, polygon2) {
            var polygons = [polygon1, polygon2];
            var minA, maxA, projected, minB, maxB;

            for (var i = 0; i < polygons.length; i++) {
                // for each polygon, look at each edge of the polygon, and determine if it separates
                // the two shapes
                var polygon = polygons[i];
                for (var i1 = 0; i1 < polygon.length; i1++) {
                    // grab 2 vertices to create an edge
                    var i2 = (i1 + 1) % polygon.length;
                    var p1 = polygon[i1];
                    var p2 = polygon[i2];

                    // find the line perpendicular to this edge
                    var normal = { x: p2.y - p1.y, y: p1.x - p2.x };

                    minA = maxA = undefined;
                    // for each vertex in the first shape, project it onto the line perpendicular to the edge
                    // and keep track of the min and max of these values
                    for (var j = 0; j < polygon1.length; j++) {
                        projected = normal.x * polygon1[j].x + normal.y * polygon1[j].y;
                        if (!dl.check.isDefined(minA) || projected < minA) {
                            minA = projected;
                        }
                        if (!dl.check.isDefined(maxA) || projected > maxA) {
                            maxA = projected;
                        }
                    }

                    // for each vertex in the second shape, project it onto the line perpendicular to the edge
                    // and keep track of the min and max of these values
                    minB = maxB = undefined;
                    for (var j = 0; j < polygon2.length; j++) {
                        projected = normal.x * polygon2[j].x + normal.y * polygon2[j].y;
                        if (!dl.check.isDefined(minB) || projected < minB) {
                            minB = projected;
                        }
                        if (!dl.check.isDefined(maxB) || projected > maxB) {
                            maxB = projected;
                        }
                    }

                    // if there is no overlap between the projects, the edge we are looking at separates the two
                    // polygons, and we know there is no overlap
                    if (maxA < minB || maxB < minA) {
                        return false;
                    }
                }
            }

            return true;
        }
    };
});
