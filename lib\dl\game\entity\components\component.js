ig.module(
    'dl.game.entity.components.component'
).requires(

).defines(function () {
    'use strict';

    dl.EntityComponent = ig.Class.extend({
        staticInstantiate: function (entity) {
            this._componentId = ++dl.EntityComponent.LAST_COMPONENT_ID;
            this.setComponentName();

            this.entity = entity;
            // state
            this.enable = true;

            return this;
        },

        setComponentName: function () {
            this._componentName = 'dl_EntityComponent';
        },

        init: function (settings) {
            this.updateProperties(settings);
        },

        update: function () { },

        lateUpdate: function () { },

        draw: function (ctx) { },

        updateProperties: function (properties) {
            if (dl.check.isEmptyObject(properties)) return;

            ig.merge(this, properties);
            this.onPropertiesChanged(properties);
        },

        onPropertiesChanged: function (properties) {
            // call when properties changed
            // implement in child class
        },

        onDrawChanged: function () {
            this.entity.triggerEvent('componentDrawChanged');
        }
    });

    dl.EntityComponent.LAST_COMPONENT_ID = 0;
    /**
     * Add mixins
     */
    dl.EntityComponent.inject(dl.EventHandlerMixin);
});
