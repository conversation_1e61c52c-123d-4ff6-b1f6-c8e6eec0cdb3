ig.module('plugins.achievement.achievement-plugin')
    .requires(
        'plugins.achievement.achievement-settings',
        'plugins.achievement.achievement-game-object',
        'plugins.achievement.achievement-rounded-rect',
        'plugins.achievement.achievement-rounded-button',
        'plugins.achievement.achievement-item',
        'plugins.achievement.achievement-reward-fx',
        'plugins.achievement.achievement-popup',
        'plugins.achievement.achievement-notification-drop-down',
        'plugins.achievement.achievement-notification-dot'
    )
    .defines(function () {

        ig.Achievement = {
            onClickNotification: new ig.AchievementSignal(),
            data: {
                achievements: []
            },
            claimReward: function (id) {
                this.data.achievements[id].claimed = true;
                this.saveData();
                ig.game.client.saveClaim(id);
            },

            incrementProgress: function (id, progress) {
                this.data.achievements[id].progress += progress;
                if (this.data.achievements[id].progress > this.list[id].goal) {
                    this.data.achievements[id].progress = this.list[id].goal
                }
                this.dataNeedSave = true;
            },

            decrementProgress: function (id, progress) {
                
                if(this.data.achievements[id].progress>0){
                    this.data.achievements[id].progress -= progress;
                    this.data.achievements[id].claimed   = false;
                }
                this.dataNeedSave = true;
            },

            getAchievementData: function (id) {
                try{
                return {
                    id: id,
                    name: this.list[id].name,
                    description: this.list[id].description,
                    reward: this.list[id].reward,
                    value: this.list[id].value,
                    goal: this.list[id].goal,
                    icon: this.list[id].icon,
                    progress: this.data.achievements[id].progress,
                    popupShown: this.data.achievements[id].popupShown,
                    claimed: this.data.achievements[id].claimed,
                    achieved: (this.data.achievements[id].progress >= this.list[id].goal)
                };
                }catch(e){
                    console.log(id);
                }
            },

            isAchievementClaimed: function (id) {
                return this.data.achievements[id].claimed;
            },

            isAchievementAchieved: function (id) {
                return (this.data.achievements[id].progress >= this.list[id].goal)
            },

            clearData: function () {
                this.data = { achievements: [] }
                for (var i = 0; i < this.list.length; i++) {
                    this.data.achievements.push({
                        id: i,
                        progress: 0,
                        popupShown: false,
                        claimed: false
                    })
                }
                this.saveData();
            },

            clearData2: function () {
                this.data = { achievements: [] }
                for (var i = 0; i < this.list.length; i++) {
                    this.data.achievements.push({
                        id: i,
                        progress: this.list[i].goal,
                        popupShown: false,
                        claimed: false
                    })
                }
                this.saveData();
            },

            saveData: function () {
                var dataString = JSON.stringify(this.data);
                ig.LSAchievement.set(this.saveName, dataString);
            },

            loadData: function () {
                if (!ig.LSAchievement) {
                    ig.LSAchievement = new SecureLS({ encodingType: 'aes' });
                    this.saveName = this.hash(ig.game.name + "-mjs-achievement-save-data", '').replace("-", "s");
                }
                var dataString = ig.LSAchievement.get(this.saveName)
                if (dataString == "") {
                    this.clearData();
                } else {
                    this.data = JSON.parse(dataString);
                }


            },

            hash: function (s) {
                var hash = 0, i, chr;
                if (s.length === 0) return hash;
                for (i = 0; i < s.length; i++) {
                    chr = s.charCodeAt(i);
                    hash = ((hash << 5) - hash) + chr;
                    hash |= 0; // Convert to 32bit integer
                }
                var hexHash = hash.toString(36);
                return hexHash;
            },
            notifShowStatus:true,
            notificationCheck: function () {
                if (ig && ig.game && !ig.Achievement.saveName) ig.Achievement.loadData();
                if (this.dataNeedSave) {
                    this.dataNeedSave = false;
                    this.saveData();
                }
                var nextCheckTime = 2000;
                if (ig && ig.game) {

                    if(this.notifShowStatus){
                        this.notifShowStatus = false;
                        ig.Achievement.notificationInstance = ig.game.spawnEntityBackward(ig.AchievementNotificationDropDown, 0, 0);                        
                    }

                    if (ig.Achievement.data && ig.Achievement.data.achievements && ig.Achievement.notificationInstance && !ig.Achievement.notificationInstance.isShown) {
                        for (var i = 0; i < ig.Achievement.data.achievements.length; i++) {
                            var data = ig.Achievement.getAchievementData(i);
                            if (data.achieved && !data.popupShown) {
                                ig.Achievement.notificationInstance.show(i);
                                break;
                            }
                        }
                    }

                }
                setTimeout(function () {
                    ig.Achievement.notificationCheck()
                }, nextCheckTime);
            }
        };

        ig.initAchievementSettings();
        
        if (ig && ig.game && !ig.Achievement.saveName) ig.Achievement.loadData();
        if (!ig.Achievement.notificationCheckRunning) {
            ig.Achievement.notificationCheckRunning = true;
            ig.Achievement.notificationCheck();
        }
    });
