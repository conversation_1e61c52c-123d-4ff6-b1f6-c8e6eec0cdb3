ig.module(
    'plugins.raycasting.ray'
)
.requires(
    'impact.entity'
)
.defines(function () {
    EntityRay = ig.Entity.extend({
        zIndex: 100,
        pos: new Vector2(0, 0),
        size: new Vector2(1, 1),
        dir: null,
        angle: 0,
        angle2: -0,
        parentEntity: null,
        frogEntity: null,

        init: function (x, y, settings) {
            this.parent(x, y, settings);
            this.dir = vectorFromAngle(this.angle);
            this.dir2 = vectorFromAngle(this.angle);
            this.frogEntity = this.parentEntity.frogEntity;
        },

        cast: function (pointA, pointB) {
            var x1 = pointA.x;
            var y1 = pointA.y;
            var x2 = pointB.x;
            var y2 = pointB.y;

            var x3 = this.pos.x;
            var y3 = this.pos.y;
            var x4 = this.pos.x + this.dir.x;
            var y4 = this.pos.y + this.dir.y;
            var den = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4);
            if (den == 0) {
                return;
            }

            var t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / den;
            var u = ((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / den;
            // console.log(t, u);
            if (t > 0 && t < 1 && u > 0) {
                // console.log('here2');
                var pt = new Vector2(0, 0);
                pt.x = x1 + t * (x2 - x1);
                pt.y = y1 + t * (y2 - y1);
                pt.u = u;
                return pt;
            } else {
                // console.log('here3');
                return;
            }
        },

        drawRay: function () {
            var ctx = ig.system.context;
            ctx.save();
            ctx.strokeStyle = '#ff0000';
            ctx.beginPath();
            ctx.moveTo(this.pos.x, this.pos.y);
            ctx.translate(this.pos.x, this.pos.y);
            ctx.lineTo(-this.dir.x * (10), -this.dir.y * (10));
            ctx.stroke();
            ctx.restore();
        },

        draw: function () {
            // this.parent();
        },

        update: function () {
            this.parent();
            if (ig.input.pressed('debug')) {
                // console.log(this.dir);
            }
            this.pos.x = this.parentEntity.pos.x;
            this.pos.y = this.parentEntity.pos.y;
            // this.angle = this.frogEntity.currentAnim.angle;
            this.dir = vectorFromAngle(this.angle);
        }
    });
});
