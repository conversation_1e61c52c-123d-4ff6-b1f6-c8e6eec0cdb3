ig.module(
    'dl.game.entity.components'
).requires(
    'dl.game.entity.components.component'
).defines(function () {
    'use strict';

    dl.EntityComponentsMixin = {
        staticInstantiate: function () {
            this.parent();

            this._components = [];
            this._componentsHash = {};

            return this;
        },

        init: function (settings) {
            this.initComponents();

            this.parent(settings);
        },

        update: function () {
            this.parent();

            for (var i = 0; i < this._components.length; i++) {
                var component = this._components[i];
                if (component.enable) {
                    component.update();
                }
            }
        },

        lateUpdate: function () {
            this.parent();

            for (var i = 0; i < this._components.length; i++) {
                var component = this._components[i];
                if (component.enable) {
                    component.lateUpdate();
                }
            }
        },

        draw: function (ctx) {
            this.parent(ctx);

            for (var i = 0; i < this._components.length; i++) {
                var component = this._components[i];
                if (component.enable) {
                    component.draw(ctx);
                }
            }
        },

        initComponents: function () {
            // To be implement for each entity
        },

        addComponent: function (componentClass, settings) {
            var component = new componentClass(this);
            component.init(settings || {});

            this._components.push(component);
            this._addComponentsHash(component);

            return component;
        },

        _addComponentsHash: function (component) {
            var hashArray = this.getComponents(component._componentName);
            if (!hashArray) {
                this._componentsHash[component._componentName] = [component];
            } else {
                hashArray.push(component);
            }
        },

        getComponents: function (name) {
            return this._componentsHash[name];
        },

        _mergeSettings: function (settings) {
            for (var propertyName in settings) {
                if (this[propertyName] instanceof dl.EntityComponent) {
                    this[propertyName].updateProperties(settings[propertyName]);

                    delete settings[propertyName];
                }
            }

            this.parent(settings);
        }
    };
});
