ig.module('plugins.achievement.achievement-settings')
    .requires()
    .defines(function () {

        ig.initAchievementSettings = function () {

            //Achievement list
            ig.Achievement.list = [
            /*0*/    { name: "Collect Daily Login", description: "Get reward for daily login", reward: "500 coins", goal: 1,value:500, icon: new ig.Image("media/graphics/game/achievement/acv1.png") },
            /*1*/    { name: "Taunt Enemy With Emoji", description: "Use emoji to get reward", reward: "1500 coins", goal: 3,value:1500, icon: new ig.Image("media/graphics/game/achievement/acv2.png") },
            /*2*/    { name: "Win 1 Match", description: "Get 1st position in 1 match", reward: "2500 coins", goal: 1,value:2500, icon: new ig.Image("media/graphics/game/achievement/acv3.png") },
            /*3*/    { name: "Win 2 Matches", description: "Get 1st position in 2 matches", reward: "4000 coins", goal: 2,value:4000, icon: new ig.Image("media/graphics/game/achievement/acv4.png") },
            /*4*/    { name: "Win 5 Matches", description: "Get 1st position in 5 matches", reward: "7500 coins", goal: 5,value:7500, icon: new ig.Image("media/graphics/game/achievement/acv5.png") },
            /*5*/    { name: "Win 10 Matches", description: "Get 1st position in 10 matches", reward: "12000 coins", goal: 10,value:12000, icon: new ig.Image("media/graphics/game/achievement/acv6.png") },
            /*6*/    { name: "Win 20 Matches", description: "Get 1st position in 20 matches", reward: "15000 coins", goal: 20,value:15000, icon: new ig.Image("media/graphics/game/achievement/acv7.png") }
            ];

            //Overlay Settings
            ig.Achievement.overlay = {
                color: "#000000",
                alpha: 0.6,
            }

            //Popup Settings
            ig.Achievement.popup = {
                width: 1200,
                height: 1800,
                color: "#ffffff",
                radius: 30,
                itemsPerPage: 5,
                itemsStartY: 470,
                autoSortItems: true,
                title: {
                    font: "40px montserrat-bold",
                    color: "#222222",
                    offsetY: 80,
                },

                item: {
                    width: 1100,
                    height: 200,
                    radius: 20,
                    color: "#dddddd",
                    spacing: 15,
                    innerSpacing: 15,
                    textSpacing: 10,

                    name: { font: "36px montserrat-bold", color: "#222222" },
                    description: { font: "30px montserrat", color: "#222222" },
                    reward: { font: "30px montserrat", color: "#222222" },

                    progress: {
                        font: "30px montserrat",
                        color: "#ffffff",
                        textOffsetY: -2,
                        bar: {
                            height: 40,
                            radius: 10,
                            color: "#44bb44",
                            colorBg: "#115511"
                        }
                    },

                    claimButton: {
                        x: 1000,
                        y: 70,
                        width: 160,
                        height: 80,
                        color: "#46ab46",
                        shadowColor: "#136a13",
                        font: "34px montserrat-bold",
                        textColor: "#ffffff",
                        textOffsetY: 0,
                    },

                    completedSign: {
                        x: 970,
                        y: 70,
                        width: 200,
                        height: 80,
                        color: "#555555",
                        font: "30px montserrat-bold",
                        textColor: "#ffffff",
                        textOffsetY: 2,
                    },

                    rewardEffect: {
                        width: 300,
                        height: 80,
                        color: "#e2b037",
                        font: "30px montserrat-bold",
                        textColor: "#ffffff",
                        timedLife: 0.5,
                        upSpeed: 80,
                    }
                },

                paging: {
                    button: {
                        width: 80,
                        height: 60,
                        color: "#606060",
                        shadowColor: "#1e5885",
                        symbolColor: "#ffffff",
                        symbolSize: 30,
                    },
                    display: {
                        font: "30px montserrat",
                        textColor: "#222222",
                        color: "#dddddd",
                        textOffsetY: 5,
                    }
                },

                closeButton: {
                    width: 60,
                    height: 60,
                    color: "#dd5555",
                    shadowColor: "#ae1d1d",
                    offsetX: 25,
                    offsetY: 25,
                    symbolColor: "#ffffff",
                    symbolSize: 25,
                },

                upgradeButton: {
                    width: 240,
                    height: 80,
                    color: "#dd5555",
                    shadowColor: "#ae1d1d",
                    offsetX: 25,
                    offsetY: 160,
                    symbolColor: "#ffffff",
                    symbolSize: 25,
                }
            };

            ig.Achievement.notificationDot = {
                color: "#ff5555",
                textColor: "#ffffff",
                size: 60,
                font: "30px montserrat",
                textOffsetY: 2,
            };

            ig.Achievement.notificationDropDown = {
                placement: "middle-left", //8 placement option : top-left, top-center, top-right,middle-left, middle-right, bottom-left, bottom-center, bottom-right
                width: 800,
                height: 200,
                color: "#222222",
                font: "36px montserrat",
                textColor: "#ffffff",
                textOffsetY: 50,
                lineSpacing: 2,
                padding: 40,
                showDuration: 2
            };

            //Sounds id
            ig.Achievement.sounds = {
                button: "click",
                claim: "achievementReward",
            };

            //Strings of texts used 
            ig.Achievement.strings = {
                achievementTitle: "Daily Event",
                reward: "Reward: ",
                claim: "Claim",
                completed: "Completed",
                unlocked: "Achievement Unlocked!",
                rewardFx: "Got +",
                ok: "Okay"
            };

            ig.Achievement.allowClaim = true;
        }
    });
