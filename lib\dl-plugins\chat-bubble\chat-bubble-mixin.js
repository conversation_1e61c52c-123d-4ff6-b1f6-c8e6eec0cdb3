// create namespace
dl.ChatBubble = {};

ig.module(
    'dl-plugins.chat-bubble.chat-bubble-mixin'
).requires(
    'dl-plugins.chat-bubble.chat-bubble'
).defines(function () {
    'use strict';

    dl.ChatBubble.Mixin = {
        _spawnChatBubble: function (configs) {
            // spawn entity
            var entity = this.spawnEntity(dl.ChatBubble.Entity, configs);

            // set chat bubble instance
            this.currentChatBubble = entity;

            return entity;
        },

        spawnChatBubble: function (configs) {
            if (this.currentChatBubble) {
                this.currentChatBubble.closeChatBubble();
                this.currentChatBubble = null;
            }

            if (!configs) configs = {};

            return this._spawnChatBubble(configs);
        }
    };
});
