ig.module(
    'dl-plugins.chat-bubble.factory.bubble'
).defines(function () {
    'use strict';

    dl.ChatBubble.Factory.Bubble = {
        generate: function (configs, contentCanvas) {
            var canvas = document.createElement('canvas');
            var context = canvas.getContext('2d');

            // update canvas size with content
            if (contentCanvas) {
                canvas.width = contentCanvas.width;
                canvas.height = contentCanvas.height;
            } else {
                canvas.width = configs.box.width;
                canvas.height = configs.box.height;
            }

            // update canvas size with bubble
            var contentArea = this.updateCanvasSize(configs, canvas);
            configs.contentArea = contentArea;

            // draw bubble
            this.draw(configs, context);
            this.drawContentCanvas(configs, context, contentCanvas);

            return {
                configs: configs,
                canvas: canvas
            };
        },

        updateCanvasSize: function (configs, canvas) {
            var width = canvas.width;
            var height = canvas.height;

            // content min size
            if (width < configs.box.width) width = configs.box.width;
            if (height < configs.box.height) height = configs.box.height;

            // content center pos
            var contentArea = {
                x: width * 0.5,
                y: height * 0.5,
                w: width,
                h: height
            };

            // add line width
            contentArea.x += configs.lineWidth;
            contentArea.y += configs.lineWidth;
            contentArea.w += configs.lineWidth;
            contentArea.h += configs.lineWidth;
            width += configs.lineWidth * 2;
            height += configs.lineWidth * 2;

            // add padding
            contentArea.x += configs.box.padding.x;
            contentArea.y += configs.box.padding.y;
            contentArea.w += configs.box.padding.x;
            contentArea.h += configs.box.padding.y;
            width += configs.box.padding.x * 2;
            height += configs.box.padding.y * 2;

            // add tail offset
            this.handleTailOffset(configs, contentArea);

            if (configs.tail.offset.x <= 0) contentArea.x += Math.abs(configs.tail.offset.x);
            if (configs.tail.offset.y <= 0) contentArea.y += Math.abs(configs.tail.offset.y);
            width += Math.abs(configs.tail.offset.x);
            height += Math.abs(configs.tail.offset.y);

            // add shadow offset
            contentArea.x += configs.shadowOffsetX;
            contentArea.y += configs.shadowOffsetY;
            width += configs.shadowOffsetX * 2;
            height += configs.shadowOffsetY * 2;

            // add shadow blur
            contentArea.x += configs.shadowBlur;
            contentArea.y += configs.shadowBlur;
            width += configs.shadowBlur * 2;
            height += configs.shadowBlur * 2;

            // update canvas size
            canvas.width = width;
            canvas.height = height;

            // return content center pos
            return contentArea;
        },

        handleTailOffset: function (configs, contentArea) {
            // prevent tail inside
            if (configs.tail.direction.x < 0) configs.tail.direction.x = 0;
            if (configs.tail.direction.x > 1) configs.tail.direction.x = 1;
            if (configs.tail.direction.y < 0) configs.tail.direction.y = 0;
            if (configs.tail.direction.y > 1) configs.tail.direction.y = 1;
            // lock a direction
            configs.tail.direction.xLock = (configs.tail.direction.x == 0) || (configs.tail.direction.x == 1);
            configs.tail.direction.yLock = (configs.tail.direction.y == 0) || (configs.tail.direction.y == 1);

            if (!configs.tail.direction.xLock && !configs.tail.direction.yLock) {
                var tempX = (configs.tail.direction.x - 0.5);
                var tempY = (configs.tail.direction.y - 0.5);
                if (Math.abs(tempX) > Math.abs(tempY)) {
                    configs.tail.direction.x = (tempX < 0) ? 0 : 1;
                    configs.tail.direction.xLock = true;
                } else {
                    configs.tail.direction.y = (tempY < 0) ? 0 : 1;
                    configs.tail.direction.yLock = true;
                }
            } else {
                if (configs.tail.direction.yLock && configs.tail.direction.xLock) {
                    if (contentArea.w > contentArea.h) {
                        configs.tail.direction.xLock = false;
                    } else {
                        configs.tail.direction.yLock = false;
                    }
                }
            }

            configs.tail.offset = {
                x: configs.tail.direction.xLock ? Math.round((configs.tail.direction.x - 0.5) * configs.tail.length * 2) : 0,
                y: configs.tail.direction.yLock ? Math.round((configs.tail.direction.y - 0.5) * configs.tail.length * 2) : 0
            };
        },

        draw: function (configs, ctx) {
            var round = configs.box.round;
            var x = configs.contentArea.x - configs.contentArea.w * 0.5;
            var y = configs.contentArea.y - configs.contentArea.h * 0.5;
            var w = configs.contentArea.w;
            var h = configs.contentArea.h;
            var xMax = x + w;
            var yMax = y + h;

            var tailWidth = configs.tail.width;
            var tail = {
                x: x + w * configs.tail.direction.x + configs.tail.offset.x,
                y: y + h * configs.tail.direction.y + configs.tail.offset.y
            };

            if (tail.y < y || tail.y > yMax) {
                var tail1 = Math.min(Math.max(x + round, tail.x - tailWidth), xMax - round - tailWidth * 2);
                var tail2 = Math.min(Math.max(x + round + tailWidth * 2, tail.x + tailWidth), xMax - round);
            } else {
                var tail1 = Math.min(Math.max(y + round, tail.y - tailWidth), yMax - round - tailWidth * 2);
                var tail2 = Math.min(Math.max(y + round + tailWidth * 2, tail.y + tailWidth), yMax - round);
            }

            var direction;
            if (tail.y < y) direction = 'top';
            if (tail.y > y) direction = 'bottom';
            if (tail.x < x && tail.y >= y && tail.y <= yMax) direction = 'left';
            if (tail.x > x && tail.y >= y && tail.y <= yMax) direction = 'right';
            if (tail.x >= x && tail.x <= xMax && tail.y >= y && tail.y <= yMax) direction = 'center'; // should'nt fall here

            ctx.save();
            ctx.beginPath();
            ctx.moveTo(x + round, y);
            if (direction == 'top') {
                ctx.lineTo(tail1, y);
                ctx.lineTo(tail.x, tail.y);
                ctx.lineTo(tail2, y);
                ctx.lineTo(xMax - round, y);
            } else ctx.lineTo(xMax - round, y);
            ctx.quadraticCurveTo(xMax, y, xMax, y + round);

            if (direction == 'right') {
                ctx.lineTo(xMax, tail1);
                ctx.lineTo(tail.x, tail.y);
                ctx.lineTo(xMax, tail2);
                ctx.lineTo(xMax, yMax - round);
            } else ctx.lineTo(xMax, yMax - round);
            ctx.quadraticCurveTo(xMax, yMax, xMax - round, yMax);

            if (direction == 'bottom') {
                ctx.lineTo(tail2, yMax);
                ctx.lineTo(tail.x, tail.y);
                ctx.lineTo(tail1, yMax);
                ctx.lineTo(x + round, yMax);
            } else ctx.lineTo(x + round, yMax);
            ctx.quadraticCurveTo(x, yMax, x, yMax - round);

            if (direction == 'left') {
                ctx.lineTo(x, tail2);
                ctx.lineTo(tail.x, tail.y);
                ctx.lineTo(x, tail1);
                ctx.lineTo(x, y + round);
            } else ctx.lineTo(x, y + round);
            ctx.quadraticCurveTo(x, y, x + round, y);
            ctx.closePath();

            ctx.save();
            ctx.shadowColor = configs.shadowColor;
            ctx.shadowBlur = configs.shadowBlur;
            ctx.shadowOffsetX = configs.shadowOffsetX;
            ctx.shadowOffsetY = configs.shadowOffsetY;
            ctx.fillStyle = configs.fillStyle;
            ctx.fill();
            ctx.restore();

            ctx.lineWidth = configs.lineWidth;
            ctx.strokeStyle = configs.strokeStyle;
            ctx.stroke();

            ctx.restore();
        },

        drawContentCanvas: function (configs, ctx, contentCanvas) {
            if (!contentCanvas) return ctx;

            var x = configs.contentArea.x - contentCanvas.width * 0.5;
            var y = configs.contentArea.y - contentCanvas.height * 0.5;

            ctx.drawImage(contentCanvas, x, y);
        }
    };

    dl.ChatBubble.Factory.Bubble.DEFAULT_CONFIGS = {
        lineWidth: 2,
        fillStyle: 'lightblue',
        strokeStyle: 'black',

        shadowColor: 'black',
        shadowBlur: 4,
        shadowOffsetX: 4,
        shadowOffsetY: 4,

        box: { width: 200, height: 100, round: 10, padding: { x: 10, y: 10 } },
        tail: { length: 30, width: 15, direction: { x: 0.5, y: 0 } }
    };
});
