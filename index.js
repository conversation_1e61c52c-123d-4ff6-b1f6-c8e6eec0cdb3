// var sslPath = '/etc/letsencrypt/live/testing.marketjs-multiplayer.com/';
// var sslPath = '/etc/letsencrypt/live/realtime3-singapore1.marketjs-multiplayer.com/';
// var sslPath = '/etc/letsencrypt/live/realtime2-amsterdam1.marketjs-multiplayer.com/';
var sslPath = '/etc/letsencrypt/live/realtime1-newyork1.marketjs-multiplayer.com/';

var privateKey = null;
var certificate = null;

try {
    var fs = require('fs');
    privateKey = fs.readFileSync(sslPath + 'privkey.pem');
    certificate = fs.readFileSync(sslPath + 'fullchain.pem');
    console.log('certificates found');
} catch (err) {
    console.log('certificates not found');
}

var credentials = {
    key: privateKey,
    cert: certificate
};

HOME_PATH=__dirname;

/**
 * socket io require
 */
var app = require('express')();
var http = require('http').Server(app);
var https = require('https').Server(credentials, app);
var io = require('socket.io')(http);
var io_https = require('socket.io')(https);
/**
 * Game requires
 */
var SERVER_CONFIGS = require('./lib/dl/network/shared/server-configs.js').SERVER_CONFIGS;
var NetworkServer = require('./lib/dl/network/server/network-server.js').NetworkServer;
var Host = require('./lib/dl/network/server/host.js').Host;
/**
 * Set up server messages
 */
var server = new NetworkServer();
var host = new Host(server);
global.serverHost = host;

var handler = function (socket) {
    server.onClientConnect(socket);
    socket.on('disconnect', function () {
        server.onClientDisconnect(socket);
    });
    socket.on(SERVER_CONFIGS.TAGS.MESSAGE, function (data) {
        server.onClientMessage(socket, data);
    });
    socket.on(SERVER_CONFIGS.TAGS.PING, function (data) {
        server.onClientPing(socket, data);
    });
    socket.on(SERVER_CONFIGS.TAGS.PING_REPLY, function (data) {
        server.onClientPingReply(socket, data);
    });
};

io.on('connection', handler);
io_https.on('connection', handler);

// Listen on HTTP port
http.listen(SERVER_CONFIGS.HTTP_PORT, function () {
    console.log('listening on *: ' + SERVER_CONFIGS.HTTP_PORT);
});
// Listen on HTTPS port
https.listen(SERVER_CONFIGS.HTTPS_PORT, function () {
    console.log('https listening on *: ' + SERVER_CONFIGS.HTTPS_PORT);
});

app.get('/health', (req, res) => {
    const data = {
      uptime: process.uptime(),
      message: 'Ok',
      date: new Date()
    }
 
    res.status(200).send(data);
  });

