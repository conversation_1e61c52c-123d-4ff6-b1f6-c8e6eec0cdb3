ig.module(
    'dl.game.entity.components.div-layers.div-layer'
).requires(
    'dl.game.entity.components.component',
    'dl.game.entity.components.div-layers.size-handler-extend'
).defines(function () {
    'use strict';

    dl.DivLayerComponent = dl.EntityComponent.extend({
        staticInstantiate: function (entity) {
            this.parent(entity);
            this._componentName = 'dl_DivLayerComponent';

            // position and size
            this.size = { x: 16, y: 16 };
            this.pos = { x: 0, y: 0 };

            // div properties
            this.divLayerName = 'ClickableDivLayer';
            this.link = null;
            this.callback = null;
            this.openInNewWindow = true;
            this.invisibleImagePath = 'media/graphics/misc/invisible.png';

            // bind events
            this.entity.onEvent('positionChanged', this.updateAll.bind(this));

            return this;
        },

        init: function (settings) {
            this.parent(settings);
            this.createClickableLayer();
        },

        updateAll: function () {
            this.updateSize();
            this.updatePos();

            ig.sizeHandler.dynamicClickableEntityDivs[this.divLayerName] = {};
            ig.sizeHandler.dynamicClickableEntityDivs[this.divLayerName].width = this.size.x;
            ig.sizeHandler.dynamicClickableEntityDivs[this.divLayerName].height = this.size.y;
            ig.sizeHandler.dynamicClickableEntityDivs[this.divLayerName].entity_pos_x = this.pos.x - this.size.x * 0.5;
            ig.sizeHandler.dynamicClickableEntityDivs[this.divLayerName].entity_pos_y = this.pos.y - this.size.y * 0.5;
            ig.sizeHandler.resizeLayersDiv();
        },

        updateSize: function () {
            var screeSize = dl.game.camera.getScreenSizeFromGameSize(this.entity.size.x, this.entity.size.y);
            this.size.x = Math.round(screeSize.x);
            this.size.y = Math.round(screeSize.y);
        },

        updatePos: function () {
            var screenPosition = dl.game.camera.getScreenPositionFromGamePosition(this.entity.pos.x, this.entity.pos.y);
            this.pos.x = Math.round(screenPosition.x);
            this.pos.y = Math.round(screenPosition.y);
        },

        createClickableLayer: function () {
            var id = '#' + this.divLayerName;
            var elem = ig.domHandler.getElementById(id);

            if (elem) {
                ig.domHandler.show(elem);
                if (this.link) {
                    ig.domHandler.attr(elem, 'href', this.link);
                }
                if (this.callback) {
                    ig.domHandler.attr(elem, 'onclick', this.callback);
                }
            } else {
                this.createClickableOutboundLayer();
            }
        },

        createClickableOutboundLayer: function () {
            var div = ig.domHandler.create('div');
            ig.domHandler.attr(div, 'id', this.divLayerName);

            /* PATCH FOR ISSUE #8 */
            /* PREVENT DEFAULT MOUSEDOWN BEHAVIOR */
            ig.domHandler.addEvent(div, 'mousedown', function (e) {
                e.preventDefault();
            });

            var newLink = ig.domHandler.create('a');

            if (this.callback) {
                ig.domHandler.attr(div, 'onclick', this.callback);
            }
            if (this.link) {
                if (this.openInNewWindow) {
                    ig.domHandler.attr(newLink, 'href', this.link);
                    ig.domHandler.attr(newLink, 'target', '_blank');
                }
                else {
                    ig.domHandler.attr(newLink, 'href', this.link);
                }
            }

            var linkImg = ig.domHandler.create('img');
            ig.domHandler.css(linkImg, { width: '100%', height: '100%' });
            ig.domHandler.attr(linkImg, 'src', this.invisibleImagePath);

            var aspectRatioMin = Math.min(ig.sizeHandler.scaleRatioMultiplier.x, ig.sizeHandler.scaleRatioMultiplier.y);
            if (ig.ua.mobile && !ig.ua.iPad) {
                var canvas = ig.domHandler.getElementById('#canvas');

                var offsets = ig.domHandler.getOffsets(canvas);

                var offsetLeft = offsets.left;
                var offsetTop = offsets.top;

                if (ig.sizeHandler.disableStretchToFitOnMobileFlag) {
                    var divleft = Math.floor(offsetLeft + this.pos.x * ig.sizeHandler.scaleRatioMultiplier.x) + 'px';
                    var divtop = Math.floor(offsetTop + this.pos.y * ig.sizeHandler.scaleRatioMultiplier.y) + 'px';
                    var divwidth = Math.floor(this.size.x * ig.sizeHandler.scaleRatioMultiplier.x) + 'px';
                    var divheight = Math.floor(this.size.y * ig.sizeHandler.scaleRatioMultiplier.y) + 'px';
                } else {
                    var divleft = Math.floor(this.pos.x * ig.sizeHandler.sizeRatio.x) + 'px';
                    var divtop = Math.floor(this.pos.y * ig.sizeHandler.sizeRatio.y) + 'px';
                    var divwidth = Math.floor(this.size.x * ig.sizeHandler.sizeRatio.x) + 'px';
                    var divheight = Math.floor(this.size.y * ig.sizeHandler.sizeRatio.y) + 'px';
                }

                ig.domHandler.css(div
                    , {
                        float: 'left',
                         position: 'absolute',
                         left: divleft,
                         top: divtop,
                         width: divwidth,
                         height: divheight,
                         'z-index': 3
                    }
                );
            } else {
                var canvas = ig.domHandler.getElementById('#canvas');

                var offsets = ig.domHandler.getOffsets(canvas);

                var offsetLeft = offsets.left;
                var offsetTop = offsets.top;
                if (ig.sizeHandler.enableStretchToFitOnDesktopFlag) {
                    var divleft = Math.floor(offsetLeft + this.pos.x * ig.sizeHandler.sizeRatio.x) + 'px';
                    var divtop = Math.floor(offsetTop + this.pos.y * ig.sizeHandler.sizeRatio.y) + 'px';
                    var divwidth = Math.floor(this.size.x * ig.sizeHandler.sizeRatio.x) + 'px';
                    var divheight = Math.floor(this.size.y * ig.sizeHandler.sizeRatio.y) + 'px';
                } else {
                    var divleft = Math.floor(offsetLeft + this.pos.x * aspectRatioMin) + 'px';
                    var divtop = Math.floor(offsetTop + this.pos.y * aspectRatioMin) + 'px';
                    var divwidth = Math.floor(this.size.x * aspectRatioMin) + 'px';
                    var divheight = Math.floor(this.size.y * aspectRatioMin) + 'px';
                }
                ig.domHandler.css(div
                    , {
                        float: 'left',
                         position: 'absolute',
                         left: divleft,
                         top: divtop,
                         width: divwidth,
                         height: divheight,
                         'z-index': 3
                    }
                );
            }

            ig.domHandler.addEvent(div, 'mousemove', ig.input.mousemove.bind(ig.input), false);
            ig.domHandler.appendChild(newLink, linkImg);
            ig.domHandler.appendChild(div, newLink);
            ig.domHandler.appendToBody(div);

            this.updateAll();
        },

        showDiv: function () {
            var id = '#' + this.divLayerName;
            var elem = ig.domHandler.getElementById(id);
            if (elem) {
                ig.domHandler.show(elem);
            }
        },

        hideDiv: function () {
            var id = '#' + this.divLayerName;
            var elem = ig.domHandler.getElementById(id);
            if (elem) {
                ig.domHandler.hide(elem);
            }
        }
    });
});
