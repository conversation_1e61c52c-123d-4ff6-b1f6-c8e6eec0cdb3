/**
 * Created by <PERSON><PERSON>
 * Handle room events on server side
 */

if (typeof require !== 'undefined') {
	// var SERVER_MESSAGE = require('../shared/server-message.js').SERVER_MESSAGE;
    var RoomEvent = require('../shared/room-event.js').RoomEvent;
    var NetworkGame = require('./network-game.js').NetworkGame;
    var ROOM_CONFIGS = require('./room-configs.js').ROOM_CONFIGS;
}

var RoomEventHandler = {};

RoomEventHandler.handleEvent = function (room, event) {
    switch (event.type) {
        case RoomEvent.EVENT_TYPE.PLAYER_JOIN:
            {
                return RoomEventHandler.onPlayerJoin(room, event);
            } break;

        case RoomEvent.EVENT_TYPE.REPLACE_MATCHING_BOT:
            {
                return RoomEventHandler.onReplaceMatchingBot(room, event);
            } break;


        case RoomEvent.EVENT_TYPE.PLAYER_LEAVE:
            {
                return RoomEventHandler.onPlayerLeave(room, event);
            } break;

        case RoomEvent.EVENT_TYPE.PLAYER_READY:
            {
                return RoomEventHandler.onPlayerReady(room, event);
            } break;

        case RoomEvent.EVENT_TYPE.AUTO_START_STATE_CHANGE:
            {
                return RoomEventHandler.onAutoStartStateChange(room, event);
            } break;

        case RoomEvent.EVENT_TYPE.KICK_PLAYER:
            {
                return RoomEventHandler.onKickPlayer(room, event);
            } break;

        case RoomEvent.EVENT_TYPE.SEND_EMOJI:
            {
                return RoomEventHandler.onSendEmoji(room, event);
            } break;

        case RoomEvent.EVENT_TYPE.ROOM_START:
            {
                return RoomEventHandler.onRoomStart(room, event);
            } break;

        case RoomEvent.EVENT_TYPE.ROOM_STARTED:
            {
                return RoomEventHandler.onRoomStarted(room, event);
            } break;

        case RoomEvent.EVENT_TYPE.ROOM_END:
            {
                return RoomEventHandler.onRoomEnd(room, event);
            } break;

        case RoomEvent.EVENT_TYPE.CLIENT_GAME_EVENT:
            {
                return RoomEventHandler.onClientGameEvent(room, event);
            } break;

        case RoomEvent.EVENT_TYPE.NETWORK_GAME_EVENT:
            {
                return RoomEventHandler.onNetworkGameEvent(room, event);
            } break;
        case RoomEvent.EVENT_TYPE.SYNC_PLAYER_COUNT:
            {
                return RoomEventHandler.onSyncPlayerCount(room, event);
            } break;
    }

    return;
};

// ----------------
// Start utilities functions
// ----------------
RoomEventHandler.sendEventToClient = function (room, event) {
    var data = {};
    data.event = event;
    data.roomData = room.packData();

    room.sendRoomEvent(data);
};

RoomEventHandler.handleAddBot = function (room) {
    if (!room.roomUseBot) return;

    // stop available timer
    if (room._roomAddBotTimer) {
        room._roomAddBotTimer.forceFinished(false);
        room._roomAddBotTimer = null;
    }

    // if not enough player - start new timer
    if (room.playerList.length < room.roomMaxPlayer) {
        room._roomAddBotTimer = room.startTimer(ROOM_CONFIGS.ADD_BOT_DELAY, function () {
            this._roomAddBotTimer = null;
            for (var i = room.playerList.length; i < room.roomMaxPlayer; i++) {
                room.startTimer(ROOM_CONFIGS.ADD_BOT_INTERVAL * i, function () {
                    this.addPlayer();
                }.bind(this)).start();
            }
        }.bind(room));
        room._roomAddBotTimer.start();
    }
};
// ----------------
// End utilities functions
// ----------------

RoomEventHandler.onReplaceMatchingBot = function (room, event) {
    if (!room.roomUseBot) return;

    // stop available timer
    if (room._roomAddBotTimer) {
        room._roomAddBotTimer.forceFinished(false);
        room._roomAddBotTimer = null;
    }

    // if not enough player - start new timer
    if (room.playerList.length < room.roomMaxPlayer) {
        room._roomAddBotTimer = room.startTimer(100, function () {
            this._roomAddBotTimer = null;
            for (var i = room.playerList.length; i < room.roomMaxPlayer; i++) {
                room.startTimer(1000 * i, function () {
                    this.addPlayer();
                }.bind(this)).start();
            }
        }.bind(room));
        room._roomAddBotTimer.start();
    }
};

RoomEventHandler.onPlayerJoin = function (room, event) {
    RoomEventHandler.handleAddBot(room);
    RoomEventHandler.sendEventToClient(room, event);        
    return true;
};

RoomEventHandler.onPlayerLeave = function (room, event) {
    // RoomEventHandler.handleAddBot(room);

    if (room.networkGame) {
        room.networkGame.playerLeave(event.info.playerId);
    }

    RoomEventHandler.sendEventToClient(room, event);
    return true;
};

RoomEventHandler.onPlayerReady = function (room, event) {
    var player = room.findPlayerById(event.clientPlayerId);
    if (!player) return;

    /**
     * startRoom if host player
     * otherwise set playerReady
     */

    if (room.roomHostPlayerId == player.playerId) {
        room.startRoom();
    } else {
        player.playerReady = (!player.playerReady);

        // Send player ready to client
        RoomEventHandler.sendEventToClient(room, room.createEvent(RoomEvent.EVENT_TYPE.PLAYER_READY, {
            playerId: player.playerId,
            playerName: player.playerName
        }));
    }

    return true;
};

RoomEventHandler.onAutoStartStateChange = function (room, event) {
    room.autoStartRoomFlag = event.info.newState;

    RoomEventHandler.sendEventToClient(room, event);
    return true;
};

RoomEventHandler.onKickPlayer = function (room, event) {
    if (event.clientPlayerId != room.roomHostPlayerId) return false;
    // if(typeof(SERVER_MESSAGE)=='undefined') return;

    var player = room.findPlayerById(event.info.kickPlayerId);
    if (!player) return;

    // if (player.socket && SERVER_MESSAGE && SERVER_MESSAGE.HOST && SERVER_MESSAGE.HOST.PLAYER_KICKED) {
    if (player.socket) {        
        room.host.server.sendMessage(player.socket, {
            type: 104,
            data: {}
            // type: SERVER_MESSAGE.HOST.PLAYER_KICKED,
            // data: {}
        });
    }
    room.removePlayer(player);

    return true;
};

RoomEventHandler.onSendEmoji = function (room, event) {
    event.info.startTime = Date.now();

    RoomEventHandler.sendEventToClient(room, event);
    return true;
};

RoomEventHandler.onRoomStart = function (room, event) {
    if(!room || !event) return;
    room.networkGame = new NetworkGame(room);
    // start timer
    room._roomStartingTimer = room.startTimer(ROOM_CONFIGS.STARTING_TIME, function () {
        this._roomStartingTimer = null;
        this.onRoomStarted();
    }.bind(room));

    RoomEventHandler.sendEventToClient(room, event);
    room._roomStartingTimer.start();
    return true;
};

RoomEventHandler.onRoomStarted = function (room, event) {
    room.networkGame.start();
    room.roomClose = true;
    RoomEventHandler.sendEventToClient(room, event);
    return true;
};

RoomEventHandler.onRoomEnd = function (room, event) {
    try{    
        room.networkGame.end();
        RoomEventHandler.sendEventToClient(room, event);
        return true;
    }catch(e){}
};

RoomEventHandler.onClientGameEvent = function (room, event) {
    if(room && room.networkGame && room.networkGame.addPendingEvent){
        room.networkGame.addPendingEvent(event.clientPlayerId, event.info);
    }
    
    return true;
};

RoomEventHandler.onNetworkGameEvent = function (room, event) {
    RoomEventHandler.sendEventToClient(room, event);
    return true;
};

RoomEventHandler.onSyncPlayerCount = function (room, event) {
    // console.log('server onSyncPlayerCount');
    RoomEventHandler.sendEventToClient(room, event);
    return true;
};

/**
 * Export module
 */
if (typeof module !== 'undefined') {
    module.exports.RoomEventHandler = RoomEventHandler;
}
