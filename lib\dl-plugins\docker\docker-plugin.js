ig.module(
    'dl-plugins.docker.docker-plugin'
).requires(
    'dl-plugins.docker.docker-component',
    'dl-plugins.docker.docker-mixin'
).defines(function () {
    'use strict';

    /**
     * Docker Plugin - Main Entry Point
     * 
     * A unified, self-contained plugin that provides Docker positioning functionality
     * for entities. This plugin consolidates the functionality from:
     * - lib/dl/game/entity/components/position/docker.js
     * - lib/dl/templates/mixins/docker.js
     * 
     * Features:
     * - Position entities relative to other objects
     * - Percentage-based positioning with offset support
     * - Event-driven position updates
     * - Easy integration via mixin pattern
     * - Backward compatibility with existing Docker usage
     * 
     * Usage:
     *   // Include in your module requirements
     *   .requires('dl-plugins.docker.docker-plugin')
     * 
     *   // Use the mixin for easy integration
     *   dl.Entity.extend(dl.Docker.Mixin).extend({
     *       // your entity implementation
     *   });
     * 
     *   // Or manually add the component
     *   this.c_DockerComponent = this.addComponent(dl.Docker.Component, {
     *       dockerObject: targetObject,
     *       dockerPercent: { x: 0.5, y: 0.5 },
     *       dockerOffset: { x: 0, y: 0 }
     *   });
     * 
     * Version: 1.0.0
     * Created by: Augment Agent (consolidating existing Docker functionality)
     */

    // Ensure Docker namespace exists
    if (!window.dl.Docker) {
        dl.Docker = {};
    }

    // Plugin metadata
    dl.Docker.Plugin = {
        version: '1.0.0',
        name: 'Docker Plugin',
        description: 'Unified Docker positioning plugin for entities',
        
        /**
         * Plugin initialization (optional)
         * Called when the plugin is first loaded
         */
        init: function () {
            // Plugin is ready to use
            console.log('Docker Plugin v' + this.version + ' loaded successfully');
        },

        /**
         * Create a Docker component instance
         * @param {Object} entity - Entity to attach component to
         * @param {Object} config - Optional configuration
         * @returns {dl.Docker.Component} Docker component instance
         */
        createComponent: function (entity, config) {
            return new dl.Docker.Component(entity).init(config || {});
        },

        /**
         * Helper method to quickly dock an entity to camera
         * @param {Object} entity - Entity to dock
         * @param {Object} options - Docker configuration
         */
        dockToCamera: function (entity, options) {
            options = options || {};
            
            if (!entity.c_DockerComponent) {
                entity.c_DockerComponent = entity.addComponent(dl.Docker.Component);
            }
            
            entity.c_DockerComponent.dockToCamera(options);
        },

        /**
         * Helper method to dock an entity to another object
         * @param {Object} entity - Entity to dock
         * @param {Object} dockerObject - Object to dock to
         * @param {Object} options - Docker configuration
         */
        dockTo: function (entity, dockerObject, options) {
            if (!entity.c_DockerComponent) {
                entity.c_DockerComponent = entity.addComponent(dl.Docker.Component);
            }
            
            entity.c_DockerComponent.setDockerObject(dockerObject, options);
        }
    };

    // Auto-initialize plugin
    dl.Docker.Plugin.init();

    // Backward compatibility aliases
    // This ensures existing code using dl.DockerComponent still works
    if (!dl.DockerComponent) {
        dl.DockerComponent = dl.Docker.Component;
    }
    
    if (!dl.MixinDocker) {
        dl.MixinDocker = dl.Docker.Mixin;
    }
});
