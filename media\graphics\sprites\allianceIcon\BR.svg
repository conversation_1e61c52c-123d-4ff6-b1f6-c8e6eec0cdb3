<svg width="32" height="24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <mask id="a" maskUnits="userSpaceOnUse" x="0" y="0" width="32" height="24">
    <path fill="#fff" d="M0 0h32v24H0z"/>
  </mask>
  <g mask="url(#a)">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0v24h32V0H0z" fill="#093"/>
    <mask id="b" maskUnits="userSpaceOnUse" x="0" y="0" width="32" height="24">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0v24h32V0H0z" fill="#fff"/>
    </mask>
    <g mask="url(#b)">
      <g filter="url(#BR_-_Brazil__filter0_d)" fill-rule="evenodd" clip-rule="evenodd">
        <path d="M15.927 3.704l12.202 8.504L15.76 20.17 3.809 12.043l12.118-8.339z" fill="#FFD221"/>
        <path d="M15.927 3.704l12.202 8.504L15.76 20.17 3.809 12.043l12.118-8.339z" fill="url(#BR_-_Brazil__paint0_linear)"/>
      </g>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M16 17.2a5 5 0 100-10 5 5 0 000 10z" fill="#2E42A5"/>
      <mask id="c" maskUnits="userSpaceOnUse" x="11" y="7" width="10" height="11">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M16 17.2a5 5 0 100-10 5 5 0 000 10z" fill="#fff"/>
      </mask>
      <g mask="url(#c)">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M14.38 14.57l-.223.117.042-.248-.18-.176.25-.037.111-.226.112.226.25.037-.181.176.042.248-.223-.117zm2 0l-.223.117.042-.248-.18-.176.25-.037.111-.226.112.226.25.037-.181.176.042.248-.223-.117zm0 1.2l-.223.117.042-.248-.18-.176.25-.037.111-.226.112.226.25.037-.181.176.042.248-.223-.117zm-1-4.2l-.223.117.042-.248-.18-.176.25-.037.111-.226.112.226.25.037-.181.176.042.248-.223-.117zm0 2l-.223.117.042-.248-.18-.176.25-.037.111-.226.112.226.25.037-.181.176.042.248-.223-.117zm-1.4-1l-.223.117.042-.248-.18-.176.25-.037.111-.226.112.226.25.037-.181.176.042.248-.223-.117zm-1.4.8l-.223.117.042-.248-.18-.176.25-.037.111-.226.112.226.25.037-.181.176.042.248-.223-.117zm4.6-3.4l-.223.117.042-.248-.18-.176.25-.037.111-.226.112.226.25.037-.181.176.042.248-.223-.117z" fill="#F7FCFF"/>
        <path d="M9.925 10.997l.15-1.994c4.798.362 8.585 1.94 11.313 4.745l-1.434 1.395c-2.364-2.433-5.692-3.819-10.03-4.146z" fill="#F7FCFF"/>
        <path d="M12.29 10.603l.05-.498c3.094.32 5.747 1.505 7.952 3.552l-.34.366c-2.125-1.971-4.677-3.11-7.663-3.42z" fill="#093"/>
      </g>
    </g>
  </g>
  <defs>
    <linearGradient id="BR_-_Brazil__paint0_linear" x1="32" y1="24" x2="32" y2="0" gradientUnits="userSpaceOnUse">
      <stop stop-color="#FFC600"/>
      <stop offset="1" stop-color="#FFDE42"/>
    </linearGradient>
    <filter id="BR_-_Brazil__filter0_d" x="3.809" y="3.704" width="24.32" height="16.467" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feColorMatrix values="0 0 0 0 0.0313726 0 0 0 0 0.368627 0 0 0 0 0 0 0 0 0.28 0"/>
      <feBlend in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
  </defs>
</svg>
