ig.module(
    'dl.templates.entities.buttons.entity-button-sound'
).requires(
    'dl.game.entity',
    'dl.templates.mixins.docker',
    'dl.templates.mixins.button',
    'dl.templates.mixins.animation-sheet',
    'dl.templates.mixins.button-effect'
).defines(function () {
    'use strict';

    dl.EntityButtonSound = dl.Entity
        .extend(dl.MixinDocker)
        .extend(dl.MixinButton)
        .extend(dl.MixinAnimationSheet)
        .extend(dl.MixinButtonScaleEffect)
        .extend({
            staticInstantiate: function () {
                this.parent();

                this._useAnimationSheetAsSize = true;
                this.image = dl.preload['button-sound'];

                return this;
            },

            postInit: function () {
                this.parent();

                this._updateAnimation(ig.game.sessionData.sound);
            },

            updateImage: function (image) {
                if (!image) return;

                this.c_AnimationSheetComponent.updateProperties({
                    _animationSheetImage: this.image,
                    _animationSheetRow: 1,
                    _animationSheetCol: 2,
                    _setupDefaultAnimation: function () {
                        this.addAnimation('off', 0, [0], true);
                        this.addAnimation('on', 0, [1], true);
                        this.setAnimation('off');
                    }
                });
            },

            _updateAnimation: function (newState) {
                if (newState) {
                    this.c_AnimationSheetComponent.setAnimation('on');
                } else {
                    this.c_AnimationSheetComponent.setAnimation('off');
                }
            },

            onPointerReleased: function () {
                this.onButtonClicked();
            },

            onButtonClicked: function () {
                var currentState = ig.game.sessionData.sound;
                currentState = !currentState;

                ig.game.sessionData.music = currentState;
                ig.soundHandler.bgmPlayer.volume(currentState ? 1 : 0);

                ig.game.sessionData.sound = currentState;
                ig.soundHandler.sfxPlayer.volume(currentState ? 1 : 0);

                ig.game.saveAll();

                this._updateAnimation(ig.game.sessionData.sound);
            }
        });

    // Enable cache
    dl.enableCacheCanvas(dl.EntityButtonSound);
});
