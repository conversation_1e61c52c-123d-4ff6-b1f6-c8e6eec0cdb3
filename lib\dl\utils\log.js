ig.module(
    'dl.utils.log'
).defines(function () {
    'use strict';

    dl.log = window.console.log.bind(window.console, '[Log]');
    dl.warn = window.console.error.bind(window.console, '[Warning]');
    dl.error = window.console.error.bind(window.console, '[Error]');

    // Calculate time
    dl.time = window.console.time.bind(window.console);
    dl.timeEnd = window.console.timeEnd.bind(window.console);

    // Warn
    dl.warnImplement = window.console.warn.bind(window.console, '[Not implemented]');
    dl.warnDefine = window.console.warn.bind(window.console, '[Not defined]');

    window.log = window.console.log.bind(window.console, '[Quick-Log]');
});
