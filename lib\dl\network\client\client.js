/**
 * Created by <PERSON><PERSON>
 * Handle server's messages.
 * Sending message to server
 * Store player and room information
 */
ig.module(
	'dl.network.client.client'
).requires(
	'dl.network.client.room'
).defines(function () {
	Client = ig.Class.extend({
		init: function (networkClient) {
			this.clientVersion = 0.01;
			this.networkClient = networkClient;

			this.connectedToServer = false;
			this.serverUserCount = 0;

			this.INVITE_CODE_KEY = 'code';
			this.INVITE_MAX_PLAYER_KEY = 'maxplayer';
			this.INVITE_SERVER = 'server';
			this.haveInviteCode = false;

			this.roomPassword = '';
			this.roomMaxPlayer = 2;

			this.clientGameRoom = null;
			this.roomId = null;
			this.playerId = null;
			this.newUserStatus = false;

			this.coinsfxStatus=false;

			// handlers
			this.networkClient.onServerConnectHandlers.push(this.onServerConnect.bind(this));
			this.networkClient.onServerDisconnectHandlers.push(this.onServerConnectFailed.bind(this));
			this.networkClient.onServerMessageHandlers.push(this.onServerMessage.bind(this));

			// reset and get information
			this.reset();
			this.getInviteCode();
			this.getMaxPlayer();
			this.getServer();
		
		},

		// ----------------
		// Start client's handler functions
		// ----------------
		onServerConnect: function () {
			this.connectedToServer = true;
			this.networkClient.pingServer();
		},

		onServerConnectFailed: function () {
			dl.log('Connection to server error');
			ig.game.resetApp();

			this.reset();
			this.serverUserCount = 0;
			this.connectedToServer = false;
		},

		onServerMessage: function (msg) {
			if (!msg) return;

			switch (msg.type) {
				case SERVER_MESSAGE.USER.CONFIRMED:
					this.onUserConfirmed(msg.data);
					break;
				case SERVER_MESSAGE.USER.CONNECTED:
					this.onUserConnected(msg.data);
					break;
				case SERVER_MESSAGE.USER.DISCONNECTED:
					this.onUserDisconnected(msg.data);
					break;

				/**
				 * Request return message
				 */
				case SERVER_MESSAGE.HOST.REQUEST_ROOM_SUCCEED:
					this.onRequestRoomSucceed(msg.data);
					break;
				case SERVER_MESSAGE.HOST.REQUEST_ROOM_REJECTED:
					this.onRequestRoomRejected(msg.data);
					break;
				case SERVER_MESSAGE.HOST.ROOM_EVENT:
					this.onRoomEvent(msg.data);
					break;
				case SERVER_MESSAGE.HOST.PLAYER_KICKED:
					this.onPlayerKicked(msg.data);
					break;
				case SERVER_MESSAGE.HOST.CHECK_PASSWORD_RESULT:
					this.onCheckPasswordResult(msg.data);
					break;
				case SERVER_MESSAGE.HOST.CHECK_NAME_RESULT:
					this.onCheckNameResult(msg.data);
					break;
				case SERVER_MESSAGE.HOST.SVAS_PLAYER_DATA_RESULT:
					this.onSvasGetData(msg.data);
					break;
				case SERVER_MESSAGE.HOST.SVAS_CLAIM_SUCCESS:
					this.onClaimSuccess(msg.data);
					break;
				case SERVER_MESSAGE.HOST.SVAS_RV_SUCCESS:
					this.onRvSuccess(msg.data);
					break;
				case SERVER_MESSAGE.HOST.SVAS_GEMS_SUCCESS:
					this.onGemsSuccess(msg.data);
					break;
				case SERVER_MESSAGE.HOST.SVAS_DEDUCT_SUCCESS:
					this.onDeductSuccess(msg.data);
					break;
				case SERVER_MESSAGE.HOST.SVAS_DEDUCT_GEMS_SUCCESS:
					this.onDeductGemsSuccess(msg.data);
					break;
				case SERVER_MESSAGE.HOST.ALLIANCE_GET_CLAIM_SUCCESS:
					this.onGetClaimSuccess(msg.data);
					break;


			}
		},
		// ----------------
		// End client's handler functions
		// ----------------

		// ----------------
		// Start server's message functions
		// ----------------
		onUserConfirmed: function (data) {
			this.serverUserCount = data;
		},

		onUserConnected: function (data) {
			this.serverUserCount = data;
		},

		onUserDisconnected: function (data) {
			this.serverUserCount = data;
		},

		onRequestRoomSucceed: function (data) {
			this.roomId = data.roomId;
			this.playerId = data.playerId;
			this.clientGameRoom = new ClientRoom(this, data);

			// callback
			if (typeof this.onRequestRoomSucceedCallback == 'function') {
				this.onRequestRoomSucceedCallback(data);
			}
			this.onRequestRoomSucceedCallback = null;
		},

		onRequestRoomRejected: function (data) {
			this.roomId = null;
			this.playerId = null;

			switch (data) {
				case SERVER_MESSAGE.REQUEST_REJECT_REASON.UNKNOWN:
					console.error('REQUEST REJECTED: UNKNOWN');
					break;
				case SERVER_MESSAGE.REQUEST_REJECT_REASON.SERVER_ERROR:
					console.error('REQUEST REJECTED: SERVER ERROR');
					break;
				case SERVER_MESSAGE.REQUEST_REJECT_REASON.INVALID_REQUEST:
					console.error('REQUEST REJECTED: INVALID REQUEST');
					break;
				case SERVER_MESSAGE.REQUEST_REJECT_REASON.VERSION_MISMATCH:
					console.error('REQUEST REJECTED: VERSION MISMATCH');
					break;
				case SERVER_MESSAGE.REQUEST_REJECT_REASON.CANNOT_FIND_ROOM:
					console.error('REQUEST REJECTED: CANNOT FIND ROOM');
					break;
			}

			// callback
			if (typeof this.onRequestRoomRejectedCallback == 'function') {
				this.onRequestRoomRejectedCallback(data);
			}
			this.onRequestRoomRejectedCallback = null;
		},

		onPlayerKicked: function (data) {
			dl.log('Player kicked');
			ig.game.kickedFlag = true;
			ig.game.resetApp();

			this.reset();
		},

		onRoomEvent: function (data) {
			if (!this.clientGameRoom) return;

			this.clientGameRoom.handleRoomEvent(data);
		},

		onCheckPasswordResult: function (data) {
			if (typeof this.onRequestCheckPasswordCallback == 'function') {
				this.onRequestCheckPasswordCallback(data);
			}
			this.onRequestCheckPasswordCallback = null;
		},

		onCheckNameResult: function (data) {
			if (typeof this.onRequestCheckNameCallback == 'function') {
				this.onRequestCheckNameCallback(data);
			}
			this.onRequestCheckNameCallback = null;
		},


		// ----------------
		// End server's message functions
		// ----------------

		// ----------------
		// Start client's request functions
		// ----------------
		requestRoom: function (requestInfo, successCallback, failCallback) {
			if (!this.networkClient || !this.networkClient.socket) {
				if (typeof failCallback == 'function') failCallback();
				return false;
			}

			if (!requestInfo || typeof requestInfo != 'object') {
				requestInfo = {};
			}
			requestInfo.clientVersion = this.clientVersion;

			// callbacks
			this.onRequestRoomSucceedCallback = successCallback;
			this.onRequestRoomRejectedCallback = failCallback;

			this.networkClient.sendMessage({
				type: SERVER_MESSAGE.CLIENT.REQUEST_ROOM,
				data: requestInfo
			});

			return true;
		},

		cancelRequestRoom: function () {
			this.resetRoom();

			if (!this.networkClient || !this.networkClient.socket) return false;

			this.networkClient.sendMessage({
				type: SERVER_MESSAGE.CLIENT.LEAVE_ROOM
			});

			return true;
		},

		sendRoomEvent: function (eventData) {
			this.networkClient.sendMessage({
				type: SERVER_MESSAGE.CLIENT.ROOM_EVENT,
				data: eventData
			});

			return true;
		},

		requestCheckPassword: function (callback) {
			if (!this.networkClient) return false;
			if (!this.networkClient.socket) return false;

			this.onRequestCheckPasswordCallback = callback;

			this.networkClient.sendMessage({
				type: SERVER_MESSAGE.CLIENT.CHECK_PASSWORD,
				data: {
					roomPassword: this.roomPassword
				}
			});

			return true;
		},

		requestCheckName: function (playerName, callback) {
			if (!this.networkClient) return false;
			if (!this.networkClient.socket) return false;

			this.onRequestCheckNameCallback = callback;

			this.networkClient.sendMessage({
				type: SERVER_MESSAGE.CLIENT.CHECK_NAME,
				data: {
					playerName: playerName
				}
			});

			return true;
		},

		registrationSuccess:function(svasId){
			this.networkClient.sendMessage({
				type: SERVER_MESSAGE.CLIENT.SVAS_REG_NEW_PLAYER_DATA,
				data: {
					playerSvasId: svasId
				}
			});
			return true;
		},
		requestSvasData: function (svasId, callback) {
			if(!svasId || svasId==0 || svasId==null) return;
			if (!this.networkClient) return false;
			if (!this.networkClient.socket) return false;
			this.onrequestSvasData = callback;

			if(typeof(ig.game.svas.hideLoader)=='function'){
				ig.game.svas.showLoader();
			}

			var data = {
					playerSvasId: svasId
				};

			// console.log(ig.game.newUserStatus);
			// if(ig.game.newUserStatus==true) return;
			this.getUserData(data);

		},

	    getUserData:function(data){            

	        // var FormData = require('form-data');        
	        var postdata = new FormData();
	        postdata.append('game_id',this.gameId);
	        postdata.append('uid',data.playerSvasId.toString());
	        var url="https://svas-consumer.marketjs-cloud.com/api/user/data/read";


	        this.callXHR('POST',url,5000,postdata,
	            function(response){
	                var resdata=response.message.data;
	                this.getCurrency(data,resdata);
	            }.bind(this),
	            function(response){
	                this.registrationSuccess(data.playerSvasId.toString());	                	
	            }.bind(this)
	        );
	    },
	    
	    getCurrency:function(data,playerdata){

		        // var FormData = require('form-data');        
		        var postdata = new FormData();
		        postdata.append('game_id',this.gameId);
		        postdata.append('uid',data.playerSvasId.toString());
	            var url="https://svas-consumer.marketjs-cloud.com/api/user/virtual_currency/read";

	            var pdata=JSON.parse(playerdata);
	            // console.log(pdata);

	            var date = new Date();
	            var day = date.getDate();
	            var month = date.getMonth() + 1;
	            var year = date.getFullYear();
	            var currentDate = day+'-'+month+'-'+year;

				var weekday = ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];
				var monthname = ["January","February","March","April","May","June","July","August","September","October","November","December"];
	            var dateUTC = date.getDate();
	            var dayUTC = date.getUTCDay();
	            var daynameUTC=weekday[dayUTC];
	            var monthUTC = date.getMonth() + 1;
	            var yearUTC = date.getFullYear();
	            var serverDate={date:dateUTC,month:monthUTC,year:yearUTC,day:daynameUTC,daynumber:dayUTC,monthname:monthname[monthUTC-1],rawDate:date};

	            // if(currentDate!=pdata.lastLogin){
	            if(currentDate!=pdata.lastLogin){
	            	pdata.loginStatus = 0;
	            }else{
	            	pdata.loginStatus = 1;            	
	            }

	            this.callXHR('POST',url,5000,postdata,
	                function(response){
	                    var resdata=response.message;
	                    var res ={playerData:pdata,playerCurrency:resdata,serverDate:serverDate};
						this.onSvasGetData(res);		                
		            }.bind(this),
	                function(response){
		                // console.log(response);
		            }.bind(this)
	            );    	
	    },

		resetSvasData:function(){
			console.log('reset to anonymous player');
			ig.game.loadUpgrade();
			ig.game.svas.userInfo={};
			ig.game.svas.userInfo.id=0;
			ig.game.svas.userInfo.nickname=' ';

			ig.svas.userInfo={};
			ig.svas.userInfo.id=0;
			ig.svas.userInfo.nickname=' ';

            var date = new Date();
            var day = date.getDate();
            var month = date.getMonth() + 1;
            var year = date.getFullYear();
            var currentDate = day+'-'+month+'-'+year;

			var weekday = ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];
			var monthname = ["January","February","March","April","May","June","July","August","September","October","November","December"];
            
            var dateUTC = date.getDate();
            var dayUTC = date.getUTCDay();
            var daynameUTC=weekday[dayUTC];
            var monthUTC = date.getMonth() + 1;
            var yearUTC = date.getFullYear();
            var serverDate={date:dateUTC,month:monthUTC,year:yearUTC,day:daynameUTC,daynumber:dayUTC,monthname:monthname[monthUTC-1]};

			ig.game.serverDate = currentDate;
			ig.game.svasData.game_id=0;
			ig.game.svasData.uid=0;
			ig.game.svasData.nickname='';
            ig.game.svasData.alliance = ''; 
            ig.game.svasData.allianceName = ''; 
            ig.game.svasData.allianceIcon = null; 
            ig.game.svasData.allianceShortName = ''; 
			ig.game.registerWaitStatus =false;

			if(ig.game.resetStatus==true){
			   ig.game.svasData.coins=1000;
			   ig.game.svasData.gems=10;
			   ig.game.svasData.alliance='';
    	       ig.game.balance=1000;
    	       ig.game.gemsbalance=10;
    	       ig.game.sessionData.balance=1000;
    	       ig.game.sessionData.gemsbalance=10;
			   ig.game.resetStatus=false;
			   ig.game.sessionData.upgradeRawData = ig.game.upgradeData;
               var saveName1 = this.hash(ig.game.name + "-tiered-rv-data-01");
			   ig.game.io.storage.remove(saveName1);
               var saveName2 = this.hash(ig.game.name + "-tiered-gems-data-01");
			   ig.game.io.storage.remove(saveName2);

   			   ig.game.shopData=ig.game.shopDataRaw;

	           ig.game.skinFortUsed=0;                    
	           ig.game.skinMissileUsed=0;                                        
	           ig.game.skinTowerUsed=0;                                        
	           ig.game.skinDomeUsed=0;                                        

				setTimeout(function(){

					ig.Achievement.clearData();
					ig.Achievement.dataNeedSave = true;	
					ig.Achievement.incrementProgress(0, 1);

				}.bind(this),2000);				


			}else{
				ig.game.svasData.coins=ig.game.sessionData.balance;				
	            ig.game.balance=ig.game.sessionData.balance;
			}

			ig.game.dayText=serverDate.day+', '+serverDate.monthname+' '+serverDate.date+', '+serverDate.year+' UTC';
			ig.game.dayText2=serverDate.monthname+' '+serverDate.date+', '+serverDate.year+' UTC';
			ig.game.today  =serverDate.daynumber;
            if(document.getElementById('InputTextLayer_PlayerName')) document.getElementById('InputTextLayer_PlayerName').disabled=false;                    

            ig.game.saveLocalStorage();                    
		},
        hash: function (s){
            var hash = 0, i, chr;
            if (s.length === 0) return hash;
            for (i = 0; i < s.length; i++) {
                chr = s.charCodeAt(i);
                hash = ((hash << 5) - hash) + chr;
                hash |= 0; // Convert to 32bit integer
            }
            var hexHash = hash.toString(36);
            return hexHash;
        },
		onSvasGetData: function (data) {
			ig.game.newUserStatus=false;
			console.log(data);
			ig.game.serverDate = data.serverDate;
			ig.game.svasData.game_id=data.playerCurrency.game_id;
			ig.game.svasData.uid=data.playerCurrency.uid;
			ig.game.svasData.nickname=data.playerCurrency.nickname;
			ig.game.svasData.coins=data.playerCurrency.virtual_currency1;
			ig.game.svasData.gems=data.playerCurrency.virtual_currency2;
	        ig.game.svasData.alliance=data.playerData.playerAlliance || ig.alliance.userInfo.alliance;                                        

			ig.game.registerWaitStatus =false;

	        this.getAllianceData(ig.game.svasData.alliance);

			ig.game.gemsbalance=ig.game.svasData.gems;
			ig.game.sessionData.gemsbalance=ig.game.svasData.gems;

			ig.game.sessionData.playerName = data.playerCurrency.nickname
            ig.game.saveLocalStorage();                    

			ig.game.dayText=ig.game.serverDate.day+', '+ig.game.serverDate.monthname+' '+ig.game.serverDate.date+', '+ig.game.serverDate.year+' UTC'
			ig.game.dayText2=ig.game.serverDate.monthname+' '+ig.game.serverDate.date+', '+ig.game.serverDate.year+' UTC'
			ig.game.today  =ig.game.serverDate.daynumber;

			ig.game.rawDate = ig.game.serverDate.rawDate;

			if(data.playerData) this.setGameData(data.playerData);
			window.playerNameInput.updateData();
			ig.game.backendproccess=false;

			if(typeof(ig.game.svas.hideLoader)=='function'){
				ig.game.svas.hideLoader();
			}

			if(this.coinsfxStatus){
				this.coinsfxStatus=false;
				ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.coins1);
			}

            if(document.getElementById('InputTextLayer_PlayerName')) document.getElementById('InputTextLayer_PlayerName').disabled=true;                    
		},
        getAllianceData:function(allianceId){
        	if(allianceId=="") return;

            var url= "https://svas-consumer.marketjs-cloud.com/api/alliance/read";
            var method= "GET";
            var timeout= 5000;
            url = url + ('?game_id=' + this.gameId) + ('&alliance_id='+allianceId);

            this.callXHR(method, url, timeout, null, function(response) {
            	if(this.isMemberAlliance(response.data.members)){
	                ig.game.svasData.allianceName = response.data.name;
	                ig.game.svasData.allianceShortName = response.data.shortcode;                          
	                ig.game.svasData.allianceIcon = response.data.icon_id;                      		
            	}else{
            		ig.alliance.userInfo.alliance='';
            		ig.game.svasData.alliance='';
            	}
            }.bind(this), function(response) {
        		ig.alliance.userInfo.alliance='';
        		ig.game.svasData.alliance='';
            }.bind(this));
        },		
        isMemberAlliance:function(members){
            var result=false;
            for(var i=0;i<members.length;i++){
                if(members[i].uid==ig.game.svasData.uid){
                    var result=true;
                }
            }
            return result;
        },
        gameId: '1651673491261401',
        callXHR: function(method, url, timeout, data, successfulCallback, errorCallback) {

            var xhr = new XMLHttpRequest();
            xhr.open(method, url, true);
            xhr.timeout = timeout || 5000;
            xhr.onreadystatechange = function() {
            	// console.log(JSON.parse(xhr.responseText));
                if (xhr.readyState === 4) {
                    var response = "";
                    try {
                        response = JSON.parse(xhr.responseText);
                        if (xhr.status === 200) {
                            if (successfulCallback !== null && typeof (successfulCallback) === "function") {
                                successfulCallback(response);
                            }
                        }
                        else {
                            if (errorCallback !== null && typeof (errorCallback) === "function") {
                                errorCallback(response);
                            }
                        }
                    } catch(error) {
                        // if (errorCallback !== null && typeof (errorCallback) === "function") {
                        //     errorCallback(response);
                        // }
                    }
                }
            }.bind(this);

            if (data !== null && typeof (data) !== "undefined") {
            	try{
            		// console.log(url);
	                xhr.send(data);
	            }catch(e){}
            } else {
                xhr.send();
            }
        },
        getUpgradeMultiplier:function(){
        	var cc=ig.game.today-1;
        	if(cc==-1) cc=0;
            var upgradeData=ig.game.sessionData.upgradeRawData[cc];
            var result=0;
            for(var i=0;i<upgradeData.length;i++){
                if(upgradeData[i]==1){
                    result +=1;
                }
            }                
            ig.game.multiplierValue=result;
        },

		setGameData:function(data){

			if(!data.acvReward){
				ig.Achievement.clearData();
			}else{
				ig.Achievement.data.achievements = data.acvReward;
				ig.Achievement.dataNeedSave = true;	
			}
			if(data.upgradeData){
				ig.game.sessionData.upgradeRawData = data.upgradeData;
			}else{
				ig.game.sessionData.upgradeRawData = ig.game.upgradeData;
			}
            ig.game.saveLocalStorage();                    
			this.getUpgradeMultiplier();


			ig.game.rvData=data.rvdata;
            var saveName = this.hash(ig.game.name + "-tiered-rv-data-01");
            var dataString = JSON.stringify(ig.game.rvData);            
            var ls = new SecureLS({ encodingType: 'aes' });
            ls.set(saveName, dataString);            	

   			if(data.shopData) ig.game.shopData=data.shopData;
            if(data.gemsdata) ig.game.gemsData=data.gemsdata;
            if(data.gemsplayed) ig.game.gemsPlayed=data.gemsplayed;

	        if(data.skinFortUsed) ig.game.skinFortUsed=data.skinFortUsed;                    
	        if(data.skinMissileUsed) ig.game.skinMissileUsed=data.skinMissileUsed;                                        
	        if(data.skinTowerUsed) ig.game.skinTowerUsed=data.skinTowerUsed;                                        
	        if(data.skinDomeUsed) ig.game.skinDomeUsed=data.skinDomeUsed;                                        


            
            var saveName = this.hash(ig.game.name + "-tiered-gems-data-01");
            var dataString = JSON.stringify(ig.game.gemsData);            
            var ls = new SecureLS({ encodingType: 'aes' });
            ls.set(saveName, dataString);            	

            var saveName2 = this.hash(ig.game.name + "-tiered-gems-data-01-playedtime");
            var dataString2 = JSON.stringify(ig.game.gemsPlayed);            
            var ls = new SecureLS({ encodingType: 'aes' });
            ls.set(saveName2, dataString2);            	


			if(window.rvplugin){
				setTimeout(function(){
					window.rvplugin.tiersData_cur = ig.game.rvData;
				},1000);
			}

			if(window.gemsplugin){
				setTimeout(function(){
					window.gemsplugin.tiersData_cur = ig.game.gemsData;
					window.gemsplugin.tierCurPlayed = ig.game.gemsPlayed;
				},1000);
			}

			if(data.loginStatus==0){
				setTimeout(function(){
					ig.Achievement.clearData();
 					setTimeout(function(){
						ig.Achievement.incrementProgress(0, 1);
					}.bind(this),400);				
				}.bind(this),400);				

			}		
		},
		getAllGameData:function(){
            var date = new Date();
            var day = date.getDate();
            var month = date.getMonth() + 1;
            var year = date.getFullYear();
            var currentDate = ig.game.serverDate.date+'-'+ig.game.serverDate.month+'-'+ig.game.serverDate.year;

            var data ={lastLogin:currentDate,
	            	      acvReward:ig.Achievement.data.achievements,
	            	      upgradeData:ig.game.sessionData.upgradeRawData,
	            	      playerSvasId:ig.game.svasData.uid,
	            	      rvdata:ig.game.rvData,
	            	      gemsdata:ig.game.gemsData,
	            	      gemsplayed:ig.game.gemsPlayed,
	            	      shopData:ig.game.shopData,
				          skinFortUsed:ig.game.skinFortUsed,                    
				          skinMissileUsed:ig.game.skinMissileUsed,                                        
				          skinTowerUsed:ig.game.skinTowerUsed,                                        
				          skinDomeUsed:ig.game.skinDomeUsed,
				          playerAlliance:ig.game.svasData.alliance,                                        
	            	  };
	          return data;
		},

		saveGameData:function(){
			this.networkClient.sendMessage({
				type: SERVER_MESSAGE.CLIENT.SVAS_SAVE_GAME_DATA,
				data: this.getAllGameData()
			});
			return true;
		},

		saveClaim:function(achievementId){

			if(ig.game.svasData.uid==0){
				var claimValue=ig.Achievement.list[achievementId].value;
				ig.game.svasData.coins +=claimValue;
	            ig.game.balance=ig.game.svasData.coins;				
	            ig.game.sessionData.balance=ig.game.svasData.coins;				
                ig.game.saveLocalStorage();                    
	            return;
			}

			if(typeof(ig.game.svas.hideLoader)=='function'){
				ig.game.svas.showLoader();
			}
			this.coinsfxStatus=true;
            var data={
            			achievementId:achievementId,
            	        playerSvasId:ig.game.svasData.uid,
            	        bonusMultiplier:ig.game.multiplierValue,
        				alldata:this.getAllGameData()
            	  	 };

			this.networkClient.sendMessage({
				type: SERVER_MESSAGE.CLIENT.SVAS_SAVE_CLAIM_ACHIEVEMENT,
				data: data,
			});

			return true;
		},
		saveRv:function(rvValue){

			if(ig.game.svasData.uid==0){
				ig.game.svasData.coins +=rvValue;
	            ig.game.balance=ig.game.svasData.coins;				
	            ig.game.sessionData.balance=ig.game.svasData.coins;				
                ig.game.saveLocalStorage();                    
	            return;
			}

			if(typeof(ig.game.svas.hideLoader)=='function'){
				ig.game.svas.showLoader();
			}
			this.coinsfxStatus=true;
			// ig.soundHandler.sfxPlayer.play(ig.soundHandler.sfxPlayer.soundList.coins1);
            var data={
            			rvValue:rvValue,
            	        playerSvasId:ig.game.svasData.uid
            	  	 };
			this.networkClient.sendMessage({
				type: SERVER_MESSAGE.CLIENT.SVAS_SAVE_RV_VALUE,
				data: data
			});
			return true;
		},
		saveGems:function(gemsValue){

			if(ig.game.svasData.uid==0){
				ig.game.svasData.gems +=gemsValue;
	            ig.game.gemsbalance=ig.game.svasData.gems;				
	            ig.game.sessionData.gemsbalance=ig.game.svasData.gems;				
                ig.game.saveLocalStorage();                    
	            return;
			}

			if(typeof(ig.game.svas.hideLoader)=='function'){
				ig.game.svas.showLoader();
			}
			this.coinsfxStatus=true;
            var data={
            			gemsValue:gemsValue,
            	        playerSvasId:ig.game.svasData.uid
            	  	 };
			this.networkClient.sendMessage({
				type: SERVER_MESSAGE.CLIENT.SVAS_SAVE_GEMS_VALUE,
				data: data
			});
			return true;
		},

		getGiftList:function(alliance,receiver){

			if(typeof(ig.game.svas.hideLoader)=='function'){
				ig.game.svas.showLoader();
			}
			this.coinsfxStatus=true;
            var data={
            			allianceId:alliance,
            	        receiverId:receiver
            	  	 };
			this.networkClient.sendMessage({
				type: SERVER_MESSAGE.CLIENT.ALLIANCE_GET_CLAIM,
				data: data
			});
			return true;
		},
		onGetClaimSuccess:function(data){
			if(typeof(ig.game.svas.hideLoader)=='function'){
				ig.game.svas.hideLoader();
			}
			// console.log(data);
			ig.alliance.listGiftAllianceDiv(data.data);

		},
		claimGift:function(alliance,receiver,giftid){

			if(typeof(ig.game.svas.hideLoader)=='function'){
				ig.game.svas.showLoader();
			}
			this.coinsfxStatus=true;
            var data={
            			allianceId:alliance,
            	        receiverId:receiver,
            	        giftId:giftid,
            	        playerSvasId:ig.game.svasData.uid,
            	  	 };
			this.networkClient.sendMessage({
				type: SERVER_MESSAGE.CLIENT.ALLIANCE_CLAIM_GIFT,
				data: data
			});
			return true;
		},

		deductUpgrade:function(deductValue){
			if(ig.game.svasData.uid==0){
				ig.game.svasData.coins -=deductValue;
	            ig.game.balance=ig.game.svasData.coins;				
	            ig.game.sessionData.balance=ig.game.svasData.coins;				
                ig.game.saveLocalStorage();                    
	            return;
			}
			if(typeof(ig.game.svas.hideLoader)=='function'){
				ig.game.svas.showLoader();
    		}

			this.coinsfxStatus=true;

            var data={
            			deductValue:deductValue,
            	        playerSvasId:ig.game.svasData.uid,
        				alldata:this.getAllGameData()
            	  	 };
			this.networkClient.sendMessage({
				type: SERVER_MESSAGE.CLIENT.SVAS_DEDUCT_UPGRADE,
				data: data
			});
			return true;
		},

		buySkin:function(buildingIndex,skinIndex,gemsValue){
			if(ig.game.svasData.uid==0){
				ig.game.svasData.gems -=gemsValue;
	            ig.game.gemsbalance=ig.game.svasData.gems;				
	            ig.game.sessionData.gemsbalance=ig.game.svasData.gems;				
                ig.game.saveLocalStorage();                    
	            return;
			}
			if(typeof(ig.game.svas.hideLoader)=='function'){
				ig.game.svas.showLoader();
    		}

			this.coinsfxStatus=true;

            var data={	buildingIndex:buildingIndex,
            			skinIndex:skinIndex,
            	        playerSvasId:ig.game.svasData.uid
            	  	 };
			this.networkClient.sendMessage({
				type: SERVER_MESSAGE.CLIENT.SVAS_DEDUCT_GEMS,
				data: data
			});
			return true;
		},

		onClaimSuccess:function(data){
			this.saveGameData();				
		},
		onRvSuccess:function(data){
			this.saveGameData();
		},
		onGemsSuccess:function(data){
			this.saveGameData();
		},
		onDeductSuccess:function(data){
			this.saveGameData();
		},
		onDeductGemsSuccess:function(data){
			this.saveGameData();
		},

		// ----------------
		// End client's request functions
		// ----------------

		// ----------------
		// Start utilities functions
		// ----------------
		reset: function () {
			// this.roomPassword = '';
			this.resetRoom();
		},

		resetRoom: function () {
			this.clientGameRoom = null;
			this.roomId = null;
			this.playerId = null;
		},

		getInviteCode: function () {
			this.roomPassword = getQueryVariable(this.INVITE_CODE_KEY) || '';
			if(this.roomPassword!==''){
				ig.game.isHost = false;
			}else{
				ig.game.isHost = true;
			}

			var url = window.location.href;
			var removeString = '&' + this.INVITE_CODE_KEY + '=' + encodeURIComponent(this.roomPassword);
			var newUrl = url.replace(removeString, '');
			window.history.replaceState(null, null, newUrl);

			if (this.roomPassword != '') {
				this.haveInviteCode = true;
			}
		},

		getMaxPlayer: function () {
			this.roomMaxPlayer = parseInt(getQueryVariable(this.INVITE_MAX_PLAYER_KEY)) || 2;
			this.roommax = this.roomMaxPlayer;

			var url = window.location.href;
			var removeString = '&' + this.INVITE_MAX_PLAYER_KEY + '=' + encodeURIComponent(this.roomMaxPlayer);
			var newUrl = url.replace(removeString, '');
			window.history.replaceState(null, null, newUrl);
		},

		getServer: function () {
			dl.game.serverIP = getQueryVariable(this.INVITE_SERVER) || 'xxx';
			if(dl.game.serverIP!='xxx'){
				ig.game.disableBtStatus = true;
				ig.game.defaultServer=dl.game.serverIP;
                ig.game.sessionData.defaultServer = ig.game.defaultServer;
                ig.game.saveLocalStorage();
			}

			var url = window.location.href;
			var removeString = '&' + this.INVITE_SERVER + '=' + encodeURIComponent(ig.game.defaultServer);
			var newUrl = url.replace(removeString, '');
			window.history.replaceState(null, null, newUrl);				
		},

		isMyPlayerId: function (checkId) {
			if (typeof (checkId) !== 'number') return false;
			if (typeof (ig.game.client.playerId) !== 'number') return false;

			return ig.game.client.playerId == checkId;
		}
		// ----------------
		// End utilities functions
		// ----------------
	});
});
