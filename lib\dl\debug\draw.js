ig.module(
    'dl.debug.draw'
).requires(
    'dl.debug.debug'
).defines(function () {
    'use strict';

    /**
     * Draw call
     */
    dl.debug.DRAW_CALL = true;
    dl.debug.resetDrawCall = function () {
        this.drawCall = 0;
    };
    dl.debug.increaseDrawCall = function () {
        this.drawCall += 1;
    };
    dl.debug.drawDrawCall = function () {
        var ctx = dl.debug.getContext();
        ctx.save();
        ctx.font = dl.debug.FONT_SIZE + 'px Arial';
        ctx.fillText(
            'Draw: ' + this.drawCall,
            0,
            0 + dl.debug.FONT_SIZE * 1);
        ctx.stroke();
        ctx.restore();
    };

    dl.debug.enableDebugDraw = function () {
        MyGame.inject({
            draw: function () {
                dl.debug.resetDrawCall();
                this.parent();
                dl.debug.drawDrawCall();
            }
        });
    };
});
